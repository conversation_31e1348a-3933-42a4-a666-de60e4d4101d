# Enhanced Classification Script with Table Output

## Overview

The enhanced `test_classification.py` script now includes a comprehensive table output feature that displays classification results in an easy-to-read format at the end of each run.

## New Features

### 📋 Classification Results Table

At the end of each classification run, the script now prints a detailed table showing:

- **PDF File Links**: Clickable file:// links to open PDFs directly from terminal
- **Page Numbers**: Individual page numbers for multi-page documents
- **Classified Categories**: The document type classification for each page
- **JSON Output Links**: Clickable links to the corresponding JSON result files
- **Compact View**: A condensed table for quick overview

### Example Output

```
============================================================================================================================================
📋 CLASSIFICATION RESULTS TABLE
============================================================================================================================================
PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK
--------------------------------------------------------------------------------------------------------------------------------------------
invoice_document.pdf                               1      invoice              run1_invoice_document.json
  PDF → file:///path/to/invoice_document.pdf
  JSON→ file:///path/to/output/run1_invoice_document.json

bol_document.pdf                                   1      bol                  run1_bol_document.json
  PDF → file:///path/to/bol_document.pdf
  JSON→ file:///path/to/output/run1_bol_document.json

multi_page_doc.pdf                                 1      pack_list            run1_multi_page_doc.json
  PDF → file:///path/to/multi_page_doc.pdf
  JSON→ file:///path/to/output/run1_multi_page_doc.json

multi_page_doc.pdf                                 2      pack_list            run1_multi_page_doc.json
  PDF → file:///path/to/multi_page_doc.pdf
  JSON→ file:///path/to/output/run1_multi_page_doc.json
--------------------------------------------------------------------------------------------------------------------------------------------
Total entries: 4
============================================================================================================================================

📋 COMPACT TABLE VIEW:
----------------------------------------------------------------------------------------------------
  1. invoice_document.pdf                Page 1   → invoice         | run1_invoice_document.json
  2. bol_document.pdf                    Page 1   → bol             | run1_bol_document.json
  3. multi_page_doc.pdf                  Page 1   → pack_list       | run1_multi_page_doc.json
  4. multi_page_doc.pdf                  Page 2   → pack_list       | run1_multi_page_doc.json
----------------------------------------------------------------------------------------------------
```

## Usage

### Command Line Usage

```bash
# Basic usage with table output
python scripts/test_classification.py /path/to/documents --max-files 10

# Specify output directory
python scripts/test_classification.py /path/to/documents --output-dir results --run-number 1
```

### Programmatic Usage

```python
from scripts.test_classification import FileProcessor

async def classify_with_table():
    processor = FileProcessor("output", "logs")
    
    stats = await processor.process_files(
        folder_path="/path/to/documents",
        max_files=10,
        run_number=1
    )
    
    # Table is automatically printed at the end
    return stats
```

## Benefits

### 🎯 Easy Verification
- Quickly verify if classifications are correct
- Spot misclassifications at a glance
- Compare expected vs actual document types

### 🔗 Direct Access
- Click file:// links to open PDFs directly
- Click JSON links to view detailed classification results
- No need to navigate to file locations manually
- Instant access for verification and analysis

### 📊 Comprehensive Overview
- See all processed files in one view
- Track page-by-page classifications
- Monitor processing results efficiently

### 🔍 Quality Control
- Identify patterns in misclassifications
- Review specific documents that need attention
- Validate classification accuracy across batches

## File Structure

```
Augment_test/
├── test_classification_table_demo.py    # Demo script
├── example_usage.py                     # Usage examples
├── README_table_feature.md              # This documentation
├── output/                              # Test output files
├── logs/                               # Test log files
└── output_multi/                       # Multi-category test output
```

## Testing

Run the demo script to see the table feature in action:

```bash
python Augment_test/test_classification_table_demo.py
```

Run the example usage script:

```bash
python Augment_test/example_usage.py
```

## Technical Details

### Table Generation
- Extracts classification results from JSON output
- Maps filenames to full file paths for clickable links
- Handles multi-page documents with separate entries per page
- Provides both detailed and compact table formats

### File Link Format
- Uses `file://` protocol for terminal compatibility
- Shows absolute paths for reliable access
- Compatible with most terminal applications

### Error Handling
- Gracefully handles missing classification data
- Shows "No classification" for failed documents
- Maintains table structure even with partial failures

## Integration

The table feature is automatically integrated into the existing `test_classification.py` script. No additional setup required - just run the script as usual and the table will appear at the end of processing.
