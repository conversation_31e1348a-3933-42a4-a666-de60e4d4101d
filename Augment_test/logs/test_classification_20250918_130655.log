2025-09-18 13:06:55,679 - INFO - Logging initialized. Log file: Augment_test/logs/test_classification_20250918_130655.log
2025-09-18 13:06:55,680 - INFO - ⬆️ [13:06:55] Uploading: 1_bol_1.pdf
2025-09-18 13:06:57,300 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/1_bol/1_bol_1.pdf -> s3://document-extraction-logistically/temp/62ccc41a_1_bol_1.pdf
2025-09-18 13:06:57,301 - INFO - 🔍 [13:06:57] Starting classification: 1_bol_1.pdf
2025-09-18 13:06:57,303 - INFO - Initializing TextractProcessor...
2025-09-18 13:06:57,317 - INFO - Initializing BedrockProcessor...
2025-09-18 13:06:57,321 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/62ccc41a_1_bol_1.pdf
2025-09-18 13:06:57,322 - INFO - Processing PDF from S3...
2025-09-18 13:06:57,322 - INFO - Downloading PDF from S3 to /tmp/tmpl_apcvit/62ccc41a_1_bol_1.pdf
2025-09-18 13:06:58,749 - INFO - Splitting PDF into individual pages...
2025-09-18 13:06:58,751 - INFO - Splitting PDF 62ccc41a_1_bol_1 into 1 pages
2025-09-18 13:06:58,754 - INFO - Split PDF into 1 pages
2025-09-18 13:06:58,754 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:06:58,754 - INFO - Expected pages: [1]
2025-09-18 13:07:05,305 - INFO - Page 1: Extracted 2681 characters, 83 lines from 62ccc41a_1_bol_1_c7e5078c_page_001.pdf
2025-09-18 13:07:05,305 - INFO - Successfully processed page 1
2025-09-18 13:07:05,305 - INFO - Combined 1 pages into final text
2025-09-18 13:07:05,305 - INFO - Text validation for 62ccc41a_1_bol_1: 2698 characters, 1 pages
2025-09-18 13:07:05,306 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:07:05,306 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:07:07,055 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/62ccc41a_1_bol_1.pdf
2025-09-18 13:07:07,093 - INFO - 

1_bol_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 13:07:07,094 - INFO - 

✓ Saved result: Augment_test/output_multi/run998_1_bol_1.json
2025-09-18 13:07:08,044 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/62ccc41a_1_bol_1.pdf
2025-09-18 13:07:08,044 - INFO - ⬆️ [13:07:08] Uploading: 10_invoice_1.pdf
2025-09-18 13:07:08,908 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice/10_invoice_1.pdf -> s3://document-extraction-logistically/temp/098582a6_10_invoice_1.pdf
2025-09-18 13:07:08,908 - INFO - 🔍 [13:07:08] Starting classification: 10_invoice_1.pdf
2025-09-18 13:07:08,910 - INFO - Initializing TextractProcessor...
2025-09-18 13:07:08,953 - INFO - Initializing BedrockProcessor...
2025-09-18 13:07:08,956 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/098582a6_10_invoice_1.pdf
2025-09-18 13:07:08,956 - INFO - Processing PDF from S3...
2025-09-18 13:07:08,956 - INFO - Downloading PDF from S3 to /tmp/tmpafzo6n2p/098582a6_10_invoice_1.pdf
2025-09-18 13:07:10,201 - INFO - Splitting PDF into individual pages...
2025-09-18 13:07:10,202 - INFO - Splitting PDF 098582a6_10_invoice_1 into 1 pages
2025-09-18 13:07:10,203 - INFO - Split PDF into 1 pages
2025-09-18 13:07:10,203 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:07:10,203 - INFO - Expected pages: [1]
2025-09-18 13:07:15,229 - INFO - Page 1: Extracted 1418 characters, 97 lines from 098582a6_10_invoice_1_53385485_page_001.pdf
2025-09-18 13:07:15,230 - INFO - Successfully processed page 1
2025-09-18 13:07:15,230 - INFO - Combined 1 pages into final text
2025-09-18 13:07:15,231 - INFO - Text validation for 098582a6_10_invoice_1: 1435 characters, 1 pages
2025-09-18 13:07:15,231 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:07:15,231 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:07:18,074 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/098582a6_10_invoice_1.pdf
2025-09-18 13:07:18,118 - INFO - 

10_invoice_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 13:07:18,118 - INFO - 

✓ Saved result: Augment_test/output_multi/run998_10_invoice_1.json
2025-09-18 13:07:19,408 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/098582a6_10_invoice_1.pdf
2025-09-18 13:07:19,408 - INFO - ⬆️ [13:07:19] Uploading: 2_pod_1.pdf
2025-09-18 13:07:22,819 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_1.pdf -> s3://document-extraction-logistically/temp/b9c710f5_2_pod_1.pdf
2025-09-18 13:07:22,820 - INFO - 🔍 [13:07:22] Starting classification: 2_pod_1.pdf
2025-09-18 13:07:22,822 - INFO - Initializing TextractProcessor...
2025-09-18 13:07:22,834 - INFO - Initializing BedrockProcessor...
2025-09-18 13:07:22,839 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b9c710f5_2_pod_1.pdf
2025-09-18 13:07:22,839 - INFO - Processing PDF from S3...
2025-09-18 13:07:22,839 - INFO - Downloading PDF from S3 to /tmp/tmpn19wwji1/b9c710f5_2_pod_1.pdf
2025-09-18 13:07:25,471 - INFO - Splitting PDF into individual pages...
2025-09-18 13:07:25,473 - INFO - Splitting PDF b9c710f5_2_pod_1 into 3 pages
2025-09-18 13:07:25,479 - INFO - Split PDF into 3 pages
2025-09-18 13:07:25,479 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:07:25,479 - INFO - Expected pages: [1, 2, 3]
2025-09-18 13:07:31,271 - INFO - Page 3: Extracted 441 characters, 20 lines from b9c710f5_2_pod_1_6d89298f_page_003.pdf
2025-09-18 13:07:31,271 - INFO - Successfully processed page 3
2025-09-18 13:07:33,353 - INFO - Page 2: Extracted 1895 characters, 65 lines from b9c710f5_2_pod_1_6d89298f_page_002.pdf
2025-09-18 13:07:33,353 - INFO - Successfully processed page 2
2025-09-18 13:07:33,907 - INFO - Page 1: Extracted 1597 characters, 78 lines from b9c710f5_2_pod_1_6d89298f_page_001.pdf
2025-09-18 13:07:33,907 - INFO - Successfully processed page 1
2025-09-18 13:07:33,907 - INFO - Combined 3 pages into final text
2025-09-18 13:07:33,908 - INFO - Text validation for b9c710f5_2_pod_1: 3988 characters, 3 pages
2025-09-18 13:07:33,908 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:07:33,909 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:07:37,031 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b9c710f5_2_pod_1.pdf
2025-09-18 13:07:37,089 - INFO - 

2_pod_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        },
        {
            "page_no": 2,
            "doc_type": "pack_list"
        },
        {
            "page_no": 3,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 13:07:37,089 - INFO - 

✓ Saved result: Augment_test/output_multi/run998_2_pod_1.json
2025-09-18 13:07:38,345 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b9c710f5_2_pod_1.pdf
2025-09-18 13:07:38,345 - INFO - 
========================================================================================================================
2025-09-18 13:07:38,345 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 13:07:38,345 - INFO - ========================================================================================================================
2025-09-18 13:07:38,346 - INFO - PDF FILE LINK                                                PAGE     CLASSIFIED CATEGORY           
2025-09-18 13:07:38,346 - INFO - ------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:07:38,346 - INFO - 1_bol_1.pdf                                                  1        bol                           
2025-09-18 13:07:38,346 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/1_bol/1_bol_1.pdf
2025-09-18 13:07:38,346 - INFO - 
2025-09-18 13:07:38,346 - INFO - 10_invoice_1.pdf                                             1        invoice                       
2025-09-18 13:07:38,346 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice/10_invoice_1.pdf
2025-09-18 13:07:38,346 - INFO - 
2025-09-18 13:07:38,346 - INFO - 2_pod_1.pdf                                                  1        pack_list                     
2025-09-18 13:07:38,347 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_1.pdf
2025-09-18 13:07:38,347 - INFO - 
2025-09-18 13:07:38,347 - INFO - 2_pod_1.pdf                                                  2        pack_list                     
2025-09-18 13:07:38,347 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_1.pdf
2025-09-18 13:07:38,347 - INFO - 
2025-09-18 13:07:38,347 - INFO - 2_pod_1.pdf                                                  3        pack_list                     
2025-09-18 13:07:38,347 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_1.pdf
2025-09-18 13:07:38,347 - INFO - 
2025-09-18 13:07:38,347 - INFO - ------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:07:38,348 - INFO - Total entries: 5
2025-09-18 13:07:38,348 - INFO - ========================================================================================================================
2025-09-18 13:07:38,348 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 13:07:38,348 - INFO - --------------------------------------------------------------------------------
2025-09-18 13:07:38,348 - INFO -   1. 1_bol_1.pdf                              Page 1   → bol
2025-09-18 13:07:38,348 - INFO -   2. 10_invoice_1.pdf                         Page 1   → invoice
2025-09-18 13:07:38,348 - INFO -   3. 2_pod_1.pdf                              Page 1   → pack_list
2025-09-18 13:07:38,348 - INFO -   4. 2_pod_1.pdf                              Page 2   → pack_list
2025-09-18 13:07:38,348 - INFO -   5. 2_pod_1.pdf                              Page 3   → pack_list
2025-09-18 13:07:38,348 - INFO - --------------------------------------------------------------------------------
