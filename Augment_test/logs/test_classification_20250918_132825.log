2025-09-18 13:28:25,193 - INFO - Logging initialized. Log file: Augment_test/logs/test_classification_20250918_132825.log
2025-09-18 13:28:25,194 - INFO - 📁 Found 3 files to process
2025-09-18 13:28:25,194 - INFO - 🚀 Starting processing with run number: 999
2025-09-18 13:28:25,194 - INFO - 🚀 Processing 3 files in FORCED PARALLEL MODE...
2025-09-18 13:28:25,194 - INFO - 🚀 Creating 3 parallel tasks...
2025-09-18 13:28:25,194 - INFO - 🚀 All 3 tasks created - executing in parallel...
2025-09-18 13:28:25,194 - INFO - ⬆️ [13:28:25] Uploading: 6_scale_ticket_1.pdf
2025-09-18 13:28:27,988 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_1.pdf -> s3://document-extraction-logistically/temp/5ef340e9_6_scale_ticket_1.pdf
2025-09-18 13:28:27,988 - INFO - 🔍 [13:28:27] Starting classification: 6_scale_ticket_1.pdf
2025-09-18 13:28:27,990 - INFO - ⬆️ [13:28:27] Uploading: 6_scale_ticket_10.png
2025-09-18 13:28:27,990 - INFO - Initializing TextractProcessor...
2025-09-18 13:28:28,018 - INFO - Initializing BedrockProcessor...
2025-09-18 13:28:28,024 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5ef340e9_6_scale_ticket_1.pdf
2025-09-18 13:28:28,024 - INFO - Processing PDF from S3...
2025-09-18 13:28:28,025 - INFO - Downloading PDF from S3 to /tmp/tmp77hxo_zq/5ef340e9_6_scale_ticket_1.pdf
2025-09-18 13:28:29,217 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_10.png -> s3://document-extraction-logistically/temp/854e8f1f_6_scale_ticket_10.png
2025-09-18 13:28:29,217 - INFO - 🔍 [13:28:29] Starting classification: 6_scale_ticket_10.png
2025-09-18 13:28:29,218 - INFO - ⬆️ [13:28:29] Uploading: 6_scale_ticket_11.jpg
2025-09-18 13:28:29,220 - INFO - Initializing TextractProcessor...
2025-09-18 13:28:29,238 - INFO - Initializing BedrockProcessor...
2025-09-18 13:28:29,242 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/854e8f1f_6_scale_ticket_10.png
2025-09-18 13:28:29,242 - INFO - Processing image from S3...
2025-09-18 13:28:30,662 - INFO - Splitting PDF into individual pages...
2025-09-18 13:28:30,665 - INFO - Splitting PDF 5ef340e9_6_scale_ticket_1 into 1 pages
2025-09-18 13:28:30,668 - INFO - Split PDF into 1 pages
2025-09-18 13:28:30,668 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:28:30,668 - INFO - Expected pages: [1]
2025-09-18 13:28:30,702 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_11.jpg -> s3://document-extraction-logistically/temp/5bde3a4e_6_scale_ticket_11.jpg
2025-09-18 13:28:30,703 - INFO - 🔍 [13:28:30] Starting classification: 6_scale_ticket_11.jpg
2025-09-18 13:28:30,704 - INFO - Initializing TextractProcessor...
2025-09-18 13:28:30,716 - INFO - Initializing BedrockProcessor...
2025-09-18 13:28:30,721 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5bde3a4e_6_scale_ticket_11.jpg
2025-09-18 13:28:30,721 - INFO - Processing image from S3...
2025-09-18 13:28:32,306 - INFO - S3 Image temp/854e8f1f_6_scale_ticket_10.png: Extracted 751 characters, 49 lines
2025-09-18 13:28:32,306 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:28:32,306 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:28:33,859 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/854e8f1f_6_scale_ticket_10.png
2025-09-18 13:28:33,881 - INFO - 

6_scale_ticket_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:28:33,881 - INFO - 

✓ Saved result: Augment_test/output/run999_6_scale_ticket_10.json
2025-09-18 13:28:34,179 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/854e8f1f_6_scale_ticket_10.png
2025-09-18 13:28:34,328 - INFO - S3 Image temp/5bde3a4e_6_scale_ticket_11.jpg: Extracted 361 characters, 32 lines
2025-09-18 13:28:34,328 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:28:34,328 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:28:36,299 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5bde3a4e_6_scale_ticket_11.jpg
2025-09-18 13:28:36,315 - INFO - 

6_scale_ticket_11.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:28:36,315 - INFO - 

✓ Saved result: Augment_test/output/run999_6_scale_ticket_11.json
2025-09-18 13:28:36,614 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5bde3a4e_6_scale_ticket_11.jpg
2025-09-18 13:28:37,221 - INFO - Page 1: Extracted 1382 characters, 62 lines from 5ef340e9_6_scale_ticket_1_d9c0b55c_page_001.pdf
2025-09-18 13:28:37,222 - INFO - Successfully processed page 1
2025-09-18 13:28:37,222 - INFO - Combined 1 pages into final text
2025-09-18 13:28:37,223 - INFO - Text validation for 5ef340e9_6_scale_ticket_1: 1399 characters, 1 pages
2025-09-18 13:28:37,223 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:28:37,223 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:28:39,493 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5ef340e9_6_scale_ticket_1.pdf
2025-09-18 13:28:39,525 - INFO - 

6_scale_ticket_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:28:39,525 - INFO - 

✓ Saved result: Augment_test/output/run999_6_scale_ticket_1.json
2025-09-18 13:28:39,821 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5ef340e9_6_scale_ticket_1.pdf
2025-09-18 13:28:39,822 - INFO - 
📊 Processing Summary:
2025-09-18 13:28:39,823 - INFO -    Total files: 3
2025-09-18 13:28:39,823 - INFO -    Successful: 3
2025-09-18 13:28:39,823 - INFO -    Failed: 0
2025-09-18 13:28:39,823 - INFO -    Duration: 14.63 seconds
2025-09-18 13:28:39,823 - INFO -    Output directory: Augment_test/output
2025-09-18 13:28:39,823 - INFO - 
📋 Successfully Processed Files:
2025-09-18 13:28:39,823 - INFO -    📄 6_scale_ticket_1.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:28:39,823 - INFO -    📄 6_scale_ticket_10.png: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:28:39,824 - INFO -    📄 6_scale_ticket_11.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:28:39,824 - INFO - 
============================================================================================================================================
2025-09-18 13:28:39,824 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 13:28:39,824 - INFO - ============================================================================================================================================
2025-09-18 13:28:39,824 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 13:28:39,824 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:28:39,825 - INFO - 6_scale_ticket_1.pdf                               1      scale_ticket         run999_6_scale_ticket_1.json                      
2025-09-18 13:28:39,825 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_1.pdf
2025-09-18 13:28:39,825 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/Augment_test/output/run999_6_scale_ticket_1.json
2025-09-18 13:28:39,825 - INFO - 
2025-09-18 13:28:39,825 - INFO - 6_scale_ticket_10.png                              1      scale_ticket         run999_6_scale_ticket_10.json                     
2025-09-18 13:28:39,825 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_10.png
2025-09-18 13:28:39,825 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/Augment_test/output/run999_6_scale_ticket_10.json
2025-09-18 13:28:39,825 - INFO - 
2025-09-18 13:28:39,825 - INFO - 6_scale_ticket_11.jpg                              1      scale_ticket         run999_6_scale_ticket_11.json                     
2025-09-18 13:28:39,826 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_11.jpg
2025-09-18 13:28:39,826 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/Augment_test/output/run999_6_scale_ticket_11.json
2025-09-18 13:28:39,826 - INFO - 
2025-09-18 13:28:39,826 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:28:39,826 - INFO - Total entries: 3
2025-09-18 13:28:39,826 - INFO - ============================================================================================================================================
2025-09-18 13:28:39,826 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 13:28:39,826 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 13:28:39,826 - INFO -   1. 6_scale_ticket_1.pdf                Page 1   → scale_ticket    | run999_6_scale_ticket_1.json
2025-09-18 13:28:39,827 - INFO -   2. 6_scale_ticket_10.png               Page 1   → scale_ticket    | run999_6_scale_ticket_10.json
2025-09-18 13:28:39,827 - INFO -   3. 6_scale_ticket_11.jpg               Page 1   → scale_ticket    | run999_6_scale_ticket_11.json
2025-09-18 13:28:39,827 - INFO - ----------------------------------------------------------------------------------------------------
