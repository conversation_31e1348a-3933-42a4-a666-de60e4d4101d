2025-09-18 13:28:39,842 - INFO - Logging initialized. Log file: Augment_test/logs/test_classification_20250918_132839.log
2025-09-18 13:28:39,842 - INFO - ⬆️ [13:28:39] Uploading: 1_bol_1.pdf
2025-09-18 13:28:41,421 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/1_bol/1_bol_1.pdf -> s3://document-extraction-logistically/temp/350698e5_1_bol_1.pdf
2025-09-18 13:28:41,421 - INFO - 🔍 [13:28:41] Starting classification: 1_bol_1.pdf
2025-09-18 13:28:41,422 - INFO - Initializing TextractProcessor...
2025-09-18 13:28:41,439 - INFO - Initializing BedrockProcessor...
2025-09-18 13:28:41,444 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/350698e5_1_bol_1.pdf
2025-09-18 13:28:41,444 - INFO - Processing PDF from S3...
2025-09-18 13:28:41,444 - INFO - Downloading PDF from S3 to /tmp/tmpqi32jpui/350698e5_1_bol_1.pdf
2025-09-18 13:28:43,144 - INFO - Splitting PDF into individual pages...
2025-09-18 13:28:43,146 - INFO - Splitting PDF 350698e5_1_bol_1 into 1 pages
2025-09-18 13:28:43,149 - INFO - Split PDF into 1 pages
2025-09-18 13:28:43,149 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:28:43,149 - INFO - Expected pages: [1]
2025-09-18 13:28:50,228 - INFO - Page 1: Extracted 2681 characters, 83 lines from 350698e5_1_bol_1_b90e8b67_page_001.pdf
2025-09-18 13:28:50,229 - INFO - Successfully processed page 1
2025-09-18 13:28:50,229 - INFO - Combined 1 pages into final text
2025-09-18 13:28:50,229 - INFO - Text validation for 350698e5_1_bol_1: 2698 characters, 1 pages
2025-09-18 13:28:50,229 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:28:50,229 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:28:52,341 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/350698e5_1_bol_1.pdf
2025-09-18 13:28:52,398 - INFO - 

1_bol_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 13:28:52,398 - INFO - 

✓ Saved result: Augment_test/output_multi/run998_1_bol_1.json
2025-09-18 13:28:53,433 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/350698e5_1_bol_1.pdf
2025-09-18 13:28:53,434 - INFO - ⬆️ [13:28:53] Uploading: 10_invoice_1.pdf
2025-09-18 13:28:54,515 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice/10_invoice_1.pdf -> s3://document-extraction-logistically/temp/409fbf8d_10_invoice_1.pdf
2025-09-18 13:28:54,515 - INFO - 🔍 [13:28:54] Starting classification: 10_invoice_1.pdf
2025-09-18 13:28:54,518 - INFO - Initializing TextractProcessor...
2025-09-18 13:28:54,564 - INFO - Initializing BedrockProcessor...
2025-09-18 13:28:54,566 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/409fbf8d_10_invoice_1.pdf
2025-09-18 13:28:54,567 - INFO - Processing PDF from S3...
2025-09-18 13:28:54,567 - INFO - Downloading PDF from S3 to /tmp/tmp05dzkb4i/409fbf8d_10_invoice_1.pdf
2025-09-18 13:28:56,660 - INFO - Splitting PDF into individual pages...
2025-09-18 13:28:56,662 - INFO - Splitting PDF 409fbf8d_10_invoice_1 into 1 pages
2025-09-18 13:28:56,666 - INFO - Split PDF into 1 pages
2025-09-18 13:28:56,666 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:28:56,667 - INFO - Expected pages: [1]
2025-09-18 13:29:01,697 - INFO - Page 1: Extracted 1418 characters, 97 lines from 409fbf8d_10_invoice_1_5cc25dc8_page_001.pdf
2025-09-18 13:29:01,697 - INFO - Successfully processed page 1
2025-09-18 13:29:01,697 - INFO - Combined 1 pages into final text
2025-09-18 13:29:01,697 - INFO - Text validation for 409fbf8d_10_invoice_1: 1435 characters, 1 pages
2025-09-18 13:29:01,697 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:29:01,697 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:29:04,438 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/409fbf8d_10_invoice_1.pdf
2025-09-18 13:29:04,461 - INFO - 

10_invoice_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 13:29:04,461 - INFO - 

✓ Saved result: Augment_test/output_multi/run998_10_invoice_1.json
2025-09-18 13:29:05,572 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/409fbf8d_10_invoice_1.pdf
2025-09-18 13:29:05,572 - INFO - ⬆️ [13:29:05] Uploading: 2_pod_1.pdf
2025-09-18 13:29:08,538 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_1.pdf -> s3://document-extraction-logistically/temp/eca2489f_2_pod_1.pdf
2025-09-18 13:29:08,538 - INFO - 🔍 [13:29:08] Starting classification: 2_pod_1.pdf
2025-09-18 13:29:08,540 - INFO - Initializing TextractProcessor...
2025-09-18 13:29:08,556 - INFO - Initializing BedrockProcessor...
2025-09-18 13:29:08,561 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/eca2489f_2_pod_1.pdf
2025-09-18 13:29:08,561 - INFO - Processing PDF from S3...
2025-09-18 13:29:08,562 - INFO - Downloading PDF from S3 to /tmp/tmpx0dfs1bu/eca2489f_2_pod_1.pdf
2025-09-18 13:29:11,203 - INFO - Splitting PDF into individual pages...
2025-09-18 13:29:11,205 - INFO - Splitting PDF eca2489f_2_pod_1 into 3 pages
2025-09-18 13:29:11,210 - INFO - Split PDF into 3 pages
2025-09-18 13:29:11,210 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:29:11,210 - INFO - Expected pages: [1, 2, 3]
2025-09-18 13:29:17,065 - INFO - Page 3: Extracted 441 characters, 20 lines from eca2489f_2_pod_1_aac198aa_page_003.pdf
2025-09-18 13:29:17,066 - INFO - Successfully processed page 3
2025-09-18 13:29:18,289 - INFO - Page 1: Extracted 1597 characters, 78 lines from eca2489f_2_pod_1_aac198aa_page_001.pdf
2025-09-18 13:29:18,290 - INFO - Successfully processed page 1
2025-09-18 13:29:20,200 - INFO - Page 2: Extracted 1895 characters, 65 lines from eca2489f_2_pod_1_aac198aa_page_002.pdf
2025-09-18 13:29:20,200 - INFO - Successfully processed page 2
2025-09-18 13:29:20,200 - INFO - Combined 3 pages into final text
2025-09-18 13:29:20,201 - INFO - Text validation for eca2489f_2_pod_1: 3988 characters, 3 pages
2025-09-18 13:29:20,201 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:29:20,201 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:29:23,449 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/eca2489f_2_pod_1.pdf
2025-09-18 13:29:23,505 - INFO - 

2_pod_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        },
        {
            "page_no": 3,
            "doc_type": "other"
        }
    ]
}
2025-09-18 13:29:23,505 - INFO - 

✓ Saved result: Augment_test/output_multi/run998_2_pod_1.json
2025-09-18 13:29:24,732 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/eca2489f_2_pod_1.pdf
2025-09-18 13:29:24,733 - INFO - 
============================================================================================================================================
2025-09-18 13:29:24,733 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 13:29:24,733 - INFO - ============================================================================================================================================
2025-09-18 13:29:24,733 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 13:29:24,733 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:29:24,733 - INFO - 1_bol_1.pdf                                        1      bol                  run998_1_bol_1.json                               
2025-09-18 13:29:24,734 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/1_bol/1_bol_1.pdf
2025-09-18 13:29:24,734 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/Augment_test/output_multi/run998_1_bol_1.json
2025-09-18 13:29:24,734 - INFO - 
2025-09-18 13:29:24,734 - INFO - 10_invoice_1.pdf                                   1      invoice              run998_10_invoice_1.json                          
2025-09-18 13:29:24,734 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice/10_invoice_1.pdf
2025-09-18 13:29:24,734 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/Augment_test/output_multi/run998_10_invoice_1.json
2025-09-18 13:29:24,734 - INFO - 
2025-09-18 13:29:24,734 - INFO - 2_pod_1.pdf                                        1      other                run998_2_pod_1.json                               
2025-09-18 13:29:24,734 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_1.pdf
2025-09-18 13:29:24,734 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/Augment_test/output_multi/run998_2_pod_1.json
2025-09-18 13:29:24,734 - INFO - 
2025-09-18 13:29:24,735 - INFO - 2_pod_1.pdf                                        2      other                run998_2_pod_1.json                               
2025-09-18 13:29:24,735 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_1.pdf
2025-09-18 13:29:24,735 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/Augment_test/output_multi/run998_2_pod_1.json
2025-09-18 13:29:24,735 - INFO - 
2025-09-18 13:29:24,735 - INFO - 2_pod_1.pdf                                        3      other                run998_2_pod_1.json                               
2025-09-18 13:29:24,735 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod/2_pod_1.pdf
2025-09-18 13:29:24,735 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/Augment_test/output_multi/run998_2_pod_1.json
2025-09-18 13:29:24,735 - INFO - 
2025-09-18 13:29:24,735 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:29:24,735 - INFO - Total entries: 5
2025-09-18 13:29:24,735 - INFO - ============================================================================================================================================
2025-09-18 13:29:24,735 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 13:29:24,736 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 13:29:24,736 - INFO -   1. 1_bol_1.pdf                         Page 1   → bol             | run998_1_bol_1.json
2025-09-18 13:29:24,736 - INFO -   2. 10_invoice_1.pdf                    Page 1   → invoice         | run998_10_invoice_1.json
2025-09-18 13:29:24,736 - INFO -   3. 2_pod_1.pdf                         Page 1   → other           | run998_2_pod_1.json
2025-09-18 13:29:24,736 - INFO -   4. 2_pod_1.pdf                         Page 2   → other           | run998_2_pod_1.json
2025-09-18 13:29:24,736 - INFO -   5. 2_pod_1.pdf                         Page 3   → other           | run998_2_pod_1.json
2025-09-18 13:29:24,736 - INFO - ----------------------------------------------------------------------------------------------------
