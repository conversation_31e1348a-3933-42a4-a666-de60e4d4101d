{"classification_result": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.8867974877357483, "Height": 0.8775275945663452, "Left": 0.019867422059178352, "Top": 0.03701331093907356}, "Polygon": [{"X": 0.03830203041434288, "Y": 0.03701331093907356}, {"X": 0.8969070315361023, "Y": 0.08918356150388718}, {"X": 0.9066649079322815, "Y": 0.9126594066619873}, {"X": 0.019867422059178352, "Y": 0.9145408868789673}]}, "Id": "5ddfbc4c-fced-4e01-b578-6113d0a42df2", "Relationships": [{"Type": "CHILD", "Ids": ["1d4f6547-3180-4a72-bdec-d4e816668420", "c936ccbe-1440-4f22-ab7d-c93d41e100c6", "6c006cca-6198-4ffc-b153-8a6e9f48494c", "ed4d20c5-5633-460c-8fdf-74ffda7d2dfa", "d3518e4d-8e0c-4212-baf7-a60eb422ceec", "0b635268-1460-4ad3-877e-041e69bf811e", "ac348d17-2a33-44b6-a89a-5d6ae6f22a71", "1beed00e-fe9b-4012-8d18-91f6089fb3d3", "5c5e6ac6-d3b8-4c86-8e3e-cef3722a7f72", "5c4d7981-a728-4b53-9e92-f68df781f137", "65ffd696-53bf-4275-98cb-316df4de1f15", "d0fae827-75e8-4a1b-b193-bea21e31e03b", "207c0037-10dd-4280-9ad5-2b5be0b80a22", "5f6b45e9-d89c-44f4-8a6a-9c4b01b1520f", "84e78f65-3f60-491b-b0f6-5e48ab37415c", "980e48ce-b7a5-409e-ba0d-3b3c81d586a7", "9de0ee14-d7c4-4f97-a182-5390490ca8d7", "441fe583-de2a-4416-b5dc-4fd5ac2ff1f8", "9095d38d-d0a3-480d-b40a-545156cd196c", "465c241f-9701-4695-a24d-d46bf35d78f7", "7308ad69-da07-43d3-9bc2-861082066e79", "db8951de-ea79-442e-b76e-ea63f4984edd", "aa904fe6-199f-4ec8-ac59-ecc79b55c3cc", "c62ef41e-9904-49a9-9df2-02cdf5fad6dd", "e4ade537-1a8f-4b8f-9228-260aecdd4754", "60c29cb7-473a-4ba7-9694-6e9ed3fdb255", "4f62e4af-67df-4977-b086-e514567ec16a", "94993895-7211-481a-b197-2a8d8eb8b855", "a88abcd8-9e32-4b72-abea-a121a7369348", "81426f4d-e05f-4b02-8ee3-791fca44f633", "9519f9b8-ae50-41af-8b86-9f6b935a9aba", "eda29679-10a7-4c11-8beb-f30fa6bfbd12"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "ND Paper", "Geometry": {"BoundingBox": {"Width": 0.07423651218414307, "Height": 0.015979807823896408, "Left": 0.43452948331832886, "Top": 0.10246113687753677}, "Polygon": [{"X": 0.434597909450531, "Y": 0.10246113687753677}, {"X": 0.5087659955024719, "Y": 0.10674141347408295}, {"X": 0.5087311267852783, "Y": 0.11844094842672348}, {"X": 0.43452948331832886, "Y": 0.11422313749790192}]}, "Id": "1d4f6547-3180-4a72-bdec-d4e816668420", "Relationships": [{"Type": "CHILD", "Ids": ["2821db07-a73a-4204-b784-83f3d826ae25", "a5617e5b-04d5-44d3-a48b-48a21fa1a95a"]}]}, {"BlockType": "LINE", "Confidence": 98.40740203857422, "Text": "ND", "Geometry": {"BoundingBox": {"Width": 0.02534719556570053, "Height": 0.012783447280526161, "Left": 0.6854881048202515, "Top": 0.11459143459796906}, "Polygon": [{"X": 0.6854881048202515, "Y": 0.11459143459796906}, {"X": 0.7107817530632019, "Y": 0.11605560779571533}, {"X": 0.710835337638855, "Y": 0.12737488746643066}, {"X": 0.6855308413505554, "Y": 0.12593165040016174}]}, "Id": "c936ccbe-1440-4f22-ab7d-c93d41e100c6", "Relationships": [{"Type": "CHILD", "Ids": ["2da58847-adbf-4398-ab8e-3a901a3d44a1"]}]}, {"BlockType": "LINE", "Confidence": 99.97030639648438, "Text": "ND PAPER", "Geometry": {"BoundingBox": {"Width": 0.12345172464847565, "Height": 0.02765122428536415, "Left": 0.7288892865180969, "Top": 0.11300218105316162}, "Polygon": [{"X": 0.7288892865180969, "Y": 0.11300218105316162}, {"X": 0.8521336317062378, "Y": 0.12017452716827393}, {"X": 0.8523409962654114, "Y": 0.14065340161323547}, {"X": 0.7290013432502747, "Y": 0.13366761803627014}]}, "Id": "6c006cca-6198-4ffc-b153-8a6e9f48494c", "Relationships": [{"Type": "CHILD", "Ids": ["f5aae90c-0fab-4a82-9017-c42afb1ac1bf", "2411ad71-46b3-4595-aadc-1c1ef8943a7e"]}]}, {"BlockType": "LINE", "Confidence": 99.56304168701172, "Text": "Biron Scale(Biron)", "Geometry": {"BoundingBox": {"Width": 0.14416295289993286, "Height": 0.022115180268883705, "Left": 0.39838242530822754, "Top": 0.11768478155136108}, "Polygon": [{"X": 0.3984840512275696, "Y": 0.11768478155136108}, {"X": 0.5425453782081604, "Y": 0.12581519782543182}, {"X": 0.5425217151641846, "Y": 0.13979995250701904}, {"X": 0.39838242530822754, "Y": 0.13181501626968384}]}, "Id": "ed4d20c5-5633-460c-8fdf-74ffda7d2dfa", "Relationships": [{"Type": "CHILD", "Ids": ["7703f875-c881-48a5-a48d-9d1bba0e52cf", "fe8adc3e-0396-44b2-913b-b43765c6d7ab"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "29210", "Geometry": {"BoundingBox": {"Width": 0.04831111058592796, "Height": 0.013186685740947723, "Left": 0.11325331777334213, "Top": 0.12163593620061874}, "Polygon": [{"X": 0.11344444006681442, "Y": 0.12163593620061874}, {"X": 0.1615644246339798, "Y": 0.12428221851587296}, {"X": 0.16139332950115204, "Y": 0.13482262194156647}, {"X": 0.11325331777334213, "Y": 0.13221195340156555}]}, "Id": "d3518e4d-8e0c-4212-baf7-a60eb422ceec", "Relationships": [{"Type": "CHILD", "Ids": ["8c8fa56e-a305-42cf-be71-d1d9430abc8c"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Source", "Geometry": {"BoundingBox": {"Width": 0.0461830236017704, "Height": 0.01109231635928154, "Left": 0.4676319658756256, "Top": 0.14745007455348969}, "Polygon": [{"X": 0.46767106652259827, "Y": 0.14745007455348969}, {"X": 0.5138149857521057, "Y": 0.14996609091758728}, {"X": 0.5137910842895508, "Y": 0.15854239463806152}, {"X": 0.4676319658756256, "Y": 0.15605494379997253}]}, "Id": "0b635268-1460-4ad3-877e-041e69bf811e", "Relationships": [{"Type": "CHILD", "Ids": ["40315037-fa19-4f68-a7e9-01da378181b4"]}]}, {"BlockType": "LINE", "Confidence": 60.105777740478516, "Text": "VIM_AURO_IL_RATHBONE(VIM RECYCLERSL P)", "Geometry": {"BoundingBox": {"Width": 0.314569354057312, "Height": 0.03880530223250389, "Left": 0.5538837313652039, "Top": 0.13808947801589966}, "Polygon": [{"X": 0.5539112091064453, "Y": 0.13808947801589966}, {"X": 0.8682242035865784, "Y": 0.15555636584758759}, {"X": 0.8684530854225159, "Y": 0.17689478397369385}, {"X": 0.5538837313652039, "Y": 0.15992482006549835}]}, "Id": "ac348d17-2a33-44b6-a89a-5d6ae6f22a71", "Relationships": [{"Type": "CHILD", "Ids": ["4c654e6f-8bcb-4e7d-98de-abc8a84ddd6c", "ca96c5d4-b5f1-4170-8734-9ff57aadc988"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Contract", "Geometry": {"BoundingBox": {"Width": 0.06306661665439606, "Height": 0.01468481682240963, "Left": 0.0724496990442276, "Top": 0.13722670078277588}, "Polygon": [{"X": 0.0726725235581398, "Y": 0.13722670078277588}, {"X": 0.13551631569862366, "Y": 0.1406022012233734}, {"X": 0.13532161712646484, "Y": 0.15191152691841125}, {"X": 0.0724496990442276, "Y": 0.14858588576316833}]}, "Id": "1beed00e-fe9b-4012-8d18-91f6089fb3d3", "Relationships": [{"Type": "CHILD", "Ids": ["1acf730d-a22c-4c1c-b607-1710fa9d5caa"]}]}, {"BlockType": "LINE", "Confidence": 99.78724670410156, "Text": "018539 (HARVESTER)", "Geometry": {"BoundingBox": {"Width": 0.1485840082168579, "Height": 0.021718839183449745, "Left": 0.1807393580675125, "Top": 0.14646676182746887}, "Polygon": [{"X": 0.18095500767230988, "Y": 0.14646676182746887}, {"X": 0.32932335138320923, "Y": 0.15439918637275696}, {"X": 0.3291879892349243, "Y": 0.16818559169769287}, {"X": 0.1807393580675125, "Y": 0.16039873659610748}]}, "Id": "5c5e6ac6-d3b8-4c86-8e3e-cef3722a7f72", "Relationships": [{"Type": "CHILD", "Ids": ["c10ef6af-ea57-4163-ab04-c728fd24af3d", "17dfc0e1-95a7-476b-8d26-80aa835635df"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Destination", "Geometry": {"BoundingBox": {"Width": 0.07361311465501785, "Height": 0.014335665851831436, "Left": 0.4676561653614044, "Top": 0.16074106097221375}, "Polygon": [{"X": 0.467703640460968, "Y": 0.16074106097221375}, {"X": 0.5412693023681641, "Y": 0.16467994451522827}, {"X": 0.5412511825561523, "Y": 0.17507672309875488}, {"X": 0.4676561653614044, "Y": 0.17119313776493073}]}, "Id": "5c4d7981-a728-4b53-9e92-f68df781f137", "Relationships": [{"Type": "CHILD", "Ids": ["a6f4b70a-f942-48f3-935f-0825585d1d05"]}]}, {"BlockType": "LINE", "Confidence": 96.3954849243164, "Text": "BIRON(BIRON MILL)", "Geometry": {"BoundingBox": {"Width": 0.12918375432491302, "Height": 0.02115754410624504, "Left": 0.553171694278717, "Top": 0.15978416800498962}, "Polygon": [{"X": 0.5531900525093079, "Y": 0.15978416800498962}, {"X": 0.6823038458824158, "Y": 0.16675041615962982}, {"X": 0.6823554635047913, "Y": 0.18094171583652496}, {"X": 0.553171694278717, "Y": 0.17410939931869507}]}, "Id": "65ffd696-53bf-4275-98cb-316df4de1f15", "Relationships": [{"Type": "CHILD", "Ids": ["daeb7f44-5920-4cd0-90a7-7e8b98e10fcf", "1f549c71-5ad3-4019-899e-534a2daaf9ba"]}]}, {"BlockType": "LINE", "Confidence": 99.87145233154297, "Text": "Trailer AT26", "Geometry": {"BoundingBox": {"Width": 0.09377062320709229, "Height": 0.016530269756913185, "Left": 0.7396330833435059, "Top": 0.1934497356414795}, "Polygon": [{"X": 0.7396330833435059, "Y": 0.1934497356414795}, {"X": 0.8332945108413696, "Y": 0.19833634793758392}, {"X": 0.8334037065505981, "Y": 0.20998001098632812}, {"X": 0.7397012710571289, "Y": 0.20517413318157196}]}, "Id": "d0fae827-75e8-4a1b-b193-bea21e31e03b", "Relationships": [{"Type": "CHILD", "Ids": ["da3c0029-abb6-400b-8732-03731c11308a", "e680a691-becb-48a5-9282-5bfa417faabc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Driver", "Geometry": {"BoundingBox": {"Width": 0.03953065350651741, "Height": 0.01093638688325882, "Left": 0.4669767916202545, "Top": 0.19175946712493896}, "Polygon": [{"X": 0.4670175611972809, "Y": 0.19175946712493896}, {"X": 0.5065074563026428, "Y": 0.19378319382667542}, {"X": 0.5064801573753357, "Y": 0.20269586145877838}, {"X": 0.4669767916202545, "Y": 0.20069755613803864}]}, "Id": "207c0037-10dd-4280-9ad5-2b5be0b80a22", "Relationships": [{"Type": "CHILD", "Ids": ["e65c3f61-8b81-470d-a180-8165a15808fe"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "ZACH", "Geometry": {"BoundingBox": {"Width": 0.037287645041942596, "Height": 0.011582777835428715, "Left": 0.5518341660499573, "Top": 0.19258533418178558}, "Polygon": [{"X": 0.5518471002578735, "Y": 0.19258533418178558}, {"X": 0.5891209840774536, "Y": 0.1945052593946457}, {"X": 0.5891218185424805, "Y": 0.20416811108589172}, {"X": 0.5518341660499573, "Y": 0.2022743672132492}]}, "Id": "5f6b45e9-d89c-44f4-8a6a-9c4b01b1520f", "Relationships": [{"Type": "CHILD", "Ids": ["bcff1c99-1d5b-4233-9b67-c508be591324"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "BOL", "Geometry": {"BoundingBox": {"Width": 0.0318727008998394, "Height": 0.01108391024172306, "Left": 0.07185383141040802, "Top": 0.18583805859088898}, "Polygon": [{"X": 0.07204034179449081, "Y": 0.18583805859088898}, {"X": 0.10372653603553772, "Y": 0.1874292641878128}, {"X": 0.10355190932750702, "Y": 0.1969219595193863}, {"X": 0.07185383141040802, "Y": 0.19535183906555176}]}, "Id": "84e78f65-3f60-491b-b0f6-5e48ab37415c", "Relationships": [{"Type": "CHILD", "Ids": ["93a55b8c-af9f-4724-b0df-1a2663a24d66"]}]}, {"BlockType": "LINE", "Confidence": 99.91077423095703, "Text": "Trip Ticket BIR0029912", "Geometry": {"BoundingBox": {"Width": 0.17235781252384186, "Height": 0.020067404955625534, "Left": 0.07053522765636444, "Top": 0.20313788950443268}, "Polygon": [{"X": 0.07076658308506012, "Y": 0.20313788950443268}, {"X": 0.2428930401802063, "Y": 0.21156702935695648}, {"X": 0.24274075031280518, "Y": 0.2232052981853485}, {"X": 0.07053522765636444, "Y": 0.21491815149784088}]}, "Id": "980e48ce-b7a5-409e-ba0d-3b3c81d586a7", "Relationships": [{"Type": "CHILD", "Ids": ["587c4cbd-6a90-454c-b6ec-6dd2f165e009", "fa571f20-620c-47ac-8154-fd469beb4338", "d150f8e5-c498-4c24-b46e-aa9fd9b9c813"]}]}, {"BlockType": "LINE", "Confidence": 99.6834487915039, "Text": "Product OCC 11D", "Geometry": {"BoundingBox": {"Width": 0.13654102385044098, "Height": 0.019607676193118095, "Left": 0.46371403336524963, "Top": 0.2204982042312622}, "Polygon": [{"X": 0.46377503871917725, "Y": 0.2204982042312622}, {"X": 0.6002484560012817, "Y": 0.22720041871070862}, {"X": 0.600255012512207, "Y": 0.24010588228702545}, {"X": 0.46371403336524963, "Y": 0.23353193700313568}]}, "Id": "9de0ee14-d7c4-4f97-a182-5390490ca8d7", "Relationships": [{"Type": "CHILD", "Ids": ["862f9ffa-40aa-432b-ba55-1c972c2af4cc", "2b436838-c69e-48ec-af6f-a337118a76f0", "4593ee9d-189c-42c4-924b-cec3cc9d85b0"]}]}, {"BlockType": "LINE", "Confidence": 99.5179672241211, "Text": "Load # BIR-29210", "Geometry": {"BoundingBox": {"Width": 0.1690688133239746, "Height": 0.02063271962106228, "Left": 0.07089979946613312, "Top": 0.2207895815372467}, "Polygon": [{"X": 0.07114945352077484, "Y": 0.2207895815372467}, {"X": 0.23996861279010773, "Y": 0.22884292900562286}, {"X": 0.23980271816253662, "Y": 0.24142229557037354}, {"X": 0.07089979946613312, "Y": 0.2335195392370224}]}, "Id": "441fe583-de2a-4416-b5dc-4fd5ac2ff1f8", "Relationships": [{"Type": "CHILD", "Ids": ["16389899-8076-4b4d-b09a-ef8b38128c0b", "7c79ffa5-86cf-4531-9bc2-e00fdee68cd5", "9ec4d822-edff-427b-a813-71afe4bb5dbf"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Tons", "Geometry": {"BoundingBox": {"Width": 0.03479186072945595, "Height": 0.010224103927612305, "Left": 0.18484605848789215, "Top": 0.3172008991241455}, "Polygon": [{"X": 0.1849803626537323, "Y": 0.3172008991241455}, {"X": 0.2196379154920578, "Y": 0.3186258375644684}, {"X": 0.21951548755168915, "Y": 0.3274250030517578}, {"X": 0.18484605848789215, "Y": 0.3260217308998108}]}, "Id": "9095d38d-d0a3-480d-b40a-545156cd196c", "Relationships": [{"Type": "CHILD", "Ids": ["3e61ec70-1391-41a1-9a51-25efbc82c5c9"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "LB", "Geometry": {"BoundingBox": {"Width": 0.018349122256040573, "Height": 0.009492579847574234, "Left": 0.27276042103767395, "Top": 0.321911096572876}, "Polygon": [{"X": 0.27286458015441895, "Y": 0.321911096572876}, {"X": 0.291109561920166, "Y": 0.32265979051589966}, {"X": 0.2910115718841553, "Y": 0.3314036726951599}, {"X": 0.27276042103767395, "Y": 0.3306663930416107}]}, "Id": "465c241f-9701-4695-a24d-d46bf35d78f7", "Relationships": [{"Type": "CHILD", "Ids": ["aaef9cb8-96a3-4303-8d4c-1d4e04afca77"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Gross", "Geometry": {"BoundingBox": {"Width": 0.04350621625781059, "Height": 0.010831925086677074, "Left": 0.06832753866910934, "Top": 0.326516330242157}, "Polygon": [{"X": 0.0685066357254982, "Y": 0.326516330242157}, {"X": 0.11183375120162964, "Y": 0.328253835439682}, {"X": 0.11167013645172119, "Y": 0.33734825253486633}, {"X": 0.06832753866910934, "Y": 0.33563855290412903}]}, "Id": "7308ad69-da07-43d3-9bc2-861082066e79", "Relationships": [{"Type": "CHILD", "Ids": ["18874c24-84a9-4b29-979a-88c1ce12da4c"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "36.67", "Geometry": {"BoundingBox": {"Width": 0.035758018493652344, "Height": 0.010406378656625748, "Left": 0.18296101689338684, "Top": 0.33157312870025635}, "Polygon": [{"X": 0.1830986589193344, "Y": 0.33157312870025635}, {"X": 0.21871903538703918, "Y": 0.3330003619194031}, {"X": 0.21859386563301086, "Y": 0.3419795036315918}, {"X": 0.18296101689338684, "Y": 0.3405749797821045}]}, "Id": "db8951de-ea79-442e-b76e-ea63f4984edd", "Relationships": [{"Type": "CHILD", "Ids": ["301e45bb-8aaf-430f-9efd-fbb9291bda9e"]}]}, {"BlockType": "LINE", "Confidence": 99.67135620117188, "Text": "73,340", "Geometry": {"BoundingBox": {"Width": 0.04313262552022934, "Height": 0.01164830569177866, "Left": 0.24963611364364624, "Top": 0.33459728956222534}, "Polygon": [{"X": 0.24976320564746857, "Y": 0.33459728956222534}, {"X": 0.2927687466144562, "Y": 0.33631935715675354}, {"X": 0.2926582098007202, "Y": 0.34624558687210083}, {"X": 0.24963611364364624, "Y": 0.34455406665802}]}, "Id": "aa904fe6-199f-4ec8-ac59-ecc79b55c3cc", "Relationships": [{"Type": "CHILD", "Ids": ["23165698-093f-4458-b3f9-effe72e8755e"]}]}, {"BlockType": "LINE", "Confidence": 99.16265106201172, "Text": "Date In 11/11/22 4:49:27", "Geometry": {"BoundingBox": {"Width": 0.1692066788673401, "Height": 0.018061326816678047, "Left": 0.3174854815006256, "Top": 0.3350638449192047}, "Polygon": [{"X": 0.3176017105579376, "Y": 0.3350638449192047}, {"X": 0.4866921603679657, "Y": 0.3418624997138977}, {"X": 0.48664945363998413, "Y": 0.3531251549720764}, {"X": 0.3174854815006256, "Y": 0.3464646339416504}]}, "Id": "c62ef41e-9904-49a9-9df2-02cdf5fad6dd", "Relationships": [{"Type": "CHILD", "Ids": ["2110752a-af3a-4f54-8349-0896703b4ab4", "2887558c-8870-49bd-85d2-3f60201a2a06", "4e8a9f57-83c5-458e-ae43-6d7017abf3d5", "d28024a9-260e-4446-b73b-387ba7c76605"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.032564740628004074, "Height": 0.010362623259425163, "Left": 0.06769140809774399, "Top": 0.3403594195842743}, "Polygon": [{"X": 0.06787052750587463, "Y": 0.3403594195842743}, {"X": 0.10025614500045776, "Y": 0.3416258692741394}, {"X": 0.10008859634399414, "Y": 0.3507220447063446}, {"X": 0.06769140809774399, "Y": 0.34947633743286133}]}, "Id": "e4ade537-1a8f-4b8f-9228-260aecdd4754", "Relationships": [{"Type": "CHILD", "Ids": ["d32a0647-d918-457f-92ab-69245ab4bdc8"]}]}, {"BlockType": "LINE", "Confidence": 99.60140228271484, "Text": "15.30", "Geometry": {"BoundingBox": {"Width": 0.03524325042963028, "Height": 0.01012217253446579, "Left": 0.18350127339363098, "Top": 0.3454247713088989}, "Polygon": [{"X": 0.1836351454257965, "Y": 0.3454247713088989}, {"X": 0.21874451637268066, "Y": 0.3467963635921478}, {"X": 0.2186226099729538, "Y": 0.3555469214916229}, {"X": 0.18350127339363098, "Y": 0.3541971743106842}]}, "Id": "60c29cb7-473a-4ba7-9694-6e9ed3fdb255", "Relationships": [{"Type": "CHILD", "Ids": ["4da16f9d-78e5-4a62-b689-50594f2bb818"]}]}, {"BlockType": "LINE", "Confidence": 99.59163665771484, "Text": "30,600", "Geometry": {"BoundingBox": {"Width": 0.042908672243356705, "Height": 0.011480916291475296, "Left": 0.24950359761714935, "Top": 0.3484209179878235}, "Polygon": [{"X": 0.2496291846036911, "Y": 0.3484209179878235}, {"X": 0.29241225123405457, "Y": 0.35009101033210754}, {"X": 0.2923029363155365, "Y": 0.3599018156528473}, {"X": 0.24950359761714935, "Y": 0.35826173424720764}]}, "Id": "4f62e4af-67df-4977-b086-e514567ec16a", "Relationships": [{"Type": "CHILD", "Ids": ["ed7c0245-b739-4771-b587-1e1749f6d703"]}]}, {"BlockType": "LINE", "Confidence": 99.68002319335938, "Text": "Date Out 11/11/22 5:32:40", "Geometry": {"BoundingBox": {"Width": 0.16895774006843567, "Height": 0.018160879611968994, "Left": 0.3173535168170929, "Top": 0.3489113450050354}, "Polygon": [{"X": 0.3174726366996765, "Y": 0.3489113450050354}, {"X": 0.48631125688552856, "Y": 0.3555288314819336}, {"X": 0.4862673282623291, "Y": 0.3670722246170044}, {"X": 0.3173535168170929, "Y": 0.3605961501598358}]}, "Id": "94993895-7211-481a-b197-2a8d8eb8b855", "Relationships": [{"Type": "CHILD", "Ids": ["06076ab0-ad70-4913-9c9a-5d19fdbf5b3a", "9a05669f-2bf7-4672-8bbf-942b1cdbcd6d", "73cc17b8-868c-45b8-b220-6713f178926f", "812afc1f-2358-4ec8-9bd2-29941334f39d"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Net", "Geometry": {"BoundingBox": {"Width": 0.024831296876072884, "Height": 0.010065760463476181, "Left": 0.06927561014890671, "Top": 0.3550315797328949}, "Polygon": [{"X": 0.0694546028971672, "Y": 0.3550315797328949}, {"X": 0.09410690516233444, "Y": 0.35596975684165955}, {"X": 0.0939367413520813, "Y": 0.3650973439216614}, {"X": 0.06927561014890671, "Y": 0.364175021648407}]}, "Id": "a88abcd8-9e32-4b72-abea-a121a7369348", "Relationships": [{"Type": "CHILD", "Ids": ["26d1b1e6-568e-48ae-bf7d-1500218e2675"]}]}, {"BlockType": "LINE", "Confidence": 99.32318115234375, "Text": "21.37", "Geometry": {"BoundingBox": {"Width": 0.03584318980574608, "Height": 0.010149598121643066, "Left": 0.1821271926164627, "Top": 0.35976719856262207}, "Polygon": [{"X": 0.1822620928287506, "Y": 0.35976719856262207}, {"X": 0.2179703712463379, "Y": 0.36112499237060547}, {"X": 0.21784770488739014, "Y": 0.36991679668426514}, {"X": 0.1821271926164627, "Y": 0.3685813546180725}]}, "Id": "81426f4d-e05f-4b02-8ee3-791fca44f633", "Relationships": [{"Type": "CHILD", "Ids": ["fc77f541-bf68-4eab-8f0f-d3648c10efd3"]}]}, {"BlockType": "LINE", "Confidence": 98.77490997314453, "Text": "42,740", "Geometry": {"BoundingBox": {"Width": 0.043745700269937515, "Height": 0.011544073931872845, "Left": 0.24982568621635437, "Top": 0.3625621497631073}, "Polygon": [{"X": 0.2499520629644394, "Y": 0.3625621497631073}, {"X": 0.2935713827610016, "Y": 0.3642200231552124}, {"X": 0.29346171021461487, "Y": 0.37410619854927063}, {"X": 0.24982568621635437, "Y": 0.3724791705608368}]}, "Id": "9519f9b8-ae50-41af-8b86-9f6b935a9aba", "Relationships": [{"Type": "CHILD", "Ids": ["5a1e0b3b-c514-4703-af86-1d147b1b3604"]}]}, {"BlockType": "LINE", "Confidence": 99.87104797363281, "Text": "Buyer: 018539", "Geometry": {"BoundingBox": {"Width": 0.11180038005113602, "Height": 0.012203041464090347, "Left": 0.06391336023807526, "Top": 0.8455177545547485}, "Polygon": [{"X": 0.06414603441953659, "Y": 0.8455177545547485}, {"X": 0.17571374773979187, "Y": 0.8458331823348999}, {"X": 0.17553219199180603, "Y": 0.8577207922935486}, {"X": 0.06391336023807526, "Y": 0.8575012683868408}]}, "Id": "eda29679-10a7-4c11-8beb-f30fa6bfbd12", "Relationships": [{"Type": "CHILD", "Ids": ["077c2814-8f87-495e-825e-73e2c048b691", "a875726e-eafd-462e-b527-9b7d2fcb6cff"]}]}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "ND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02303648181259632, "Height": 0.011502865701913834, "Left": 0.43453511595726013, "Top": 0.10306371748447418}, "Polygon": [{"X": 0.43459439277648926, "Y": 0.10306371748447418}, {"X": 0.4575715959072113, "Y": 0.10438871383666992}, {"X": 0.45752131938934326, "Y": 0.11456657946109772}, {"X": 0.43453511595726013, "Y": 0.11325834691524506}]}, "Id": "2821db07-a73a-4204-b784-83f3d826ae25"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Paper", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04625142365694046, "Height": 0.014365507289767265, "Left": 0.46251457929611206, "Top": 0.10407543927431107}, "Polygon": [{"X": 0.46257030963897705, "Y": 0.10407543927431107}, {"X": 0.5087659955024719, "Y": 0.10674141347408295}, {"X": 0.5087311267852783, "Y": 0.11844094842672348}, {"X": 0.46251457929611206, "Y": 0.11581388115882874}]}, "Id": "a5617e5b-04d5-44d3-a48b-48a21fa1a95a"}, {"BlockType": "WORD", "Confidence": 98.40740203857422, "Text": "ND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02534719556570053, "Height": 0.012783447280526161, "Left": 0.6854881048202515, "Top": 0.11459143459796906}, "Polygon": [{"X": 0.6854881048202515, "Y": 0.11459143459796906}, {"X": 0.7107817530632019, "Y": 0.11605560779571533}, {"X": 0.710835337638855, "Y": 0.12737488746643066}, {"X": 0.6855308413505554, "Y": 0.12593165040016174}]}, "Id": "2da58847-adbf-4398-ab8e-3a901a3d44a1"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "ND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037064213305711746, "Height": 0.019600829109549522, "Left": 0.7289064526557922, "Top": 0.11616068333387375}, "Polygon": [{"X": 0.7289064526557922, "Y": 0.11616068333387375}, {"X": 0.7658513188362122, "Y": 0.11830193549394608}, {"X": 0.7659706473350525, "Y": 0.13576151430606842}, {"X": 0.7290013432502747, "Y": 0.13366761803627014}]}, "Id": "f5aae90c-0fab-4a82-9017-c42afb1ac1bf"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "PAPER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07873455435037613, "Height": 0.02363426238298416, "Left": 0.7735921144485474, "Top": 0.11560371518135071}, "Polygon": [{"X": 0.7735921144485474, "Y": 0.11560371518135071}, {"X": 0.8521336317062378, "Y": 0.12017452716827393}, {"X": 0.8523266911506653, "Y": 0.13923798501491547}, {"X": 0.7737288475036621, "Y": 0.13477784395217896}]}, "Id": "2411ad71-46b3-4595-aadc-1c1ef8943a7e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Biron", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04243960604071617, "Height": 0.012543072924017906, "Left": 0.3983845114707947, "Top": 0.12132956087589264}, "Polygon": [{"X": 0.3984578549861908, "Y": 0.12132956087589264}, {"X": 0.44082412123680115, "Y": 0.12370923161506653}, {"X": 0.44076746702194214, "Y": 0.1338726282119751}, {"X": 0.3983845114707947, "Y": 0.13152381777763367}]}, "Id": "7703f875-c881-48a5-a48d-9d1bba0e52cf"}, {"BlockType": "WORD", "Confidence": 99.1260757446289, "Text": "Scale(Biron)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09634093940258026, "Height": 0.01941770873963833, "Left": 0.44620445370674133, "Top": 0.12038224935531616}, "Polygon": [{"X": 0.44628003239631653, "Y": 0.12038224935531616}, {"X": 0.5425453782081604, "Y": 0.12581519782543182}, {"X": 0.5425217151641846, "Y": 0.13979995250701904}, {"X": 0.44620445370674133, "Y": 0.13446423411369324}]}, "Id": "fe8adc3e-0396-44b2-913b-b43765c6d7ab"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "29210", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04831111058592796, "Height": 0.013186685740947723, "Left": 0.11325331777334213, "Top": 0.12163593620061874}, "Polygon": [{"X": 0.11344444006681442, "Y": 0.12163593620061874}, {"X": 0.1615644246339798, "Y": 0.12428221851587296}, {"X": 0.16139332950115204, "Y": 0.13482262194156647}, {"X": 0.11325331777334213, "Y": 0.13221195340156555}]}, "Id": "8c8fa56e-a305-42cf-be71-d1d9430abc8c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Source", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0461830236017704, "Height": 0.01109231635928154, "Left": 0.4676319658756256, "Top": 0.14745007455348969}, "Polygon": [{"X": 0.46767106652259827, "Y": 0.14745007455348969}, {"X": 0.5138149857521057, "Y": 0.14996609091758728}, {"X": 0.5137910842895508, "Y": 0.15854239463806152}, {"X": 0.4676319658756256, "Y": 0.15605494379997253}]}, "Id": "40315037-fa19-4f68-a7e9-01da378181b4"}, {"BlockType": "WORD", "Confidence": 21.44660758972168, "Text": "VIM_AURO_IL_RATHBONE(VIM RECYCLERSL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2976658046245575, "Height": 0.03789341449737549, "Left": 0.5538837313652039, "Top": 0.13808947801589966}, "Polygon": [{"X": 0.5539112091064453, "Y": 0.13808947801589966}, {"X": 0.8513341546058655, "Y": 0.15461775660514832}, {"X": 0.8515495657920837, "Y": 0.17598289251327515}, {"X": 0.5538837313652039, "Y": 0.15992482006549835}]}, "Id": "4c654e6f-8bcb-4e7d-98de-abc8a84ddd6c"}, {"BlockType": "WORD", "Confidence": 98.76495361328125, "Text": "P)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01279358472675085, "Height": 0.011127179488539696, "Left": 0.8555433750152588, "Top": 0.15493690967559814}, "Polygon": [{"X": 0.8555433750152588, "Y": 0.15493690967559814}, {"X": 0.8682251572608948, "Y": 0.155641570687294}, {"X": 0.8683369159698486, "Y": 0.1660640984773636}, {"X": 0.8556501865386963, "Y": 0.16536922752857208}]}, "Id": "ca96c5d4-b5f1-4170-8734-9ff57aadc988"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Contract", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06306661665439606, "Height": 0.01468481682240963, "Left": 0.0724496990442276, "Top": 0.13722670078277588}, "Polygon": [{"X": 0.0726725235581398, "Y": 0.13722670078277588}, {"X": 0.13551631569862366, "Y": 0.1406022012233734}, {"X": 0.13532161712646484, "Y": 0.15191152691841125}, {"X": 0.0724496990442276, "Y": 0.14858588576316833}]}, "Id": "1acf730d-a22c-4c1c-b607-1710fa9d5caa"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "018539", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04824904352426529, "Height": 0.01246823463588953, "Left": 0.18080130219459534, "Top": 0.14646676182746887}, "Polygon": [{"X": 0.18095500767230988, "Y": 0.14646676182746887}, {"X": 0.22905033826828003, "Y": 0.1490381509065628}, {"X": 0.22891530394554138, "Y": 0.15893499553203583}, {"X": 0.18080130219459534, "Y": 0.15639722347259521}]}, "Id": "c10ef6af-ea57-4163-ab04-c728fd24af3d"}, {"BlockType": "WORD", "Confidence": 99.68192291259766, "Text": "(HARVESTER)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09644284099340439, "Height": 0.017852887511253357, "Left": 0.2328699827194214, "Top": 0.15033270418643951}, "Polygon": [{"X": 0.23304268717765808, "Y": 0.15033270418643951}, {"X": 0.32931283116340637, "Y": 0.15547217428684235}, {"X": 0.3291879892349243, "Y": 0.16818559169769287}, {"X": 0.2328699827194214, "Y": 0.16313324868679047}]}, "Id": "17dfc0e1-95a7-476b-8d26-80aa835635df"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Destination", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07361311465501785, "Height": 0.014335665851831436, "Left": 0.4676561653614044, "Top": 0.16074106097221375}, "Polygon": [{"X": 0.467703640460968, "Y": 0.16074106097221375}, {"X": 0.5412693023681641, "Y": 0.16467994451522827}, {"X": 0.5412511825561523, "Y": 0.17507672309875488}, {"X": 0.4676561653614044, "Y": 0.17119313776493073}]}, "Id": "a6f4b70a-f942-48f3-935f-0825585d1d05"}, {"BlockType": "WORD", "Confidence": 95.91757202148438, "Text": "BIRON(BIRON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09057942032814026, "Height": 0.018105806782841682, "Left": 0.553171694278717, "Top": 0.1607941836118698}, "Polygon": [{"X": 0.5531888008117676, "Y": 0.1607941836118698}, {"X": 0.643722414970398, "Y": 0.16567206382751465}, {"X": 0.6437510848045349, "Y": 0.17889998853206635}, {"X": 0.553171694278717, "Y": 0.17410939931869507}]}, "Id": "daeb7f44-5920-4cd0-90a7-7e8b98e10fcf"}, {"BlockType": "WORD", "Confidence": 96.87340545654297, "Text": "MILL)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03480115532875061, "Height": 0.012240788899362087, "Left": 0.6475403904914856, "Top": 0.16487477719783783}, "Polygon": [{"X": 0.6475403904914856, "Y": 0.16487477719783783}, {"X": 0.6823038458824158, "Y": 0.16675041615962982}, {"X": 0.6823415160179138, "Y": 0.1771155744791031}, {"X": 0.6475644111633301, "Y": 0.17526626586914062}]}, "Id": "1f549c71-5ad3-4019-899e-534a2daaf9ba"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041886042803525925, "Height": 0.011855510994791985, "Left": 0.7396447658538818, "Top": 0.19546400010585785}, "Polygon": [{"X": 0.7396447658538818, "Y": 0.19546400010585785}, {"X": 0.7814591526985168, "Y": 0.19763924181461334}, {"X": 0.7815308570861816, "Y": 0.20731951296329498}, {"X": 0.7397012710571289, "Y": 0.20517413318157196}]}, "Id": "da3c0029-abb6-400b-8732-03731c11308a"}, {"BlockType": "WORD", "Confidence": 99.81205749511719, "Text": "AT26", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0333498939871788, "Height": 0.011552166193723679, "Left": 0.8000366687774658, "Top": 0.19660118222236633}, "Polygon": [{"X": 0.8000366687774658, "Y": 0.19660118222236633}, {"X": 0.8332945108413696, "Y": 0.19833634793758392}, {"X": 0.8333866000175476, "Y": 0.2081533521413803}, {"X": 0.8001165390014648, "Y": 0.20644235610961914}]}, "Id": "e680a691-becb-48a5-9282-5bfa417faabc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03953065350651741, "Height": 0.01093638688325882, "Left": 0.4669767916202545, "Top": 0.19175946712493896}, "Polygon": [{"X": 0.4670175611972809, "Y": 0.19175946712493896}, {"X": 0.5065074563026428, "Y": 0.19378319382667542}, {"X": 0.5064801573753357, "Y": 0.20269586145877838}, {"X": 0.4669767916202545, "Y": 0.20069755613803864}]}, "Id": "e65c3f61-8b81-470d-a180-8165a15808fe"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "ZACH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037287645041942596, "Height": 0.011582777835428715, "Left": 0.5518341660499573, "Top": 0.19258533418178558}, "Polygon": [{"X": 0.5518471002578735, "Y": 0.19258533418178558}, {"X": 0.5891209840774536, "Y": 0.1945052593946457}, {"X": 0.5891218185424805, "Y": 0.20416811108589172}, {"X": 0.5518341660499573, "Y": 0.2022743672132492}]}, "Id": "bcff1c99-1d5b-4233-9b67-c508be591324"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "BOL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0318727008998394, "Height": 0.01108391024172306, "Left": 0.07185383141040802, "Top": 0.18583805859088898}, "Polygon": [{"X": 0.07204034179449081, "Y": 0.18583805859088898}, {"X": 0.10372653603553772, "Y": 0.1874292641878128}, {"X": 0.10355190932750702, "Y": 0.1969219595193863}, {"X": 0.07185383141040802, "Y": 0.19535183906555176}]}, "Id": "93a55b8c-af9f-4724-b0df-1a2663a24d66"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Trip", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029393751174211502, "Height": 0.013052918016910553, "Left": 0.07053781300783157, "Top": 0.20313788950443268}, "Polygon": [{"X": 0.07076658308506012, "Y": 0.20313788950443268}, {"X": 0.09993156790733337, "Y": 0.2045661211013794}, {"X": 0.0997161790728569, "Y": 0.21619081497192383}, {"X": 0.07053781300783157, "Y": 0.2147863656282425}]}, "Id": "587c4cbd-6a90-454c-b6ec-6dd2f165e009"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Ticket", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04412739351391792, "Height": 0.0118827223777771, "Left": 0.10367673635482788, "Top": 0.20529618859291077}, "Polygon": [{"X": 0.103856161236763, "Y": 0.20529618859291077}, {"X": 0.1478041261434555, "Y": 0.2074466496706009}, {"X": 0.14764155447483063, "Y": 0.21717891097068787}, {"X": 0.10367673635482788, "Y": 0.2150585651397705}]}, "Id": "fa571f20-620c-47ac-8154-fd469beb4338"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "BIR0029912", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07964839041233063, "Height": 0.014256478287279606, "Left": 0.16322806477546692, "Top": 0.20894882082939148}, "Polygon": [{"X": 0.16339610517024994, "Y": 0.20894882082939148}, {"X": 0.24287645518779755, "Y": 0.2128337025642395}, {"X": 0.24274075031280518, "Y": 0.2232052981853485}, {"X": 0.16322806477546692, "Y": 0.21937885880470276}]}, "Id": "d150f8e5-c498-4c24-b46e-aa9fd9b9c813"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05204581096768379, "Height": 0.012009023688733578, "Left": 0.46371403336524963, "Top": 0.22402749955654144}, "Polygon": [{"X": 0.4637584984302521, "Y": 0.22402749955654144}, {"X": 0.5157598257064819, "Y": 0.22656771540641785}, {"X": 0.5157342553138733, "Y": 0.2360365241765976}, {"X": 0.46371403336524963, "Y": 0.23353193700313568}]}, "Id": "862f9ffa-40aa-432b-ba55-1c972c2af4cc"}, {"BlockType": "WORD", "Confidence": 99.68590545654297, "Text": "OCC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030197203159332275, "Height": 0.01096768956631422, "Left": 0.5412454605102539, "Top": 0.22518888115882874}, "Polygon": [{"X": 0.5412619709968567, "Y": 0.22518888115882874}, {"X": 0.5714426636695862, "Y": 0.2266690731048584}, {"X": 0.5714371204376221, "Y": 0.23615656793117523}, {"X": 0.5412454605102539, "Y": 0.23469719290733337}]}, "Id": "2b436838-c69e-48ec-af6f-a337118a76f0"}, {"BlockType": "WORD", "Confidence": 99.4140625, "Text": "11D", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024471506476402283, "Height": 0.010535159148275852, "Left": 0.5757817029953003, "Top": 0.22599905729293823}, "Polygon": [{"X": 0.5757856369018555, "Y": 0.22599905729293823}, {"X": 0.6002484560012817, "Y": 0.22720041871070862}, {"X": 0.6002532243728638, "Y": 0.2365342080593109}, {"X": 0.5757817029953003, "Y": 0.23534947633743286}]}, "Id": "4593ee9d-189c-42c4-924b-cec3cc9d85b0"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03927716612815857, "Height": 0.012242043390870094, "Left": 0.0709453672170639, "Top": 0.2207895815372467}, "Polygon": [{"X": 0.07114945352077484, "Y": 0.2207895815372467}, {"X": 0.11022252589464188, "Y": 0.22265352308750153}, {"X": 0.11003443598747253, "Y": 0.23303161561489105}, {"X": 0.0709453672170639, "Y": 0.23119616508483887}]}, "Id": "16389899-8076-4b4d-b09a-ef8b38128c0b"}, {"BlockType": "WORD", "Confidence": 98.798828125, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010462954640388489, "Height": 0.010016856715083122, "Left": 0.11445949226617813, "Top": 0.22370648384094238}, "Polygon": [{"X": 0.11463067680597305, "Y": 0.22370648384094238}, {"X": 0.12492244690656662, "Y": 0.22419682145118713}, {"X": 0.1247551217675209, "Y": 0.23372334241867065}, {"X": 0.11445949226617813, "Y": 0.23323990404605865}]}, "Id": "7c79ffa5-86cf-4531-9bc2-e00fdee68cd5"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "BIR-29210", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07712502032518387, "Height": 0.014598649926483631, "Left": 0.1628219336271286, "Top": 0.22682364284992218}, "Polygon": [{"X": 0.16299913823604584, "Y": 0.22682364284992218}, {"X": 0.23994694650173187, "Y": 0.23048517107963562}, {"X": 0.23980271816253662, "Y": 0.24142229557037354}, {"X": 0.1628219336271286, "Y": 0.23782046139240265}]}, "Id": "9ec4d822-edff-427b-a813-71afe4bb5dbf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Tons", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03479186072945595, "Height": 0.010224103927612305, "Left": 0.18484605848789215, "Top": 0.3172008991241455}, "Polygon": [{"X": 0.1849803626537323, "Y": 0.3172008991241455}, {"X": 0.2196379154920578, "Y": 0.3186258375644684}, {"X": 0.21951548755168915, "Y": 0.3274250030517578}, {"X": 0.18484605848789215, "Y": 0.3260217308998108}]}, "Id": "3e61ec70-1391-41a1-9a51-25efbc82c5c9"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "LB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018349122256040573, "Height": 0.009492579847574234, "Left": 0.27276042103767395, "Top": 0.321911096572876}, "Polygon": [{"X": 0.27286458015441895, "Y": 0.321911096572876}, {"X": 0.291109561920166, "Y": 0.32265979051589966}, {"X": 0.2910115718841553, "Y": 0.3314036726951599}, {"X": 0.27276042103767395, "Y": 0.3306663930416107}]}, "Id": "aaef9cb8-96a3-4303-8d4c-1d4e04afca77"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Gross", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04350621625781059, "Height": 0.010831925086677074, "Left": 0.06832753866910934, "Top": 0.326516330242157}, "Polygon": [{"X": 0.0685066357254982, "Y": 0.326516330242157}, {"X": 0.11183375120162964, "Y": 0.328253835439682}, {"X": 0.11167013645172119, "Y": 0.33734825253486633}, {"X": 0.06832753866910934, "Y": 0.33563855290412903}]}, "Id": "18874c24-84a9-4b29-979a-88c1ce12da4c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "36.67", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035758018493652344, "Height": 0.010406378656625748, "Left": 0.18296101689338684, "Top": 0.33157312870025635}, "Polygon": [{"X": 0.1830986589193344, "Y": 0.33157312870025635}, {"X": 0.21871903538703918, "Y": 0.3330003619194031}, {"X": 0.21859386563301086, "Y": 0.3419795036315918}, {"X": 0.18296101689338684, "Y": 0.3405749797821045}]}, "Id": "301e45bb-8aaf-430f-9efd-fbb9291bda9e"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "73,340", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04313262552022934, "Height": 0.01164830569177866, "Left": 0.24963611364364624, "Top": 0.33459728956222534}, "Polygon": [{"X": 0.24976320564746857, "Y": 0.33459728956222534}, {"X": 0.2927687466144562, "Y": 0.33631935715675354}, {"X": 0.2926582098007202, "Y": 0.34624558687210083}, {"X": 0.24963611364364624, "Y": 0.34455406665802}]}, "Id": "23165698-093f-4458-b3f9-effe72e8755e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029839908704161644, "Height": 0.009625136852264404, "Left": 0.3174854815006256, "Top": 0.33801138401031494}, "Polygon": [{"X": 0.3175716698169708, "Y": 0.33801138401031494}, {"X": 0.3473253846168518, "Y": 0.33920127153396606}, {"X": 0.347248911857605, "Y": 0.34763652086257935}, {"X": 0.3174854815006256, "Y": 0.3464646339416504}]}, "Id": "2110752a-af3a-4f54-8349-0896703b4ab4"}, {"BlockType": "WORD", "Confidence": 97.91832733154297, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011558234691619873, "Height": 0.008902370929718018, "Left": 0.351659893989563, "Top": 0.33919307589530945}, "Polygon": [{"X": 0.35173511505126953, "Y": 0.33919307589530945}, {"X": 0.36321812868118286, "Y": 0.33965247869491577}, {"X": 0.3631466329097748, "Y": 0.34809544682502747}, {"X": 0.351659893989563, "Y": 0.34764304757118225}]}, "Id": "2887558c-8870-49bd-85d2-3f60201a2a06"}, {"BlockType": "WORD", "Confidence": 99.02523040771484, "Text": "11/11/22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05208694562315941, "Height": 0.011130745522677898, "Left": 0.385672390460968, "Top": 0.33888164162635803}, "Polygon": [{"X": 0.3857415020465851, "Y": 0.33888164162635803}, {"X": 0.4377593398094177, "Y": 0.3409690260887146}, {"X": 0.4377082884311676, "Y": 0.3500123918056488}, {"X": 0.385672390460968, "Y": 0.3479590117931366}]}, "Id": "4e8a9f57-83c5-458e-ae43-6d7017abf3d5"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "4:49:27", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04588759317994118, "Height": 0.011061602272093296, "Left": 0.4408045709133148, "Top": 0.340019553899765}, "Polygon": [{"X": 0.4408556818962097, "Y": 0.340019553899765}, {"X": 0.4866921603679657, "Y": 0.3418624997138977}, {"X": 0.48665720224380493, "Y": 0.35108113288879395}, {"X": 0.4408045709133148, "Y": 0.3492688238620758}]}, "Id": "d28024a9-260e-4446-b73b-387ba7c76605"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032564740628004074, "Height": 0.010362623259425163, "Left": 0.06769140809774399, "Top": 0.3403594195842743}, "Polygon": [{"X": 0.06787052750587463, "Y": 0.3403594195842743}, {"X": 0.10025614500045776, "Y": 0.3416258692741394}, {"X": 0.10008859634399414, "Y": 0.3507220447063446}, {"X": 0.06769140809774399, "Y": 0.34947633743286133}]}, "Id": "d32a0647-d918-457f-92ab-69245ab4bdc8"}, {"BlockType": "WORD", "Confidence": 99.60140228271484, "Text": "15.30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03524325042963028, "Height": 0.01012217253446579, "Left": 0.18350127339363098, "Top": 0.3454247713088989}, "Polygon": [{"X": 0.1836351454257965, "Y": 0.3454247713088989}, {"X": 0.21874451637268066, "Y": 0.3467963635921478}, {"X": 0.2186226099729538, "Y": 0.3555469214916229}, {"X": 0.18350127339363098, "Y": 0.3541971743106842}]}, "Id": "4da16f9d-78e5-4a62-b689-50594f2bb818"}, {"BlockType": "WORD", "Confidence": 99.59163665771484, "Text": "30,600", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042908672243356705, "Height": 0.011480916291475296, "Left": 0.24950359761714935, "Top": 0.3484209179878235}, "Polygon": [{"X": 0.2496291846036911, "Y": 0.3484209179878235}, {"X": 0.29241225123405457, "Y": 0.35009101033210754}, {"X": 0.2923029363155365, "Y": 0.3599018156528473}, {"X": 0.24950359761714935, "Y": 0.35826173424720764}]}, "Id": "ed7c0245-b739-4771-b587-1e1749f6d703"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029668033123016357, "Height": 0.00973564200103283, "Left": 0.3173535168170929, "Top": 0.35199496150016785}, "Polygon": [{"X": 0.3174411952495575, "Y": 0.35199496150016785}, {"X": 0.34702154994010925, "Y": 0.3531476855278015}, {"X": 0.3469436764717102, "Y": 0.3617306053638458}, {"X": 0.3173535168170929, "Y": 0.3605961501598358}]}, "Id": "06076ab0-ad70-4913-9c9a-5d19fdbf5b3a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Out", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02425566129386425, "Height": 0.0095891822129488, "Left": 0.3510790467262268, "Top": 0.3529113829135895}, "Polygon": [{"X": 0.3511562943458557, "Y": 0.3529113829135895}, {"X": 0.3753347098827362, "Y": 0.35385429859161377}, {"X": 0.375265508890152, "Y": 0.3625005781650543}, {"X": 0.3510790467262268, "Y": 0.3615727126598358}]}, "Id": "9a05669f-2bf7-4672-8bbf-942b1cdbcd6d"}, {"BlockType": "WORD", "Confidence": 98.88612365722656, "Text": "11/11/22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0517602302134037, "Height": 0.011139686219394207, "Left": 0.3854284882545471, "Top": 0.35277697443962097}, "Polygon": [{"X": 0.38549819588661194, "Y": 0.35277697443962097}, {"X": 0.4371887147426605, "Y": 0.35479840636253357}, {"X": 0.43713706731796265, "Y": 0.36391666531562805}, {"X": 0.3854284882545471, "Y": 0.361929327249527}]}, "Id": "73cc17b8-868c-45b8-b220-6713f178926f"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "5:32:40", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04497740790247917, "Height": 0.011073242872953415, "Left": 0.4413338601589203, "Top": 0.353767991065979}, "Polygon": [{"X": 0.44138526916503906, "Y": 0.353767991065979}, {"X": 0.48631125688552856, "Y": 0.3555288314819336}, {"X": 0.4862758219242096, "Y": 0.3648412525653839}, {"X": 0.4413338601589203, "Y": 0.36311075091362}]}, "Id": "812afc1f-2358-4ec8-9bd2-29941334f39d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024831296876072884, "Height": 0.010065760463476181, "Left": 0.06927561014890671, "Top": 0.3550315797328949}, "Polygon": [{"X": 0.0694546028971672, "Y": 0.3550315797328949}, {"X": 0.09410690516233444, "Y": 0.35596975684165955}, {"X": 0.0939367413520813, "Y": 0.3650973439216614}, {"X": 0.06927561014890671, "Y": 0.364175021648407}]}, "Id": "26d1b1e6-568e-48ae-bf7d-1500218e2675"}, {"BlockType": "WORD", "Confidence": 99.32318115234375, "Text": "21.37", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03584318980574608, "Height": 0.010149598121643066, "Left": 0.1821271926164627, "Top": 0.35976719856262207}, "Polygon": [{"X": 0.1822620928287506, "Y": 0.35976719856262207}, {"X": 0.2179703712463379, "Y": 0.36112499237060547}, {"X": 0.21784770488739014, "Y": 0.36991679668426514}, {"X": 0.1821271926164627, "Y": 0.3685813546180725}]}, "Id": "fc77f541-bf68-4eab-8f0f-d3648c10efd3"}, {"BlockType": "WORD", "Confidence": 98.77490997314453, "Text": "42,740", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043745700269937515, "Height": 0.011544073931872845, "Left": 0.24982568621635437, "Top": 0.3625621497631073}, "Polygon": [{"X": 0.2499520629644394, "Y": 0.3625621497631073}, {"X": 0.2935713827610016, "Y": 0.3642200231552124}, {"X": 0.29346171021461487, "Y": 0.37410619854927063}, {"X": 0.24982568621635437, "Y": 0.3724791705608368}]}, "Id": "5a1e0b3b-c514-4703-af86-1d147b1b3604"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Buyer:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045221924781799316, "Height": 0.01109800860285759, "Left": 0.06391336023807526, "Top": 0.846491813659668}, "Polygon": [{"X": 0.0641271248459816, "Y": 0.846491813659668}, {"X": 0.10913528501987457, "Y": 0.8466159105300903}, {"X": 0.10894055664539337, "Y": 0.8575897812843323}, {"X": 0.06391336023807526, "Y": 0.8575012683868408}]}, "Id": "077c2814-8f87-495e-825e-73e2c048b691"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "018539", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047770172357559204, "Height": 0.009151441976428032, "Left": 0.12794357538223267, "Top": 0.8456985354423523}, "Polygon": [{"X": 0.1280977576971054, "Y": 0.8456985354423523}, {"X": 0.17571374773979187, "Y": 0.8458331823348999}, {"X": 0.17557603120803833, "Y": 0.8548499941825867}, {"X": 0.12794357538223267, "Y": 0.854746401309967}]}, "Id": "a875726e-eafd-462e-b527-9b7d2fcb6cff"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "3a1e63b7-5846-429c-80d7-f6165c6f66ad", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "3a1e63b7-5846-429c-80d7-f6165c6f66ad", "content-type": "application/x-amz-json-1.1", "content-length": "44668", "date": "Thu, 18 Sep 2025 07:58:33 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "e3681264-c480-4886-b144-fe7f7f86e128", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 07:58:36 GMT", "content-type": "application/json", "content-length": "1005", "connection": "keep-alive", "x-amzn-requestid": "e3681264-c480-4886-b144-fe7f7f86e128"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a page with <PERSON><PERSON>, Trip Ticket, load #, gross, tare, net. Looks like a scale ticket (weight record). There are weight details: Gross 36.67 tons, 73,340 lb, Tare 15.30 tons 30,600 lb, Net 21.37 tons 42,740 lb. It also says BOL, Trip Ticket BIR0029912. The page includes BOL and Trip Ticket, but it's likely a trip ticket or scale ticket. The keywords: \"Gross\", \"Tare\", \"Net\", \"Date In\", \"Date Out\", weight units. That matches scale_ticket. Also BOL is present but the page is weight record. So classify as scale_ticket. It's only one page. So output page 1 with doc_type scale_ticket."}}}, {"toolUse": {"toolUseId": "tooluse_kPQ4clLZSHq5b7jUx7BdPQ", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2250, "outputTokens": 206, "totalTokens": 2456}, "metrics": {"latencyMs": 991}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/5bde3a4e_6_scale_ticket_11.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check for contination of previous page by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nND Paper\nND\nND PAPER\nBiron Scale(Biron)\n29210\nSource\nVIM_AURO_IL_RATHBONE(VIM RECYCLERSL P)\nContract\n018539 (HARVESTER)\nDestination\nBIRON(BIRON MILL)\nTrailer AT26\nDriver\nZACH\nBOL\nTrip Ticket BIR0029912\nProduct OCC 11D\nLoad # BIR-29210\nTons\nLB\nGross\n36.67\n73,340\nDate In 11/11/22 4:49:27\nTare\n15.30\n30,600\nDate Out 11/11/22 5:32:40\nNet\n21.37\n42,740\nBuyer: 018539\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}