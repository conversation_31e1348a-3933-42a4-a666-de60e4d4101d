2025-09-18 12:58:06,321 - INFO - Logging initialized. Log file: logs/test_classification_20250918_125806.log
2025-09-18 12:58:06,322 - INFO - 📁 Found 1 files to process
2025-09-18 12:58:06,322 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 12:58:06,322 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-18 12:58:06,322 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-18 12:58:06,322 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-18 12:58:06,322 - INFO - ⬆️ [12:58:06] Uploading: 85QVSA10311169.pdf
2025-09-18 12:58:09,459 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_random/85QVSA10311169.pdf -> s3://document-extraction-logistically/temp/ae0e35f9_85QVSA10311169.pdf
2025-09-18 12:58:09,459 - INFO - 🔍 [12:58:09] Starting classification: 85QVSA10311169.pdf
2025-09-18 12:58:09,460 - INFO - Initializing TextractProcessor...
2025-09-18 12:58:09,477 - INFO - Initializing BedrockProcessor...
2025-09-18 12:58:09,482 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ae0e35f9_85QVSA10311169.pdf
2025-09-18 12:58:09,482 - INFO - Processing PDF from S3...
2025-09-18 12:58:09,482 - INFO - Downloading PDF from S3 to /tmp/tmpe3jeputl/ae0e35f9_85QVSA10311169.pdf
2025-09-18 12:58:20,317 - INFO - Splitting PDF into individual pages...
2025-09-18 12:58:20,319 - INFO - Splitting PDF ae0e35f9_85QVSA10311169 into 6 pages
2025-09-18 12:58:20,327 - INFO - Split PDF into 6 pages
2025-09-18 12:58:20,327 - INFO - Processing pages with Textract in parallel...
2025-09-18 12:58:20,327 - INFO - Expected pages: [1, 2, 3, 4, 5, 6]
2025-09-18 12:58:24,811 - INFO - Page 6: Extracted 45 characters, 2 lines from ae0e35f9_85QVSA10311169_fadb6683_page_006.pdf
2025-09-18 12:58:24,812 - INFO - Successfully processed page 6
2025-09-18 12:58:25,186 - INFO - Page 1: Extracted 701 characters, 51 lines from ae0e35f9_85QVSA10311169_fadb6683_page_001.pdf
2025-09-18 12:58:25,187 - INFO - Successfully processed page 1
2025-09-18 12:58:25,318 - INFO - Page 5: Extracted 38 characters, 2 lines from ae0e35f9_85QVSA10311169_fadb6683_page_005.pdf
2025-09-18 12:58:25,318 - INFO - Successfully processed page 5
2025-09-18 12:58:26,538 - INFO - Page 2: Extracted 1443 characters, 67 lines from ae0e35f9_85QVSA10311169_fadb6683_page_002.pdf
2025-09-18 12:58:26,539 - INFO - Successfully processed page 2
2025-09-18 12:58:26,925 - INFO - Page 3: Extracted 2046 characters, 27 lines from ae0e35f9_85QVSA10311169_fadb6683_page_003.pdf
2025-09-18 12:58:26,925 - INFO - Successfully processed page 3
2025-09-18 12:58:28,454 - INFO - Page 4: Extracted 3212 characters, 98 lines from ae0e35f9_85QVSA10311169_fadb6683_page_004.pdf
2025-09-18 12:58:28,454 - INFO - Successfully processed page 4
2025-09-18 12:58:28,455 - INFO - Combined 6 pages into final text
2025-09-18 12:58:28,455 - INFO - Text validation for ae0e35f9_85QVSA10311169: 7597 characters, 6 pages
2025-09-18 12:58:28,456 - INFO - Analyzing document types with Bedrock...
2025-09-18 12:58:28,456 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 12:58:33,396 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ae0e35f9_85QVSA10311169.pdf
2025-09-18 12:58:33,472 - INFO - 

📄 File: 85QVSA10311169.pdf
2025-09-18 12:58:33,472 - INFO - 

📋 Classification: {
  "documents": [
    {
      "page_no": 1,
      "doc_type": "combined_carrier_documents"
    },
    {
      "page_no": 2,
      "doc_type": "rate_confirmation"
    },
    {
      "page_no": 3,
      "doc_type": "rate_confirmation"
    },
    {
      "page_no": 4,
      "doc_type": "bol"
    },
    {
      "page_no": 5,
      "doc_type": "bol"
    },
    {
      "page_no": 6,
      "doc_type": "bol"
    }
  ]
}
2025-09-18 12:58:33,472 - INFO - 

✓ Saved result: output/run1_85QVSA10311169.json
2025-09-18 12:58:34,523 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ae0e35f9_85QVSA10311169.pdf
2025-09-18 12:58:34,525 - INFO - 
📊 Processing Summary:
2025-09-18 12:58:34,525 - INFO -    Total files: 1
2025-09-18 12:58:34,526 - INFO -    Successful: 1
2025-09-18 12:58:34,526 - INFO -    Failed: 0
2025-09-18 12:58:34,526 - INFO -    Duration: 28.20 seconds
2025-09-18 12:58:34,526 - INFO -    Output directory: output
2025-09-18 12:58:34,526 - INFO - 
📋 Successfully Processed Files:
2025-09-18 12:58:34,526 - INFO -    📄 85QVSA10311169.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"},{"page_no":2,"doc_type":"rate_confirmation"},{"page_no":3,"doc_type":"rate_confirmation"},{"page_no":4,"doc_type":"bol"},{"page_no":5,"doc_type":"bol"},{"page_no":6,"doc_type":"bol"}]}
2025-09-18 12:58:34,526 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 1, 'failed': 0, 'errors': [], 'duration_seconds': 28.203592}
