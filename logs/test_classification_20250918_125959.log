2025-09-18 12:59:59,352 - INFO - Logging initialized. Log file: logs/test_classification_20250918_125959.log
2025-09-18 12:59:59,352 - INFO - 📁 Found 1 files to process
2025-09-18 12:59:59,352 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 12:59:59,353 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-18 12:59:59,353 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-18 12:59:59,353 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-18 12:59:59,353 - INFO - ⬆️ [12:59:59] Uploading: 6_scale_ticket_1.pdf
2025-09-18 13:00:02,179 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_1.pdf -> s3://document-extraction-logistically/temp/967c58f7_6_scale_ticket_1.pdf
2025-09-18 13:00:02,179 - INFO - 🔍 [13:00:02] Starting classification: 6_scale_ticket_1.pdf
2025-09-18 13:00:02,180 - INFO - Initializing TextractProcessor...
2025-09-18 13:00:02,193 - INFO - Initializing BedrockProcessor...
2025-09-18 13:00:02,198 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/967c58f7_6_scale_ticket_1.pdf
2025-09-18 13:00:02,198 - INFO - Processing PDF from S3...
2025-09-18 13:00:02,198 - INFO - Downloading PDF from S3 to /tmp/tmpt8tsc6s1/967c58f7_6_scale_ticket_1.pdf
2025-09-18 13:00:04,685 - INFO - Splitting PDF into individual pages...
2025-09-18 13:00:04,688 - INFO - Splitting PDF 967c58f7_6_scale_ticket_1 into 1 pages
2025-09-18 13:00:04,691 - INFO - Split PDF into 1 pages
2025-09-18 13:00:04,692 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:00:04,692 - INFO - Expected pages: [1]
2025-09-18 13:00:10,683 - INFO - Page 1: Extracted 1382 characters, 62 lines from 967c58f7_6_scale_ticket_1_16936f29_page_001.pdf
2025-09-18 13:00:10,684 - INFO - Successfully processed page 1
2025-09-18 13:00:10,684 - INFO - Combined 1 pages into final text
2025-09-18 13:00:10,685 - INFO - Text validation for 967c58f7_6_scale_ticket_1: 1399 characters, 1 pages
2025-09-18 13:00:10,685 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:00:10,685 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:00:14,051 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/967c58f7_6_scale_ticket_1.pdf
2025-09-18 13:00:14,078 - INFO - 

6_scale_ticket_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:00:14,078 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_1.json
2025-09-18 13:00:15,382 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/967c58f7_6_scale_ticket_1.pdf
2025-09-18 13:00:15,382 - INFO - 
📊 Processing Summary:
2025-09-18 13:00:15,383 - INFO -    Total files: 1
2025-09-18 13:00:15,383 - INFO -    Successful: 1
2025-09-18 13:00:15,383 - INFO -    Failed: 0
2025-09-18 13:00:15,383 - INFO -    Duration: 16.03 seconds
2025-09-18 13:00:15,383 - INFO -    Output directory: output
2025-09-18 13:00:15,383 - INFO - 
📋 Successfully Processed Files:
2025-09-18 13:00:15,383 - INFO -    📄 6_scale_ticket_1.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:00:15,383 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 1, 'failed': 0, 'errors': [], 'duration_seconds': 16.029874}
