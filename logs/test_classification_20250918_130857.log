2025-09-18 13:08:57,416 - INFO - Logging initialized. Log file: logs/test_classification_20250918_130857.log
2025-09-18 13:08:57,417 - INFO - 📁 Found 2 files to process
2025-09-18 13:08:57,417 - INFO - 🚀 Starting processing with run number: 200
2025-09-18 13:08:57,417 - INFO - 🚀 Processing 2 files in FORCED PARALLEL MODE...
2025-09-18 13:08:57,417 - INFO - 🚀 Creating 2 parallel tasks...
2025-09-18 13:08:57,417 - INFO - 🚀 All 2 tasks created - executing in parallel...
2025-09-18 13:08:57,417 - INFO - ⬆️ [13:08:57] Uploading: 6_scale_ticket_1.pdf
2025-09-18 13:09:00,268 - INFO - ✓ Uploaded: input_data_3_per_cat/6_scale_ticket/6_scale_ticket_1.pdf -> s3://document-extraction-logistically/temp/5144fd05_6_scale_ticket_1.pdf
2025-09-18 13:09:00,268 - INFO - 🔍 [13:09:00] Starting classification: 6_scale_ticket_1.pdf
2025-09-18 13:09:00,269 - INFO - ⬆️ [13:09:00] Uploading: 6_scale_ticket_10.png
2025-09-18 13:09:00,271 - INFO - Initializing TextractProcessor...
2025-09-18 13:09:00,297 - INFO - Initializing BedrockProcessor...
2025-09-18 13:09:00,301 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5144fd05_6_scale_ticket_1.pdf
2025-09-18 13:09:00,301 - INFO - Processing PDF from S3...
2025-09-18 13:09:00,301 - INFO - Downloading PDF from S3 to /tmp/tmptn49ss6e/5144fd05_6_scale_ticket_1.pdf
2025-09-18 13:09:02,372 - INFO - Splitting PDF into individual pages...
2025-09-18 13:09:02,376 - INFO - Splitting PDF 5144fd05_6_scale_ticket_1 into 1 pages
2025-09-18 13:09:02,379 - INFO - Split PDF into 1 pages
2025-09-18 13:09:02,379 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:09:02,380 - INFO - Expected pages: [1]
2025-09-18 13:09:03,213 - INFO - ✓ Uploaded: input_data_3_per_cat/6_scale_ticket/6_scale_ticket_10.png -> s3://document-extraction-logistically/temp/2be688e3_6_scale_ticket_10.png
2025-09-18 13:09:03,213 - INFO - 🔍 [13:09:03] Starting classification: 6_scale_ticket_10.png
2025-09-18 13:09:03,214 - INFO - Initializing TextractProcessor...
2025-09-18 13:09:03,231 - INFO - Initializing BedrockProcessor...
2025-09-18 13:09:03,235 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2be688e3_6_scale_ticket_10.png
2025-09-18 13:09:03,236 - INFO - Processing image from S3...
2025-09-18 13:09:06,451 - INFO - S3 Image temp/2be688e3_6_scale_ticket_10.png: Extracted 751 characters, 49 lines
2025-09-18 13:09:06,451 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:09:06,451 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:09:08,044 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2be688e3_6_scale_ticket_10.png
2025-09-18 13:09:08,075 - INFO - Page 1: Extracted 1382 characters, 62 lines from 5144fd05_6_scale_ticket_1_92432396_page_001.pdf
2025-09-18 13:09:08,078 - INFO - Successfully processed page 1
2025-09-18 13:09:08,081 - INFO - Combined 1 pages into final text
2025-09-18 13:09:08,083 - INFO - Text validation for 5144fd05_6_scale_ticket_1: 1399 characters, 1 pages
2025-09-18 13:09:08,089 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:09:08,089 - INFO - 

6_scale_ticket_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:09:08,089 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:09:08,089 - INFO - 

✓ Saved result: Augment_test/cli_output/run200_6_scale_ticket_10.json
2025-09-18 13:09:08,462 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2be688e3_6_scale_ticket_10.png
2025-09-18 13:09:10,295 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5144fd05_6_scale_ticket_1.pdf
2025-09-18 13:09:10,324 - INFO - 

6_scale_ticket_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:09:10,324 - INFO - 

✓ Saved result: Augment_test/cli_output/run200_6_scale_ticket_1.json
2025-09-18 13:09:10,704 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5144fd05_6_scale_ticket_1.pdf
2025-09-18 13:09:10,705 - INFO - 
📊 Processing Summary:
2025-09-18 13:09:10,705 - INFO -    Total files: 2
2025-09-18 13:09:10,705 - INFO -    Successful: 2
2025-09-18 13:09:10,705 - INFO -    Failed: 0
2025-09-18 13:09:10,705 - INFO -    Duration: 13.29 seconds
2025-09-18 13:09:10,705 - INFO -    Output directory: Augment_test/cli_output
2025-09-18 13:09:10,706 - INFO - 
📋 Successfully Processed Files:
2025-09-18 13:09:10,706 - INFO -    📄 6_scale_ticket_1.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:09:10,706 - INFO -    📄 6_scale_ticket_10.png: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:09:10,706 - INFO - 
========================================================================================================================
2025-09-18 13:09:10,706 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 13:09:10,706 - INFO - ========================================================================================================================
2025-09-18 13:09:10,707 - INFO - PDF FILE LINK                                                PAGE     CLASSIFIED CATEGORY           
2025-09-18 13:09:10,707 - INFO - ------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:09:10,707 - INFO - 6_scale_ticket_1.pdf                                         1        scale_ticket                  
2025-09-18 13:09:10,707 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_1.pdf
2025-09-18 13:09:10,707 - INFO - 
2025-09-18 13:09:10,707 - INFO - 6_scale_ticket_10.png                                        1        scale_ticket                  
2025-09-18 13:09:10,707 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_10.png
2025-09-18 13:09:10,707 - INFO - 
2025-09-18 13:09:10,707 - INFO - ------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:09:10,707 - INFO - Total entries: 2
2025-09-18 13:09:10,707 - INFO - ========================================================================================================================
2025-09-18 13:09:10,707 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 13:09:10,708 - INFO - --------------------------------------------------------------------------------
2025-09-18 13:09:10,708 - INFO -   1. 6_scale_ticket_1.pdf                     Page 1   → scale_ticket
2025-09-18 13:09:10,708 - INFO -   2. 6_scale_ticket_10.png                    Page 1   → scale_ticket
2025-09-18 13:09:10,708 - INFO - --------------------------------------------------------------------------------
2025-09-18 13:09:10,708 - INFO - 
📈 Statistics saved to: Augment_test/cli_output/run200_stats.json
2025-09-18 13:09:10,708 - INFO - 
🎉 Processing completed!
2025-09-18 13:09:10,709 - INFO - 📊 Summary: 2/2 files processed successfully
2025-09-18 13:09:10,709 - INFO - ⏱️  Total duration: 13.29 seconds
2025-09-18 13:09:10,709 - INFO - 📁 Output directory: Augment_test/cli_output
2025-09-18 13:09:10,709 - INFO - 📈 Statistics file: Augment_test/cli_output/run200_stats.json
