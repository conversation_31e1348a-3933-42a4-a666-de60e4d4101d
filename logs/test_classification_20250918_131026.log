2025-09-18 13:10:26,220 - INFO - Logging initialized. Log file: logs/test_classification_20250918_131026.log
2025-09-18 13:10:26,220 - INFO - 📁 Found 16 files to process
2025-09-18 13:10:26,220 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 13:10:26,220 - INFO - 🚀 Processing 16 files in FORCED PARALLEL MODE...
2025-09-18 13:10:26,220 - INFO - 🚀 Creating 16 parallel tasks...
2025-09-18 13:10:26,221 - INFO - 🚀 All 16 tasks created - executing in parallel...
2025-09-18 13:10:26,221 - INFO - ⬆️ [13:10:26] Uploading: 6_scale_ticket_1.pdf
2025-09-18 13:10:28,909 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_1.pdf -> s3://document-extraction-logistically/temp/a592af46_6_scale_ticket_1.pdf
2025-09-18 13:10:28,909 - INFO - 🔍 [13:10:28] Starting classification: 6_scale_ticket_1.pdf
2025-09-18 13:10:28,910 - INFO - ⬆️ [13:10:28] Uploading: 6_scale_ticket_10.png
2025-09-18 13:10:28,910 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:28,924 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:28,931 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a592af46_6_scale_ticket_1.pdf
2025-09-18 13:10:28,931 - INFO - Processing PDF from S3...
2025-09-18 13:10:28,931 - INFO - Downloading PDF from S3 to /tmp/tmpv4levkha/a592af46_6_scale_ticket_1.pdf
2025-09-18 13:10:31,237 - INFO - Splitting PDF into individual pages...
2025-09-18 13:10:31,239 - INFO - Splitting PDF a592af46_6_scale_ticket_1 into 1 pages
2025-09-18 13:10:31,242 - INFO - Split PDF into 1 pages
2025-09-18 13:10:31,242 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:10:31,242 - INFO - Expected pages: [1]
2025-09-18 13:10:31,824 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_10.png -> s3://document-extraction-logistically/temp/881cf8c8_6_scale_ticket_10.png
2025-09-18 13:10:31,824 - INFO - 🔍 [13:10:31] Starting classification: 6_scale_ticket_10.png
2025-09-18 13:10:31,825 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:31,826 - INFO - ⬆️ [13:10:31] Uploading: 6_scale_ticket_11.jpg
2025-09-18 13:10:31,844 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:31,852 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/881cf8c8_6_scale_ticket_10.png
2025-09-18 13:10:31,852 - INFO - Processing image from S3...
2025-09-18 13:10:35,020 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_11.jpg -> s3://document-extraction-logistically/temp/0b4b5ac1_6_scale_ticket_11.jpg
2025-09-18 13:10:35,020 - INFO - 🔍 [13:10:35] Starting classification: 6_scale_ticket_11.jpg
2025-09-18 13:10:35,021 - INFO - ⬆️ [13:10:35] Uploading: 6_scale_ticket_12.jpg
2025-09-18 13:10:35,022 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:35,038 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:35,047 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0b4b5ac1_6_scale_ticket_11.jpg
2025-09-18 13:10:35,048 - INFO - Processing image from S3...
2025-09-18 13:10:35,107 - INFO - S3 Image temp/881cf8c8_6_scale_ticket_10.png: Extracted 751 characters, 49 lines
2025-09-18 13:10:35,107 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:35,108 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:36,624 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/881cf8c8_6_scale_ticket_10.png
2025-09-18 13:10:36,956 - INFO - Page 1: Extracted 1382 characters, 62 lines from a592af46_6_scale_ticket_1_d1e2496e_page_001.pdf
2025-09-18 13:10:36,956 - INFO - Successfully processed page 1
2025-09-18 13:10:36,957 - INFO - Combined 1 pages into final text
2025-09-18 13:10:36,957 - INFO - Text validation for a592af46_6_scale_ticket_1: 1399 characters, 1 pages
2025-09-18 13:10:36,957 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:36,957 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:37,549 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_12.jpg -> s3://document-extraction-logistically/temp/9508b71c_6_scale_ticket_12.jpg
2025-09-18 13:10:37,550 - INFO - 🔍 [13:10:37] Starting classification: 6_scale_ticket_12.jpg
2025-09-18 13:10:37,551 - INFO - ⬆️ [13:10:37] Uploading: 6_scale_ticket_13.jpg
2025-09-18 13:10:37,552 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:37,566 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:37,570 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9508b71c_6_scale_ticket_12.jpg
2025-09-18 13:10:37,570 - INFO - Processing image from S3...
2025-09-18 13:10:38,470 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_13.jpg -> s3://document-extraction-logistically/temp/e7a67896_6_scale_ticket_13.jpg
2025-09-18 13:10:38,471 - INFO - 🔍 [13:10:38] Starting classification: 6_scale_ticket_13.jpg
2025-09-18 13:10:38,472 - INFO - ⬆️ [13:10:38] Uploading: 6_scale_ticket_14.jpg
2025-09-18 13:10:38,473 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:38,483 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:38,486 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e7a67896_6_scale_ticket_13.jpg
2025-09-18 13:10:38,486 - INFO - Processing image from S3...
2025-09-18 13:10:38,907 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a592af46_6_scale_ticket_1.pdf
2025-09-18 13:10:40,562 - INFO - S3 Image temp/0b4b5ac1_6_scale_ticket_11.jpg: Extracted 361 characters, 32 lines
2025-09-18 13:10:40,562 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:40,562 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:40,586 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_14.jpg -> s3://document-extraction-logistically/temp/5d8fca35_6_scale_ticket_14.jpg
2025-09-18 13:10:40,586 - INFO - 🔍 [13:10:40] Starting classification: 6_scale_ticket_14.jpg
2025-09-18 13:10:40,586 - INFO - ⬆️ [13:10:40] Uploading: 6_scale_ticket_15.pdf
2025-09-18 13:10:40,587 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:40,609 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:40,613 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5d8fca35_6_scale_ticket_14.jpg
2025-09-18 13:10:40,613 - INFO - Processing image from S3...
2025-09-18 13:10:41,221 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_15.pdf -> s3://document-extraction-logistically/temp/05c5d062_6_scale_ticket_15.pdf
2025-09-18 13:10:41,221 - INFO - 🔍 [13:10:41] Starting classification: 6_scale_ticket_15.pdf
2025-09-18 13:10:41,222 - INFO - ⬆️ [13:10:41] Uploading: 6_scale_ticket_16.pdf
2025-09-18 13:10:41,223 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:41,275 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:41,278 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/05c5d062_6_scale_ticket_15.pdf
2025-09-18 13:10:41,278 - INFO - Processing PDF from S3...
2025-09-18 13:10:41,278 - INFO - Downloading PDF from S3 to /tmp/tmpapy3c9ft/05c5d062_6_scale_ticket_15.pdf
2025-09-18 13:10:41,339 - INFO - S3 Image temp/9508b71c_6_scale_ticket_12.jpg: Extracted 360 characters, 32 lines
2025-09-18 13:10:41,339 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:41,339 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:42,109 - INFO - S3 Image temp/e7a67896_6_scale_ticket_13.jpg: Extracted 904 characters, 46 lines
2025-09-18 13:10:42,109 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:42,109 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:42,192 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_16.pdf -> s3://document-extraction-logistically/temp/faec06e1_6_scale_ticket_16.pdf
2025-09-18 13:10:42,193 - INFO - 🔍 [13:10:42] Starting classification: 6_scale_ticket_16.pdf
2025-09-18 13:10:42,193 - INFO - ⬆️ [13:10:42] Uploading: 6_scale_ticket_2.pdf
2025-09-18 13:10:42,194 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:42,213 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:42,225 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/faec06e1_6_scale_ticket_16.pdf
2025-09-18 13:10:42,225 - INFO - Processing PDF from S3...
2025-09-18 13:10:42,226 - INFO - Downloading PDF from S3 to /tmp/tmpc2nu1cah/faec06e1_6_scale_ticket_16.pdf
2025-09-18 13:10:42,317 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0b4b5ac1_6_scale_ticket_11.jpg
2025-09-18 13:10:42,903 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_2.pdf -> s3://document-extraction-logistically/temp/12218a63_6_scale_ticket_2.pdf
2025-09-18 13:10:42,903 - INFO - 🔍 [13:10:42] Starting classification: 6_scale_ticket_2.pdf
2025-09-18 13:10:42,904 - INFO - ⬆️ [13:10:42] Uploading: 6_scale_ticket_3.pdf
2025-09-18 13:10:42,904 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:42,924 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:42,926 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/12218a63_6_scale_ticket_2.pdf
2025-09-18 13:10:42,926 - INFO - Processing PDF from S3...
2025-09-18 13:10:42,927 - INFO - Downloading PDF from S3 to /tmp/tmpa_edcx1v/12218a63_6_scale_ticket_2.pdf
2025-09-18 13:10:43,035 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9508b71c_6_scale_ticket_12.jpg
2025-09-18 13:10:43,551 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_3.pdf -> s3://document-extraction-logistically/temp/013bad9e_6_scale_ticket_3.pdf
2025-09-18 13:10:43,551 - INFO - 🔍 [13:10:43] Starting classification: 6_scale_ticket_3.pdf
2025-09-18 13:10:43,552 - INFO - ⬆️ [13:10:43] Uploading: 6_scale_ticket_4.pdf
2025-09-18 13:10:43,553 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:43,572 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:43,580 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/013bad9e_6_scale_ticket_3.pdf
2025-09-18 13:10:43,580 - INFO - Processing PDF from S3...
2025-09-18 13:10:43,580 - INFO - Downloading PDF from S3 to /tmp/tmpqvqe6ilb/013bad9e_6_scale_ticket_3.pdf
2025-09-18 13:10:43,917 - INFO - Splitting PDF into individual pages...
2025-09-18 13:10:43,918 - INFO - Splitting PDF 05c5d062_6_scale_ticket_15 into 1 pages
2025-09-18 13:10:43,920 - INFO - Split PDF into 1 pages
2025-09-18 13:10:43,920 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:10:43,920 - INFO - Expected pages: [1]
2025-09-18 13:10:44,222 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e7a67896_6_scale_ticket_13.jpg
2025-09-18 13:10:44,295 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_4.pdf -> s3://document-extraction-logistically/temp/286c578a_6_scale_ticket_4.pdf
2025-09-18 13:10:44,295 - INFO - 🔍 [13:10:44] Starting classification: 6_scale_ticket_4.pdf
2025-09-18 13:10:44,296 - INFO - ⬆️ [13:10:44] Uploading: 6_scale_ticket_5.pdf
2025-09-18 13:10:44,297 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:44,311 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:44,315 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/286c578a_6_scale_ticket_4.pdf
2025-09-18 13:10:44,315 - INFO - Processing PDF from S3...
2025-09-18 13:10:44,315 - INFO - Downloading PDF from S3 to /tmp/tmpii3e4_se/286c578a_6_scale_ticket_4.pdf
2025-09-18 13:10:44,527 - INFO - Splitting PDF into individual pages...
2025-09-18 13:10:44,528 - INFO - Splitting PDF faec06e1_6_scale_ticket_16 into 1 pages
2025-09-18 13:10:44,531 - INFO - Split PDF into 1 pages
2025-09-18 13:10:44,531 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:10:44,531 - INFO - Expected pages: [1]
2025-09-18 13:10:44,860 - INFO - S3 Image temp/5d8fca35_6_scale_ticket_14.jpg: Extracted 489 characters, 64 lines
2025-09-18 13:10:44,860 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:44,860 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:44,934 - INFO - Splitting PDF into individual pages...
2025-09-18 13:10:44,935 - INFO - Splitting PDF 12218a63_6_scale_ticket_2 into 1 pages
2025-09-18 13:10:44,937 - INFO - Split PDF into 1 pages
2025-09-18 13:10:44,937 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:10:44,937 - INFO - Expected pages: [1]
2025-09-18 13:10:44,946 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_5.pdf -> s3://document-extraction-logistically/temp/acd5b539_6_scale_ticket_5.pdf
2025-09-18 13:10:44,946 - INFO - 🔍 [13:10:44] Starting classification: 6_scale_ticket_5.pdf
2025-09-18 13:10:44,947 - INFO - ⬆️ [13:10:44] Uploading: 6_scale_ticket_6.pdf
2025-09-18 13:10:44,947 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:44,966 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:44,970 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/acd5b539_6_scale_ticket_5.pdf
2025-09-18 13:10:44,971 - INFO - Processing PDF from S3...
2025-09-18 13:10:44,971 - INFO - Downloading PDF from S3 to /tmp/tmp1y5_jv24/acd5b539_6_scale_ticket_5.pdf
2025-09-18 13:10:45,362 - INFO - Splitting PDF into individual pages...
2025-09-18 13:10:45,364 - INFO - Splitting PDF 013bad9e_6_scale_ticket_3 into 1 pages
2025-09-18 13:10:45,366 - INFO - Split PDF into 1 pages
2025-09-18 13:10:45,366 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:10:45,366 - INFO - Expected pages: [1]
2025-09-18 13:10:45,650 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_6.pdf -> s3://document-extraction-logistically/temp/f94df38d_6_scale_ticket_6.pdf
2025-09-18 13:10:45,650 - INFO - 🔍 [13:10:45] Starting classification: 6_scale_ticket_6.pdf
2025-09-18 13:10:45,651 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:45,652 - INFO - ⬆️ [13:10:45] Uploading: 6_scale_ticket_7.pdf
2025-09-18 13:10:45,669 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:45,678 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f94df38d_6_scale_ticket_6.pdf
2025-09-18 13:10:45,678 - INFO - Processing PDF from S3...
2025-09-18 13:10:45,679 - INFO - Downloading PDF from S3 to /tmp/tmpk6wqgdok/f94df38d_6_scale_ticket_6.pdf
2025-09-18 13:10:46,290 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_7.pdf -> s3://document-extraction-logistically/temp/ec1a3c81_6_scale_ticket_7.pdf
2025-09-18 13:10:46,291 - INFO - 🔍 [13:10:46] Starting classification: 6_scale_ticket_7.pdf
2025-09-18 13:10:46,292 - INFO - ⬆️ [13:10:46] Uploading: 6_scale_ticket_8.pdf
2025-09-18 13:10:46,298 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:46,310 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:46,317 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ec1a3c81_6_scale_ticket_7.pdf
2025-09-18 13:10:46,317 - INFO - Processing PDF from S3...
2025-09-18 13:10:46,317 - INFO - Downloading PDF from S3 to /tmp/tmp_8t71b4y/ec1a3c81_6_scale_ticket_7.pdf
2025-09-18 13:10:46,562 - INFO - Splitting PDF into individual pages...
2025-09-18 13:10:46,564 - INFO - Splitting PDF 286c578a_6_scale_ticket_4 into 1 pages
2025-09-18 13:10:46,565 - INFO - Split PDF into 1 pages
2025-09-18 13:10:46,566 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:10:46,566 - INFO - Expected pages: [1]
2025-09-18 13:10:46,637 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5d8fca35_6_scale_ticket_14.jpg
2025-09-18 13:10:46,948 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_8.pdf -> s3://document-extraction-logistically/temp/f8176bff_6_scale_ticket_8.pdf
2025-09-18 13:10:46,948 - INFO - 🔍 [13:10:46] Starting classification: 6_scale_ticket_8.pdf
2025-09-18 13:10:46,949 - INFO - ⬆️ [13:10:46] Uploading: 6_scale_ticket_9.pdf
2025-09-18 13:10:46,950 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:46,965 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:46,970 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f8176bff_6_scale_ticket_8.pdf
2025-09-18 13:10:46,970 - INFO - Processing PDF from S3...
2025-09-18 13:10:46,971 - INFO - Downloading PDF from S3 to /tmp/tmplhhxjdlz/f8176bff_6_scale_ticket_8.pdf
2025-09-18 13:10:47,028 - INFO - Splitting PDF into individual pages...
2025-09-18 13:10:47,032 - INFO - Splitting PDF acd5b539_6_scale_ticket_5 into 1 pages
2025-09-18 13:10:47,034 - INFO - Split PDF into 1 pages
2025-09-18 13:10:47,034 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:10:47,035 - INFO - Expected pages: [1]
2025-09-18 13:10:47,598 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_9.pdf -> s3://document-extraction-logistically/temp/3210dd9b_6_scale_ticket_9.pdf
2025-09-18 13:10:47,598 - INFO - 🔍 [13:10:47] Starting classification: 6_scale_ticket_9.pdf
2025-09-18 13:10:47,599 - INFO - Initializing TextractProcessor...
2025-09-18 13:10:47,616 - INFO - Initializing BedrockProcessor...
2025-09-18 13:10:47,622 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3210dd9b_6_scale_ticket_9.pdf
2025-09-18 13:10:47,625 - INFO - Processing PDF from S3...
2025-09-18 13:10:47,628 - INFO - Downloading PDF from S3 to /tmp/tmpsi7pt94u/3210dd9b_6_scale_ticket_9.pdf
2025-09-18 13:10:47,645 - INFO - 

6_scale_ticket_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:10:47,646 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_10.json
2025-09-18 13:10:47,953 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/881cf8c8_6_scale_ticket_10.png
2025-09-18 13:10:47,970 - INFO - 

6_scale_ticket_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:10:47,970 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_1.json
2025-09-18 13:10:47,974 - INFO - Splitting PDF into individual pages...
2025-09-18 13:10:47,975 - INFO - Splitting PDF f94df38d_6_scale_ticket_6 into 1 pages
2025-09-18 13:10:47,977 - INFO - Split PDF into 1 pages
2025-09-18 13:10:47,977 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:10:47,977 - INFO - Expected pages: [1]
2025-09-18 13:10:48,100 - INFO - Splitting PDF into individual pages...
2025-09-18 13:10:48,101 - INFO - Splitting PDF ec1a3c81_6_scale_ticket_7 into 1 pages
2025-09-18 13:10:48,103 - INFO - Split PDF into 1 pages
2025-09-18 13:10:48,103 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:10:48,103 - INFO - Expected pages: [1]
2025-09-18 13:10:48,272 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a592af46_6_scale_ticket_1.pdf
2025-09-18 13:10:48,284 - INFO - 

6_scale_ticket_11.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:10:48,284 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_11.json
2025-09-18 13:10:48,584 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0b4b5ac1_6_scale_ticket_11.jpg
2025-09-18 13:10:48,595 - INFO - 

6_scale_ticket_12.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:10:48,595 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_12.json
2025-09-18 13:10:48,895 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9508b71c_6_scale_ticket_12.jpg
2025-09-18 13:10:48,913 - INFO - 

6_scale_ticket_13.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 13:10:48,913 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_13.json
2025-09-18 13:10:49,209 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e7a67896_6_scale_ticket_13.jpg
2025-09-18 13:10:49,231 - INFO - 

6_scale_ticket_14.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:10:49,231 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_14.json
2025-09-18 13:10:49,259 - INFO - Splitting PDF into individual pages...
2025-09-18 13:10:49,260 - INFO - Splitting PDF f8176bff_6_scale_ticket_8 into 1 pages
2025-09-18 13:10:49,262 - INFO - Split PDF into 1 pages
2025-09-18 13:10:49,262 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:10:49,262 - INFO - Expected pages: [1]
2025-09-18 13:10:49,532 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5d8fca35_6_scale_ticket_14.jpg
2025-09-18 13:10:49,799 - INFO - Page 1: Extracted 678 characters, 29 lines from 05c5d062_6_scale_ticket_15_b571aa53_page_001.pdf
2025-09-18 13:10:49,799 - INFO - Successfully processed page 1
2025-09-18 13:10:49,799 - INFO - Combined 1 pages into final text
2025-09-18 13:10:49,799 - INFO - Text validation for 05c5d062_6_scale_ticket_15: 695 characters, 1 pages
2025-09-18 13:10:49,800 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:49,800 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:49,978 - INFO - Splitting PDF into individual pages...
2025-09-18 13:10:49,979 - INFO - Splitting PDF 3210dd9b_6_scale_ticket_9 into 1 pages
2025-09-18 13:10:49,982 - INFO - Split PDF into 1 pages
2025-09-18 13:10:49,982 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:10:49,982 - INFO - Expected pages: [1]
2025-09-18 13:10:50,992 - INFO - Page 1: Extracted 2006 characters, 95 lines from faec06e1_6_scale_ticket_16_fee413a6_page_001.pdf
2025-09-18 13:10:50,992 - INFO - Successfully processed page 1
2025-09-18 13:10:50,993 - INFO - Combined 1 pages into final text
2025-09-18 13:10:50,993 - INFO - Text validation for faec06e1_6_scale_ticket_16: 2023 characters, 1 pages
2025-09-18 13:10:50,993 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:50,993 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:51,078 - INFO - Page 1: Extracted 702 characters, 33 lines from 013bad9e_6_scale_ticket_3_6519aa4f_page_001.pdf
2025-09-18 13:10:51,078 - INFO - Successfully processed page 1
2025-09-18 13:10:51,079 - INFO - Combined 1 pages into final text
2025-09-18 13:10:51,079 - INFO - Text validation for 013bad9e_6_scale_ticket_3: 719 characters, 1 pages
2025-09-18 13:10:51,080 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:51,080 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:51,254 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/05c5d062_6_scale_ticket_15.pdf
2025-09-18 13:10:51,274 - INFO - 

6_scale_ticket_15.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:10:51,274 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_15.json
2025-09-18 13:10:51,578 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/05c5d062_6_scale_ticket_15.pdf
2025-09-18 13:10:51,681 - INFO - Page 1: Extracted 1041 characters, 72 lines from 12218a63_6_scale_ticket_2_c8b607e6_page_001.pdf
2025-09-18 13:10:51,681 - INFO - Successfully processed page 1
2025-09-18 13:10:51,681 - INFO - Combined 1 pages into final text
2025-09-18 13:10:51,681 - INFO - Text validation for 12218a63_6_scale_ticket_2: 1058 characters, 1 pages
2025-09-18 13:10:51,682 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:51,682 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:52,745 - INFO - Page 1: Extracted 679 characters, 29 lines from acd5b539_6_scale_ticket_5_1e688c61_page_001.pdf
2025-09-18 13:10:52,745 - INFO - Successfully processed page 1
2025-09-18 13:10:52,746 - INFO - Combined 1 pages into final text
2025-09-18 13:10:52,746 - INFO - Text validation for acd5b539_6_scale_ticket_5: 696 characters, 1 pages
2025-09-18 13:10:52,746 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:52,746 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:53,415 - INFO - Page 1: Extracted 1020 characters, 58 lines from ec1a3c81_6_scale_ticket_7_40a5f724_page_001.pdf
2025-09-18 13:10:53,418 - INFO - Page 1: Extracted 421 characters, 38 lines from 286c578a_6_scale_ticket_4_c3d5ac12_page_001.pdf
2025-09-18 13:10:53,418 - INFO - Successfully processed page 1
2025-09-18 13:10:53,419 - INFO - Successfully processed page 1
2025-09-18 13:10:53,419 - INFO - Combined 1 pages into final text
2025-09-18 13:10:53,419 - INFO - Text validation for ec1a3c81_6_scale_ticket_7: 1037 characters, 1 pages
2025-09-18 13:10:53,419 - INFO - Combined 1 pages into final text
2025-09-18 13:10:53,419 - INFO - Text validation for 286c578a_6_scale_ticket_4: 438 characters, 1 pages
2025-09-18 13:10:53,419 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:53,419 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:53,419 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:53,421 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:53,496 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/013bad9e_6_scale_ticket_3.pdf
2025-09-18 13:10:53,514 - INFO - 

6_scale_ticket_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:10:53,514 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_3.json
2025-09-18 13:10:53,639 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/faec06e1_6_scale_ticket_16.pdf
2025-09-18 13:10:53,760 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/12218a63_6_scale_ticket_2.pdf
2025-09-18 13:10:53,818 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/013bad9e_6_scale_ticket_3.pdf
2025-09-18 13:10:53,840 - INFO - 

6_scale_ticket_16.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:10:53,840 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_16.json
2025-09-18 13:10:53,983 - INFO - Page 1: Extracted 473 characters, 36 lines from f94df38d_6_scale_ticket_6_f12bd889_page_001.pdf
2025-09-18 13:10:53,984 - INFO - Successfully processed page 1
2025-09-18 13:10:53,985 - INFO - Combined 1 pages into final text
2025-09-18 13:10:53,985 - INFO - Text validation for f94df38d_6_scale_ticket_6: 490 characters, 1 pages
2025-09-18 13:10:53,985 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:53,985 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:54,139 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/faec06e1_6_scale_ticket_16.pdf
2025-09-18 13:10:54,162 - INFO - 

6_scale_ticket_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 13:10:54,162 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_2.json
2025-09-18 13:10:54,373 - INFO - Page 1: Extracted 622 characters, 27 lines from f8176bff_6_scale_ticket_8_82474109_page_001.pdf
2025-09-18 13:10:54,373 - INFO - Successfully processed page 1
2025-09-18 13:10:54,374 - INFO - Combined 1 pages into final text
2025-09-18 13:10:54,374 - INFO - Text validation for f8176bff_6_scale_ticket_8: 639 characters, 1 pages
2025-09-18 13:10:54,374 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:54,374 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:54,384 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/acd5b539_6_scale_ticket_5.pdf
2025-09-18 13:10:54,465 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/12218a63_6_scale_ticket_2.pdf
2025-09-18 13:10:54,483 - INFO - 

6_scale_ticket_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:10:54,483 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_5.json
2025-09-18 13:10:54,787 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/acd5b539_6_scale_ticket_5.pdf
2025-09-18 13:10:54,831 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/286c578a_6_scale_ticket_4.pdf
2025-09-18 13:10:54,842 - INFO - 

6_scale_ticket_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:10:54,842 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_4.json
2025-09-18 13:10:55,144 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/286c578a_6_scale_ticket_4.pdf
2025-09-18 13:10:55,206 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ec1a3c81_6_scale_ticket_7.pdf
2025-09-18 13:10:55,228 - INFO - 

6_scale_ticket_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:10:55,228 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_7.json
2025-09-18 13:10:55,402 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f94df38d_6_scale_ticket_6.pdf
2025-09-18 13:10:55,526 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ec1a3c81_6_scale_ticket_7.pdf
2025-09-18 13:10:55,540 - INFO - 

6_scale_ticket_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:10:55,540 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_6.json
2025-09-18 13:10:55,843 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f94df38d_6_scale_ticket_6.pdf
2025-09-18 13:10:56,081 - INFO - Page 1: Extracted 731 characters, 33 lines from 3210dd9b_6_scale_ticket_9_a8cef72f_page_001.pdf
2025-09-18 13:10:56,081 - INFO - Successfully processed page 1
2025-09-18 13:10:56,082 - INFO - Combined 1 pages into final text
2025-09-18 13:10:56,082 - INFO - Text validation for 3210dd9b_6_scale_ticket_9: 748 characters, 1 pages
2025-09-18 13:10:56,082 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:10:56,082 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:10:56,091 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f8176bff_6_scale_ticket_8.pdf
2025-09-18 13:10:56,111 - INFO - 

6_scale_ticket_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:10:56,111 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_8.json
2025-09-18 13:10:56,411 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f8176bff_6_scale_ticket_8.pdf
2025-09-18 13:10:57,855 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3210dd9b_6_scale_ticket_9.pdf
2025-09-18 13:10:57,874 - INFO - 

6_scale_ticket_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:10:57,874 - INFO - 

✓ Saved result: output/run1_6_scale_ticket_9.json
2025-09-18 13:10:58,176 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3210dd9b_6_scale_ticket_9.pdf
2025-09-18 13:10:58,177 - INFO - 
📊 Processing Summary:
2025-09-18 13:10:58,177 - INFO -    Total files: 16
2025-09-18 13:10:58,177 - INFO -    Successful: 16
2025-09-18 13:10:58,177 - INFO -    Failed: 0
2025-09-18 13:10:58,178 - INFO -    Duration: 31.96 seconds
2025-09-18 13:10:58,178 - INFO -    Output directory: output
2025-09-18 13:10:58,178 - INFO - 
📋 Successfully Processed Files:
2025-09-18 13:10:58,178 - INFO -    📄 6_scale_ticket_1.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:10:58,178 - INFO -    📄 6_scale_ticket_10.png: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:10:58,178 - INFO -    📄 6_scale_ticket_11.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:10:58,178 - INFO -    📄 6_scale_ticket_12.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:10:58,178 - INFO -    📄 6_scale_ticket_13.jpg: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"}]}
2025-09-18 13:10:58,178 - INFO -    📄 6_scale_ticket_14.jpg: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:10:58,178 - INFO -    📄 6_scale_ticket_15.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:10:58,178 - INFO -    📄 6_scale_ticket_16.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:10:58,179 - INFO -    📄 6_scale_ticket_2.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"}]}
2025-09-18 13:10:58,179 - INFO -    📄 6_scale_ticket_3.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:10:58,179 - INFO -    📄 6_scale_ticket_4.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:10:58,179 - INFO -    📄 6_scale_ticket_5.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:10:58,179 - INFO -    📄 6_scale_ticket_6.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:10:58,179 - INFO -    📄 6_scale_ticket_7.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:10:58,179 - INFO -    📄 6_scale_ticket_8.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:10:58,179 - INFO -    📄 6_scale_ticket_9.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:10:58,180 - INFO - 
========================================================================================================================
2025-09-18 13:10:58,180 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 13:10:58,180 - INFO - ========================================================================================================================
2025-09-18 13:10:58,180 - INFO - PDF FILE LINK                                                PAGE     CLASSIFIED CATEGORY           
2025-09-18 13:10:58,180 - INFO - ------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:10:58,180 - INFO - 6_scale_ticket_1.pdf                                         1        scale_ticket                  
2025-09-18 13:10:58,180 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_1.pdf
2025-09-18 13:10:58,180 - INFO - 
2025-09-18 13:10:58,180 - INFO - 6_scale_ticket_10.png                                        1        scale_ticket                  
2025-09-18 13:10:58,180 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_10.png
2025-09-18 13:10:58,180 - INFO - 
2025-09-18 13:10:58,180 - INFO - 6_scale_ticket_11.jpg                                        1        scale_ticket                  
2025-09-18 13:10:58,180 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_11.jpg
2025-09-18 13:10:58,180 - INFO - 
2025-09-18 13:10:58,180 - INFO - 6_scale_ticket_12.jpg                                        1        scale_ticket                  
2025-09-18 13:10:58,180 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_12.jpg
2025-09-18 13:10:58,180 - INFO - 
2025-09-18 13:10:58,180 - INFO - 6_scale_ticket_13.jpg                                        1        combined_carrier_documents    
2025-09-18 13:10:58,181 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_13.jpg
2025-09-18 13:10:58,181 - INFO - 
2025-09-18 13:10:58,181 - INFO - 6_scale_ticket_14.jpg                                        1        scale_ticket                  
2025-09-18 13:10:58,181 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_14.jpg
2025-09-18 13:10:58,181 - INFO - 
2025-09-18 13:10:58,181 - INFO - 6_scale_ticket_15.pdf                                        1        scale_ticket                  
2025-09-18 13:10:58,181 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_15.pdf
2025-09-18 13:10:58,181 - INFO - 
2025-09-18 13:10:58,181 - INFO - 6_scale_ticket_16.pdf                                        1        scale_ticket                  
2025-09-18 13:10:58,181 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_16.pdf
2025-09-18 13:10:58,181 - INFO - 
2025-09-18 13:10:58,181 - INFO - 6_scale_ticket_2.pdf                                         1        combined_carrier_documents    
2025-09-18 13:10:58,181 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_2.pdf
2025-09-18 13:10:58,181 - INFO - 
2025-09-18 13:10:58,181 - INFO - 6_scale_ticket_3.pdf                                         1        scale_ticket                  
2025-09-18 13:10:58,181 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_3.pdf
2025-09-18 13:10:58,181 - INFO - 
2025-09-18 13:10:58,181 - INFO - 6_scale_ticket_4.pdf                                         1        scale_ticket                  
2025-09-18 13:10:58,181 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_4.pdf
2025-09-18 13:10:58,181 - INFO - 
2025-09-18 13:10:58,181 - INFO - 6_scale_ticket_5.pdf                                         1        scale_ticket                  
2025-09-18 13:10:58,182 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_5.pdf
2025-09-18 13:10:58,182 - INFO - 
2025-09-18 13:10:58,182 - INFO - 6_scale_ticket_6.pdf                                         1        scale_ticket                  
2025-09-18 13:10:58,182 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_6.pdf
2025-09-18 13:10:58,182 - INFO - 
2025-09-18 13:10:58,182 - INFO - 6_scale_ticket_7.pdf                                         1        scale_ticket                  
2025-09-18 13:10:58,182 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_7.pdf
2025-09-18 13:10:58,182 - INFO - 
2025-09-18 13:10:58,182 - INFO - 6_scale_ticket_8.pdf                                         1        scale_ticket                  
2025-09-18 13:10:58,182 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_8.pdf
2025-09-18 13:10:58,182 - INFO - 
2025-09-18 13:10:58,182 - INFO - 6_scale_ticket_9.pdf                                         1        scale_ticket                  
2025-09-18 13:10:58,182 - INFO -   → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_9.pdf
2025-09-18 13:10:58,182 - INFO - 
2025-09-18 13:10:58,182 - INFO - ------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:10:58,182 - INFO - Total entries: 16
2025-09-18 13:10:58,182 - INFO - ========================================================================================================================
2025-09-18 13:10:58,182 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 13:10:58,182 - INFO - --------------------------------------------------------------------------------
2025-09-18 13:10:58,182 - INFO -   1. 6_scale_ticket_1.pdf                     Page 1   → scale_ticket
2025-09-18 13:10:58,182 - INFO -   2. 6_scale_ticket_10.png                    Page 1   → scale_ticket
2025-09-18 13:10:58,182 - INFO -   3. 6_scale_ticket_11.jpg                    Page 1   → scale_ticket
2025-09-18 13:10:58,183 - INFO -   4. 6_scale_ticket_12.jpg                    Page 1   → scale_ticket
2025-09-18 13:10:58,183 - INFO -   5. 6_scale_ticket_13.jpg                    Page 1   → combined_carrier_documents
2025-09-18 13:10:58,183 - INFO -   6. 6_scale_ticket_14.jpg                    Page 1   → scale_ticket
2025-09-18 13:10:58,183 - INFO -   7. 6_scale_ticket_15.pdf                    Page 1   → scale_ticket
2025-09-18 13:10:58,183 - INFO -   8. 6_scale_ticket_16.pdf                    Page 1   → scale_ticket
2025-09-18 13:10:58,183 - INFO -   9. 6_scale_ticket_2.pdf                     Page 1   → combined_carrier_documents
2025-09-18 13:10:58,183 - INFO -  10. 6_scale_ticket_3.pdf                     Page 1   → scale_ticket
2025-09-18 13:10:58,183 - INFO -  11. 6_scale_ticket_4.pdf                     Page 1   → scale_ticket
2025-09-18 13:10:58,183 - INFO -  12. 6_scale_ticket_5.pdf                     Page 1   → scale_ticket
2025-09-18 13:10:58,183 - INFO -  13. 6_scale_ticket_6.pdf                     Page 1   → scale_ticket
2025-09-18 13:10:58,183 - INFO -  14. 6_scale_ticket_7.pdf                     Page 1   → scale_ticket
2025-09-18 13:10:58,183 - INFO -  15. 6_scale_ticket_8.pdf                     Page 1   → scale_ticket
2025-09-18 13:10:58,183 - INFO -  16. 6_scale_ticket_9.pdf                     Page 1   → scale_ticket
2025-09-18 13:10:58,183 - INFO - --------------------------------------------------------------------------------
2025-09-18 13:10:58,184 - INFO - 
✅ Test completed: {'total_files': 16, 'processed': 16, 'failed': 0, 'errors': [], 'duration_seconds': 31.95654, 'processed_files': [{'filename': '6_scale_ticket_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_1.pdf'}, {'filename': '6_scale_ticket_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_10.png'}, {'filename': '6_scale_ticket_11.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_11.jpg'}, {'filename': '6_scale_ticket_12.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_12.jpg'}, {'filename': '6_scale_ticket_13.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_13.jpg'}, {'filename': '6_scale_ticket_14.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_14.jpg'}, {'filename': '6_scale_ticket_15.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_15.pdf'}, {'filename': '6_scale_ticket_16.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_16.pdf'}, {'filename': '6_scale_ticket_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_2.pdf'}, {'filename': '6_scale_ticket_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_3.pdf'}, {'filename': '6_scale_ticket_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_4.pdf'}, {'filename': '6_scale_ticket_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_5.pdf'}, {'filename': '6_scale_ticket_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_6.pdf'}, {'filename': '6_scale_ticket_7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_7.pdf'}, {'filename': '6_scale_ticket_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_8.pdf'}, {'filename': '6_scale_ticket_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/6_scale_ticket/6_scale_ticket_9.pdf'}]}
