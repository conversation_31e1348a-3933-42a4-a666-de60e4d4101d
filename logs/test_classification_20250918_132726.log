2025-09-18 13:27:26,506 - INFO - Logging initialized. Log file: logs/test_classification_20250918_132726.log
2025-09-18 13:27:26,506 - INFO - 📁 Found 2 files to process
2025-09-18 13:27:26,506 - INFO - 🚀 Starting processing with run number: 300
2025-09-18 13:27:26,506 - INFO - 🚀 Processing 2 files in FORCED PARALLEL MODE...
2025-09-18 13:27:26,506 - INFO - 🚀 Creating 2 parallel tasks...
2025-09-18 13:27:26,506 - INFO - 🚀 All 2 tasks created - executing in parallel...
2025-09-18 13:27:26,507 - INFO - ⬆️ [13:27:26] Uploading: 6_scale_ticket_1.pdf
2025-09-18 13:27:29,492 - INFO - ✓ Uploaded: input_data_3_per_cat/6_scale_ticket/6_scale_ticket_1.pdf -> s3://document-extraction-logistically/temp/56bf71fc_6_scale_ticket_1.pdf
2025-09-18 13:27:29,492 - INFO - 🔍 [13:27:29] Starting classification: 6_scale_ticket_1.pdf
2025-09-18 13:27:29,493 - INFO - ⬆️ [13:27:29] Uploading: 6_scale_ticket_10.png
2025-09-18 13:27:29,494 - INFO - Initializing TextractProcessor...
2025-09-18 13:27:29,522 - INFO - Initializing BedrockProcessor...
2025-09-18 13:27:29,527 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/56bf71fc_6_scale_ticket_1.pdf
2025-09-18 13:27:29,527 - INFO - Processing PDF from S3...
2025-09-18 13:27:29,527 - INFO - Downloading PDF from S3 to /tmp/tmp_f_2upn7/56bf71fc_6_scale_ticket_1.pdf
2025-09-18 13:27:31,546 - INFO - Splitting PDF into individual pages...
2025-09-18 13:27:31,549 - INFO - Splitting PDF 56bf71fc_6_scale_ticket_1 into 1 pages
2025-09-18 13:27:31,552 - INFO - Split PDF into 1 pages
2025-09-18 13:27:31,552 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:27:31,552 - INFO - Expected pages: [1]
2025-09-18 13:27:32,254 - INFO - ✓ Uploaded: input_data_3_per_cat/6_scale_ticket/6_scale_ticket_10.png -> s3://document-extraction-logistically/temp/e6a2a0bd_6_scale_ticket_10.png
2025-09-18 13:27:32,254 - INFO - 🔍 [13:27:32] Starting classification: 6_scale_ticket_10.png
2025-09-18 13:27:32,256 - INFO - Initializing TextractProcessor...
2025-09-18 13:27:32,269 - INFO - Initializing BedrockProcessor...
2025-09-18 13:27:32,274 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e6a2a0bd_6_scale_ticket_10.png
2025-09-18 13:27:32,274 - INFO - Processing image from S3...
2025-09-18 13:27:35,966 - INFO - S3 Image temp/e6a2a0bd_6_scale_ticket_10.png: Extracted 751 characters, 49 lines
2025-09-18 13:27:35,966 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:27:35,966 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:27:37,521 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e6a2a0bd_6_scale_ticket_10.png
2025-09-18 13:27:37,544 - INFO - 

6_scale_ticket_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:27:37,544 - INFO - 

✓ Saved result: Augment_test/json_test_output/run300_6_scale_ticket_10.json
2025-09-18 13:27:37,929 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e6a2a0bd_6_scale_ticket_10.png
2025-09-18 13:27:38,958 - INFO - Page 1: Extracted 1382 characters, 62 lines from 56bf71fc_6_scale_ticket_1_5c1c13f9_page_001.pdf
2025-09-18 13:27:38,959 - INFO - Successfully processed page 1
2025-09-18 13:27:38,959 - INFO - Combined 1 pages into final text
2025-09-18 13:27:38,959 - INFO - Text validation for 56bf71fc_6_scale_ticket_1: 1399 characters, 1 pages
2025-09-18 13:27:38,960 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:27:38,960 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:27:40,879 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/56bf71fc_6_scale_ticket_1.pdf
2025-09-18 13:27:40,912 - INFO - 

6_scale_ticket_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-18 13:27:40,912 - INFO - 

✓ Saved result: Augment_test/json_test_output/run300_6_scale_ticket_1.json
2025-09-18 13:27:41,196 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/56bf71fc_6_scale_ticket_1.pdf
2025-09-18 13:27:41,197 - INFO - 
📊 Processing Summary:
2025-09-18 13:27:41,197 - INFO -    Total files: 2
2025-09-18 13:27:41,197 - INFO -    Successful: 2
2025-09-18 13:27:41,197 - INFO -    Failed: 0
2025-09-18 13:27:41,197 - INFO -    Duration: 14.69 seconds
2025-09-18 13:27:41,197 - INFO -    Output directory: Augment_test/json_test_output
2025-09-18 13:27:41,197 - INFO - 
📋 Successfully Processed Files:
2025-09-18 13:27:41,197 - INFO -    📄 6_scale_ticket_1.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:27:41,197 - INFO -    📄 6_scale_ticket_10.png: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-18 13:27:41,198 - INFO - 
============================================================================================================================================
2025-09-18 13:27:41,198 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 13:27:41,198 - INFO - ============================================================================================================================================
2025-09-18 13:27:41,198 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 13:27:41,198 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:27:41,198 - INFO - 6_scale_ticket_1.pdf                               1      scale_ticket         run300_6_scale_ticket_1.json                      
2025-09-18 13:27:41,198 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_1.pdf
2025-09-18 13:27:41,198 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/Augment_test/json_test_output/run300_6_scale_ticket_1.json
2025-09-18 13:27:41,198 - INFO - 
2025-09-18 13:27:41,198 - INFO - 6_scale_ticket_10.png                              1      scale_ticket         run300_6_scale_ticket_10.json                     
2025-09-18 13:27:41,198 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket/6_scale_ticket_10.png
2025-09-18 13:27:41,198 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/Augment_test/json_test_output/run300_6_scale_ticket_10.json
2025-09-18 13:27:41,198 - INFO - 
2025-09-18 13:27:41,198 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:27:41,198 - INFO - Total entries: 2
2025-09-18 13:27:41,198 - INFO - ============================================================================================================================================
2025-09-18 13:27:41,198 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 13:27:41,198 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 13:27:41,199 - INFO -   1. 6_scale_ticket_1.pdf                Page 1   → scale_ticket    | run300_6_scale_ticket_1.json
2025-09-18 13:27:41,199 - INFO -   2. 6_scale_ticket_10.png               Page 1   → scale_ticket    | run300_6_scale_ticket_10.json
2025-09-18 13:27:41,199 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 13:27:41,199 - INFO - 
📈 Statistics saved to: Augment_test/json_test_output/run300_stats.json
2025-09-18 13:27:41,199 - INFO - 
🎉 Processing completed!
2025-09-18 13:27:41,199 - INFO - 📊 Summary: 2/2 files processed successfully
2025-09-18 13:27:41,199 - INFO - ⏱️  Total duration: 14.69 seconds
2025-09-18 13:27:41,199 - INFO - 📁 Output directory: Augment_test/json_test_output
2025-09-18 13:27:41,199 - INFO - 📈 Statistics file: Augment_test/json_test_output/run300_stats.json
