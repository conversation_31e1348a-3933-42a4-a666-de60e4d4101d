2025-09-18 13:27:49,988 - INFO - Logging initialized. Log file: logs/test_classification_20250918_132749.log
2025-09-18 13:27:49,988 - INFO - 📁 Found 1 files to process
2025-09-18 13:27:49,988 - INFO - 🚀 Starting processing with run number: 400
2025-09-18 13:27:49,989 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-18 13:27:49,989 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-18 13:27:49,989 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-18 13:27:49,989 - INFO - ⬆️ [13:27:49] Uploading: 2_pod_1.pdf
2025-09-18 13:27:53,384 - INFO - ✓ Uploaded: input_data_3_per_cat/2_pod/2_pod_1.pdf -> s3://document-extraction-logistically/temp/801e2b40_2_pod_1.pdf
2025-09-18 13:27:53,384 - INFO - 🔍 [13:27:53] Starting classification: 2_pod_1.pdf
2025-09-18 13:27:53,385 - INFO - Initializing TextractProcessor...
2025-09-18 13:27:53,406 - INFO - Initializing BedrockProcessor...
2025-09-18 13:27:53,411 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/801e2b40_2_pod_1.pdf
2025-09-18 13:27:53,412 - INFO - Processing PDF from S3...
2025-09-18 13:27:53,412 - INFO - Downloading PDF from S3 to /tmp/tmp48gjkeh3/801e2b40_2_pod_1.pdf
2025-09-18 13:28:02,910 - INFO - Splitting PDF into individual pages...
2025-09-18 13:28:02,912 - INFO - Splitting PDF 801e2b40_2_pod_1 into 3 pages
2025-09-18 13:28:02,921 - INFO - Split PDF into 3 pages
2025-09-18 13:28:02,921 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:28:02,921 - INFO - Expected pages: [1, 2, 3]
2025-09-18 13:28:08,848 - INFO - Page 3: Extracted 441 characters, 20 lines from 801e2b40_2_pod_1_db2636e4_page_003.pdf
2025-09-18 13:28:08,848 - INFO - Successfully processed page 3
2025-09-18 13:28:08,945 - INFO - Page 2: Extracted 1895 characters, 65 lines from 801e2b40_2_pod_1_db2636e4_page_002.pdf
2025-09-18 13:28:08,945 - INFO - Successfully processed page 2
2025-09-18 13:28:09,059 - INFO - Page 1: Extracted 1597 characters, 78 lines from 801e2b40_2_pod_1_db2636e4_page_001.pdf
2025-09-18 13:28:09,060 - INFO - Successfully processed page 1
2025-09-18 13:28:09,060 - INFO - Combined 3 pages into final text
2025-09-18 13:28:09,060 - INFO - Text validation for 801e2b40_2_pod_1: 3988 characters, 3 pages
2025-09-18 13:28:09,061 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:28:09,061 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:28:14,483 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-18 13:28:14,484 - ERROR - Processing failed for s3://document-extraction-logistically/temp/801e2b40_2_pod_1.pdf: Unexpected tool response format from Bedrock
2025-09-18 13:28:14,485 - ERROR - ✗ Failed to process input_data_3_per_cat/2_pod/2_pod_1.pdf: Unexpected tool response format from Bedrock
2025-09-18 13:28:15,613 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/801e2b40_2_pod_1.pdf
2025-09-18 13:28:15,613 - INFO - 
📊 Processing Summary:
2025-09-18 13:28:15,613 - INFO -    Total files: 1
2025-09-18 13:28:15,613 - INFO -    Successful: 0
2025-09-18 13:28:15,613 - INFO -    Failed: 1
2025-09-18 13:28:15,613 - INFO -    Duration: 25.62 seconds
2025-09-18 13:28:15,613 - INFO -    Output directory: Augment_test/multipage_test
2025-09-18 13:28:15,613 - ERROR - 
❌ Errors:
2025-09-18 13:28:15,613 - ERROR -    Failed to process input_data_3_per_cat/2_pod/2_pod_1.pdf: Unexpected tool response format from Bedrock
2025-09-18 13:28:15,614 - INFO - 
📋 No files were successfully processed.
2025-09-18 13:28:15,614 - INFO - 
📈 Statistics saved to: Augment_test/multipage_test/run400_stats.json
2025-09-18 13:28:15,614 - INFO - 
🎉 Processing completed!
2025-09-18 13:28:15,614 - INFO - 📊 Summary: 0/1 files processed successfully
2025-09-18 13:28:15,614 - INFO - ❌ 1 files failed
2025-09-18 13:28:15,614 - INFO - ⏱️  Total duration: 25.62 seconds
2025-09-18 13:28:15,614 - INFO - 📁 Output directory: Augment_test/multipage_test
2025-09-18 13:28:15,614 - INFO - 📈 Statistics file: Augment_test/multipage_test/run400_stats.json
