2025-09-18 13:30:09,012 - INFO - Logging initialized. Log file: logs/test_classification_20250918_133009.log
2025-09-18 13:30:09,012 - INFO - 📁 Found 12 files to process
2025-09-18 13:30:09,012 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 13:30:09,012 - INFO - 🚀 Processing 12 files in FORCED PARALLEL MODE...
2025-09-18 13:30:09,012 - INFO - 🚀 Creating 12 parallel tasks...
2025-09-18 13:30:09,012 - INFO - 🚀 All 12 tasks created - executing in parallel...
2025-09-18 13:30:09,012 - INFO - ⬆️ [13:30:09] Uploading: 26_outgate_1.pdf
2025-09-18 13:30:12,439 - INFO - ✓ Uploaded: input_data_individual/26_outgate/26_outgate_1.pdf -> s3://document-extraction-logistically/temp/b6ef20a8_26_outgate_1.pdf
2025-09-18 13:30:12,440 - INFO - 🔍 [13:30:12] Starting classification: 26_outgate_1.pdf
2025-09-18 13:30:12,441 - INFO - ⬆️ [13:30:12] Uploading: 26_outgate_10.pdf
2025-09-18 13:30:12,443 - INFO - Initializing TextractProcessor...
2025-09-18 13:30:12,463 - INFO - Initializing BedrockProcessor...
2025-09-18 13:30:12,468 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b6ef20a8_26_outgate_1.pdf
2025-09-18 13:30:12,469 - INFO - Processing PDF from S3...
2025-09-18 13:30:12,469 - INFO - Downloading PDF from S3 to /tmp/tmpc5e7mv12/b6ef20a8_26_outgate_1.pdf
2025-09-18 13:30:13,039 - INFO - ✓ Uploaded: input_data_individual/26_outgate/26_outgate_10.pdf -> s3://document-extraction-logistically/temp/8e86dc1c_26_outgate_10.pdf
2025-09-18 13:30:13,039 - INFO - 🔍 [13:30:13] Starting classification: 26_outgate_10.pdf
2025-09-18 13:30:13,040 - INFO - ⬆️ [13:30:13] Uploading: 26_outgate_11.pdf
2025-09-18 13:30:13,042 - INFO - Initializing TextractProcessor...
2025-09-18 13:30:13,060 - INFO - Initializing BedrockProcessor...
2025-09-18 13:30:13,065 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8e86dc1c_26_outgate_10.pdf
2025-09-18 13:30:13,065 - INFO - Processing PDF from S3...
2025-09-18 13:30:13,065 - INFO - Downloading PDF from S3 to /tmp/tmplgk1ei5_/8e86dc1c_26_outgate_10.pdf
2025-09-18 13:30:13,636 - INFO - ✓ Uploaded: input_data_individual/26_outgate/26_outgate_11.pdf -> s3://document-extraction-logistically/temp/0e4da1c9_26_outgate_11.pdf
2025-09-18 13:30:13,637 - INFO - 🔍 [13:30:13] Starting classification: 26_outgate_11.pdf
2025-09-18 13:30:13,637 - INFO - ⬆️ [13:30:13] Uploading: 26_outgate_12.png
2025-09-18 13:30:13,638 - INFO - Initializing TextractProcessor...
2025-09-18 13:30:13,658 - INFO - Initializing BedrockProcessor...
2025-09-18 13:30:13,661 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0e4da1c9_26_outgate_11.pdf
2025-09-18 13:30:13,661 - INFO - Processing PDF from S3...
2025-09-18 13:30:13,661 - INFO - Downloading PDF from S3 to /tmp/tmpn92tijrl/0e4da1c9_26_outgate_11.pdf
2025-09-18 13:30:14,220 - INFO - ✓ Uploaded: input_data_individual/26_outgate/26_outgate_12.png -> s3://document-extraction-logistically/temp/fcf74ff8_26_outgate_12.png
2025-09-18 13:30:14,220 - INFO - 🔍 [13:30:14] Starting classification: 26_outgate_12.png
2025-09-18 13:30:14,221 - INFO - ⬆️ [13:30:14] Uploading: 26_outgate_2.pdf
2025-09-18 13:30:14,222 - INFO - Initializing TextractProcessor...
2025-09-18 13:30:14,241 - INFO - Initializing BedrockProcessor...
2025-09-18 13:30:14,245 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fcf74ff8_26_outgate_12.png
2025-09-18 13:30:14,245 - INFO - Processing image from S3...
2025-09-18 13:30:14,816 - INFO - ✓ Uploaded: input_data_individual/26_outgate/26_outgate_2.pdf -> s3://document-extraction-logistically/temp/543df58e_26_outgate_2.pdf
2025-09-18 13:30:14,817 - INFO - 🔍 [13:30:14] Starting classification: 26_outgate_2.pdf
2025-09-18 13:30:14,817 - INFO - ⬆️ [13:30:14] Uploading: 26_outgate_3.pdf
2025-09-18 13:30:14,818 - INFO - Initializing TextractProcessor...
2025-09-18 13:30:14,832 - INFO - Initializing BedrockProcessor...
2025-09-18 13:30:14,837 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/543df58e_26_outgate_2.pdf
2025-09-18 13:30:14,837 - INFO - Processing PDF from S3...
2025-09-18 13:30:14,838 - INFO - Downloading PDF from S3 to /tmp/tmpky5fxv5i/543df58e_26_outgate_2.pdf
2025-09-18 13:30:14,855 - INFO - Splitting PDF into individual pages...
2025-09-18 13:30:14,857 - INFO - Splitting PDF 8e86dc1c_26_outgate_10 into 1 pages
2025-09-18 13:30:14,858 - INFO - Split PDF into 1 pages
2025-09-18 13:30:14,858 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:30:14,858 - INFO - Expected pages: [1]
2025-09-18 13:30:15,324 - INFO - Splitting PDF into individual pages...
2025-09-18 13:30:15,326 - INFO - Splitting PDF b6ef20a8_26_outgate_1 into 2 pages
2025-09-18 13:30:15,395 - INFO - ✓ Uploaded: input_data_individual/26_outgate/26_outgate_3.pdf -> s3://document-extraction-logistically/temp/c44ddfa4_26_outgate_3.pdf
2025-09-18 13:30:15,395 - INFO - 🔍 [13:30:15] Starting classification: 26_outgate_3.pdf
2025-09-18 13:30:15,396 - INFO - ⬆️ [13:30:15] Uploading: 26_outgate_4.jpg
2025-09-18 13:30:15,396 - INFO - Initializing TextractProcessor...
2025-09-18 13:30:15,406 - INFO - Initializing BedrockProcessor...
2025-09-18 13:30:15,410 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c44ddfa4_26_outgate_3.pdf
2025-09-18 13:30:15,410 - INFO - Processing PDF from S3...
2025-09-18 13:30:15,410 - INFO - Downloading PDF from S3 to /tmp/tmpwzpmtftx/c44ddfa4_26_outgate_3.pdf
2025-09-18 13:30:15,448 - INFO - Split PDF into 2 pages
2025-09-18 13:30:15,448 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:30:15,448 - INFO - Expected pages: [1, 2]
2025-09-18 13:30:15,876 - INFO - Splitting PDF into individual pages...
2025-09-18 13:30:15,876 - INFO - Splitting PDF 0e4da1c9_26_outgate_11 into 1 pages
2025-09-18 13:30:15,877 - INFO - Split PDF into 1 pages
2025-09-18 13:30:15,877 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:30:15,877 - INFO - Expected pages: [1]
2025-09-18 13:30:15,975 - INFO - ✓ Uploaded: input_data_individual/26_outgate/26_outgate_4.jpg -> s3://document-extraction-logistically/temp/00cd0334_26_outgate_4.jpg
2025-09-18 13:30:15,975 - INFO - 🔍 [13:30:15] Starting classification: 26_outgate_4.jpg
2025-09-18 13:30:15,975 - INFO - ⬆️ [13:30:15] Uploading: 26_outgate_5.pdf
2025-09-18 13:30:15,977 - INFO - Initializing TextractProcessor...
2025-09-18 13:30:15,988 - INFO - Initializing BedrockProcessor...
2025-09-18 13:30:15,991 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/00cd0334_26_outgate_4.jpg
2025-09-18 13:30:15,991 - INFO - Processing image from S3...
2025-09-18 13:30:16,318 - INFO - S3 Image temp/fcf74ff8_26_outgate_12.png: Extracted 168 characters, 17 lines
2025-09-18 13:30:16,319 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:30:16,319 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:30:16,563 - INFO - ✓ Uploaded: input_data_individual/26_outgate/26_outgate_5.pdf -> s3://document-extraction-logistically/temp/2abc0ee0_26_outgate_5.pdf
2025-09-18 13:30:16,563 - INFO - 🔍 [13:30:16] Starting classification: 26_outgate_5.pdf
2025-09-18 13:30:16,564 - INFO - ⬆️ [13:30:16] Uploading: 26_outgate_6.pdf
2025-09-18 13:30:16,565 - INFO - Initializing TextractProcessor...
2025-09-18 13:30:16,582 - INFO - Initializing BedrockProcessor...
2025-09-18 13:30:16,585 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2abc0ee0_26_outgate_5.pdf
2025-09-18 13:30:16,586 - INFO - Processing PDF from S3...
2025-09-18 13:30:16,586 - INFO - Downloading PDF from S3 to /tmp/tmp8dgwu60x/2abc0ee0_26_outgate_5.pdf
2025-09-18 13:30:16,672 - INFO - Splitting PDF into individual pages...
2025-09-18 13:30:16,675 - INFO - Splitting PDF 543df58e_26_outgate_2 into 2 pages
2025-09-18 13:30:16,686 - INFO - Split PDF into 2 pages
2025-09-18 13:30:16,686 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:30:16,686 - INFO - Expected pages: [1, 2]
2025-09-18 13:30:16,957 - INFO - Splitting PDF into individual pages...
2025-09-18 13:30:16,959 - INFO - Splitting PDF c44ddfa4_26_outgate_3 into 4 pages
2025-09-18 13:30:16,964 - INFO - Split PDF into 4 pages
2025-09-18 13:30:16,964 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:30:16,964 - INFO - Expected pages: [1, 2, 3, 4]
2025-09-18 13:30:17,158 - INFO - ✓ Uploaded: input_data_individual/26_outgate/26_outgate_6.pdf -> s3://document-extraction-logistically/temp/f94a4782_26_outgate_6.pdf
2025-09-18 13:30:17,158 - INFO - 🔍 [13:30:17] Starting classification: 26_outgate_6.pdf
2025-09-18 13:30:17,159 - INFO - ⬆️ [13:30:17] Uploading: 26_outgate_7.pdf
2025-09-18 13:30:17,160 - INFO - Initializing TextractProcessor...
2025-09-18 13:30:17,169 - INFO - Initializing BedrockProcessor...
2025-09-18 13:30:17,172 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f94a4782_26_outgate_6.pdf
2025-09-18 13:30:17,172 - INFO - Processing PDF from S3...
2025-09-18 13:30:17,172 - INFO - Downloading PDF from S3 to /tmp/tmpka_tyxzz/f94a4782_26_outgate_6.pdf
2025-09-18 13:30:17,739 - INFO - ✓ Uploaded: input_data_individual/26_outgate/26_outgate_7.pdf -> s3://document-extraction-logistically/temp/ab4017f5_26_outgate_7.pdf
2025-09-18 13:30:17,739 - INFO - 🔍 [13:30:17] Starting classification: 26_outgate_7.pdf
2025-09-18 13:30:17,740 - INFO - ⬆️ [13:30:17] Uploading: 26_outgate_8.pdf
2025-09-18 13:30:17,742 - INFO - Initializing TextractProcessor...
2025-09-18 13:30:17,751 - INFO - Initializing BedrockProcessor...
2025-09-18 13:30:17,753 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ab4017f5_26_outgate_7.pdf
2025-09-18 13:30:17,753 - INFO - Processing PDF from S3...
2025-09-18 13:30:17,753 - INFO - Downloading PDF from S3 to /tmp/tmpb0l_7dnl/ab4017f5_26_outgate_7.pdf
2025-09-18 13:30:17,876 - INFO - Splitting PDF into individual pages...
2025-09-18 13:30:17,877 - INFO - Splitting PDF 2abc0ee0_26_outgate_5 into 1 pages
2025-09-18 13:30:17,880 - INFO - Split PDF into 1 pages
2025-09-18 13:30:17,880 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:30:17,880 - INFO - Expected pages: [1]
2025-09-18 13:30:18,240 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fcf74ff8_26_outgate_12.png
2025-09-18 13:30:18,381 - INFO - ✓ Uploaded: input_data_individual/26_outgate/26_outgate_8.pdf -> s3://document-extraction-logistically/temp/1cd02497_26_outgate_8.pdf
2025-09-18 13:30:18,381 - INFO - 🔍 [13:30:18] Starting classification: 26_outgate_8.pdf
2025-09-18 13:30:18,382 - INFO - ⬆️ [13:30:18] Uploading: 26_outgate_9.pdf
2025-09-18 13:30:18,383 - INFO - Initializing TextractProcessor...
2025-09-18 13:30:18,399 - INFO - Initializing BedrockProcessor...
2025-09-18 13:30:18,404 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1cd02497_26_outgate_8.pdf
2025-09-18 13:30:18,404 - INFO - Processing PDF from S3...
2025-09-18 13:30:18,405 - INFO - Downloading PDF from S3 to /tmp/tmpjnhsrn9z/1cd02497_26_outgate_8.pdf
2025-09-18 13:30:18,978 - INFO - ✓ Uploaded: input_data_individual/26_outgate/26_outgate_9.pdf -> s3://document-extraction-logistically/temp/9621d234_26_outgate_9.pdf
2025-09-18 13:30:18,979 - INFO - 🔍 [13:30:18] Starting classification: 26_outgate_9.pdf
2025-09-18 13:30:18,980 - INFO - Initializing TextractProcessor...
2025-09-18 13:30:19,006 - INFO - Initializing BedrockProcessor...
2025-09-18 13:30:19,013 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9621d234_26_outgate_9.pdf
2025-09-18 13:30:19,016 - INFO - Processing PDF from S3...
2025-09-18 13:30:19,017 - INFO - Downloading PDF from S3 to /tmp/tmpg7or92a6/9621d234_26_outgate_9.pdf
2025-09-18 13:30:19,026 - INFO - Splitting PDF into individual pages...
2025-09-18 13:30:19,026 - INFO - 

26_outgate_12.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 13:30:19,027 - INFO - 

✓ Saved result: output/run1_26_outgate_12.json
2025-09-18 13:30:19,035 - INFO - Splitting PDF ab4017f5_26_outgate_7 into 1 pages
2025-09-18 13:30:19,036 - INFO - Split PDF into 1 pages
2025-09-18 13:30:19,036 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:30:19,036 - INFO - Expected pages: [1]
2025-09-18 13:30:19,287 - INFO - Splitting PDF into individual pages...
2025-09-18 13:30:19,288 - INFO - Splitting PDF f94a4782_26_outgate_6 into 1 pages
2025-09-18 13:30:19,289 - INFO - Split PDF into 1 pages
2025-09-18 13:30:19,289 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:30:19,289 - INFO - Expected pages: [1]
2025-09-18 13:30:19,324 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fcf74ff8_26_outgate_12.png
2025-09-18 13:30:19,328 - INFO - S3 Image temp/00cd0334_26_outgate_4.jpg: Extracted 415 characters, 29 lines
2025-09-18 13:30:19,328 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:30:19,329 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:30:19,426 - INFO - Page 4: Extracted 56 characters, 3 lines from c44ddfa4_26_outgate_3_9045200a_page_004.pdf
2025-09-18 13:30:19,427 - INFO - Successfully processed page 4
2025-09-18 13:30:20,515 - INFO - Splitting PDF into individual pages...
2025-09-18 13:30:20,517 - INFO - Splitting PDF 1cd02497_26_outgate_8 into 1 pages
2025-09-18 13:30:20,571 - INFO - Split PDF into 1 pages
2025-09-18 13:30:20,571 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:30:20,571 - INFO - Expected pages: [1]
2025-09-18 13:30:20,780 - INFO - Splitting PDF into individual pages...
2025-09-18 13:30:20,781 - INFO - Splitting PDF 9621d234_26_outgate_9 into 1 pages
2025-09-18 13:30:20,848 - INFO - Split PDF into 1 pages
2025-09-18 13:30:20,848 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:30:20,848 - INFO - Expected pages: [1]
2025-09-18 13:30:20,950 - INFO - Page 1: Extracted 838 characters, 47 lines from 0e4da1c9_26_outgate_11_eb7fbf48_page_001.pdf
2025-09-18 13:30:20,951 - INFO - Successfully processed page 1
2025-09-18 13:30:20,951 - INFO - Combined 1 pages into final text
2025-09-18 13:30:20,951 - INFO - Text validation for 0e4da1c9_26_outgate_11: 855 characters, 1 pages
2025-09-18 13:30:20,951 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:30:20,952 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:30:20,981 - INFO - Page 1: Extracted 575 characters, 31 lines from c44ddfa4_26_outgate_3_9045200a_page_001.pdf
2025-09-18 13:30:20,981 - INFO - Successfully processed page 1
2025-09-18 13:30:21,050 - INFO - Page 1: Extracted 404 characters, 28 lines from 2abc0ee0_26_outgate_5_9612673c_page_001.pdf
2025-09-18 13:30:21,050 - INFO - Successfully processed page 1
2025-09-18 13:30:21,051 - INFO - Combined 1 pages into final text
2025-09-18 13:30:21,051 - INFO - Text validation for 2abc0ee0_26_outgate_5: 421 characters, 1 pages
2025-09-18 13:30:21,051 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:30:21,051 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:30:21,095 - INFO - Page 2: Extracted 457 characters, 4 lines from 543df58e_26_outgate_2_82298c3d_page_002.pdf
2025-09-18 13:30:21,095 - INFO - Successfully processed page 2
2025-09-18 13:30:21,197 - INFO - Page 3: Extracted 435 characters, 28 lines from c44ddfa4_26_outgate_3_9045200a_page_003.pdf
2025-09-18 13:30:21,197 - INFO - Successfully processed page 3
2025-09-18 13:30:21,363 - INFO - Page 2: Extracted 556 characters, 30 lines from c44ddfa4_26_outgate_3_9045200a_page_002.pdf
2025-09-18 13:30:21,363 - INFO - Successfully processed page 2
2025-09-18 13:30:21,364 - INFO - Combined 4 pages into final text
2025-09-18 13:30:21,364 - INFO - Text validation for c44ddfa4_26_outgate_3: 1696 characters, 4 pages
2025-09-18 13:30:21,365 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:30:21,365 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:30:21,505 - INFO - Page 1: Extracted 558 characters, 30 lines from b6ef20a8_26_outgate_1_a69aa5aa_page_001.pdf
2025-09-18 13:30:21,506 - INFO - Successfully processed page 1
2025-09-18 13:30:21,635 - INFO - Page 2: Extracted 269 characters, 15 lines from b6ef20a8_26_outgate_1_a69aa5aa_page_002.pdf
2025-09-18 13:30:21,635 - INFO - Successfully processed page 2
2025-09-18 13:30:21,635 - INFO - Combined 2 pages into final text
2025-09-18 13:30:21,636 - INFO - Text validation for b6ef20a8_26_outgate_1: 863 characters, 2 pages
2025-09-18 13:30:21,636 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:30:21,636 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:30:21,971 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/00cd0334_26_outgate_4.jpg
2025-09-18 13:30:22,002 - INFO - Page 1: Extracted 669 characters, 29 lines from 543df58e_26_outgate_2_82298c3d_page_001.pdf
2025-09-18 13:30:22,002 - INFO - 

26_outgate_4.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 13:30:22,002 - INFO - Successfully processed page 1
2025-09-18 13:30:22,002 - INFO - 

✓ Saved result: output/run1_26_outgate_4.json
2025-09-18 13:30:22,003 - INFO - Combined 2 pages into final text
2025-09-18 13:30:22,004 - INFO - Text validation for 543df58e_26_outgate_2: 1162 characters, 2 pages
2025-09-18 13:30:22,004 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:30:22,004 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:30:22,290 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/00cd0334_26_outgate_4.jpg
2025-09-18 13:30:22,748 - INFO - Page 1: Extracted 851 characters, 49 lines from 8e86dc1c_26_outgate_10_edd0a29a_page_001.pdf
2025-09-18 13:30:22,748 - INFO - Successfully processed page 1
2025-09-18 13:30:22,749 - INFO - Combined 1 pages into final text
2025-09-18 13:30:22,749 - INFO - Text validation for 8e86dc1c_26_outgate_10: 868 characters, 1 pages
2025-09-18 13:30:22,749 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:30:22,749 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:30:22,759 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2abc0ee0_26_outgate_5.pdf
2025-09-18 13:30:22,772 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0e4da1c9_26_outgate_11.pdf
2025-09-18 13:30:22,773 - INFO - 

26_outgate_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 13:30:22,775 - INFO - 

✓ Saved result: output/run1_26_outgate_5.json
2025-09-18 13:30:23,072 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2abc0ee0_26_outgate_5.pdf
2025-09-18 13:30:23,089 - INFO - 

26_outgate_11.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 13:30:23,089 - INFO - 

✓ Saved result: output/run1_26_outgate_11.json
2025-09-18 13:30:23,394 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0e4da1c9_26_outgate_11.pdf
2025-09-18 13:30:23,540 - INFO - Page 1: Extracted 817 characters, 67 lines from ab4017f5_26_outgate_7_fdd3bc36_page_001.pdf
2025-09-18 13:30:23,540 - INFO - Successfully processed page 1
2025-09-18 13:30:23,540 - INFO - Combined 1 pages into final text
2025-09-18 13:30:23,541 - INFO - Text validation for ab4017f5_26_outgate_7: 834 characters, 1 pages
2025-09-18 13:30:23,541 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:30:23,541 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:30:23,718 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b6ef20a8_26_outgate_1.pdf
2025-09-18 13:30:23,736 - INFO - 

26_outgate_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        },
        {
            "page_no": 2,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 13:30:23,736 - INFO - 

✓ Saved result: output/run1_26_outgate_1.json
2025-09-18 13:30:23,854 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c44ddfa4_26_outgate_3.pdf
2025-09-18 13:30:24,032 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b6ef20a8_26_outgate_1.pdf
2025-09-18 13:30:24,070 - INFO - 

26_outgate_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        },
        {
            "page_no": 2,
            "doc_type": "outgate"
        },
        {
            "page_no": 3,
            "doc_type": "outgate"
        },
        {
            "page_no": 4,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 13:30:24,071 - INFO - 

✓ Saved result: output/run1_26_outgate_3.json
2025-09-18 13:30:24,373 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c44ddfa4_26_outgate_3.pdf
2025-09-18 13:30:24,474 - INFO - Page 1: Extracted 770 characters, 42 lines from f94a4782_26_outgate_6_03258ff3_page_001.pdf
2025-09-18 13:30:24,474 - INFO - Successfully processed page 1
2025-09-18 13:30:24,474 - INFO - Combined 1 pages into final text
2025-09-18 13:30:24,475 - INFO - Text validation for f94a4782_26_outgate_6: 787 characters, 1 pages
2025-09-18 13:30:24,475 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:30:24,475 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:30:24,545 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8e86dc1c_26_outgate_10.pdf
2025-09-18 13:30:24,576 - INFO - 

26_outgate_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 13:30:24,576 - INFO - 

✓ Saved result: output/run1_26_outgate_10.json
2025-09-18 13:30:24,875 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8e86dc1c_26_outgate_10.pdf
2025-09-18 13:30:24,919 - INFO - Page 1: Extracted 761 characters, 39 lines from 9621d234_26_outgate_9_012c96d0_page_001.pdf
2025-09-18 13:30:24,919 - INFO - Successfully processed page 1
2025-09-18 13:30:24,919 - INFO - Combined 1 pages into final text
2025-09-18 13:30:24,920 - INFO - Text validation for 9621d234_26_outgate_9: 778 characters, 1 pages
2025-09-18 13:30:24,920 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:30:24,920 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:30:25,167 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ab4017f5_26_outgate_7.pdf
2025-09-18 13:30:25,199 - INFO - 

26_outgate_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 13:30:25,200 - INFO - 

✓ Saved result: output/run1_26_outgate_7.json
2025-09-18 13:30:25,497 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ab4017f5_26_outgate_7.pdf
2025-09-18 13:30:25,801 - INFO - Page 1: Extracted 583 characters, 33 lines from 1cd02497_26_outgate_8_a9084fc2_page_001.pdf
2025-09-18 13:30:25,802 - INFO - Successfully processed page 1
2025-09-18 13:30:25,802 - INFO - Combined 1 pages into final text
2025-09-18 13:30:25,802 - INFO - Text validation for 1cd02497_26_outgate_8: 600 characters, 1 pages
2025-09-18 13:30:25,803 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:30:25,803 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:30:26,171 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f94a4782_26_outgate_6.pdf
2025-09-18 13:30:26,186 - INFO - 

26_outgate_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 13:30:26,186 - INFO - 

✓ Saved result: output/run1_26_outgate_6.json
2025-09-18 13:30:26,231 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/543df58e_26_outgate_2.pdf
2025-09-18 13:30:26,475 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f94a4782_26_outgate_6.pdf
2025-09-18 13:30:26,490 - INFO - 

26_outgate_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "ingate"
        },
        {
            "page_no": 2,
            "doc_type": "other"
        }
    ]
}
2025-09-18 13:30:26,491 - INFO - 

✓ Saved result: output/run1_26_outgate_2.json
2025-09-18 13:30:26,787 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/543df58e_26_outgate_2.pdf
2025-09-18 13:30:26,951 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9621d234_26_outgate_9.pdf
2025-09-18 13:30:26,969 - INFO - 

26_outgate_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 13:30:26,969 - INFO - 

✓ Saved result: output/run1_26_outgate_9.json
2025-09-18 13:30:27,266 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9621d234_26_outgate_9.pdf
2025-09-18 13:30:27,803 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1cd02497_26_outgate_8.pdf
2025-09-18 13:30:27,821 - INFO - 

26_outgate_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 13:30:27,821 - INFO - 

✓ Saved result: output/run1_26_outgate_8.json
2025-09-18 13:30:28,117 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1cd02497_26_outgate_8.pdf
2025-09-18 13:30:28,117 - INFO - 
📊 Processing Summary:
2025-09-18 13:30:28,117 - INFO -    Total files: 12
2025-09-18 13:30:28,117 - INFO -    Successful: 12
2025-09-18 13:30:28,117 - INFO -    Failed: 0
2025-09-18 13:30:28,117 - INFO -    Duration: 19.10 seconds
2025-09-18 13:30:28,117 - INFO -    Output directory: output
2025-09-18 13:30:28,117 - INFO - 
📋 Successfully Processed Files:
2025-09-18 13:30:28,117 - INFO -    📄 26_outgate_1.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"},{"page_no":2,"doc_type":"outgate"}]}
2025-09-18 13:30:28,118 - INFO -    📄 26_outgate_10.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-18 13:30:28,118 - INFO -    📄 26_outgate_11.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-18 13:30:28,118 - INFO -    📄 26_outgate_12.png: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-18 13:30:28,118 - INFO -    📄 26_outgate_2.pdf: {"documents":[{"page_no":1,"doc_type":"ingate"},{"page_no":2,"doc_type":"other"}]}
2025-09-18 13:30:28,118 - INFO -    📄 26_outgate_3.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"},{"page_no":2,"doc_type":"outgate"},{"page_no":3,"doc_type":"outgate"},{"page_no":4,"doc_type":"outgate"}]}
2025-09-18 13:30:28,118 - INFO -    📄 26_outgate_4.jpg: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-18 13:30:28,118 - INFO -    📄 26_outgate_5.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-18 13:30:28,118 - INFO -    📄 26_outgate_6.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-18 13:30:28,118 - INFO -    📄 26_outgate_7.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-18 13:30:28,118 - INFO -    📄 26_outgate_8.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-18 13:30:28,118 - INFO -    📄 26_outgate_9.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"}]}
2025-09-18 13:30:28,118 - INFO - 
============================================================================================================================================
2025-09-18 13:30:28,118 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 13:30:28,118 - INFO - ============================================================================================================================================
2025-09-18 13:30:28,118 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 13:30:28,119 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:30:28,119 - INFO - 26_outgate_1.pdf                                   1      outgate              run1_26_outgate_1.json                            
2025-09-18 13:30:28,119 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_1.pdf
2025-09-18 13:30:28,119 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_1.json
2025-09-18 13:30:28,119 - INFO - 
2025-09-18 13:30:28,119 - INFO - 26_outgate_1.pdf                                   2      outgate              run1_26_outgate_1.json                            
2025-09-18 13:30:28,119 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_1.pdf
2025-09-18 13:30:28,119 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_1.json
2025-09-18 13:30:28,119 - INFO - 
2025-09-18 13:30:28,119 - INFO - 26_outgate_10.pdf                                  1      outgate              run1_26_outgate_10.json                           
2025-09-18 13:30:28,119 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_10.pdf
2025-09-18 13:30:28,119 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_10.json
2025-09-18 13:30:28,119 - INFO - 
2025-09-18 13:30:28,119 - INFO - 26_outgate_11.pdf                                  1      outgate              run1_26_outgate_11.json                           
2025-09-18 13:30:28,119 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_11.pdf
2025-09-18 13:30:28,119 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_11.json
2025-09-18 13:30:28,119 - INFO - 
2025-09-18 13:30:28,119 - INFO - 26_outgate_12.png                                  1      outgate              run1_26_outgate_12.json                           
2025-09-18 13:30:28,119 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_12.png
2025-09-18 13:30:28,119 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_12.json
2025-09-18 13:30:28,119 - INFO - 
2025-09-18 13:30:28,119 - INFO - 26_outgate_2.pdf                                   1      ingate               run1_26_outgate_2.json                            
2025-09-18 13:30:28,119 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_2.pdf
2025-09-18 13:30:28,119 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_2.json
2025-09-18 13:30:28,120 - INFO - 
2025-09-18 13:30:28,120 - INFO - 26_outgate_2.pdf                                   2      other                run1_26_outgate_2.json                            
2025-09-18 13:30:28,120 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_2.pdf
2025-09-18 13:30:28,120 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_2.json
2025-09-18 13:30:28,120 - INFO - 
2025-09-18 13:30:28,120 - INFO - 26_outgate_3.pdf                                   1      outgate              run1_26_outgate_3.json                            
2025-09-18 13:30:28,120 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_3.pdf
2025-09-18 13:30:28,120 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_3.json
2025-09-18 13:30:28,120 - INFO - 
2025-09-18 13:30:28,120 - INFO - 26_outgate_3.pdf                                   2      outgate              run1_26_outgate_3.json                            
2025-09-18 13:30:28,120 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_3.pdf
2025-09-18 13:30:28,120 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_3.json
2025-09-18 13:30:28,120 - INFO - 
2025-09-18 13:30:28,120 - INFO - 26_outgate_3.pdf                                   3      outgate              run1_26_outgate_3.json                            
2025-09-18 13:30:28,120 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_3.pdf
2025-09-18 13:30:28,120 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_3.json
2025-09-18 13:30:28,120 - INFO - 
2025-09-18 13:30:28,120 - INFO - 26_outgate_3.pdf                                   4      outgate              run1_26_outgate_3.json                            
2025-09-18 13:30:28,120 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_3.pdf
2025-09-18 13:30:28,120 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_3.json
2025-09-18 13:30:28,120 - INFO - 
2025-09-18 13:30:28,120 - INFO - 26_outgate_4.jpg                                   1      outgate              run1_26_outgate_4.json                            
2025-09-18 13:30:28,120 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_4.jpg
2025-09-18 13:30:28,120 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_4.json
2025-09-18 13:30:28,120 - INFO - 
2025-09-18 13:30:28,120 - INFO - 26_outgate_5.pdf                                   1      outgate              run1_26_outgate_5.json                            
2025-09-18 13:30:28,120 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_5.pdf
2025-09-18 13:30:28,120 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_5.json
2025-09-18 13:30:28,120 - INFO - 
2025-09-18 13:30:28,120 - INFO - 26_outgate_6.pdf                                   1      outgate              run1_26_outgate_6.json                            
2025-09-18 13:30:28,120 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_6.pdf
2025-09-18 13:30:28,120 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_6.json
2025-09-18 13:30:28,120 - INFO - 
2025-09-18 13:30:28,121 - INFO - 26_outgate_7.pdf                                   1      outgate              run1_26_outgate_7.json                            
2025-09-18 13:30:28,121 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_7.pdf
2025-09-18 13:30:28,121 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_7.json
2025-09-18 13:30:28,121 - INFO - 
2025-09-18 13:30:28,121 - INFO - 26_outgate_8.pdf                                   1      outgate              run1_26_outgate_8.json                            
2025-09-18 13:30:28,121 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_8.pdf
2025-09-18 13:30:28,121 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_8.json
2025-09-18 13:30:28,121 - INFO - 
2025-09-18 13:30:28,121 - INFO - 26_outgate_9.pdf                                   1      outgate              run1_26_outgate_9.json                            
2025-09-18 13:30:28,121 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/26_outgate/26_outgate_9.pdf
2025-09-18 13:30:28,121 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_9.json
2025-09-18 13:30:28,121 - INFO - 
2025-09-18 13:30:28,121 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:30:28,121 - INFO - Total entries: 17
2025-09-18 13:30:28,121 - INFO - ============================================================================================================================================
2025-09-18 13:30:28,121 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 13:30:28,121 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 13:30:28,121 - INFO -   1. 26_outgate_1.pdf                    Page 1   → outgate         | run1_26_outgate_1.json
2025-09-18 13:30:28,121 - INFO -   2. 26_outgate_1.pdf                    Page 2   → outgate         | run1_26_outgate_1.json
2025-09-18 13:30:28,121 - INFO -   3. 26_outgate_10.pdf                   Page 1   → outgate         | run1_26_outgate_10.json
2025-09-18 13:30:28,121 - INFO -   4. 26_outgate_11.pdf                   Page 1   → outgate         | run1_26_outgate_11.json
2025-09-18 13:30:28,121 - INFO -   5. 26_outgate_12.png                   Page 1   → outgate         | run1_26_outgate_12.json
2025-09-18 13:30:28,121 - INFO -   6. 26_outgate_2.pdf                    Page 1   → ingate          | run1_26_outgate_2.json
2025-09-18 13:30:28,121 - INFO -   7. 26_outgate_2.pdf                    Page 2   → other           | run1_26_outgate_2.json
2025-09-18 13:30:28,121 - INFO -   8. 26_outgate_3.pdf                    Page 1   → outgate         | run1_26_outgate_3.json
2025-09-18 13:30:28,121 - INFO -   9. 26_outgate_3.pdf                    Page 2   → outgate         | run1_26_outgate_3.json
2025-09-18 13:30:28,121 - INFO -  10. 26_outgate_3.pdf                    Page 3   → outgate         | run1_26_outgate_3.json
2025-09-18 13:30:28,121 - INFO -  11. 26_outgate_3.pdf                    Page 4   → outgate         | run1_26_outgate_3.json
2025-09-18 13:30:28,121 - INFO -  12. 26_outgate_4.jpg                    Page 1   → outgate         | run1_26_outgate_4.json
2025-09-18 13:30:28,122 - INFO -  13. 26_outgate_5.pdf                    Page 1   → outgate         | run1_26_outgate_5.json
2025-09-18 13:30:28,122 - INFO -  14. 26_outgate_6.pdf                    Page 1   → outgate         | run1_26_outgate_6.json
2025-09-18 13:30:28,122 - INFO -  15. 26_outgate_7.pdf                    Page 1   → outgate         | run1_26_outgate_7.json
2025-09-18 13:30:28,122 - INFO -  16. 26_outgate_8.pdf                    Page 1   → outgate         | run1_26_outgate_8.json
2025-09-18 13:30:28,122 - INFO -  17. 26_outgate_9.pdf                    Page 1   → outgate         | run1_26_outgate_9.json
2025-09-18 13:30:28,122 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 13:30:28,123 - INFO - 
✅ Test completed: {'total_files': 12, 'processed': 12, 'failed': 0, 'errors': [], 'duration_seconds': 19.104911, 'processed_files': [{'filename': '26_outgate_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}, {'page_no': 2, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_individual/26_outgate/26_outgate_1.pdf'}, {'filename': '26_outgate_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_individual/26_outgate/26_outgate_10.pdf'}, {'filename': '26_outgate_11.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_individual/26_outgate/26_outgate_11.pdf'}, {'filename': '26_outgate_12.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_individual/26_outgate/26_outgate_12.png'}, {'filename': '26_outgate_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'ingate'}, {'page_no': 2, 'doc_type': 'other'}]}, 'file_path': 'input_data_individual/26_outgate/26_outgate_2.pdf'}, {'filename': '26_outgate_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}, {'page_no': 2, 'doc_type': 'outgate'}, {'page_no': 3, 'doc_type': 'outgate'}, {'page_no': 4, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_individual/26_outgate/26_outgate_3.pdf'}, {'filename': '26_outgate_4.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_individual/26_outgate/26_outgate_4.jpg'}, {'filename': '26_outgate_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_individual/26_outgate/26_outgate_5.pdf'}, {'filename': '26_outgate_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_individual/26_outgate/26_outgate_6.pdf'}, {'filename': '26_outgate_7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_individual/26_outgate/26_outgate_7.pdf'}, {'filename': '26_outgate_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_individual/26_outgate/26_outgate_8.pdf'}, {'filename': '26_outgate_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_individual/26_outgate/26_outgate_9.pdf'}]}
