2025-09-18 13:33:16,474 - INFO - Logging initialized. Log file: logs/test_classification_20250918_133316.log
2025-09-18 13:33:16,475 - INFO - 📁 Found 1 files to process
2025-09-18 13:33:16,475 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 13:33:16,475 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-18 13:33:16,475 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-18 13:33:16,475 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-18 13:33:16,475 - INFO - ⬆️ [13:33:16] Uploading: 26_outgate_2.pdf
2025-09-18 13:33:18,917 - INFO - ✓ Uploaded: input_data_random/26_outgate_2.pdf -> s3://document-extraction-logistically/temp/55d2b534_26_outgate_2.pdf
2025-09-18 13:33:18,917 - INFO - 🔍 [13:33:18] Starting classification: 26_outgate_2.pdf
2025-09-18 13:33:18,918 - INFO - Initializing TextractProcessor...
2025-09-18 13:33:18,935 - INFO - Initializing BedrockProcessor...
2025-09-18 13:33:18,942 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/55d2b534_26_outgate_2.pdf
2025-09-18 13:33:18,942 - INFO - Processing PDF from S3...
2025-09-18 13:33:18,943 - INFO - Downloading PDF from S3 to /tmp/tmpyybkcvf7/55d2b534_26_outgate_2.pdf
2025-09-18 13:33:21,836 - INFO - Splitting PDF into individual pages...
2025-09-18 13:33:21,838 - INFO - Splitting PDF 55d2b534_26_outgate_2 into 2 pages
2025-09-18 13:33:21,848 - INFO - Split PDF into 2 pages
2025-09-18 13:33:21,848 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:33:21,848 - INFO - Expected pages: [1, 2]
2025-09-18 13:33:25,891 - INFO - Page 2: Extracted 457 characters, 4 lines from 55d2b534_26_outgate_2_e79fa419_page_002.pdf
2025-09-18 13:33:25,891 - INFO - Successfully processed page 2
2025-09-18 13:33:26,504 - INFO - Page 1: Extracted 669 characters, 29 lines from 55d2b534_26_outgate_2_e79fa419_page_001.pdf
2025-09-18 13:33:26,504 - INFO - Successfully processed page 1
2025-09-18 13:33:26,504 - INFO - Combined 2 pages into final text
2025-09-18 13:33:26,504 - INFO - Text validation for 55d2b534_26_outgate_2: 1162 characters, 2 pages
2025-09-18 13:33:26,505 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:33:26,505 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:33:31,212 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/55d2b534_26_outgate_2.pdf
2025-09-18 13:33:31,244 - INFO - 

26_outgate_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "outgate"
        },
        {
            "page_no": 2,
            "doc_type": "outgate"
        }
    ]
}
2025-09-18 13:33:31,244 - INFO - 

✓ Saved result: output/run1_26_outgate_2.json
2025-09-18 13:33:32,452 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/55d2b534_26_outgate_2.pdf
2025-09-18 13:33:32,453 - INFO - 
📊 Processing Summary:
2025-09-18 13:33:32,453 - INFO -    Total files: 1
2025-09-18 13:33:32,453 - INFO -    Successful: 1
2025-09-18 13:33:32,453 - INFO -    Failed: 0
2025-09-18 13:33:32,453 - INFO -    Duration: 15.98 seconds
2025-09-18 13:33:32,453 - INFO -    Output directory: output
2025-09-18 13:33:32,453 - INFO - 
📋 Successfully Processed Files:
2025-09-18 13:33:32,453 - INFO -    📄 26_outgate_2.pdf: {"documents":[{"page_no":1,"doc_type":"outgate"},{"page_no":2,"doc_type":"outgate"}]}
2025-09-18 13:33:32,454 - INFO - 
============================================================================================================================================
2025-09-18 13:33:32,454 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 13:33:32,454 - INFO - ============================================================================================================================================
2025-09-18 13:33:32,454 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 13:33:32,454 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:33:32,454 - INFO - 26_outgate_2.pdf                                   1      outgate              run1_26_outgate_2.json                            
2025-09-18 13:33:32,454 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_random/26_outgate_2.pdf
2025-09-18 13:33:32,454 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_2.json
2025-09-18 13:33:32,454 - INFO - 
2025-09-18 13:33:32,454 - INFO - 26_outgate_2.pdf                                   2      outgate              run1_26_outgate_2.json                            
2025-09-18 13:33:32,454 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_random/26_outgate_2.pdf
2025-09-18 13:33:32,455 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_26_outgate_2.json
2025-09-18 13:33:32,455 - INFO - 
2025-09-18 13:33:32,455 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:33:32,455 - INFO - Total entries: 2
2025-09-18 13:33:32,455 - INFO - ============================================================================================================================================
2025-09-18 13:33:32,455 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 13:33:32,455 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 13:33:32,455 - INFO -   1. 26_outgate_2.pdf                    Page 1   → outgate         | run1_26_outgate_2.json
2025-09-18 13:33:32,455 - INFO -   2. 26_outgate_2.pdf                    Page 2   → outgate         | run1_26_outgate_2.json
2025-09-18 13:33:32,455 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 13:33:32,455 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 1, 'failed': 0, 'errors': [], 'duration_seconds': 15.977999, 'processed_files': [{'filename': '26_outgate_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'outgate'}, {'page_no': 2, 'doc_type': 'outgate'}]}, 'file_path': 'input_data_random/26_outgate_2.pdf'}]}
