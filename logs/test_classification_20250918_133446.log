2025-09-18 13:34:46,655 - INFO - Logging initialized. Log file: logs/test_classification_20250918_133446.log
2025-09-18 13:34:46,655 - INFO - 📁 Found 4 files to process
2025-09-18 13:34:46,655 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 13:34:46,655 - INFO - 🚀 Processing 4 files in FORCED PARALLEL MODE...
2025-09-18 13:34:46,655 - INFO - 🚀 Creating 4 parallel tasks...
2025-09-18 13:34:46,655 - INFO - 🚀 All 4 tasks created - executing in parallel...
2025-09-18 13:34:46,655 - INFO - ⬆️ [13:34:46] Uploading: 20_tender_from_cust_1.pdf
2025-09-18 13:34:47,922 - INFO - ✓ Uploaded: input_data_individual/20_tender_from_cust/20_tender_from_cust_1.pdf -> s3://document-extraction-logistically/temp/40960453_20_tender_from_cust_1.pdf
2025-09-18 13:34:47,922 - INFO - 🔍 [13:34:47] Starting classification: 20_tender_from_cust_1.pdf
2025-09-18 13:34:47,923 - INFO - ⬆️ [13:34:47] Uploading: 20_tender_from_cust_2.pdf
2025-09-18 13:34:47,929 - INFO - Initializing TextractProcessor...
2025-09-18 13:34:47,946 - INFO - Initializing BedrockProcessor...
2025-09-18 13:34:47,952 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/40960453_20_tender_from_cust_1.pdf
2025-09-18 13:34:47,952 - INFO - Processing PDF from S3...
2025-09-18 13:34:47,953 - INFO - Downloading PDF from S3 to /tmp/tmpcthfq9dm/40960453_20_tender_from_cust_1.pdf
2025-09-18 13:34:48,535 - INFO - ✓ Uploaded: input_data_individual/20_tender_from_cust/20_tender_from_cust_2.pdf -> s3://document-extraction-logistically/temp/465835ea_20_tender_from_cust_2.pdf
2025-09-18 13:34:48,535 - INFO - 🔍 [13:34:48] Starting classification: 20_tender_from_cust_2.pdf
2025-09-18 13:34:48,536 - INFO - Initializing TextractProcessor...
2025-09-18 13:34:48,536 - INFO - ⬆️ [13:34:48] Uploading: 20_tender_from_cust_3.pdf
2025-09-18 13:34:48,549 - INFO - Initializing BedrockProcessor...
2025-09-18 13:34:48,556 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/465835ea_20_tender_from_cust_2.pdf
2025-09-18 13:34:48,557 - INFO - Processing PDF from S3...
2025-09-18 13:34:48,557 - INFO - Downloading PDF from S3 to /tmp/tmpd2p5cpzo/465835ea_20_tender_from_cust_2.pdf
2025-09-18 13:34:49,357 - INFO - Splitting PDF into individual pages...
2025-09-18 13:34:49,358 - INFO - Splitting PDF 40960453_20_tender_from_cust_1 into 2 pages
2025-09-18 13:34:49,362 - INFO - Split PDF into 2 pages
2025-09-18 13:34:49,363 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:34:49,363 - INFO - Expected pages: [1, 2]
2025-09-18 13:34:49,972 - INFO - Splitting PDF into individual pages...
2025-09-18 13:34:49,974 - INFO - Splitting PDF 465835ea_20_tender_from_cust_2 into 2 pages
2025-09-18 13:34:49,977 - INFO - Split PDF into 2 pages
2025-09-18 13:34:49,978 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:34:49,978 - INFO - Expected pages: [1, 2]
2025-09-18 13:34:50,069 - INFO - ✓ Uploaded: input_data_individual/20_tender_from_cust/20_tender_from_cust_3.pdf -> s3://document-extraction-logistically/temp/9c0684f2_20_tender_from_cust_3.pdf
2025-09-18 13:34:50,069 - INFO - 🔍 [13:34:50] Starting classification: 20_tender_from_cust_3.pdf
2025-09-18 13:34:50,070 - INFO - ⬆️ [13:34:50] Uploading: 20_tender_from_cust_4.pdf
2025-09-18 13:34:50,071 - INFO - Initializing TextractProcessor...
2025-09-18 13:34:50,088 - INFO - Initializing BedrockProcessor...
2025-09-18 13:34:50,093 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9c0684f2_20_tender_from_cust_3.pdf
2025-09-18 13:34:50,094 - INFO - Processing PDF from S3...
2025-09-18 13:34:50,094 - INFO - Downloading PDF from S3 to /tmp/tmpuf0abn4n/9c0684f2_20_tender_from_cust_3.pdf
2025-09-18 13:34:51,096 - INFO - ✓ Uploaded: input_data_individual/20_tender_from_cust/20_tender_from_cust_4.pdf -> s3://document-extraction-logistically/temp/859882c9_20_tender_from_cust_4.pdf
2025-09-18 13:34:51,096 - INFO - 🔍 [13:34:51] Starting classification: 20_tender_from_cust_4.pdf
2025-09-18 13:34:51,097 - INFO - Initializing TextractProcessor...
2025-09-18 13:34:51,106 - INFO - Initializing BedrockProcessor...
2025-09-18 13:34:51,108 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/859882c9_20_tender_from_cust_4.pdf
2025-09-18 13:34:51,108 - INFO - Processing PDF from S3...
2025-09-18 13:34:51,108 - INFO - Downloading PDF from S3 to /tmp/tmpe5b81ixz/859882c9_20_tender_from_cust_4.pdf
2025-09-18 13:34:52,295 - INFO - Splitting PDF into individual pages...
2025-09-18 13:34:52,297 - INFO - Splitting PDF 9c0684f2_20_tender_from_cust_3 into 3 pages
2025-09-18 13:34:52,315 - INFO - Split PDF into 3 pages
2025-09-18 13:34:52,315 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:34:52,315 - INFO - Expected pages: [1, 2, 3]
2025-09-18 13:34:52,527 - INFO - Page 2: Extracted 29 characters, 2 lines from 40960453_20_tender_from_cust_1_27aa49fe_page_002.pdf
2025-09-18 13:34:52,528 - INFO - Successfully processed page 2
2025-09-18 13:34:52,947 - INFO - Splitting PDF into individual pages...
2025-09-18 13:34:52,949 - INFO - Splitting PDF 859882c9_20_tender_from_cust_4 into 2 pages
2025-09-18 13:34:52,957 - INFO - Split PDF into 2 pages
2025-09-18 13:34:52,957 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:34:52,957 - INFO - Expected pages: [1, 2]
2025-09-18 13:34:53,199 - INFO - Page 2: Extracted 29 characters, 2 lines from 465835ea_20_tender_from_cust_2_e9ec6ee1_page_002.pdf
2025-09-18 13:34:53,199 - INFO - Successfully processed page 2
2025-09-18 13:34:53,981 - INFO - Page 1: Extracted 1339 characters, 75 lines from 40960453_20_tender_from_cust_1_27aa49fe_page_001.pdf
2025-09-18 13:34:53,981 - INFO - Successfully processed page 1
2025-09-18 13:34:53,982 - INFO - Combined 2 pages into final text
2025-09-18 13:34:53,982 - INFO - Text validation for 40960453_20_tender_from_cust_1: 1404 characters, 2 pages
2025-09-18 13:34:53,983 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:34:53,983 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:34:54,094 - INFO - Page 1: Extracted 1262 characters, 73 lines from 465835ea_20_tender_from_cust_2_e9ec6ee1_page_001.pdf
2025-09-18 13:34:54,094 - INFO - Successfully processed page 1
2025-09-18 13:34:54,094 - INFO - Combined 2 pages into final text
2025-09-18 13:34:54,094 - INFO - Text validation for 465835ea_20_tender_from_cust_2: 1327 characters, 2 pages
2025-09-18 13:34:54,095 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:34:54,095 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:34:56,050 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/465835ea_20_tender_from_cust_2.pdf
2025-09-18 13:34:56,077 - INFO - 

20_tender_from_cust_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 2,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-18 13:34:56,077 - INFO - 

✓ Saved result: output/run1_20_tender_from_cust_2.json
2025-09-18 13:34:56,275 - INFO - Page 2: Extracted 229 characters, 10 lines from 9c0684f2_20_tender_from_cust_3_24ad0bb0_page_002.pdf
2025-09-18 13:34:56,276 - INFO - Successfully processed page 2
2025-09-18 13:34:56,395 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/465835ea_20_tender_from_cust_2.pdf
2025-09-18 13:34:56,705 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/40960453_20_tender_from_cust_1.pdf
2025-09-18 13:34:56,730 - INFO - 

20_tender_from_cust_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 2,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-18 13:34:56,730 - INFO - 

✓ Saved result: output/run1_20_tender_from_cust_1.json
2025-09-18 13:34:57,029 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/40960453_20_tender_from_cust_1.pdf
2025-09-18 13:34:57,365 - INFO - Page 1: Extracted 2091 characters, 153 lines from 9c0684f2_20_tender_from_cust_3_24ad0bb0_page_001.pdf
2025-09-18 13:34:57,365 - INFO - Successfully processed page 1
2025-09-18 13:34:57,912 - INFO - Page 2: Extracted 1932 characters, 28 lines from 859882c9_20_tender_from_cust_4_1d636ad1_page_002.pdf
2025-09-18 13:34:57,912 - INFO - Successfully processed page 2
2025-09-18 13:34:58,684 - INFO - Page 3: Extracted 6281 characters, 47 lines from 9c0684f2_20_tender_from_cust_3_24ad0bb0_page_003.pdf
2025-09-18 13:34:58,684 - INFO - Successfully processed page 3
2025-09-18 13:34:58,685 - INFO - Combined 3 pages into final text
2025-09-18 13:34:58,685 - INFO - Text validation for 9c0684f2_20_tender_from_cust_3: 8656 characters, 3 pages
2025-09-18 13:34:58,686 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:34:58,686 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:34:59,387 - INFO - Page 1: Extracted 5927 characters, 101 lines from 859882c9_20_tender_from_cust_4_1d636ad1_page_001.pdf
2025-09-18 13:34:59,387 - INFO - Successfully processed page 1
2025-09-18 13:34:59,388 - INFO - Combined 2 pages into final text
2025-09-18 13:34:59,388 - INFO - Text validation for 859882c9_20_tender_from_cust_4: 7895 characters, 2 pages
2025-09-18 13:34:59,388 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:34:59,388 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:35:01,316 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9c0684f2_20_tender_from_cust_3.pdf
2025-09-18 13:35:01,396 - INFO - 

20_tender_from_cust_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 2,
            "doc_type": "tender_from_cust"
        },
        {
            "page_no": 3,
            "doc_type": "tender_from_cust"
        }
    ]
}
2025-09-18 13:35:01,396 - INFO - 

✓ Saved result: output/run1_20_tender_from_cust_3.json
2025-09-18 13:35:01,619 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/859882c9_20_tender_from_cust_4.pdf
2025-09-18 13:35:01,700 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9c0684f2_20_tender_from_cust_3.pdf
2025-09-18 13:35:01,778 - INFO - 

20_tender_from_cust_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "rate_confirmation"
        },
        {
            "page_no": 2,
            "doc_type": "rate_confirmation"
        }
    ]
}
2025-09-18 13:35:01,778 - INFO - 

✓ Saved result: output/run1_20_tender_from_cust_4.json
2025-09-18 13:35:02,079 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/859882c9_20_tender_from_cust_4.pdf
2025-09-18 13:35:02,080 - INFO - 
📊 Processing Summary:
2025-09-18 13:35:02,080 - INFO -    Total files: 4
2025-09-18 13:35:02,081 - INFO -    Successful: 4
2025-09-18 13:35:02,081 - INFO -    Failed: 0
2025-09-18 13:35:02,081 - INFO -    Duration: 15.43 seconds
2025-09-18 13:35:02,081 - INFO -    Output directory: output
2025-09-18 13:35:02,081 - INFO - 
📋 Successfully Processed Files:
2025-09-18 13:35:02,081 - INFO -    📄 20_tender_from_cust_1.pdf: {"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"}]}
2025-09-18 13:35:02,081 - INFO -    📄 20_tender_from_cust_2.pdf: {"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"}]}
2025-09-18 13:35:02,081 - INFO -    📄 20_tender_from_cust_3.pdf: {"documents":[{"page_no":1,"doc_type":"tender_from_cust"},{"page_no":2,"doc_type":"tender_from_cust"},{"page_no":3,"doc_type":"tender_from_cust"}]}
2025-09-18 13:35:02,081 - INFO -    📄 20_tender_from_cust_4.pdf: {"documents":[{"page_no":1,"doc_type":"rate_confirmation"},{"page_no":2,"doc_type":"rate_confirmation"}]}
2025-09-18 13:35:02,081 - INFO - 
============================================================================================================================================
2025-09-18 13:35:02,082 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 13:35:02,082 - INFO - ============================================================================================================================================
2025-09-18 13:35:02,082 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 13:35:02,082 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:35:02,082 - INFO - 20_tender_from_cust_1.pdf                          1      tender_from_cust     run1_20_tender_from_cust_1.json                   
2025-09-18 13:35:02,082 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/20_tender_from_cust/20_tender_from_cust_1.pdf
2025-09-18 13:35:02,082 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_1.json
2025-09-18 13:35:02,082 - INFO - 
2025-09-18 13:35:02,082 - INFO - 20_tender_from_cust_1.pdf                          2      tender_from_cust     run1_20_tender_from_cust_1.json                   
2025-09-18 13:35:02,082 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/20_tender_from_cust/20_tender_from_cust_1.pdf
2025-09-18 13:35:02,082 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_1.json
2025-09-18 13:35:02,082 - INFO - 
2025-09-18 13:35:02,082 - INFO - 20_tender_from_cust_2.pdf                          1      tender_from_cust     run1_20_tender_from_cust_2.json                   
2025-09-18 13:35:02,082 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/20_tender_from_cust/20_tender_from_cust_2.pdf
2025-09-18 13:35:02,082 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_2.json
2025-09-18 13:35:02,082 - INFO - 
2025-09-18 13:35:02,082 - INFO - 20_tender_from_cust_2.pdf                          2      tender_from_cust     run1_20_tender_from_cust_2.json                   
2025-09-18 13:35:02,082 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/20_tender_from_cust/20_tender_from_cust_2.pdf
2025-09-18 13:35:02,082 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_2.json
2025-09-18 13:35:02,083 - INFO - 
2025-09-18 13:35:02,083 - INFO - 20_tender_from_cust_3.pdf                          1      tender_from_cust     run1_20_tender_from_cust_3.json                   
2025-09-18 13:35:02,083 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/20_tender_from_cust/20_tender_from_cust_3.pdf
2025-09-18 13:35:02,083 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_3.json
2025-09-18 13:35:02,083 - INFO - 
2025-09-18 13:35:02,083 - INFO - 20_tender_from_cust_3.pdf                          2      tender_from_cust     run1_20_tender_from_cust_3.json                   
2025-09-18 13:35:02,083 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/20_tender_from_cust/20_tender_from_cust_3.pdf
2025-09-18 13:35:02,083 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_3.json
2025-09-18 13:35:02,083 - INFO - 
2025-09-18 13:35:02,083 - INFO - 20_tender_from_cust_3.pdf                          3      tender_from_cust     run1_20_tender_from_cust_3.json                   
2025-09-18 13:35:02,083 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/20_tender_from_cust/20_tender_from_cust_3.pdf
2025-09-18 13:35:02,083 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_3.json
2025-09-18 13:35:02,083 - INFO - 
2025-09-18 13:35:02,083 - INFO - 20_tender_from_cust_4.pdf                          1      rate_confirmation    run1_20_tender_from_cust_4.json                   
2025-09-18 13:35:02,083 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/20_tender_from_cust/20_tender_from_cust_4.pdf
2025-09-18 13:35:02,083 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_4.json
2025-09-18 13:35:02,083 - INFO - 
2025-09-18 13:35:02,083 - INFO - 20_tender_from_cust_4.pdf                          2      rate_confirmation    run1_20_tender_from_cust_4.json                   
2025-09-18 13:35:02,083 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/20_tender_from_cust/20_tender_from_cust_4.pdf
2025-09-18 13:35:02,083 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_20_tender_from_cust_4.json
2025-09-18 13:35:02,083 - INFO - 
2025-09-18 13:35:02,083 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:35:02,084 - INFO - Total entries: 9
2025-09-18 13:35:02,084 - INFO - ============================================================================================================================================
2025-09-18 13:35:02,084 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 13:35:02,084 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 13:35:02,084 - INFO -   1. 20_tender_from_cust_1.pdf           Page 1   → tender_from_cust | run1_20_tender_from_cust_1.json
2025-09-18 13:35:02,084 - INFO -   2. 20_tender_from_cust_1.pdf           Page 2   → tender_from_cust | run1_20_tender_from_cust_1.json
2025-09-18 13:35:02,084 - INFO -   3. 20_tender_from_cust_2.pdf           Page 1   → tender_from_cust | run1_20_tender_from_cust_2.json
2025-09-18 13:35:02,084 - INFO -   4. 20_tender_from_cust_2.pdf           Page 2   → tender_from_cust | run1_20_tender_from_cust_2.json
2025-09-18 13:35:02,084 - INFO -   5. 20_tender_from_cust_3.pdf           Page 1   → tender_from_cust | run1_20_tender_from_cust_3.json
2025-09-18 13:35:02,084 - INFO -   6. 20_tender_from_cust_3.pdf           Page 2   → tender_from_cust | run1_20_tender_from_cust_3.json
2025-09-18 13:35:02,084 - INFO -   7. 20_tender_from_cust_3.pdf           Page 3   → tender_from_cust | run1_20_tender_from_cust_3.json
2025-09-18 13:35:02,084 - INFO -   8. 20_tender_from_cust_4.pdf           Page 1   → rate_confirmation | run1_20_tender_from_cust_4.json
2025-09-18 13:35:02,084 - INFO -   9. 20_tender_from_cust_4.pdf           Page 2   → rate_confirmation | run1_20_tender_from_cust_4.json
2025-09-18 13:35:02,084 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 13:35:02,084 - INFO - 
✅ Test completed: {'total_files': 4, 'processed': 4, 'failed': 0, 'errors': [], 'duration_seconds': 15.425323, 'processed_files': [{'filename': '20_tender_from_cust_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'tender_from_cust'}, {'page_no': 2, 'doc_type': 'tender_from_cust'}]}, 'file_path': 'input_data_individual/20_tender_from_cust/20_tender_from_cust_1.pdf'}, {'filename': '20_tender_from_cust_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'tender_from_cust'}, {'page_no': 2, 'doc_type': 'tender_from_cust'}]}, 'file_path': 'input_data_individual/20_tender_from_cust/20_tender_from_cust_2.pdf'}, {'filename': '20_tender_from_cust_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'tender_from_cust'}, {'page_no': 2, 'doc_type': 'tender_from_cust'}, {'page_no': 3, 'doc_type': 'tender_from_cust'}]}, 'file_path': 'input_data_individual/20_tender_from_cust/20_tender_from_cust_3.pdf'}, {'filename': '20_tender_from_cust_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'rate_confirmation'}, {'page_no': 2, 'doc_type': 'rate_confirmation'}]}, 'file_path': 'input_data_individual/20_tender_from_cust/20_tender_from_cust_4.pdf'}]}
