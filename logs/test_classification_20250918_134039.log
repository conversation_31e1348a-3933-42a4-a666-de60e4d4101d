2025-09-18 13:40:39,263 - INFO - Logging initialized. Log file: logs/test_classification_20250918_134039.log
2025-09-18 13:40:39,264 - INFO - 📁 Found 7 files to process
2025-09-18 13:40:39,264 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 13:40:39,264 - INFO - 🚀 Processing 7 files in FORCED PARALLEL MODE...
2025-09-18 13:40:39,264 - INFO - 🚀 Creating 7 parallel tasks...
2025-09-18 13:40:39,264 - INFO - 🚀 All 7 tasks created - executing in parallel...
2025-09-18 13:40:39,264 - INFO - ⬆️ [13:40:39] Uploading: 16_customs_doc_1.pdf
2025-09-18 13:40:41,724 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_1.pdf -> s3://document-extraction-logistically/temp/9366eb05_16_customs_doc_1.pdf
2025-09-18 13:40:41,724 - INFO - 🔍 [13:40:41] Starting classification: 16_customs_doc_1.pdf
2025-09-18 13:40:41,724 - INFO - ⬆️ [13:40:41] Uploading: 16_customs_doc_2.tiff
2025-09-18 13:40:41,725 - INFO - Initializing TextractProcessor...
2025-09-18 13:40:41,738 - INFO - Initializing BedrockProcessor...
2025-09-18 13:40:41,742 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9366eb05_16_customs_doc_1.pdf
2025-09-18 13:40:41,743 - INFO - Processing PDF from S3...
2025-09-18 13:40:41,743 - INFO - Downloading PDF from S3 to /tmp/tmpl5lzlrdu/9366eb05_16_customs_doc_1.pdf
2025-09-18 13:40:42,338 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_2.tiff -> s3://document-extraction-logistically/temp/37f3ce06_16_customs_doc_2.tiff
2025-09-18 13:40:42,339 - INFO - 🔍 [13:40:42] Starting classification: 16_customs_doc_2.tiff
2025-09-18 13:40:42,339 - INFO - ⬆️ [13:40:42] Uploading: 16_customs_doc_3.pdf
2025-09-18 13:40:42,341 - INFO - Initializing TextractProcessor...
2025-09-18 13:40:42,367 - INFO - Initializing BedrockProcessor...
2025-09-18 13:40:42,373 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/37f3ce06_16_customs_doc_2.tiff
2025-09-18 13:40:42,374 - INFO - Processing image from S3...
2025-09-18 13:40:42,948 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_3.pdf -> s3://document-extraction-logistically/temp/5ab7aa89_16_customs_doc_3.pdf
2025-09-18 13:40:42,949 - INFO - 🔍 [13:40:42] Starting classification: 16_customs_doc_3.pdf
2025-09-18 13:40:42,949 - INFO - ⬆️ [13:40:42] Uploading: 16_customs_doc_4.pdf
2025-09-18 13:40:42,951 - INFO - Initializing TextractProcessor...
2025-09-18 13:40:42,967 - INFO - Initializing BedrockProcessor...
2025-09-18 13:40:42,969 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5ab7aa89_16_customs_doc_3.pdf
2025-09-18 13:40:42,969 - INFO - Processing PDF from S3...
2025-09-18 13:40:42,969 - INFO - Downloading PDF from S3 to /tmp/tmpn5ihkxbt/5ab7aa89_16_customs_doc_3.pdf
2025-09-18 13:40:43,545 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_4.pdf -> s3://document-extraction-logistically/temp/5138d6fe_16_customs_doc_4.pdf
2025-09-18 13:40:43,545 - INFO - 🔍 [13:40:43] Starting classification: 16_customs_doc_4.pdf
2025-09-18 13:40:43,546 - INFO - ⬆️ [13:40:43] Uploading: 16_customs_doc_5.pdf
2025-09-18 13:40:43,547 - INFO - Initializing TextractProcessor...
2025-09-18 13:40:43,565 - INFO - Initializing BedrockProcessor...
2025-09-18 13:40:43,571 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5138d6fe_16_customs_doc_4.pdf
2025-09-18 13:40:43,571 - INFO - Processing PDF from S3...
2025-09-18 13:40:43,571 - INFO - Downloading PDF from S3 to /tmp/tmp5igulrpw/5138d6fe_16_customs_doc_4.pdf
2025-09-18 13:40:44,094 - WARNING - Sync method failed for S3 image temp/37f3ce06_16_customs_doc_2.tiff, trying async method: An error occurred (UnsupportedDocumentException) when calling the DetectDocumentText operation: Request has unsupported document format
2025-09-18 13:40:44,099 - INFO - Splitting PDF into individual pages...
2025-09-18 13:40:44,102 - INFO - Splitting PDF 9366eb05_16_customs_doc_1 into 5 pages
2025-09-18 13:40:44,131 - INFO - Split PDF into 5 pages
2025-09-18 13:40:44,131 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:40:44,131 - INFO - Expected pages: [1, 2, 3, 4, 5]
2025-09-18 13:40:44,485 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_5.pdf -> s3://document-extraction-logistically/temp/238ae7df_16_customs_doc_5.pdf
2025-09-18 13:40:44,485 - INFO - 🔍 [13:40:44] Starting classification: 16_customs_doc_5.pdf
2025-09-18 13:40:44,486 - INFO - ⬆️ [13:40:44] Uploading: 16_customs_doc_6.pdf
2025-09-18 13:40:44,487 - INFO - Initializing TextractProcessor...
2025-09-18 13:40:44,501 - INFO - Initializing BedrockProcessor...
2025-09-18 13:40:44,505 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/238ae7df_16_customs_doc_5.pdf
2025-09-18 13:40:44,506 - INFO - Processing PDF from S3...
2025-09-18 13:40:44,506 - INFO - Downloading PDF from S3 to /tmp/tmphwcfmp75/238ae7df_16_customs_doc_5.pdf
2025-09-18 13:40:44,777 - INFO - Splitting PDF into individual pages...
2025-09-18 13:40:44,778 - INFO - Splitting PDF 5ab7aa89_16_customs_doc_3 into 1 pages
2025-09-18 13:40:44,780 - INFO - Split PDF into 1 pages
2025-09-18 13:40:44,780 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:40:44,780 - INFO - Expected pages: [1]
2025-09-18 13:40:45,384 - INFO - Splitting PDF into individual pages...
2025-09-18 13:40:45,389 - INFO - Splitting PDF 5138d6fe_16_customs_doc_4 into 1 pages
2025-09-18 13:40:45,399 - INFO - Split PDF into 1 pages
2025-09-18 13:40:45,399 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:40:45,399 - INFO - Expected pages: [1]
2025-09-18 13:40:45,679 - INFO - Started async Textract job 103ea3c761d9518d447b5cb25622e88757573dfbcc3af412b77991afe8405e9a for S3 image temp/37f3ce06_16_customs_doc_2.tiff
2025-09-18 13:40:45,752 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_6.pdf -> s3://document-extraction-logistically/temp/1e43d4cb_16_customs_doc_6.pdf
2025-09-18 13:40:45,753 - INFO - 🔍 [13:40:45] Starting classification: 16_customs_doc_6.pdf
2025-09-18 13:40:45,754 - INFO - ⬆️ [13:40:45] Uploading: 16_customs_doc_7.pdf
2025-09-18 13:40:45,756 - INFO - Initializing TextractProcessor...
2025-09-18 13:40:45,772 - INFO - Initializing BedrockProcessor...
2025-09-18 13:40:45,777 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1e43d4cb_16_customs_doc_6.pdf
2025-09-18 13:40:45,777 - INFO - Processing PDF from S3...
2025-09-18 13:40:45,777 - INFO - Downloading PDF from S3 to /tmp/tmpt2bp4n61/1e43d4cb_16_customs_doc_6.pdf
2025-09-18 13:40:45,964 - INFO -     Poll #1 - Status: IN_PROGRESS (Elapsed: 0s)
2025-09-18 13:40:46,350 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_7.pdf -> s3://document-extraction-logistically/temp/0141004d_16_customs_doc_7.pdf
2025-09-18 13:40:46,350 - INFO - 🔍 [13:40:46] Starting classification: 16_customs_doc_7.pdf
2025-09-18 13:40:46,351 - INFO - Initializing TextractProcessor...
2025-09-18 13:40:46,401 - INFO - Initializing BedrockProcessor...
2025-09-18 13:40:46,404 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0141004d_16_customs_doc_7.pdf
2025-09-18 13:40:46,404 - INFO - Processing PDF from S3...
2025-09-18 13:40:46,404 - INFO - Downloading PDF from S3 to /tmp/tmpji4apuxu/0141004d_16_customs_doc_7.pdf
2025-09-18 13:40:47,023 - INFO - Splitting PDF into individual pages...
2025-09-18 13:40:47,024 - INFO - Splitting PDF 238ae7df_16_customs_doc_5 into 1 pages
2025-09-18 13:40:47,026 - INFO - Split PDF into 1 pages
2025-09-18 13:40:47,026 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:40:47,027 - INFO - Expected pages: [1]
2025-09-18 13:40:48,252 - INFO -     Poll #2 - Status: IN_PROGRESS (Elapsed: 2s)
2025-09-18 13:40:48,396 - INFO - Splitting PDF into individual pages...
2025-09-18 13:40:48,399 - INFO - Splitting PDF 0141004d_16_customs_doc_7 into 1 pages
2025-09-18 13:40:48,404 - INFO - Split PDF into 1 pages
2025-09-18 13:40:48,404 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:40:48,404 - INFO - Expected pages: [1]
2025-09-18 13:40:48,456 - INFO - Page 3: Extracted 1410 characters, 53 lines from 9366eb05_16_customs_doc_1_566ad174_page_003.pdf
2025-09-18 13:40:48,457 - INFO - Successfully processed page 3
2025-09-18 13:40:48,458 - INFO - Splitting PDF into individual pages...
2025-09-18 13:40:48,459 - INFO - Splitting PDF 1e43d4cb_16_customs_doc_6 into 1 pages
2025-09-18 13:40:48,467 - INFO - Split PDF into 1 pages
2025-09-18 13:40:48,467 - INFO - Processing pages with Textract in parallel...
2025-09-18 13:40:48,467 - INFO - Expected pages: [1]
2025-09-18 13:40:48,781 - INFO - Page 5: Extracted 1011 characters, 51 lines from 9366eb05_16_customs_doc_1_566ad174_page_005.pdf
2025-09-18 13:40:48,782 - INFO - Successfully processed page 5
2025-09-18 13:40:49,070 - INFO - Page 1: Extracted 2057 characters, 116 lines from 9366eb05_16_customs_doc_1_566ad174_page_001.pdf
2025-09-18 13:40:49,070 - INFO - Successfully processed page 1
2025-09-18 13:40:49,655 - INFO - Page 2: Extracted 3834 characters, 261 lines from 9366eb05_16_customs_doc_1_566ad174_page_002.pdf
2025-09-18 13:40:49,656 - INFO - Successfully processed page 2
2025-09-18 13:40:50,534 - INFO -     Poll #3 - Status: IN_PROGRESS (Elapsed: 4s)
2025-09-18 13:40:51,294 - INFO - Page 1: Extracted 4011 characters, 246 lines from 5ab7aa89_16_customs_doc_3_d55e44df_page_001.pdf
2025-09-18 13:40:51,294 - INFO - Successfully processed page 1
2025-09-18 13:40:51,295 - INFO - Combined 1 pages into final text
2025-09-18 13:40:51,295 - INFO - Text validation for 5ab7aa89_16_customs_doc_3: 4028 characters, 1 pages
2025-09-18 13:40:51,295 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:40:51,295 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:40:51,510 - INFO - Page 4: Extracted 4895 characters, 119 lines from 9366eb05_16_customs_doc_1_566ad174_page_004.pdf
2025-09-18 13:40:51,510 - INFO - Successfully processed page 4
2025-09-18 13:40:51,511 - INFO - Combined 5 pages into final text
2025-09-18 13:40:51,511 - INFO - Text validation for 9366eb05_16_customs_doc_1: 13300 characters, 5 pages
2025-09-18 13:40:51,511 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:40:51,511 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:40:52,209 - INFO - Page 1: Extracted 3508 characters, 178 lines from 5138d6fe_16_customs_doc_4_df84b0ce_page_001.pdf
2025-09-18 13:40:52,209 - INFO - Successfully processed page 1
2025-09-18 13:40:52,210 - INFO - Combined 1 pages into final text
2025-09-18 13:40:52,210 - INFO - Text validation for 5138d6fe_16_customs_doc_4: 3525 characters, 1 pages
2025-09-18 13:40:52,210 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:40:52,210 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:40:52,819 - INFO -     Poll #4 - Status: IN_PROGRESS (Elapsed: 6s)
2025-09-18 13:40:53,458 - INFO - Page 1: Extracted 858 characters, 51 lines from 0141004d_16_customs_doc_7_d36d56d7_page_001.pdf
2025-09-18 13:40:53,458 - INFO - Successfully processed page 1
2025-09-18 13:40:53,459 - INFO - Combined 1 pages into final text
2025-09-18 13:40:53,459 - INFO - Text validation for 0141004d_16_customs_doc_7: 875 characters, 1 pages
2025-09-18 13:40:53,459 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:40:53,459 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:40:53,773 - INFO - Page 1: Extracted 2440 characters, 84 lines from 238ae7df_16_customs_doc_5_4a95aa6e_page_001.pdf
2025-09-18 13:40:53,773 - INFO - Successfully processed page 1
2025-09-18 13:40:53,774 - INFO - Combined 1 pages into final text
2025-09-18 13:40:53,774 - INFO - Text validation for 238ae7df_16_customs_doc_5: 2457 characters, 1 pages
2025-09-18 13:40:53,774 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:40:53,774 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:40:54,489 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5ab7aa89_16_customs_doc_3.pdf
2025-09-18 13:40:54,504 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5138d6fe_16_customs_doc_4.pdf
2025-09-18 13:40:54,549 - INFO - 

16_customs_doc_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-18 13:40:54,549 - INFO - 

✓ Saved result: output/run1_16_customs_doc_3.json
2025-09-18 13:40:55,060 - INFO - Page 1: Extracted 3361 characters, 141 lines from 1e43d4cb_16_customs_doc_6_57a37d5a_page_001.pdf
2025-09-18 13:40:55,060 - INFO - Successfully processed page 1
2025-09-18 13:40:55,061 - INFO - Combined 1 pages into final text
2025-09-18 13:40:55,061 - INFO - Text validation for 1e43d4cb_16_customs_doc_6: 3378 characters, 1 pages
2025-09-18 13:40:55,061 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:40:55,061 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:40:55,107 - INFO -     Poll #5 - Status: IN_PROGRESS (Elapsed: 8s)
2025-09-18 13:40:55,541 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5ab7aa89_16_customs_doc_3.pdf
2025-09-18 13:40:55,577 - INFO - 

16_customs_doc_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-18 13:40:55,578 - INFO - 

✓ Saved result: output/run1_16_customs_doc_4.json
2025-09-18 13:40:55,669 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0141004d_16_customs_doc_7.pdf
2025-09-18 13:40:55,801 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/238ae7df_16_customs_doc_5.pdf
2025-09-18 13:40:55,876 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5138d6fe_16_customs_doc_4.pdf
2025-09-18 13:40:55,897 - INFO - 

16_customs_doc_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-18 13:40:55,897 - INFO - 

✓ Saved result: output/run1_16_customs_doc_7.json
2025-09-18 13:40:56,192 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0141004d_16_customs_doc_7.pdf
2025-09-18 13:40:56,227 - INFO - 

16_customs_doc_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-18 13:40:56,227 - INFO - 

✓ Saved result: output/run1_16_customs_doc_5.json
2025-09-18 13:40:56,528 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/238ae7df_16_customs_doc_5.pdf
2025-09-18 13:40:57,111 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1e43d4cb_16_customs_doc_6.pdf
2025-09-18 13:40:57,140 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9366eb05_16_customs_doc_1.pdf
2025-09-18 13:40:57,166 - INFO - 

16_customs_doc_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-18 13:40:57,167 - INFO - 

✓ Saved result: output/run1_16_customs_doc_6.json
2025-09-18 13:40:57,468 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1e43d4cb_16_customs_doc_6.pdf
2025-09-18 13:40:57,593 - INFO - 

16_customs_doc_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        },
        {
            "page_no": 2,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 3,
            "doc_type": "customs_doc"
        },
        {
            "page_no": 4,
            "doc_type": "bol"
        },
        {
            "page_no": 5,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-18 13:40:57,593 - INFO - 

✓ Saved result: output/run1_16_customs_doc_1.json
2025-09-18 13:40:57,894 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9366eb05_16_customs_doc_1.pdf
2025-09-18 13:40:58,842 - INFO -     Poll #6 - Status: SUCCEEDED (Elapsed: 10s)
2025-09-18 13:40:58,842 - INFO - Async method succeeded for S3 image temp/37f3ce06_16_customs_doc_2.tiff
2025-09-18 13:40:58,842 - INFO - S3 Image temp/37f3ce06_16_customs_doc_2.tiff: Extracted 4903 characters, 186 lines
2025-09-18 13:40:58,842 - INFO - Analyzing document types with Bedrock...
2025-09-18 13:40:58,842 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 13:41:01,515 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/37f3ce06_16_customs_doc_2.tiff
2025-09-18 13:41:01,572 - INFO - 

16_customs_doc_2.tiff

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-18 13:41:01,572 - INFO - 

✓ Saved result: output/run1_16_customs_doc_2.json
2025-09-18 13:41:01,924 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/37f3ce06_16_customs_doc_2.tiff
2025-09-18 13:41:01,926 - INFO - 
📊 Processing Summary:
2025-09-18 13:41:01,926 - INFO -    Total files: 7
2025-09-18 13:41:01,926 - INFO -    Successful: 7
2025-09-18 13:41:01,926 - INFO -    Failed: 0
2025-09-18 13:41:01,926 - INFO -    Duration: 22.66 seconds
2025-09-18 13:41:01,926 - INFO -    Output directory: output
2025-09-18 13:41:01,926 - INFO - 
📋 Successfully Processed Files:
2025-09-18 13:41:01,926 - INFO -    📄 16_customs_doc_1.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"},{"page_no":2,"doc_type":"comm_invoice"},{"page_no":3,"doc_type":"customs_doc"},{"page_no":4,"doc_type":"bol"},{"page_no":5,"doc_type":"comm_invoice"}]}
2025-09-18 13:41:01,926 - INFO -    📄 16_customs_doc_2.tiff: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-18 13:41:01,927 - INFO -    📄 16_customs_doc_3.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-18 13:41:01,927 - INFO -    📄 16_customs_doc_4.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-18 13:41:01,927 - INFO -    📄 16_customs_doc_5.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-18 13:41:01,927 - INFO -    📄 16_customs_doc_6.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-18 13:41:01,927 - INFO -    📄 16_customs_doc_7.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"}]}
2025-09-18 13:41:01,927 - INFO - 
============================================================================================================================================
2025-09-18 13:41:01,928 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 13:41:01,928 - INFO - ============================================================================================================================================
2025-09-18 13:41:01,928 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 13:41:01,928 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:41:01,928 - INFO - 16_customs_doc_1.pdf                               1      customs_doc          run1_16_customs_doc_1.json                        
2025-09-18 13:41:01,928 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_1.pdf
2025-09-18 13:41:01,928 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 13:41:01,928 - INFO - 
2025-09-18 13:41:01,928 - INFO - 16_customs_doc_1.pdf                               2      comm_invoice         run1_16_customs_doc_1.json                        
2025-09-18 13:41:01,928 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_1.pdf
2025-09-18 13:41:01,928 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 13:41:01,928 - INFO - 
2025-09-18 13:41:01,929 - INFO - 16_customs_doc_1.pdf                               3      customs_doc          run1_16_customs_doc_1.json                        
2025-09-18 13:41:01,929 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_1.pdf
2025-09-18 13:41:01,929 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 13:41:01,929 - INFO - 
2025-09-18 13:41:01,929 - INFO - 16_customs_doc_1.pdf                               4      bol                  run1_16_customs_doc_1.json                        
2025-09-18 13:41:01,929 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_1.pdf
2025-09-18 13:41:01,929 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 13:41:01,929 - INFO - 
2025-09-18 13:41:01,929 - INFO - 16_customs_doc_1.pdf                               5      comm_invoice         run1_16_customs_doc_1.json                        
2025-09-18 13:41:01,929 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_1.pdf
2025-09-18 13:41:01,929 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 13:41:01,929 - INFO - 
2025-09-18 13:41:01,929 - INFO - 16_customs_doc_2.tiff                              1      customs_doc          run1_16_customs_doc_2.json                        
2025-09-18 13:41:01,929 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_2.tiff
2025-09-18 13:41:01,929 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_2.json
2025-09-18 13:41:01,929 - INFO - 
2025-09-18 13:41:01,929 - INFO - 16_customs_doc_3.pdf                               1      customs_doc          run1_16_customs_doc_3.json                        
2025-09-18 13:41:01,929 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_3.pdf
2025-09-18 13:41:01,929 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_3.json
2025-09-18 13:41:01,929 - INFO - 
2025-09-18 13:41:01,929 - INFO - 16_customs_doc_4.pdf                               1      customs_doc          run1_16_customs_doc_4.json                        
2025-09-18 13:41:01,930 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_4.pdf
2025-09-18 13:41:01,930 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_4.json
2025-09-18 13:41:01,930 - INFO - 
2025-09-18 13:41:01,930 - INFO - 16_customs_doc_5.pdf                               1      customs_doc          run1_16_customs_doc_5.json                        
2025-09-18 13:41:01,930 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_5.pdf
2025-09-18 13:41:01,930 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_5.json
2025-09-18 13:41:01,930 - INFO - 
2025-09-18 13:41:01,930 - INFO - 16_customs_doc_6.pdf                               1      customs_doc          run1_16_customs_doc_6.json                        
2025-09-18 13:41:01,930 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_6.pdf
2025-09-18 13:41:01,930 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_6.json
2025-09-18 13:41:01,930 - INFO - 
2025-09-18 13:41:01,930 - INFO - 16_customs_doc_7.pdf                               1      customs_doc          run1_16_customs_doc_7.json                        
2025-09-18 13:41:01,930 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_7.pdf
2025-09-18 13:41:01,930 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_7.json
2025-09-18 13:41:01,930 - INFO - 
2025-09-18 13:41:01,930 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 13:41:01,930 - INFO - Total entries: 11
2025-09-18 13:41:01,930 - INFO - ============================================================================================================================================
2025-09-18 13:41:01,930 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 13:41:01,930 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 13:41:01,930 - INFO -   1. 16_customs_doc_1.pdf                Page 1   → customs_doc     | run1_16_customs_doc_1.json
2025-09-18 13:41:01,930 - INFO -   2. 16_customs_doc_1.pdf                Page 2   → comm_invoice    | run1_16_customs_doc_1.json
2025-09-18 13:41:01,930 - INFO -   3. 16_customs_doc_1.pdf                Page 3   → customs_doc     | run1_16_customs_doc_1.json
2025-09-18 13:41:01,930 - INFO -   4. 16_customs_doc_1.pdf                Page 4   → bol             | run1_16_customs_doc_1.json
2025-09-18 13:41:01,930 - INFO -   5. 16_customs_doc_1.pdf                Page 5   → comm_invoice    | run1_16_customs_doc_1.json
2025-09-18 13:41:01,930 - INFO -   6. 16_customs_doc_2.tiff               Page 1   → customs_doc     | run1_16_customs_doc_2.json
2025-09-18 13:41:01,930 - INFO -   7. 16_customs_doc_3.pdf                Page 1   → customs_doc     | run1_16_customs_doc_3.json
2025-09-18 13:41:01,930 - INFO -   8. 16_customs_doc_4.pdf                Page 1   → customs_doc     | run1_16_customs_doc_4.json
2025-09-18 13:41:01,930 - INFO -   9. 16_customs_doc_5.pdf                Page 1   → customs_doc     | run1_16_customs_doc_5.json
2025-09-18 13:41:01,930 - INFO -  10. 16_customs_doc_6.pdf                Page 1   → customs_doc     | run1_16_customs_doc_6.json
2025-09-18 13:41:01,930 - INFO -  11. 16_customs_doc_7.pdf                Page 1   → customs_doc     | run1_16_customs_doc_7.json
2025-09-18 13:41:01,930 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 13:41:01,931 - INFO - 
✅ Test completed: {'total_files': 7, 'processed': 7, 'failed': 0, 'errors': [], 'duration_seconds': 22.66168, 'processed_files': [{'filename': '16_customs_doc_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}, {'page_no': 2, 'doc_type': 'comm_invoice'}, {'page_no': 3, 'doc_type': 'customs_doc'}, {'page_no': 4, 'doc_type': 'bol'}, {'page_no': 5, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_1.pdf'}, {'filename': '16_customs_doc_2.tiff', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_2.tiff'}, {'filename': '16_customs_doc_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_3.pdf'}, {'filename': '16_customs_doc_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_4.pdf'}, {'filename': '16_customs_doc_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_5.pdf'}, {'filename': '16_customs_doc_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_6.pdf'}, {'filename': '16_customs_doc_7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_individual/16_customs_doc/16_customs_doc_7.pdf'}]}
