2025-09-18 14:05:11,740 - INFO - Logging initialized. Log file: logs/test_classification_20250918_140511.log
2025-09-18 14:05:11,740 - INFO - 📁 Found 1 files to process
2025-09-18 14:05:11,740 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:05:11,740 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-18 14:05:11,740 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-18 14:05:11,740 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-18 14:05:11,740 - INFO - ⬆️ [14:05:11] Uploading: 16_customs_doc_1.pdf
2025-09-18 14:05:13,710 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_random/16_customs_doc_1.pdf -> s3://document-extraction-logistically/temp/b1b05f78_16_customs_doc_1.pdf
2025-09-18 14:05:13,710 - INFO - 🔍 [14:05:13] Starting classification: 16_customs_doc_1.pdf
2025-09-18 14:05:13,711 - INFO - Initializing TextractProcessor...
2025-09-18 14:05:13,734 - INFO - Initializing BedrockProcessor...
2025-09-18 14:05:13,739 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b1b05f78_16_customs_doc_1.pdf
2025-09-18 14:05:13,739 - INFO - Processing PDF from S3...
2025-09-18 14:05:13,739 - INFO - Downloading PDF from S3 to /tmp/tmprhd765k2/b1b05f78_16_customs_doc_1.pdf
2025-09-18 14:05:15,258 - INFO - Splitting PDF into individual pages...
2025-09-18 14:05:15,260 - INFO - Splitting PDF b1b05f78_16_customs_doc_1 into 3 pages
2025-09-18 14:05:15,271 - INFO - Split PDF into 3 pages
2025-09-18 14:05:15,271 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:05:15,271 - INFO - Expected pages: [1, 2, 3]
2025-09-18 14:05:21,023 - INFO - Page 3: Extracted 1410 characters, 53 lines from b1b05f78_16_customs_doc_1_a7630eb4_page_003.pdf
2025-09-18 14:05:21,023 - INFO - Successfully processed page 3
2025-09-18 14:05:21,990 - INFO - Page 1: Extracted 2057 characters, 116 lines from b1b05f78_16_customs_doc_1_a7630eb4_page_001.pdf
2025-09-18 14:05:21,991 - INFO - Successfully processed page 1
2025-09-18 14:05:22,434 - INFO - Page 2: Extracted 3834 characters, 261 lines from b1b05f78_16_customs_doc_1_a7630eb4_page_002.pdf
2025-09-18 14:05:22,434 - INFO - Successfully processed page 2
2025-09-18 14:05:22,434 - INFO - Combined 3 pages into final text
2025-09-18 14:05:22,434 - INFO - Text validation for b1b05f78_16_customs_doc_1: 7356 characters, 3 pages
2025-09-18 14:05:22,435 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:05:22,435 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:05:26,854 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b1b05f78_16_customs_doc_1.pdf
2025-09-18 14:05:26,953 - INFO - 

16_customs_doc_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        },
        {
            "page_no": 2,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 3,
            "doc_type": "other"
        }
    ]
}
2025-09-18 14:05:26,953 - INFO - 

✓ Saved result: output/run1_16_customs_doc_1.json
2025-09-18 14:05:28,284 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b1b05f78_16_customs_doc_1.pdf
2025-09-18 14:05:28,287 - INFO - 
📊 Processing Summary:
2025-09-18 14:05:28,287 - INFO -    Total files: 1
2025-09-18 14:05:28,287 - INFO -    Successful: 1
2025-09-18 14:05:28,287 - INFO -    Failed: 0
2025-09-18 14:05:28,287 - INFO -    Duration: 16.55 seconds
2025-09-18 14:05:28,287 - INFO -    Output directory: output
2025-09-18 14:05:28,287 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:05:28,287 - INFO -    📄 16_customs_doc_1.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"},{"page_no":2,"doc_type":"comm_invoice"},{"page_no":3,"doc_type":"other"}]}
2025-09-18 14:05:28,287 - INFO - 
============================================================================================================================================
2025-09-18 14:05:28,288 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:05:28,288 - INFO - ============================================================================================================================================
2025-09-18 14:05:28,288 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:05:28,288 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:05:28,288 - INFO - 16_customs_doc_1.pdf                               1      customs_doc          run1_16_customs_doc_1.json                        
2025-09-18 14:05:28,288 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_random/16_customs_doc_1.pdf
2025-09-18 14:05:28,288 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 14:05:28,288 - INFO - 
2025-09-18 14:05:28,288 - INFO - 16_customs_doc_1.pdf                               2      comm_invoice         run1_16_customs_doc_1.json                        
2025-09-18 14:05:28,288 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_random/16_customs_doc_1.pdf
2025-09-18 14:05:28,288 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 14:05:28,288 - INFO - 
2025-09-18 14:05:28,288 - INFO - 16_customs_doc_1.pdf                               3      other                run1_16_customs_doc_1.json                        
2025-09-18 14:05:28,288 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_random/16_customs_doc_1.pdf
2025-09-18 14:05:28,288 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 14:05:28,288 - INFO - 
2025-09-18 14:05:28,289 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:05:28,289 - INFO - Total entries: 3
2025-09-18 14:05:28,289 - INFO - ============================================================================================================================================
2025-09-18 14:05:28,289 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:05:28,289 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:05:28,289 - INFO -   1. 16_customs_doc_1.pdf                Page 1   → customs_doc     | run1_16_customs_doc_1.json
2025-09-18 14:05:28,289 - INFO -   2. 16_customs_doc_1.pdf                Page 2   → comm_invoice    | run1_16_customs_doc_1.json
2025-09-18 14:05:28,289 - INFO -   3. 16_customs_doc_1.pdf                Page 3   → other           | run1_16_customs_doc_1.json
2025-09-18 14:05:28,289 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:05:28,289 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 1, 'failed': 0, 'errors': [], 'duration_seconds': 16.546353, 'processed_files': [{'filename': '16_customs_doc_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}, {'page_no': 2, 'doc_type': 'comm_invoice'}, {'page_no': 3, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_random/16_customs_doc_1.pdf'}]}
