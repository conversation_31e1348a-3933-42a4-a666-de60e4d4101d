2025-09-18 14:07:30,257 - INFO - Logging initialized. Log file: logs/test_classification_20250918_140730.log
2025-09-18 14:07:30,257 - INFO - 📁 Found 1 files to process
2025-09-18 14:07:30,257 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:07:30,257 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-18 14:07:30,257 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-18 14:07:30,257 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-18 14:07:30,257 - INFO - ⬆️ [14:07:30] Uploading: 16_customs_doc_1.pdf
2025-09-18 14:07:32,503 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_random/16_customs_doc_1.pdf -> s3://document-extraction-logistically/temp/a2d45f9a_16_customs_doc_1.pdf
2025-09-18 14:07:32,503 - INFO - 🔍 [14:07:32] Starting classification: 16_customs_doc_1.pdf
2025-09-18 14:07:32,504 - INFO - Initializing TextractProcessor...
2025-09-18 14:07:32,521 - INFO - Initializing BedrockProcessor...
2025-09-18 14:07:32,530 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a2d45f9a_16_customs_doc_1.pdf
2025-09-18 14:07:32,531 - INFO - Processing PDF from S3...
2025-09-18 14:07:32,531 - INFO - Downloading PDF from S3 to /tmp/tmpexgxqw1l/a2d45f9a_16_customs_doc_1.pdf
2025-09-18 14:07:34,334 - INFO - Splitting PDF into individual pages...
2025-09-18 14:07:34,337 - INFO - Splitting PDF a2d45f9a_16_customs_doc_1 into 3 pages
2025-09-18 14:07:34,360 - INFO - Split PDF into 3 pages
2025-09-18 14:07:34,361 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:07:34,361 - INFO - Expected pages: [1, 2, 3]
2025-09-18 14:07:39,153 - INFO - Page 3: Extracted 1410 characters, 53 lines from a2d45f9a_16_customs_doc_1_d1419dc9_page_003.pdf
2025-09-18 14:07:39,154 - INFO - Successfully processed page 3
2025-09-18 14:07:39,369 - INFO - Page 1: Extracted 2057 characters, 116 lines from a2d45f9a_16_customs_doc_1_d1419dc9_page_001.pdf
2025-09-18 14:07:39,369 - INFO - Successfully processed page 1
2025-09-18 14:07:41,401 - INFO - Page 2: Extracted 3834 characters, 261 lines from a2d45f9a_16_customs_doc_1_d1419dc9_page_002.pdf
2025-09-18 14:07:41,402 - INFO - Successfully processed page 2
2025-09-18 14:07:41,402 - INFO - Combined 3 pages into final text
2025-09-18 14:07:41,403 - INFO - Text validation for a2d45f9a_16_customs_doc_1: 7356 characters, 3 pages
2025-09-18 14:07:41,403 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:07:41,403 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:07:46,936 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-18 14:07:46,937 - ERROR - Processing failed for s3://document-extraction-logistically/temp/a2d45f9a_16_customs_doc_1.pdf: Unexpected tool response format from Bedrock
2025-09-18 14:07:46,939 - ERROR - ✗ Failed to process /home/<USER>/Desktop/test_ligistically/input_data_random/16_customs_doc_1.pdf: Unexpected tool response format from Bedrock
2025-09-18 14:07:48,157 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a2d45f9a_16_customs_doc_1.pdf
2025-09-18 14:07:48,158 - INFO - 
📊 Processing Summary:
2025-09-18 14:07:48,158 - INFO -    Total files: 1
2025-09-18 14:07:48,158 - INFO -    Successful: 0
2025-09-18 14:07:48,158 - INFO -    Failed: 1
2025-09-18 14:07:48,158 - INFO -    Duration: 17.90 seconds
2025-09-18 14:07:48,158 - INFO -    Output directory: output
2025-09-18 14:07:48,159 - ERROR - 
❌ Errors:
2025-09-18 14:07:48,159 - ERROR -    Failed to process /home/<USER>/Desktop/test_ligistically/input_data_random/16_customs_doc_1.pdf: Unexpected tool response format from Bedrock
2025-09-18 14:07:48,159 - INFO - 
📋 No files were successfully processed.
2025-09-18 14:07:48,159 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 0, 'failed': 1, 'errors': ['Failed to process /home/<USER>/Desktop/test_ligistically/input_data_random/16_customs_doc_1.pdf: Unexpected tool response format from Bedrock'], 'duration_seconds': 17.900779, 'processed_files': []}
