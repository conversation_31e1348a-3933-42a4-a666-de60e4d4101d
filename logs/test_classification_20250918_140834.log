2025-09-18 14:08:34,707 - INFO - Logging initialized. Log file: logs/test_classification_20250918_140834.log
2025-09-18 14:08:34,708 - INFO - 📁 Found 1 files to process
2025-09-18 14:08:34,708 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:08:34,708 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-18 14:08:34,708 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-18 14:08:34,708 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-18 14:08:34,708 - INFO - ⬆️ [14:08:34] Uploading: 16_customs_doc_1.pdf
2025-09-18 14:08:36,799 - INFO - ✓ Uploaded: /home/<USER>/Desktop/test_ligistically/input_data_random/16_customs_doc_1.pdf -> s3://document-extraction-logistically/temp/305c3880_16_customs_doc_1.pdf
2025-09-18 14:08:36,799 - INFO - 🔍 [14:08:36] Starting classification: 16_customs_doc_1.pdf
2025-09-18 14:08:36,800 - INFO - Initializing TextractProcessor...
2025-09-18 14:08:36,812 - INFO - Initializing BedrockProcessor...
2025-09-18 14:08:36,816 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/305c3880_16_customs_doc_1.pdf
2025-09-18 14:08:36,817 - INFO - Processing PDF from S3...
2025-09-18 14:08:36,817 - INFO - Downloading PDF from S3 to /tmp/tmp_y8y1239/305c3880_16_customs_doc_1.pdf
2025-09-18 14:08:38,534 - INFO - Splitting PDF into individual pages...
2025-09-18 14:08:38,535 - INFO - Splitting PDF 305c3880_16_customs_doc_1 into 3 pages
2025-09-18 14:08:38,547 - INFO - Split PDF into 3 pages
2025-09-18 14:08:38,547 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:08:38,547 - INFO - Expected pages: [1, 2, 3]
2025-09-18 14:08:43,094 - INFO - Page 3: Extracted 1410 characters, 53 lines from 305c3880_16_customs_doc_1_62ffda41_page_003.pdf
2025-09-18 14:08:43,094 - INFO - Successfully processed page 3
2025-09-18 14:08:43,285 - INFO - Page 1: Extracted 2057 characters, 116 lines from 305c3880_16_customs_doc_1_62ffda41_page_001.pdf
2025-09-18 14:08:43,286 - INFO - Successfully processed page 1
2025-09-18 14:08:43,683 - INFO - Page 2: Extracted 3834 characters, 261 lines from 305c3880_16_customs_doc_1_62ffda41_page_002.pdf
2025-09-18 14:08:43,683 - INFO - Successfully processed page 2
2025-09-18 14:08:43,683 - INFO - Combined 3 pages into final text
2025-09-18 14:08:43,683 - INFO - Text validation for 305c3880_16_customs_doc_1: 7356 characters, 3 pages
2025-09-18 14:08:43,684 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:08:43,684 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:08:47,561 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/305c3880_16_customs_doc_1.pdf
2025-09-18 14:08:47,652 - INFO - 

16_customs_doc_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "customs_doc"
        },
        {
            "page_no": 2,
            "doc_type": "customs_doc"
        },
        {
            "page_no": 3,
            "doc_type": "other"
        }
    ]
}
2025-09-18 14:08:47,653 - INFO - 

✓ Saved result: output/run1_16_customs_doc_1.json
2025-09-18 14:08:49,197 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/305c3880_16_customs_doc_1.pdf
2025-09-18 14:08:49,199 - INFO - 
📊 Processing Summary:
2025-09-18 14:08:49,200 - INFO -    Total files: 1
2025-09-18 14:08:49,200 - INFO -    Successful: 1
2025-09-18 14:08:49,200 - INFO -    Failed: 0
2025-09-18 14:08:49,200 - INFO -    Duration: 14.49 seconds
2025-09-18 14:08:49,200 - INFO -    Output directory: output
2025-09-18 14:08:49,200 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:08:49,200 - INFO -    📄 16_customs_doc_1.pdf: {"documents":[{"page_no":1,"doc_type":"customs_doc"},{"page_no":2,"doc_type":"customs_doc"},{"page_no":3,"doc_type":"other"}]}
2025-09-18 14:08:49,200 - INFO - 
============================================================================================================================================
2025-09-18 14:08:49,200 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:08:49,200 - INFO - ============================================================================================================================================
2025-09-18 14:08:49,200 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:08:49,200 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:08:49,201 - INFO - 16_customs_doc_1.pdf                               1      customs_doc          run1_16_customs_doc_1.json                        
2025-09-18 14:08:49,201 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_random/16_customs_doc_1.pdf
2025-09-18 14:08:49,201 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 14:08:49,201 - INFO - 
2025-09-18 14:08:49,201 - INFO - 16_customs_doc_1.pdf                               2      customs_doc          run1_16_customs_doc_1.json                        
2025-09-18 14:08:49,201 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_random/16_customs_doc_1.pdf
2025-09-18 14:08:49,201 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 14:08:49,201 - INFO - 
2025-09-18 14:08:49,201 - INFO - 16_customs_doc_1.pdf                               3      other                run1_16_customs_doc_1.json                        
2025-09-18 14:08:49,201 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_random/16_customs_doc_1.pdf
2025-09-18 14:08:49,201 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_16_customs_doc_1.json
2025-09-18 14:08:49,201 - INFO - 
2025-09-18 14:08:49,201 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:08:49,201 - INFO - Total entries: 3
2025-09-18 14:08:49,201 - INFO - ============================================================================================================================================
2025-09-18 14:08:49,201 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:08:49,201 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:08:49,201 - INFO -   1. 16_customs_doc_1.pdf                Page 1   → customs_doc     | run1_16_customs_doc_1.json
2025-09-18 14:08:49,201 - INFO -   2. 16_customs_doc_1.pdf                Page 2   → customs_doc     | run1_16_customs_doc_1.json
2025-09-18 14:08:49,201 - INFO -   3. 16_customs_doc_1.pdf                Page 3   → other           | run1_16_customs_doc_1.json
2025-09-18 14:08:49,202 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:08:49,202 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 1, 'failed': 0, 'errors': [], 'duration_seconds': 14.491602, 'processed_files': [{'filename': '16_customs_doc_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'customs_doc'}, {'page_no': 2, 'doc_type': 'customs_doc'}, {'page_no': 3, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Desktop/test_ligistically/input_data_random/16_customs_doc_1.pdf'}]}
