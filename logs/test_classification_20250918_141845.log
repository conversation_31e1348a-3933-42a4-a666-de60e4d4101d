2025-09-18 14:18:45,092 - INFO - Logging initialized. Log file: logs/test_classification_20250918_141845.log
2025-09-18 14:18:45,092 - INFO - 📁 Found 5 files to process
2025-09-18 14:18:45,092 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:18:45,092 - INFO - 🚀 Processing 5 files in FORCED PARALLEL MODE...
2025-09-18 14:18:45,092 - INFO - 🚀 Creating 5 parallel tasks...
2025-09-18 14:18:45,092 - INFO - 🚀 All 5 tasks created - executing in parallel...
2025-09-18 14:18:45,092 - INFO - ⬆️ [14:18:45] Uploading: 14_po_1.pdf
2025-09-18 14:18:48,540 - INFO - ✓ Uploaded: input_data_individual/14_po/14_po_1.pdf -> s3://document-extraction-logistically/temp/2c3fdac5_14_po_1.pdf
2025-09-18 14:18:48,541 - INFO - 🔍 [14:18:48] Starting classification: 14_po_1.pdf
2025-09-18 14:18:48,541 - INFO - ⬆️ [14:18:48] Uploading: 14_po_2.pdf
2025-09-18 14:18:48,542 - INFO - Initializing TextractProcessor...
2025-09-18 14:18:48,568 - INFO - Initializing BedrockProcessor...
2025-09-18 14:18:48,576 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2c3fdac5_14_po_1.pdf
2025-09-18 14:18:48,576 - INFO - Processing PDF from S3...
2025-09-18 14:18:48,576 - INFO - Downloading PDF from S3 to /tmp/tmpp227mr2m/2c3fdac5_14_po_1.pdf
2025-09-18 14:18:49,269 - INFO - ✓ Uploaded: input_data_individual/14_po/14_po_2.pdf -> s3://document-extraction-logistically/temp/ec9936d8_14_po_2.pdf
2025-09-18 14:18:49,270 - INFO - 🔍 [14:18:49] Starting classification: 14_po_2.pdf
2025-09-18 14:18:49,271 - INFO - ⬆️ [14:18:49] Uploading: 14_po_3.pdf
2025-09-18 14:18:49,272 - INFO - Initializing TextractProcessor...
2025-09-18 14:18:49,311 - INFO - Initializing BedrockProcessor...
2025-09-18 14:18:49,318 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ec9936d8_14_po_2.pdf
2025-09-18 14:18:49,318 - INFO - Processing PDF from S3...
2025-09-18 14:18:49,319 - INFO - Downloading PDF from S3 to /tmp/tmp5fbsfm8m/ec9936d8_14_po_2.pdf
2025-09-18 14:18:49,934 - INFO - ✓ Uploaded: input_data_individual/14_po/14_po_3.pdf -> s3://document-extraction-logistically/temp/a1a3ea31_14_po_3.pdf
2025-09-18 14:18:49,935 - INFO - 🔍 [14:18:49] Starting classification: 14_po_3.pdf
2025-09-18 14:18:49,936 - INFO - ⬆️ [14:18:49] Uploading: 14_po_4.pdf
2025-09-18 14:18:49,938 - INFO - Initializing TextractProcessor...
2025-09-18 14:18:49,960 - INFO - Initializing BedrockProcessor...
2025-09-18 14:18:49,965 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a1a3ea31_14_po_3.pdf
2025-09-18 14:18:49,965 - INFO - Processing PDF from S3...
2025-09-18 14:18:49,965 - INFO - Downloading PDF from S3 to /tmp/tmp_7nkmb_7/a1a3ea31_14_po_3.pdf
2025-09-18 14:18:50,619 - INFO - ✓ Uploaded: input_data_individual/14_po/14_po_4.pdf -> s3://document-extraction-logistically/temp/b7efc171_14_po_4.pdf
2025-09-18 14:18:50,619 - INFO - 🔍 [14:18:50] Starting classification: 14_po_4.pdf
2025-09-18 14:18:50,620 - INFO - ⬆️ [14:18:50] Uploading: 14_po_5.pdf
2025-09-18 14:18:50,622 - INFO - Initializing TextractProcessor...
2025-09-18 14:18:50,642 - INFO - Initializing BedrockProcessor...
2025-09-18 14:18:50,647 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b7efc171_14_po_4.pdf
2025-09-18 14:18:50,647 - INFO - Processing PDF from S3...
2025-09-18 14:18:50,648 - INFO - Downloading PDF from S3 to /tmp/tmpkpbnf5xm/b7efc171_14_po_4.pdf
2025-09-18 14:18:50,952 - INFO - Splitting PDF into individual pages...
2025-09-18 14:18:50,954 - INFO - Splitting PDF 2c3fdac5_14_po_1 into 1 pages
2025-09-18 14:18:50,971 - INFO - Split PDF into 1 pages
2025-09-18 14:18:50,972 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:18:50,972 - INFO - Expected pages: [1]
2025-09-18 14:18:51,335 - INFO - ✓ Uploaded: input_data_individual/14_po/14_po_5.pdf -> s3://document-extraction-logistically/temp/57bf1293_14_po_5.pdf
2025-09-18 14:18:51,336 - INFO - 🔍 [14:18:51] Starting classification: 14_po_5.pdf
2025-09-18 14:18:51,337 - INFO - Initializing TextractProcessor...
2025-09-18 14:18:51,354 - INFO - Initializing BedrockProcessor...
2025-09-18 14:18:51,357 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/57bf1293_14_po_5.pdf
2025-09-18 14:18:51,358 - INFO - Processing PDF from S3...
2025-09-18 14:18:51,358 - INFO - Downloading PDF from S3 to /tmp/tmpls96ysi2/57bf1293_14_po_5.pdf
2025-09-18 14:18:51,410 - INFO - Splitting PDF into individual pages...
2025-09-18 14:18:51,411 - INFO - Splitting PDF ec9936d8_14_po_2 into 1 pages
2025-09-18 14:18:51,422 - INFO - Split PDF into 1 pages
2025-09-18 14:18:51,422 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:18:51,422 - INFO - Expected pages: [1]
2025-09-18 14:18:51,507 - INFO - Splitting PDF into individual pages...
2025-09-18 14:18:51,509 - INFO - Splitting PDF a1a3ea31_14_po_3 into 2 pages
2025-09-18 14:18:51,525 - INFO - Split PDF into 2 pages
2025-09-18 14:18:51,525 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:18:51,525 - INFO - Expected pages: [1, 2]
2025-09-18 14:18:52,176 - INFO - Splitting PDF into individual pages...
2025-09-18 14:18:52,178 - INFO - Splitting PDF b7efc171_14_po_4 into 1 pages
2025-09-18 14:18:52,192 - INFO - Split PDF into 1 pages
2025-09-18 14:18:52,192 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:18:52,192 - INFO - Expected pages: [1]
2025-09-18 14:18:53,796 - INFO - Splitting PDF into individual pages...
2025-09-18 14:18:53,798 - INFO - Splitting PDF 57bf1293_14_po_5 into 2 pages
2025-09-18 14:18:53,809 - INFO - Split PDF into 2 pages
2025-09-18 14:18:53,810 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:18:53,810 - INFO - Expected pages: [1, 2]
2025-09-18 14:18:56,087 - INFO - Page 1: Extracted 1432 characters, 83 lines from a1a3ea31_14_po_3_5a8a5223_page_001.pdf
2025-09-18 14:18:56,087 - INFO - Successfully processed page 1
2025-09-18 14:18:56,630 - INFO - Page 1: Extracted 813 characters, 71 lines from ec9936d8_14_po_2_64ed7b6d_page_001.pdf
2025-09-18 14:18:56,630 - INFO - Successfully processed page 1
2025-09-18 14:18:56,630 - INFO - Combined 1 pages into final text
2025-09-18 14:18:56,631 - INFO - Text validation for ec9936d8_14_po_2: 830 characters, 1 pages
2025-09-18 14:18:56,631 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:18:56,631 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:18:56,679 - INFO - Page 1: Extracted 1508 characters, 92 lines from b7efc171_14_po_4_202c6928_page_001.pdf
2025-09-18 14:18:56,679 - INFO - Successfully processed page 1
2025-09-18 14:18:56,679 - INFO - Combined 1 pages into final text
2025-09-18 14:18:56,679 - INFO - Text validation for b7efc171_14_po_4: 1525 characters, 1 pages
2025-09-18 14:18:56,680 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:18:56,680 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:18:56,706 - INFO - Page 1: Extracted 690 characters, 57 lines from 2c3fdac5_14_po_1_992b3dc8_page_001.pdf
2025-09-18 14:18:56,706 - INFO - Successfully processed page 1
2025-09-18 14:18:56,707 - INFO - Combined 1 pages into final text
2025-09-18 14:18:56,707 - INFO - Text validation for 2c3fdac5_14_po_1: 707 characters, 1 pages
2025-09-18 14:18:56,708 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:18:56,708 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:18:56,800 - INFO - Page 2: Extracted 669 characters, 16 lines from a1a3ea31_14_po_3_5a8a5223_page_002.pdf
2025-09-18 14:18:56,801 - INFO - Successfully processed page 2
2025-09-18 14:18:56,801 - INFO - Combined 2 pages into final text
2025-09-18 14:18:56,801 - INFO - Text validation for a1a3ea31_14_po_3: 2137 characters, 2 pages
2025-09-18 14:18:56,802 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:18:56,802 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:18:58,290 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2c3fdac5_14_po_1.pdf
2025-09-18 14:18:58,312 - INFO - 

14_po_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-18 14:18:58,312 - INFO - 

✓ Saved result: output/run1_14_po_1.json
2025-09-18 14:18:58,646 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b7efc171_14_po_4.pdf
2025-09-18 14:18:58,657 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ec9936d8_14_po_2.pdf
2025-09-18 14:18:58,703 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a1a3ea31_14_po_3.pdf
2025-09-18 14:18:59,319 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2c3fdac5_14_po_1.pdf
2025-09-18 14:18:59,367 - INFO - 

14_po_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-18 14:18:59,367 - INFO - 

✓ Saved result: output/run1_14_po_4.json
2025-09-18 14:18:59,677 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b7efc171_14_po_4.pdf
2025-09-18 14:18:59,698 - INFO - 

14_po_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-18 14:18:59,698 - INFO - 

✓ Saved result: output/run1_14_po_2.json
2025-09-18 14:18:59,720 - INFO - Page 1: Extracted 874 characters, 60 lines from 57bf1293_14_po_5_b611af35_page_001.pdf
2025-09-18 14:18:59,720 - INFO - Successfully processed page 1
2025-09-18 14:19:00,008 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ec9936d8_14_po_2.pdf
2025-09-18 14:19:00,042 - INFO - 

14_po_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-18 14:19:00,042 - INFO - 

✓ Saved result: output/run1_14_po_3.json
2025-09-18 14:19:00,327 - INFO - Page 2: Extracted 2479 characters, 57 lines from 57bf1293_14_po_5_b611af35_page_002.pdf
2025-09-18 14:19:00,327 - INFO - Successfully processed page 2
2025-09-18 14:19:00,327 - INFO - Combined 2 pages into final text
2025-09-18 14:19:00,328 - INFO - Text validation for 57bf1293_14_po_5: 3389 characters, 2 pages
2025-09-18 14:19:00,328 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:19:00,328 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:19:00,347 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a1a3ea31_14_po_3.pdf
2025-09-18 14:19:02,358 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/57bf1293_14_po_5.pdf
2025-09-18 14:19:02,400 - INFO - 

14_po_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-18 14:19:02,400 - INFO - 

✓ Saved result: output/run1_14_po_5.json
2025-09-18 14:19:02,706 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/57bf1293_14_po_5.pdf
2025-09-18 14:19:02,708 - INFO - 
📊 Processing Summary:
2025-09-18 14:19:02,708 - INFO -    Total files: 5
2025-09-18 14:19:02,708 - INFO -    Successful: 5
2025-09-18 14:19:02,708 - INFO -    Failed: 0
2025-09-18 14:19:02,708 - INFO -    Duration: 17.62 seconds
2025-09-18 14:19:02,708 - INFO -    Output directory: output
2025-09-18 14:19:02,708 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:19:02,708 - INFO -    📄 14_po_1.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-18 14:19:02,709 - INFO -    📄 14_po_2.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-18 14:19:02,709 - INFO -    📄 14_po_3.pdf: {"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}
2025-09-18 14:19:02,709 - INFO -    📄 14_po_4.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-18 14:19:02,709 - INFO -    📄 14_po_5.pdf: {"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}
2025-09-18 14:19:02,709 - INFO - 
============================================================================================================================================
2025-09-18 14:19:02,710 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:19:02,710 - INFO - ============================================================================================================================================
2025-09-18 14:19:02,710 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:19:02,710 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:19:02,710 - INFO - 14_po_1.pdf                                        1      po                   run1_14_po_1.json                                 
2025-09-18 14:19:02,710 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/14_po/14_po_1.pdf
2025-09-18 14:19:02,710 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_1.json
2025-09-18 14:19:02,710 - INFO - 
2025-09-18 14:19:02,710 - INFO - 14_po_2.pdf                                        1      po                   run1_14_po_2.json                                 
2025-09-18 14:19:02,710 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/14_po/14_po_2.pdf
2025-09-18 14:19:02,710 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_2.json
2025-09-18 14:19:02,710 - INFO - 
2025-09-18 14:19:02,710 - INFO - 14_po_3.pdf                                        1      po                   run1_14_po_3.json                                 
2025-09-18 14:19:02,711 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/14_po/14_po_3.pdf
2025-09-18 14:19:02,711 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_3.json
2025-09-18 14:19:02,711 - INFO - 
2025-09-18 14:19:02,711 - INFO - 14_po_3.pdf                                        2      po                   run1_14_po_3.json                                 
2025-09-18 14:19:02,711 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/14_po/14_po_3.pdf
2025-09-18 14:19:02,711 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_3.json
2025-09-18 14:19:02,711 - INFO - 
2025-09-18 14:19:02,711 - INFO - 14_po_4.pdf                                        1      po                   run1_14_po_4.json                                 
2025-09-18 14:19:02,711 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/14_po/14_po_4.pdf
2025-09-18 14:19:02,711 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_4.json
2025-09-18 14:19:02,711 - INFO - 
2025-09-18 14:19:02,711 - INFO - 14_po_5.pdf                                        1      po                   run1_14_po_5.json                                 
2025-09-18 14:19:02,711 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/14_po/14_po_5.pdf
2025-09-18 14:19:02,711 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_5.json
2025-09-18 14:19:02,711 - INFO - 
2025-09-18 14:19:02,711 - INFO - 14_po_5.pdf                                        2      po                   run1_14_po_5.json                                 
2025-09-18 14:19:02,712 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/14_po/14_po_5.pdf
2025-09-18 14:19:02,712 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_14_po_5.json
2025-09-18 14:19:02,712 - INFO - 
2025-09-18 14:19:02,712 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:19:02,712 - INFO - Total entries: 7
2025-09-18 14:19:02,712 - INFO - ============================================================================================================================================
2025-09-18 14:19:02,712 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:19:02,712 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:19:02,712 - INFO -   1. 14_po_1.pdf                         Page 1   → po              | run1_14_po_1.json
2025-09-18 14:19:02,712 - INFO -   2. 14_po_2.pdf                         Page 1   → po              | run1_14_po_2.json
2025-09-18 14:19:02,712 - INFO -   3. 14_po_3.pdf                         Page 1   → po              | run1_14_po_3.json
2025-09-18 14:19:02,712 - INFO -   4. 14_po_3.pdf                         Page 2   → po              | run1_14_po_3.json
2025-09-18 14:19:02,712 - INFO -   5. 14_po_4.pdf                         Page 1   → po              | run1_14_po_4.json
2025-09-18 14:19:02,713 - INFO -   6. 14_po_5.pdf                         Page 1   → po              | run1_14_po_5.json
2025-09-18 14:19:02,713 - INFO -   7. 14_po_5.pdf                         Page 2   → po              | run1_14_po_5.json
2025-09-18 14:19:02,713 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:19:02,713 - INFO - 
✅ Test completed: {'total_files': 5, 'processed': 5, 'failed': 0, 'errors': [], 'duration_seconds': 17.615338, 'processed_files': [{'filename': '14_po_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': 'input_data_individual/14_po/14_po_1.pdf'}, {'filename': '14_po_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': 'input_data_individual/14_po/14_po_2.pdf'}, {'filename': '14_po_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}, {'page_no': 2, 'doc_type': 'po'}]}, 'file_path': 'input_data_individual/14_po/14_po_3.pdf'}, {'filename': '14_po_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': 'input_data_individual/14_po/14_po_4.pdf'}, {'filename': '14_po_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}, {'page_no': 2, 'doc_type': 'po'}]}, 'file_path': 'input_data_individual/14_po/14_po_5.pdf'}]}
