2025-09-18 14:20:16,331 - INFO - Logging initialized. Log file: logs/test_classification_20250918_142016.log
2025-09-18 14:20:16,331 - INFO - 📁 Found 10 files to process
2025-09-18 14:20:16,331 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:20:16,331 - INFO - 🚀 Processing 10 files in FORCED PARALLEL MODE...
2025-09-18 14:20:16,331 - INFO - 🚀 Creating 10 parallel tasks...
2025-09-18 14:20:16,331 - INFO - 🚀 All 10 tasks created - executing in parallel...
2025-09-18 14:20:16,331 - INFO - ⬆️ [14:20:16] Uploading: 13_pack_list_1.pdf
2025-09-18 14:20:18,976 - INFO - ✓ Uploaded: input_data_individual/13_pack_list/13_pack_list_1.pdf -> s3://document-extraction-logistically/temp/359df306_13_pack_list_1.pdf
2025-09-18 14:20:18,976 - INFO - 🔍 [14:20:18] Starting classification: 13_pack_list_1.pdf
2025-09-18 14:20:18,977 - INFO - ⬆️ [14:20:18] Uploading: 13_pack_list_10.pdf
2025-09-18 14:20:18,980 - INFO - Initializing TextractProcessor...
2025-09-18 14:20:19,005 - INFO - Initializing BedrockProcessor...
2025-09-18 14:20:19,010 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/359df306_13_pack_list_1.pdf
2025-09-18 14:20:19,011 - INFO - Processing PDF from S3...
2025-09-18 14:20:19,011 - INFO - Downloading PDF from S3 to /tmp/tmpx6ndqb6q/359df306_13_pack_list_1.pdf
2025-09-18 14:20:20,459 - INFO - ✓ Uploaded: input_data_individual/13_pack_list/13_pack_list_10.pdf -> s3://document-extraction-logistically/temp/6e6dfc22_13_pack_list_10.pdf
2025-09-18 14:20:20,459 - INFO - 🔍 [14:20:20] Starting classification: 13_pack_list_10.pdf
2025-09-18 14:20:20,460 - INFO - ⬆️ [14:20:20] Uploading: 13_pack_list_2.PDF
2025-09-18 14:20:20,461 - INFO - Initializing TextractProcessor...
2025-09-18 14:20:20,474 - INFO - Initializing BedrockProcessor...
2025-09-18 14:20:20,477 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6e6dfc22_13_pack_list_10.pdf
2025-09-18 14:20:20,478 - INFO - Processing PDF from S3...
2025-09-18 14:20:20,478 - INFO - Downloading PDF from S3 to /tmp/tmpz4b25us9/6e6dfc22_13_pack_list_10.pdf
2025-09-18 14:20:21,047 - INFO - ✓ Uploaded: input_data_individual/13_pack_list/13_pack_list_2.PDF -> s3://document-extraction-logistically/temp/e855179a_13_pack_list_2.PDF
2025-09-18 14:20:21,047 - INFO - 🔍 [14:20:21] Starting classification: 13_pack_list_2.PDF
2025-09-18 14:20:21,048 - INFO - ⬆️ [14:20:21] Uploading: 13_pack_list_3.pdf
2025-09-18 14:20:21,049 - INFO - Initializing TextractProcessor...
2025-09-18 14:20:21,067 - INFO - Initializing BedrockProcessor...
2025-09-18 14:20:21,072 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e855179a_13_pack_list_2.PDF
2025-09-18 14:20:21,072 - INFO - Processing PDF from S3...
2025-09-18 14:20:21,073 - INFO - Downloading PDF from S3 to /tmp/tmp7qt0u3ud/e855179a_13_pack_list_2.PDF
2025-09-18 14:20:21,135 - INFO - Splitting PDF into individual pages...
2025-09-18 14:20:21,137 - INFO - Splitting PDF 359df306_13_pack_list_1 into 2 pages
2025-09-18 14:20:21,150 - INFO - Split PDF into 2 pages
2025-09-18 14:20:21,150 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:20:21,150 - INFO - Expected pages: [1, 2]
2025-09-18 14:20:21,691 - INFO - ✓ Uploaded: input_data_individual/13_pack_list/13_pack_list_3.pdf -> s3://document-extraction-logistically/temp/828e6653_13_pack_list_3.pdf
2025-09-18 14:20:21,691 - INFO - 🔍 [14:20:21] Starting classification: 13_pack_list_3.pdf
2025-09-18 14:20:21,692 - INFO - Initializing TextractProcessor...
2025-09-18 14:20:21,696 - INFO - ⬆️ [14:20:21] Uploading: 13_pack_list_4.pdf
2025-09-18 14:20:21,708 - INFO - Initializing BedrockProcessor...
2025-09-18 14:20:21,712 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/828e6653_13_pack_list_3.pdf
2025-09-18 14:20:21,712 - INFO - Processing PDF from S3...
2025-09-18 14:20:21,712 - INFO - Downloading PDF from S3 to /tmp/tmplldf1gir/828e6653_13_pack_list_3.pdf
2025-09-18 14:20:22,283 - INFO - ✓ Uploaded: input_data_individual/13_pack_list/13_pack_list_4.pdf -> s3://document-extraction-logistically/temp/6007a4d5_13_pack_list_4.pdf
2025-09-18 14:20:22,283 - INFO - 🔍 [14:20:22] Starting classification: 13_pack_list_4.pdf
2025-09-18 14:20:22,284 - INFO - ⬆️ [14:20:22] Uploading: 13_pack_list_5.pdf
2025-09-18 14:20:22,284 - INFO - Initializing TextractProcessor...
2025-09-18 14:20:22,307 - INFO - Initializing BedrockProcessor...
2025-09-18 14:20:22,311 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6007a4d5_13_pack_list_4.pdf
2025-09-18 14:20:22,311 - INFO - Processing PDF from S3...
2025-09-18 14:20:22,311 - INFO - Downloading PDF from S3 to /tmp/tmplftb_ac4/6007a4d5_13_pack_list_4.pdf
2025-09-18 14:20:22,894 - INFO - ✓ Uploaded: input_data_individual/13_pack_list/13_pack_list_5.pdf -> s3://document-extraction-logistically/temp/a12c123c_13_pack_list_5.pdf
2025-09-18 14:20:22,894 - INFO - 🔍 [14:20:22] Starting classification: 13_pack_list_5.pdf
2025-09-18 14:20:22,895 - INFO - ⬆️ [14:20:22] Uploading: 13_pack_list_6.pdf
2025-09-18 14:20:22,896 - INFO - Initializing TextractProcessor...
2025-09-18 14:20:22,912 - INFO - Initializing BedrockProcessor...
2025-09-18 14:20:22,916 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a12c123c_13_pack_list_5.pdf
2025-09-18 14:20:22,916 - INFO - Processing PDF from S3...
2025-09-18 14:20:22,916 - INFO - Downloading PDF from S3 to /tmp/tmpt75imc9o/a12c123c_13_pack_list_5.pdf
2025-09-18 14:20:23,095 - INFO - Splitting PDF into individual pages...
2025-09-18 14:20:23,097 - INFO - Splitting PDF e855179a_13_pack_list_2 into 1 pages
2025-09-18 14:20:23,102 - INFO - Split PDF into 1 pages
2025-09-18 14:20:23,102 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:20:23,102 - INFO - Expected pages: [1]
2025-09-18 14:20:23,499 - INFO - Splitting PDF into individual pages...
2025-09-18 14:20:23,499 - INFO - Splitting PDF 6e6dfc22_13_pack_list_10 into 1 pages
2025-09-18 14:20:23,500 - INFO - Split PDF into 1 pages
2025-09-18 14:20:23,500 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:20:23,500 - INFO - Expected pages: [1]
2025-09-18 14:20:23,526 - INFO - ✓ Uploaded: input_data_individual/13_pack_list/13_pack_list_6.pdf -> s3://document-extraction-logistically/temp/d437cab6_13_pack_list_6.pdf
2025-09-18 14:20:23,526 - INFO - 🔍 [14:20:23] Starting classification: 13_pack_list_6.pdf
2025-09-18 14:20:23,527 - INFO - ⬆️ [14:20:23] Uploading: 13_pack_list_7.pdf
2025-09-18 14:20:23,527 - INFO - Initializing TextractProcessor...
2025-09-18 14:20:23,568 - INFO - Initializing BedrockProcessor...
2025-09-18 14:20:23,571 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d437cab6_13_pack_list_6.pdf
2025-09-18 14:20:23,572 - INFO - Processing PDF from S3...
2025-09-18 14:20:23,572 - INFO - Downloading PDF from S3 to /tmp/tmpu612nqvh/d437cab6_13_pack_list_6.pdf
2025-09-18 14:20:23,867 - INFO - Splitting PDF into individual pages...
2025-09-18 14:20:23,868 - INFO - Splitting PDF 6007a4d5_13_pack_list_4 into 1 pages
2025-09-18 14:20:23,869 - INFO - Split PDF into 1 pages
2025-09-18 14:20:23,869 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:20:23,869 - INFO - Expected pages: [1]
2025-09-18 14:20:24,467 - INFO - ✓ Uploaded: input_data_individual/13_pack_list/13_pack_list_7.pdf -> s3://document-extraction-logistically/temp/f0031fda_13_pack_list_7.pdf
2025-09-18 14:20:24,468 - INFO - 🔍 [14:20:24] Starting classification: 13_pack_list_7.pdf
2025-09-18 14:20:24,469 - INFO - ⬆️ [14:20:24] Uploading: 13_pack_list_8.pdf
2025-09-18 14:20:24,470 - INFO - Initializing TextractProcessor...
2025-09-18 14:20:24,485 - INFO - Initializing BedrockProcessor...
2025-09-18 14:20:24,492 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f0031fda_13_pack_list_7.pdf
2025-09-18 14:20:24,492 - INFO - Processing PDF from S3...
2025-09-18 14:20:24,492 - INFO - Downloading PDF from S3 to /tmp/tmp7bmlmqtg/f0031fda_13_pack_list_7.pdf
2025-09-18 14:20:25,265 - INFO - Splitting PDF into individual pages...
2025-09-18 14:20:25,267 - INFO - Splitting PDF a12c123c_13_pack_list_5 into 1 pages
2025-09-18 14:20:25,270 - INFO - Split PDF into 1 pages
2025-09-18 14:20:25,270 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:20:25,270 - INFO - Expected pages: [1]
2025-09-18 14:20:25,701 - INFO - Splitting PDF into individual pages...
2025-09-18 14:20:25,702 - INFO - Splitting PDF d437cab6_13_pack_list_6 into 1 pages
2025-09-18 14:20:25,704 - INFO - Split PDF into 1 pages
2025-09-18 14:20:25,705 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:20:25,705 - INFO - Expected pages: [1]
2025-09-18 14:20:26,204 - INFO - Page 2: Extracted 276 characters, 20 lines from 359df306_13_pack_list_1_d9763038_page_002.pdf
2025-09-18 14:20:26,205 - INFO - Successfully processed page 2
2025-09-18 14:20:26,577 - INFO - Page 1: Extracted 1156 characters, 65 lines from 359df306_13_pack_list_1_d9763038_page_001.pdf
2025-09-18 14:20:26,577 - INFO - Successfully processed page 1
2025-09-18 14:20:26,577 - INFO - Combined 2 pages into final text
2025-09-18 14:20:26,577 - INFO - Text validation for 359df306_13_pack_list_1: 1468 characters, 2 pages
2025-09-18 14:20:26,578 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:20:26,578 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:20:26,623 - INFO - Splitting PDF into individual pages...
2025-09-18 14:20:26,624 - INFO - Splitting PDF 828e6653_13_pack_list_3 into 1 pages
2025-09-18 14:20:26,628 - INFO - Split PDF into 1 pages
2025-09-18 14:20:26,628 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:20:26,628 - INFO - Expected pages: [1]
2025-09-18 14:20:26,968 - INFO - Splitting PDF into individual pages...
2025-09-18 14:20:26,969 - INFO - Splitting PDF f0031fda_13_pack_list_7 into 1 pages
2025-09-18 14:20:26,971 - INFO - Split PDF into 1 pages
2025-09-18 14:20:26,971 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:20:26,971 - INFO - Expected pages: [1]
2025-09-18 14:20:27,639 - INFO - ✓ Uploaded: input_data_individual/13_pack_list/13_pack_list_8.pdf -> s3://document-extraction-logistically/temp/79cc93f2_13_pack_list_8.pdf
2025-09-18 14:20:27,639 - INFO - 🔍 [14:20:27] Starting classification: 13_pack_list_8.pdf
2025-09-18 14:20:27,640 - INFO - ⬆️ [14:20:27] Uploading: 13_pack_list_9.pdf
2025-09-18 14:20:27,642 - INFO - Initializing TextractProcessor...
2025-09-18 14:20:27,659 - INFO - Initializing BedrockProcessor...
2025-09-18 14:20:27,663 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/79cc93f2_13_pack_list_8.pdf
2025-09-18 14:20:27,664 - INFO - Processing PDF from S3...
2025-09-18 14:20:27,664 - INFO - Downloading PDF from S3 to /tmp/tmpshpume0y/79cc93f2_13_pack_list_8.pdf
2025-09-18 14:20:28,068 - INFO - Page 1: Extracted 756 characters, 60 lines from 6007a4d5_13_pack_list_4_0f2660d3_page_001.pdf
2025-09-18 14:20:28,069 - INFO - Successfully processed page 1
2025-09-18 14:20:28,069 - INFO - Combined 1 pages into final text
2025-09-18 14:20:28,069 - INFO - Text validation for 6007a4d5_13_pack_list_4: 773 characters, 1 pages
2025-09-18 14:20:28,069 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:20:28,069 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:20:28,261 - INFO - ✓ Uploaded: input_data_individual/13_pack_list/13_pack_list_9.pdf -> s3://document-extraction-logistically/temp/5e7a8f1f_13_pack_list_9.pdf
2025-09-18 14:20:28,262 - INFO - 🔍 [14:20:28] Starting classification: 13_pack_list_9.pdf
2025-09-18 14:20:28,263 - INFO - Initializing TextractProcessor...
2025-09-18 14:20:28,274 - INFO - Initializing BedrockProcessor...
2025-09-18 14:20:28,280 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5e7a8f1f_13_pack_list_9.pdf
2025-09-18 14:20:28,280 - INFO - Processing PDF from S3...
2025-09-18 14:20:28,281 - INFO - Downloading PDF from S3 to /tmp/tmp6g40j3fi/5e7a8f1f_13_pack_list_9.pdf
2025-09-18 14:20:28,628 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/359df306_13_pack_list_1.pdf
2025-09-18 14:20:28,666 - INFO - 

13_pack_list_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        },
        {
            "page_no": 2,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 14:20:28,666 - INFO - 

✓ Saved result: output/run1_13_pack_list_1.json
2025-09-18 14:20:28,958 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/359df306_13_pack_list_1.pdf
2025-09-18 14:20:29,127 - INFO - Page 1: Extracted 1069 characters, 112 lines from 6e6dfc22_13_pack_list_10_f8119bd3_page_001.pdf
2025-09-18 14:20:29,127 - INFO - Successfully processed page 1
2025-09-18 14:20:29,127 - INFO - Combined 1 pages into final text
2025-09-18 14:20:29,127 - INFO - Text validation for 6e6dfc22_13_pack_list_10: 1086 characters, 1 pages
2025-09-18 14:20:29,127 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:20:29,127 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:20:29,800 - INFO - Page 1: Extracted 1774 characters, 128 lines from e855179a_13_pack_list_2_9fbfd109_page_001.pdf
2025-09-18 14:20:29,800 - INFO - Successfully processed page 1
2025-09-18 14:20:29,802 - INFO - Combined 1 pages into final text
2025-09-18 14:20:29,802 - INFO - Text validation for e855179a_13_pack_list_2: 1791 characters, 1 pages
2025-09-18 14:20:29,803 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:20:29,803 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:20:29,971 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6007a4d5_13_pack_list_4.pdf
2025-09-18 14:20:29,986 - INFO - 

13_pack_list_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 14:20:29,986 - INFO - 

✓ Saved result: output/run1_13_pack_list_4.json
2025-09-18 14:20:30,165 - INFO - Splitting PDF into individual pages...
2025-09-18 14:20:30,166 - INFO - Splitting PDF 5e7a8f1f_13_pack_list_9 into 1 pages
2025-09-18 14:20:30,167 - INFO - Split PDF into 1 pages
2025-09-18 14:20:30,167 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:20:30,167 - INFO - Expected pages: [1]
2025-09-18 14:20:30,284 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6007a4d5_13_pack_list_4.pdf
2025-09-18 14:20:30,929 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6e6dfc22_13_pack_list_10.pdf
2025-09-18 14:20:30,966 - INFO - 

13_pack_list_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 14:20:30,967 - INFO - 

✓ Saved result: output/run1_13_pack_list_10.json
2025-09-18 14:20:31,091 - INFO - Page 1: Extracted 1006 characters, 110 lines from d437cab6_13_pack_list_6_51f636d0_page_001.pdf
2025-09-18 14:20:31,092 - INFO - Successfully processed page 1
2025-09-18 14:20:31,092 - INFO - Combined 1 pages into final text
2025-09-18 14:20:31,092 - INFO - Text validation for d437cab6_13_pack_list_6: 1023 characters, 1 pages
2025-09-18 14:20:31,093 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:20:31,093 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:20:31,136 - INFO - Page 1: Extracted 1318 characters, 141 lines from 828e6653_13_pack_list_3_b32666e5_page_001.pdf
2025-09-18 14:20:31,137 - INFO - Successfully processed page 1
2025-09-18 14:20:31,137 - INFO - Combined 1 pages into final text
2025-09-18 14:20:31,137 - INFO - Text validation for 828e6653_13_pack_list_3: 1335 characters, 1 pages
2025-09-18 14:20:31,137 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:20:31,137 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:20:31,202 - INFO - Page 1: Extracted 1111 characters, 82 lines from a12c123c_13_pack_list_5_7b9a6538_page_001.pdf
2025-09-18 14:20:31,202 - INFO - Successfully processed page 1
2025-09-18 14:20:31,203 - INFO - Combined 1 pages into final text
2025-09-18 14:20:31,203 - INFO - Text validation for a12c123c_13_pack_list_5: 1128 characters, 1 pages
2025-09-18 14:20:31,203 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:20:31,203 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:20:31,264 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6e6dfc22_13_pack_list_10.pdf
2025-09-18 14:20:31,945 - INFO - Splitting PDF into individual pages...
2025-09-18 14:20:31,947 - INFO - Splitting PDF 79cc93f2_13_pack_list_8 into 1 pages
2025-09-18 14:20:31,953 - INFO - Split PDF into 1 pages
2025-09-18 14:20:31,954 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:20:31,954 - INFO - Expected pages: [1]
2025-09-18 14:20:32,499 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e855179a_13_pack_list_2.PDF
2025-09-18 14:20:32,529 - INFO - 

13_pack_list_2.PDF

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 14:20:32,529 - INFO - 

✓ Saved result: output/run1_13_pack_list_2.json
2025-09-18 14:20:32,826 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e855179a_13_pack_list_2.PDF
2025-09-18 14:20:32,929 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d437cab6_13_pack_list_6.pdf
2025-09-18 14:20:32,961 - INFO - 

13_pack_list_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 14:20:32,961 - INFO - 

✓ Saved result: output/run1_13_pack_list_6.json
2025-09-18 14:20:33,252 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d437cab6_13_pack_list_6.pdf
2025-09-18 14:20:33,275 - INFO - Page 1: Extracted 713 characters, 78 lines from f0031fda_13_pack_list_7_5b35640a_page_001.pdf
2025-09-18 14:20:33,276 - INFO - Successfully processed page 1
2025-09-18 14:20:33,276 - INFO - Combined 1 pages into final text
2025-09-18 14:20:33,277 - INFO - Text validation for f0031fda_13_pack_list_7: 730 characters, 1 pages
2025-09-18 14:20:33,277 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:20:33,277 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:20:33,425 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a12c123c_13_pack_list_5.pdf
2025-09-18 14:20:33,458 - INFO - 

13_pack_list_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 14:20:33,458 - INFO - 

✓ Saved result: output/run1_13_pack_list_5.json
2025-09-18 14:20:33,461 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/828e6653_13_pack_list_3.pdf
2025-09-18 14:20:33,751 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a12c123c_13_pack_list_5.pdf
2025-09-18 14:20:33,782 - INFO - 

13_pack_list_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 14:20:33,782 - INFO - 

✓ Saved result: output/run1_13_pack_list_3.json
2025-09-18 14:20:34,077 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/828e6653_13_pack_list_3.pdf
2025-09-18 14:20:34,926 - INFO - Page 1: Extracted 747 characters, 87 lines from 5e7a8f1f_13_pack_list_9_b49c5de0_page_001.pdf
2025-09-18 14:20:34,927 - INFO - Successfully processed page 1
2025-09-18 14:20:34,927 - INFO - Combined 1 pages into final text
2025-09-18 14:20:34,927 - INFO - Text validation for 5e7a8f1f_13_pack_list_9: 764 characters, 1 pages
2025-09-18 14:20:34,927 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:20:34,927 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:20:35,086 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f0031fda_13_pack_list_7.pdf
2025-09-18 14:20:35,109 - INFO - 

13_pack_list_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 14:20:35,109 - INFO - 

✓ Saved result: output/run1_13_pack_list_7.json
2025-09-18 14:20:35,403 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f0031fda_13_pack_list_7.pdf
2025-09-18 14:20:37,661 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5e7a8f1f_13_pack_list_9.pdf
2025-09-18 14:20:37,714 - INFO - 

13_pack_list_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 14:20:37,714 - INFO - 

✓ Saved result: output/run1_13_pack_list_9.json
2025-09-18 14:20:38,003 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5e7a8f1f_13_pack_list_9.pdf
2025-09-18 14:20:39,535 - INFO - Page 1: Extracted 377 characters, 31 lines from 79cc93f2_13_pack_list_8_dda91c87_page_001.pdf
2025-09-18 14:20:39,536 - INFO - Successfully processed page 1
2025-09-18 14:20:39,536 - INFO - Combined 1 pages into final text
2025-09-18 14:20:39,536 - INFO - Text validation for 79cc93f2_13_pack_list_8: 394 characters, 1 pages
2025-09-18 14:20:39,538 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:20:39,538 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:20:41,631 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/79cc93f2_13_pack_list_8.pdf
2025-09-18 14:20:41,649 - INFO - 

13_pack_list_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pack_list"
        }
    ]
}
2025-09-18 14:20:41,649 - INFO - 

✓ Saved result: output/run1_13_pack_list_8.json
2025-09-18 14:20:41,937 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/79cc93f2_13_pack_list_8.pdf
2025-09-18 14:20:41,938 - INFO - 
📊 Processing Summary:
2025-09-18 14:20:41,938 - INFO -    Total files: 10
2025-09-18 14:20:41,938 - INFO -    Successful: 10
2025-09-18 14:20:41,938 - INFO -    Failed: 0
2025-09-18 14:20:41,938 - INFO -    Duration: 25.61 seconds
2025-09-18 14:20:41,938 - INFO -    Output directory: output
2025-09-18 14:20:41,938 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:20:41,938 - INFO -    📄 13_pack_list_1.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"},{"page_no":2,"doc_type":"pack_list"}]}
2025-09-18 14:20:41,938 - INFO -    📄 13_pack_list_10.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-18 14:20:41,939 - INFO -    📄 13_pack_list_2.PDF: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-18 14:20:41,939 - INFO -    📄 13_pack_list_3.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-18 14:20:41,939 - INFO -    📄 13_pack_list_4.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-18 14:20:41,939 - INFO -    📄 13_pack_list_5.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-18 14:20:41,939 - INFO -    📄 13_pack_list_6.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-18 14:20:41,939 - INFO -    📄 13_pack_list_7.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-18 14:20:41,939 - INFO -    📄 13_pack_list_8.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-18 14:20:41,939 - INFO -    📄 13_pack_list_9.pdf: {"documents":[{"page_no":1,"doc_type":"pack_list"}]}
2025-09-18 14:20:41,940 - INFO - 
============================================================================================================================================
2025-09-18 14:20:41,940 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:20:41,940 - INFO - ============================================================================================================================================
2025-09-18 14:20:41,940 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:20:41,940 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:20:41,940 - INFO - 13_pack_list_1.pdf                                 1      pack_list            run1_13_pack_list_1.json                          
2025-09-18 14:20:41,940 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/13_pack_list/13_pack_list_1.pdf
2025-09-18 14:20:41,940 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_1.json
2025-09-18 14:20:41,940 - INFO - 
2025-09-18 14:20:41,940 - INFO - 13_pack_list_1.pdf                                 2      pack_list            run1_13_pack_list_1.json                          
2025-09-18 14:20:41,940 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/13_pack_list/13_pack_list_1.pdf
2025-09-18 14:20:41,940 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_1.json
2025-09-18 14:20:41,940 - INFO - 
2025-09-18 14:20:41,940 - INFO - 13_pack_list_10.pdf                                1      pack_list            run1_13_pack_list_10.json                         
2025-09-18 14:20:41,940 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/13_pack_list/13_pack_list_10.pdf
2025-09-18 14:20:41,940 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_10.json
2025-09-18 14:20:41,940 - INFO - 
2025-09-18 14:20:41,941 - INFO - 13_pack_list_2.PDF                                 1      pack_list            run1_13_pack_list_2.json                          
2025-09-18 14:20:41,941 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/13_pack_list/13_pack_list_2.PDF
2025-09-18 14:20:41,941 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_2.json
2025-09-18 14:20:41,941 - INFO - 
2025-09-18 14:20:41,941 - INFO - 13_pack_list_3.pdf                                 1      pack_list            run1_13_pack_list_3.json                          
2025-09-18 14:20:41,941 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/13_pack_list/13_pack_list_3.pdf
2025-09-18 14:20:41,941 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_3.json
2025-09-18 14:20:41,941 - INFO - 
2025-09-18 14:20:41,941 - INFO - 13_pack_list_4.pdf                                 1      pack_list            run1_13_pack_list_4.json                          
2025-09-18 14:20:41,941 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/13_pack_list/13_pack_list_4.pdf
2025-09-18 14:20:41,941 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_4.json
2025-09-18 14:20:41,941 - INFO - 
2025-09-18 14:20:41,941 - INFO - 13_pack_list_5.pdf                                 1      pack_list            run1_13_pack_list_5.json                          
2025-09-18 14:20:41,941 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/13_pack_list/13_pack_list_5.pdf
2025-09-18 14:20:41,941 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_5.json
2025-09-18 14:20:41,941 - INFO - 
2025-09-18 14:20:41,941 - INFO - 13_pack_list_6.pdf                                 1      pack_list            run1_13_pack_list_6.json                          
2025-09-18 14:20:41,941 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/13_pack_list/13_pack_list_6.pdf
2025-09-18 14:20:41,941 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_6.json
2025-09-18 14:20:41,941 - INFO - 
2025-09-18 14:20:41,942 - INFO - 13_pack_list_7.pdf                                 1      pack_list            run1_13_pack_list_7.json                          
2025-09-18 14:20:41,942 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/13_pack_list/13_pack_list_7.pdf
2025-09-18 14:20:41,942 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_7.json
2025-09-18 14:20:41,942 - INFO - 
2025-09-18 14:20:41,942 - INFO - 13_pack_list_8.pdf                                 1      pack_list            run1_13_pack_list_8.json                          
2025-09-18 14:20:41,942 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/13_pack_list/13_pack_list_8.pdf
2025-09-18 14:20:41,942 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_8.json
2025-09-18 14:20:41,942 - INFO - 
2025-09-18 14:20:41,942 - INFO - 13_pack_list_9.pdf                                 1      pack_list            run1_13_pack_list_9.json                          
2025-09-18 14:20:41,942 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/13_pack_list/13_pack_list_9.pdf
2025-09-18 14:20:41,942 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_13_pack_list_9.json
2025-09-18 14:20:41,942 - INFO - 
2025-09-18 14:20:41,942 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:20:41,942 - INFO - Total entries: 11
2025-09-18 14:20:41,942 - INFO - ============================================================================================================================================
2025-09-18 14:20:41,942 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:20:41,942 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:20:41,942 - INFO -   1. 13_pack_list_1.pdf                  Page 1   → pack_list       | run1_13_pack_list_1.json
2025-09-18 14:20:41,942 - INFO -   2. 13_pack_list_1.pdf                  Page 2   → pack_list       | run1_13_pack_list_1.json
2025-09-18 14:20:41,942 - INFO -   3. 13_pack_list_10.pdf                 Page 1   → pack_list       | run1_13_pack_list_10.json
2025-09-18 14:20:41,942 - INFO -   4. 13_pack_list_2.PDF                  Page 1   → pack_list       | run1_13_pack_list_2.json
2025-09-18 14:20:41,942 - INFO -   5. 13_pack_list_3.pdf                  Page 1   → pack_list       | run1_13_pack_list_3.json
2025-09-18 14:20:41,943 - INFO -   6. 13_pack_list_4.pdf                  Page 1   → pack_list       | run1_13_pack_list_4.json
2025-09-18 14:20:41,943 - INFO -   7. 13_pack_list_5.pdf                  Page 1   → pack_list       | run1_13_pack_list_5.json
2025-09-18 14:20:41,943 - INFO -   8. 13_pack_list_6.pdf                  Page 1   → pack_list       | run1_13_pack_list_6.json
2025-09-18 14:20:41,943 - INFO -   9. 13_pack_list_7.pdf                  Page 1   → pack_list       | run1_13_pack_list_7.json
2025-09-18 14:20:41,943 - INFO -  10. 13_pack_list_8.pdf                  Page 1   → pack_list       | run1_13_pack_list_8.json
2025-09-18 14:20:41,943 - INFO -  11. 13_pack_list_9.pdf                  Page 1   → pack_list       | run1_13_pack_list_9.json
2025-09-18 14:20:41,943 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:20:41,943 - INFO - 
✅ Test completed: {'total_files': 10, 'processed': 10, 'failed': 0, 'errors': [], 'duration_seconds': 25.60671, 'processed_files': [{'filename': '13_pack_list_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}, {'page_no': 2, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_individual/13_pack_list/13_pack_list_1.pdf'}, {'filename': '13_pack_list_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_individual/13_pack_list/13_pack_list_10.pdf'}, {'filename': '13_pack_list_2.PDF', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_individual/13_pack_list/13_pack_list_2.PDF'}, {'filename': '13_pack_list_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_individual/13_pack_list/13_pack_list_3.pdf'}, {'filename': '13_pack_list_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_individual/13_pack_list/13_pack_list_4.pdf'}, {'filename': '13_pack_list_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_individual/13_pack_list/13_pack_list_5.pdf'}, {'filename': '13_pack_list_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_individual/13_pack_list/13_pack_list_6.pdf'}, {'filename': '13_pack_list_7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_individual/13_pack_list/13_pack_list_7.pdf'}, {'filename': '13_pack_list_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_individual/13_pack_list/13_pack_list_8.pdf'}, {'filename': '13_pack_list_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pack_list'}]}, 'file_path': 'input_data_individual/13_pack_list/13_pack_list_9.pdf'}]}
