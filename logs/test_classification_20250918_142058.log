2025-09-18 14:20:58,623 - INFO - Logging initialized. Log file: logs/test_classification_20250918_142058.log
2025-09-18 14:20:58,623 - INFO - 📁 Found 11 files to process
2025-09-18 14:20:58,623 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:20:58,623 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 14:20:58,623 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 14:20:58,623 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 14:20:58,623 - INFO - ⬆️ [14:20:58] Uploading: 9_fuel_receipt_1.png
2025-09-18 14:21:00,546 - INFO - ✓ Uploaded: input_data_individual/9_fuel_receipt/9_fuel_receipt_1.png -> s3://document-extraction-logistically/temp/757947e1_9_fuel_receipt_1.png
2025-09-18 14:21:00,546 - INFO - 🔍 [14:21:00] Starting classification: 9_fuel_receipt_1.png
2025-09-18 14:21:00,547 - INFO - ⬆️ [14:21:00] Uploading: 9_fuel_receipt_10.jpeg
2025-09-18 14:21:00,548 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:00,575 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:00,579 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/757947e1_9_fuel_receipt_1.png
2025-09-18 14:21:00,580 - INFO - Processing image from S3...
2025-09-18 14:21:03,280 - INFO - S3 Image temp/757947e1_9_fuel_receipt_1.png: Extracted 517 characters, 46 lines
2025-09-18 14:21:03,280 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:21:03,280 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:21:03,452 - INFO - ✓ Uploaded: input_data_individual/9_fuel_receipt/9_fuel_receipt_10.jpeg -> s3://document-extraction-logistically/temp/508cb8c8_9_fuel_receipt_10.jpeg
2025-09-18 14:21:03,453 - INFO - 🔍 [14:21:03] Starting classification: 9_fuel_receipt_10.jpeg
2025-09-18 14:21:03,454 - INFO - ⬆️ [14:21:03] Uploading: 9_fuel_receipt_11.pdf
2025-09-18 14:21:03,454 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:03,470 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:03,474 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/508cb8c8_9_fuel_receipt_10.jpeg
2025-09-18 14:21:03,474 - INFO - Processing image from S3...
2025-09-18 14:21:05,572 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/757947e1_9_fuel_receipt_1.png
2025-09-18 14:21:05,588 - INFO - ✓ Uploaded: input_data_individual/9_fuel_receipt/9_fuel_receipt_11.pdf -> s3://document-extraction-logistically/temp/81cdb055_9_fuel_receipt_11.pdf
2025-09-18 14:21:05,589 - INFO - 🔍 [14:21:05] Starting classification: 9_fuel_receipt_11.pdf
2025-09-18 14:21:05,589 - INFO - ⬆️ [14:21:05] Uploading: 9_fuel_receipt_2.png
2025-09-18 14:21:05,591 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:05,618 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:05,622 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/81cdb055_9_fuel_receipt_11.pdf
2025-09-18 14:21:05,623 - INFO - Processing PDF from S3...
2025-09-18 14:21:05,623 - INFO - Downloading PDF from S3 to /tmp/tmp1ivswy3j/81cdb055_9_fuel_receipt_11.pdf
2025-09-18 14:21:06,243 - INFO - ✓ Uploaded: input_data_individual/9_fuel_receipt/9_fuel_receipt_2.png -> s3://document-extraction-logistically/temp/07c56845_9_fuel_receipt_2.png
2025-09-18 14:21:06,244 - INFO - 🔍 [14:21:06] Starting classification: 9_fuel_receipt_2.png
2025-09-18 14:21:06,244 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:06,245 - INFO - ⬆️ [14:21:06] Uploading: 9_fuel_receipt_3.jpg
2025-09-18 14:21:06,257 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:06,264 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/07c56845_9_fuel_receipt_2.png
2025-09-18 14:21:06,264 - INFO - Processing image from S3...
2025-09-18 14:21:09,111 - INFO - ✓ Uploaded: input_data_individual/9_fuel_receipt/9_fuel_receipt_3.jpg -> s3://document-extraction-logistically/temp/a30d14bd_9_fuel_receipt_3.jpg
2025-09-18 14:21:09,111 - INFO - 🔍 [14:21:09] Starting classification: 9_fuel_receipt_3.jpg
2025-09-18 14:21:09,112 - INFO - ⬆️ [14:21:09] Uploading: 9_fuel_receipt_4.jpg
2025-09-18 14:21:09,114 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:09,133 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:09,137 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a30d14bd_9_fuel_receipt_3.jpg
2025-09-18 14:21:09,137 - INFO - Processing image from S3...
2025-09-18 14:21:09,884 - INFO - Splitting PDF into individual pages...
2025-09-18 14:21:09,885 - INFO - Splitting PDF 81cdb055_9_fuel_receipt_11 into 1 pages
2025-09-18 14:21:09,894 - INFO - Split PDF into 1 pages
2025-09-18 14:21:09,895 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:21:09,895 - INFO - Expected pages: [1]
2025-09-18 14:21:10,750 - INFO - S3 Image temp/508cb8c8_9_fuel_receipt_10.jpeg: Extracted 318 characters, 28 lines
2025-09-18 14:21:10,750 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:21:10,750 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:21:11,217 - INFO - S3 Image temp/07c56845_9_fuel_receipt_2.png: Extracted 562 characters, 54 lines
2025-09-18 14:21:11,217 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:21:11,217 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:21:11,564 - INFO - ✓ Uploaded: input_data_individual/9_fuel_receipt/9_fuel_receipt_4.jpg -> s3://document-extraction-logistically/temp/970b9b3f_9_fuel_receipt_4.jpg
2025-09-18 14:21:11,565 - INFO - 🔍 [14:21:11] Starting classification: 9_fuel_receipt_4.jpg
2025-09-18 14:21:11,566 - INFO - ⬆️ [14:21:11] Uploading: 9_fuel_receipt_5.jpg
2025-09-18 14:21:11,567 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:11,582 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:11,584 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/970b9b3f_9_fuel_receipt_4.jpg
2025-09-18 14:21:11,585 - INFO - Processing image from S3...
2025-09-18 14:21:12,553 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/508cb8c8_9_fuel_receipt_10.jpeg
2025-09-18 14:21:13,080 - INFO - S3 Image temp/a30d14bd_9_fuel_receipt_3.jpg: Extracted 501 characters, 35 lines
2025-09-18 14:21:13,080 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:21:13,080 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:21:13,444 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/07c56845_9_fuel_receipt_2.png
2025-09-18 14:21:13,913 - INFO - ✓ Uploaded: input_data_individual/9_fuel_receipt/9_fuel_receipt_5.jpg -> s3://document-extraction-logistically/temp/c0fcc233_9_fuel_receipt_5.jpg
2025-09-18 14:21:13,913 - INFO - 🔍 [14:21:13] Starting classification: 9_fuel_receipt_5.jpg
2025-09-18 14:21:13,914 - INFO - ⬆️ [14:21:13] Uploading: 9_fuel_receipt_6.jpg
2025-09-18 14:21:13,916 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:13,931 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:13,968 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c0fcc233_9_fuel_receipt_5.jpg
2025-09-18 14:21:13,968 - INFO - Processing image from S3...
2025-09-18 14:21:14,905 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a30d14bd_9_fuel_receipt_3.jpg
2025-09-18 14:21:15,031 - INFO - S3 Image temp/970b9b3f_9_fuel_receipt_4.jpg: Extracted 534 characters, 41 lines
2025-09-18 14:21:15,032 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:21:15,032 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:21:17,002 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/970b9b3f_9_fuel_receipt_4.jpg
2025-09-18 14:21:17,511 - INFO - S3 Image temp/c0fcc233_9_fuel_receipt_5.jpg: Extracted 811 characters, 51 lines
2025-09-18 14:21:17,511 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:21:17,511 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:21:17,967 - INFO - ✓ Uploaded: input_data_individual/9_fuel_receipt/9_fuel_receipt_6.jpg -> s3://document-extraction-logistically/temp/6db80e85_9_fuel_receipt_6.jpg
2025-09-18 14:21:17,967 - INFO - 🔍 [14:21:17] Starting classification: 9_fuel_receipt_6.jpg
2025-09-18 14:21:17,968 - INFO - ⬆️ [14:21:17] Uploading: 9_fuel_receipt_7.jpeg
2025-09-18 14:21:17,971 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:17,993 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:17,998 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6db80e85_9_fuel_receipt_6.jpg
2025-09-18 14:21:17,999 - INFO - Processing image from S3...
2025-09-18 14:21:18,767 - INFO - Page 1: Extracted 402 characters, 39 lines from 81cdb055_9_fuel_receipt_11_83e70707_page_001.pdf
2025-09-18 14:21:18,768 - INFO - Successfully processed page 1
2025-09-18 14:21:18,768 - INFO - Combined 1 pages into final text
2025-09-18 14:21:18,768 - INFO - Text validation for 81cdb055_9_fuel_receipt_11: 419 characters, 1 pages
2025-09-18 14:21:18,770 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:21:18,770 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:21:19,635 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c0fcc233_9_fuel_receipt_5.jpg
2025-09-18 14:21:20,409 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/81cdb055_9_fuel_receipt_11.pdf
2025-09-18 14:21:21,408 - INFO - ✓ Uploaded: input_data_individual/9_fuel_receipt/9_fuel_receipt_7.jpeg -> s3://document-extraction-logistically/temp/6f84522b_9_fuel_receipt_7.jpeg
2025-09-18 14:21:21,409 - INFO - 🔍 [14:21:21] Starting classification: 9_fuel_receipt_7.jpeg
2025-09-18 14:21:21,410 - INFO - ⬆️ [14:21:21] Uploading: 9_fuel_receipt_8.jpg
2025-09-18 14:21:21,413 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:21,439 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:21,444 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6f84522b_9_fuel_receipt_7.jpeg
2025-09-18 14:21:21,444 - INFO - Processing image from S3...
2025-09-18 14:21:21,849 - INFO - S3 Image temp/6db80e85_9_fuel_receipt_6.jpg: Extracted 738 characters, 41 lines
2025-09-18 14:21:21,849 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:21:21,849 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:21:24,478 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6db80e85_9_fuel_receipt_6.jpg
2025-09-18 14:21:24,632 - INFO - ✓ Uploaded: input_data_individual/9_fuel_receipt/9_fuel_receipt_8.jpg -> s3://document-extraction-logistically/temp/390d5a69_9_fuel_receipt_8.jpg
2025-09-18 14:21:24,632 - INFO - 🔍 [14:21:24] Starting classification: 9_fuel_receipt_8.jpg
2025-09-18 14:21:24,633 - INFO - ⬆️ [14:21:24] Uploading: 9_fuel_receipt_9.jpeg
2025-09-18 14:21:24,634 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:24,653 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:24,658 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/390d5a69_9_fuel_receipt_8.jpg
2025-09-18 14:21:24,658 - INFO - Processing image from S3...
2025-09-18 14:21:25,429 - INFO - S3 Image temp/6f84522b_9_fuel_receipt_7.jpeg: Extracted 277 characters, 25 lines
2025-09-18 14:21:25,429 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:21:25,430 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:21:27,420 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6f84522b_9_fuel_receipt_7.jpeg
2025-09-18 14:21:27,634 - INFO - ✓ Uploaded: input_data_individual/9_fuel_receipt/9_fuel_receipt_9.jpeg -> s3://document-extraction-logistically/temp/ce7c808e_9_fuel_receipt_9.jpeg
2025-09-18 14:21:27,634 - INFO - 🔍 [14:21:27] Starting classification: 9_fuel_receipt_9.jpeg
2025-09-18 14:21:27,635 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:27,651 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:27,658 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ce7c808e_9_fuel_receipt_9.jpeg
2025-09-18 14:21:27,661 - INFO - Processing image from S3...
2025-09-18 14:21:27,674 - INFO - 

9_fuel_receipt_1.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 14:21:27,674 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_1.json
2025-09-18 14:21:27,984 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/757947e1_9_fuel_receipt_1.png
2025-09-18 14:21:27,991 - INFO - 

9_fuel_receipt_10.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 14:21:27,991 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_10.json
2025-09-18 14:21:28,295 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/508cb8c8_9_fuel_receipt_10.jpeg
2025-09-18 14:21:28,311 - INFO - 

9_fuel_receipt_2.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 14:21:28,312 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_2.json
2025-09-18 14:21:28,613 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/07c56845_9_fuel_receipt_2.png
2025-09-18 14:21:28,626 - INFO - 

9_fuel_receipt_3.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 14:21:28,626 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_3.json
2025-09-18 14:21:28,658 - INFO - S3 Image temp/390d5a69_9_fuel_receipt_8.jpg: Extracted 747 characters, 33 lines
2025-09-18 14:21:28,658 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:21:28,658 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:21:28,923 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a30d14bd_9_fuel_receipt_3.jpg
2025-09-18 14:21:28,934 - INFO - 

9_fuel_receipt_4.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 14:21:28,934 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_4.json
2025-09-18 14:21:29,222 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/970b9b3f_9_fuel_receipt_4.jpg
2025-09-18 14:21:29,240 - INFO - 

9_fuel_receipt_5.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 14:21:29,240 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_5.json
2025-09-18 14:21:29,608 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c0fcc233_9_fuel_receipt_5.jpg
2025-09-18 14:21:29,622 - INFO - 

9_fuel_receipt_11.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 14:21:29,622 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_11.json
2025-09-18 14:21:30,013 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/81cdb055_9_fuel_receipt_11.pdf
2025-09-18 14:21:30,034 - INFO - 

9_fuel_receipt_6.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 14:21:30,034 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_6.json
2025-09-18 14:21:30,429 - INFO - S3 Image temp/ce7c808e_9_fuel_receipt_9.jpeg: Extracted 264 characters, 24 lines
2025-09-18 14:21:30,429 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:21:30,429 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:21:30,433 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6db80e85_9_fuel_receipt_6.jpg
2025-09-18 14:21:30,441 - INFO - 

9_fuel_receipt_7.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 14:21:30,441 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_7.json
2025-09-18 14:21:30,734 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6f84522b_9_fuel_receipt_7.jpeg
2025-09-18 14:21:31,649 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/390d5a69_9_fuel_receipt_8.jpg
2025-09-18 14:21:31,675 - INFO - 

9_fuel_receipt_8.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 14:21:31,675 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_8.json
2025-09-18 14:21:31,971 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/390d5a69_9_fuel_receipt_8.jpg
2025-09-18 14:21:33,015 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ce7c808e_9_fuel_receipt_9.jpeg
2025-09-18 14:21:33,023 - INFO - 

9_fuel_receipt_9.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "fuel_receipt"
        }
    ]
}
2025-09-18 14:21:33,023 - INFO - 

✓ Saved result: output/run1_9_fuel_receipt_9.json
2025-09-18 14:21:33,319 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ce7c808e_9_fuel_receipt_9.jpeg
2025-09-18 14:21:33,320 - INFO - 
📊 Processing Summary:
2025-09-18 14:21:33,320 - INFO -    Total files: 11
2025-09-18 14:21:33,321 - INFO -    Successful: 11
2025-09-18 14:21:33,321 - INFO -    Failed: 0
2025-09-18 14:21:33,321 - INFO -    Duration: 34.70 seconds
2025-09-18 14:21:33,321 - INFO -    Output directory: output
2025-09-18 14:21:33,321 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:21:33,321 - INFO -    📄 9_fuel_receipt_1.png: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 14:21:33,321 - INFO -    📄 9_fuel_receipt_10.jpeg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 14:21:33,321 - INFO -    📄 9_fuel_receipt_11.pdf: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 14:21:33,321 - INFO -    📄 9_fuel_receipt_2.png: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 14:21:33,322 - INFO -    📄 9_fuel_receipt_3.jpg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 14:21:33,322 - INFO -    📄 9_fuel_receipt_4.jpg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 14:21:33,322 - INFO -    📄 9_fuel_receipt_5.jpg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 14:21:33,322 - INFO -    📄 9_fuel_receipt_6.jpg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 14:21:33,322 - INFO -    📄 9_fuel_receipt_7.jpeg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 14:21:33,322 - INFO -    📄 9_fuel_receipt_8.jpg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 14:21:33,322 - INFO -    📄 9_fuel_receipt_9.jpeg: {"documents":[{"page_no":1,"doc_type":"fuel_receipt"}]}
2025-09-18 14:21:33,323 - INFO - 
============================================================================================================================================
2025-09-18 14:21:33,323 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:21:33,323 - INFO - ============================================================================================================================================
2025-09-18 14:21:33,323 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:21:33,323 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:21:33,323 - INFO - 9_fuel_receipt_1.png                               1      fuel_receipt         run1_9_fuel_receipt_1.json                        
2025-09-18 14:21:33,323 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/9_fuel_receipt/9_fuel_receipt_1.png
2025-09-18 14:21:33,324 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_1.json
2025-09-18 14:21:33,324 - INFO - 
2025-09-18 14:21:33,324 - INFO - 9_fuel_receipt_10.jpeg                             1      fuel_receipt         run1_9_fuel_receipt_10.json                       
2025-09-18 14:21:33,324 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/9_fuel_receipt/9_fuel_receipt_10.jpeg
2025-09-18 14:21:33,324 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_10.json
2025-09-18 14:21:33,324 - INFO - 
2025-09-18 14:21:33,324 - INFO - 9_fuel_receipt_11.pdf                              1      fuel_receipt         run1_9_fuel_receipt_11.json                       
2025-09-18 14:21:33,324 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/9_fuel_receipt/9_fuel_receipt_11.pdf
2025-09-18 14:21:33,324 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_11.json
2025-09-18 14:21:33,324 - INFO - 
2025-09-18 14:21:33,324 - INFO - 9_fuel_receipt_2.png                               1      fuel_receipt         run1_9_fuel_receipt_2.json                        
2025-09-18 14:21:33,324 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/9_fuel_receipt/9_fuel_receipt_2.png
2025-09-18 14:21:33,324 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_2.json
2025-09-18 14:21:33,325 - INFO - 
2025-09-18 14:21:33,325 - INFO - 9_fuel_receipt_3.jpg                               1      fuel_receipt         run1_9_fuel_receipt_3.json                        
2025-09-18 14:21:33,325 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/9_fuel_receipt/9_fuel_receipt_3.jpg
2025-09-18 14:21:33,325 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_3.json
2025-09-18 14:21:33,325 - INFO - 
2025-09-18 14:21:33,325 - INFO - 9_fuel_receipt_4.jpg                               1      fuel_receipt         run1_9_fuel_receipt_4.json                        
2025-09-18 14:21:33,325 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/9_fuel_receipt/9_fuel_receipt_4.jpg
2025-09-18 14:21:33,325 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_4.json
2025-09-18 14:21:33,325 - INFO - 
2025-09-18 14:21:33,325 - INFO - 9_fuel_receipt_5.jpg                               1      fuel_receipt         run1_9_fuel_receipt_5.json                        
2025-09-18 14:21:33,325 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/9_fuel_receipt/9_fuel_receipt_5.jpg
2025-09-18 14:21:33,325 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_5.json
2025-09-18 14:21:33,325 - INFO - 
2025-09-18 14:21:33,325 - INFO - 9_fuel_receipt_6.jpg                               1      fuel_receipt         run1_9_fuel_receipt_6.json                        
2025-09-18 14:21:33,325 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/9_fuel_receipt/9_fuel_receipt_6.jpg
2025-09-18 14:21:33,325 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_6.json
2025-09-18 14:21:33,325 - INFO - 
2025-09-18 14:21:33,325 - INFO - 9_fuel_receipt_7.jpeg                              1      fuel_receipt         run1_9_fuel_receipt_7.json                        
2025-09-18 14:21:33,325 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/9_fuel_receipt/9_fuel_receipt_7.jpeg
2025-09-18 14:21:33,325 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_7.json
2025-09-18 14:21:33,325 - INFO - 
2025-09-18 14:21:33,325 - INFO - 9_fuel_receipt_8.jpg                               1      fuel_receipt         run1_9_fuel_receipt_8.json                        
2025-09-18 14:21:33,325 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/9_fuel_receipt/9_fuel_receipt_8.jpg
2025-09-18 14:21:33,325 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_8.json
2025-09-18 14:21:33,325 - INFO - 
2025-09-18 14:21:33,325 - INFO - 9_fuel_receipt_9.jpeg                              1      fuel_receipt         run1_9_fuel_receipt_9.json                        
2025-09-18 14:21:33,326 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/9_fuel_receipt/9_fuel_receipt_9.jpeg
2025-09-18 14:21:33,326 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_9_fuel_receipt_9.json
2025-09-18 14:21:33,326 - INFO - 
2025-09-18 14:21:33,326 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:21:33,326 - INFO - Total entries: 11
2025-09-18 14:21:33,326 - INFO - ============================================================================================================================================
2025-09-18 14:21:33,326 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:21:33,326 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:21:33,326 - INFO -   1. 9_fuel_receipt_1.png                Page 1   → fuel_receipt    | run1_9_fuel_receipt_1.json
2025-09-18 14:21:33,326 - INFO -   2. 9_fuel_receipt_10.jpeg              Page 1   → fuel_receipt    | run1_9_fuel_receipt_10.json
2025-09-18 14:21:33,326 - INFO -   3. 9_fuel_receipt_11.pdf               Page 1   → fuel_receipt    | run1_9_fuel_receipt_11.json
2025-09-18 14:21:33,326 - INFO -   4. 9_fuel_receipt_2.png                Page 1   → fuel_receipt    | run1_9_fuel_receipt_2.json
2025-09-18 14:21:33,326 - INFO -   5. 9_fuel_receipt_3.jpg                Page 1   → fuel_receipt    | run1_9_fuel_receipt_3.json
2025-09-18 14:21:33,326 - INFO -   6. 9_fuel_receipt_4.jpg                Page 1   → fuel_receipt    | run1_9_fuel_receipt_4.json
2025-09-18 14:21:33,326 - INFO -   7. 9_fuel_receipt_5.jpg                Page 1   → fuel_receipt    | run1_9_fuel_receipt_5.json
2025-09-18 14:21:33,326 - INFO -   8. 9_fuel_receipt_6.jpg                Page 1   → fuel_receipt    | run1_9_fuel_receipt_6.json
2025-09-18 14:21:33,326 - INFO -   9. 9_fuel_receipt_7.jpeg               Page 1   → fuel_receipt    | run1_9_fuel_receipt_7.json
2025-09-18 14:21:33,326 - INFO -  10. 9_fuel_receipt_8.jpg                Page 1   → fuel_receipt    | run1_9_fuel_receipt_8.json
2025-09-18 14:21:33,326 - INFO -  11. 9_fuel_receipt_9.jpeg               Page 1   → fuel_receipt    | run1_9_fuel_receipt_9.json
2025-09-18 14:21:33,326 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:21:33,326 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 34.696852, 'processed_files': [{'filename': '9_fuel_receipt_1.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_individual/9_fuel_receipt/9_fuel_receipt_1.png'}, {'filename': '9_fuel_receipt_10.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_individual/9_fuel_receipt/9_fuel_receipt_10.jpeg'}, {'filename': '9_fuel_receipt_11.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_individual/9_fuel_receipt/9_fuel_receipt_11.pdf'}, {'filename': '9_fuel_receipt_2.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_individual/9_fuel_receipt/9_fuel_receipt_2.png'}, {'filename': '9_fuel_receipt_3.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_individual/9_fuel_receipt/9_fuel_receipt_3.jpg'}, {'filename': '9_fuel_receipt_4.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_individual/9_fuel_receipt/9_fuel_receipt_4.jpg'}, {'filename': '9_fuel_receipt_5.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_individual/9_fuel_receipt/9_fuel_receipt_5.jpg'}, {'filename': '9_fuel_receipt_6.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_individual/9_fuel_receipt/9_fuel_receipt_6.jpg'}, {'filename': '9_fuel_receipt_7.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_individual/9_fuel_receipt/9_fuel_receipt_7.jpeg'}, {'filename': '9_fuel_receipt_8.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_individual/9_fuel_receipt/9_fuel_receipt_8.jpg'}, {'filename': '9_fuel_receipt_9.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'fuel_receipt'}]}, 'file_path': 'input_data_individual/9_fuel_receipt/9_fuel_receipt_9.jpeg'}]}
