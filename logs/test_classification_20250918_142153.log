2025-09-18 14:21:53,116 - INFO - Logging initialized. Log file: logs/test_classification_20250918_142153.log
2025-09-18 14:21:53,117 - INFO - 📁 Found 14 files to process
2025-09-18 14:21:53,117 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:21:53,117 - INFO - 🚀 Processing 14 files in FORCED PARALLEL MODE...
2025-09-18 14:21:53,117 - INFO - 🚀 Creating 14 parallel tasks...
2025-09-18 14:21:53,117 - INFO - 🚀 All 14 tasks created - executing in parallel...
2025-09-18 14:21:53,117 - INFO - ⬆️ [14:21:53] Uploading: 2_pod_1.pdf
2025-09-18 14:21:57,004 - INFO - ✓ Uploaded: input_data_individual/2_pod/2_pod_1.pdf -> s3://document-extraction-logistically/temp/e37fb697_2_pod_1.pdf
2025-09-18 14:21:57,005 - INFO - 🔍 [14:21:57] Starting classification: 2_pod_1.pdf
2025-09-18 14:21:57,006 - INFO - ⬆️ [14:21:57] Uploading: 2_pod_10.pdf
2025-09-18 14:21:57,007 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:57,035 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:57,042 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e37fb697_2_pod_1.pdf
2025-09-18 14:21:57,042 - INFO - Processing PDF from S3...
2025-09-18 14:21:57,042 - INFO - Downloading PDF from S3 to /tmp/tmp_ja6huws/e37fb697_2_pod_1.pdf
2025-09-18 14:21:57,592 - INFO - ✓ Uploaded: input_data_individual/2_pod/2_pod_10.pdf -> s3://document-extraction-logistically/temp/eb091496_2_pod_10.pdf
2025-09-18 14:21:57,592 - INFO - 🔍 [14:21:57] Starting classification: 2_pod_10.pdf
2025-09-18 14:21:57,593 - INFO - ⬆️ [14:21:57] Uploading: 2_pod_11.pdf
2025-09-18 14:21:57,595 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:57,612 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:57,619 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/eb091496_2_pod_10.pdf
2025-09-18 14:21:57,620 - INFO - Processing PDF from S3...
2025-09-18 14:21:57,621 - INFO - Downloading PDF from S3 to /tmp/tmpvjk9ef8c/eb091496_2_pod_10.pdf
2025-09-18 14:21:58,565 - INFO - ✓ Uploaded: input_data_individual/2_pod/2_pod_11.pdf -> s3://document-extraction-logistically/temp/14fda2ab_2_pod_11.pdf
2025-09-18 14:21:58,565 - INFO - 🔍 [14:21:58] Starting classification: 2_pod_11.pdf
2025-09-18 14:21:58,566 - INFO - ⬆️ [14:21:58] Uploading: 2_pod_12.pdf
2025-09-18 14:21:58,566 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:58,583 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:58,588 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/14fda2ab_2_pod_11.pdf
2025-09-18 14:21:58,588 - INFO - Processing PDF from S3...
2025-09-18 14:21:58,589 - INFO - Downloading PDF from S3 to /tmp/tmplogg4qnb/14fda2ab_2_pod_11.pdf
2025-09-18 14:21:58,850 - INFO - Splitting PDF into individual pages...
2025-09-18 14:21:58,851 - INFO - Splitting PDF eb091496_2_pod_10 into 1 pages
2025-09-18 14:21:58,854 - INFO - Split PDF into 1 pages
2025-09-18 14:21:58,855 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:21:58,855 - INFO - Expected pages: [1]
2025-09-18 14:21:59,192 - INFO - ✓ Uploaded: input_data_individual/2_pod/2_pod_12.pdf -> s3://document-extraction-logistically/temp/afbb26c0_2_pod_12.pdf
2025-09-18 14:21:59,192 - INFO - 🔍 [14:21:59] Starting classification: 2_pod_12.pdf
2025-09-18 14:21:59,192 - INFO - ⬆️ [14:21:59] Uploading: 2_pod_13.pdf
2025-09-18 14:21:59,193 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:59,201 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:59,203 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/afbb26c0_2_pod_12.pdf
2025-09-18 14:21:59,203 - INFO - Processing PDF from S3...
2025-09-18 14:21:59,203 - INFO - Downloading PDF from S3 to /tmp/tmpa0qodh09/afbb26c0_2_pod_12.pdf
2025-09-18 14:21:59,917 - INFO - Splitting PDF into individual pages...
2025-09-18 14:21:59,917 - INFO - ✓ Uploaded: input_data_individual/2_pod/2_pod_13.pdf -> s3://document-extraction-logistically/temp/1c999d38_2_pod_13.pdf
2025-09-18 14:21:59,917 - INFO - 🔍 [14:21:59] Starting classification: 2_pod_13.pdf
2025-09-18 14:21:59,918 - INFO - ⬆️ [14:21:59] Uploading: 2_pod_14.pdf
2025-09-18 14:21:59,920 - INFO - Initializing TextractProcessor...
2025-09-18 14:21:59,923 - INFO - Splitting PDF e37fb697_2_pod_1 into 3 pages
2025-09-18 14:21:59,936 - INFO - Initializing BedrockProcessor...
2025-09-18 14:21:59,941 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1c999d38_2_pod_13.pdf
2025-09-18 14:21:59,941 - INFO - Processing PDF from S3...
2025-09-18 14:21:59,942 - INFO - Downloading PDF from S3 to /tmp/tmp24owm4lx/1c999d38_2_pod_13.pdf
2025-09-18 14:21:59,949 - INFO - Split PDF into 3 pages
2025-09-18 14:21:59,949 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:21:59,949 - INFO - Expected pages: [1, 2, 3]
2025-09-18 14:22:00,523 - INFO - ✓ Uploaded: input_data_individual/2_pod/2_pod_14.pdf -> s3://document-extraction-logistically/temp/c052caa6_2_pod_14.pdf
2025-09-18 14:22:00,524 - INFO - 🔍 [14:22:00] Starting classification: 2_pod_14.pdf
2025-09-18 14:22:00,525 - INFO - ⬆️ [14:22:00] Uploading: 2_pod_2.pdf
2025-09-18 14:22:00,530 - INFO - Initializing TextractProcessor...
2025-09-18 14:22:00,544 - INFO - Initializing BedrockProcessor...
2025-09-18 14:22:00,552 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c052caa6_2_pod_14.pdf
2025-09-18 14:22:00,553 - INFO - Processing PDF from S3...
2025-09-18 14:22:00,553 - INFO - Downloading PDF from S3 to /tmp/tmp1jcy41o1/c052caa6_2_pod_14.pdf
2025-09-18 14:22:01,022 - INFO - Splitting PDF into individual pages...
2025-09-18 14:22:01,023 - INFO - Splitting PDF 14fda2ab_2_pod_11 into 1 pages
2025-09-18 14:22:01,025 - INFO - Split PDF into 1 pages
2025-09-18 14:22:01,025 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:22:01,025 - INFO - Expected pages: [1]
2025-09-18 14:22:01,035 - INFO - Splitting PDF into individual pages...
2025-09-18 14:22:01,036 - INFO - Splitting PDF afbb26c0_2_pod_12 into 1 pages
2025-09-18 14:22:01,042 - INFO - Split PDF into 1 pages
2025-09-18 14:22:01,043 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:22:01,043 - INFO - Expected pages: [1]
2025-09-18 14:22:01,793 - INFO - Splitting PDF into individual pages...
2025-09-18 14:22:01,794 - INFO - Splitting PDF 1c999d38_2_pod_13 into 1 pages
2025-09-18 14:22:01,796 - INFO - Split PDF into 1 pages
2025-09-18 14:22:01,796 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:22:01,796 - INFO - Expected pages: [1]
2025-09-18 14:22:02,030 - INFO - ✓ Uploaded: input_data_individual/2_pod/2_pod_2.pdf -> s3://document-extraction-logistically/temp/9f9794b7_2_pod_2.pdf
2025-09-18 14:22:02,031 - INFO - 🔍 [14:22:02] Starting classification: 2_pod_2.pdf
2025-09-18 14:22:02,031 - INFO - ⬆️ [14:22:02] Uploading: 2_pod_3.pdf
2025-09-18 14:22:02,033 - INFO - Initializing TextractProcessor...
2025-09-18 14:22:02,048 - INFO - Initializing BedrockProcessor...
2025-09-18 14:22:02,055 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9f9794b7_2_pod_2.pdf
2025-09-18 14:22:02,055 - INFO - Processing PDF from S3...
2025-09-18 14:22:02,055 - INFO - Downloading PDF from S3 to /tmp/tmp3uy9gszg/9f9794b7_2_pod_2.pdf
2025-09-18 14:22:02,146 - INFO - Splitting PDF into individual pages...
2025-09-18 14:22:02,148 - INFO - Splitting PDF c052caa6_2_pod_14 into 1 pages
2025-09-18 14:22:02,150 - INFO - Split PDF into 1 pages
2025-09-18 14:22:02,150 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:22:02,150 - INFO - Expected pages: [1]
2025-09-18 14:22:02,967 - INFO - ✓ Uploaded: input_data_individual/2_pod/2_pod_3.pdf -> s3://document-extraction-logistically/temp/58586e34_2_pod_3.pdf
2025-09-18 14:22:02,968 - INFO - 🔍 [14:22:02] Starting classification: 2_pod_3.pdf
2025-09-18 14:22:02,969 - INFO - ⬆️ [14:22:02] Uploading: 2_pod_4.pdf
2025-09-18 14:22:02,970 - INFO - Initializing TextractProcessor...
2025-09-18 14:22:02,988 - INFO - Initializing BedrockProcessor...
2025-09-18 14:22:03,006 - INFO - Page 1: Extracted 961 characters, 60 lines from eb091496_2_pod_10_6d5ec5e4_page_001.pdf
2025-09-18 14:22:03,007 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/58586e34_2_pod_3.pdf
2025-09-18 14:22:03,008 - INFO - Processing PDF from S3...
2025-09-18 14:22:03,008 - INFO - Successfully processed page 1
2025-09-18 14:22:03,009 - INFO - Downloading PDF from S3 to /tmp/tmpqwd_852p/58586e34_2_pod_3.pdf
2025-09-18 14:22:03,009 - INFO - Combined 1 pages into final text
2025-09-18 14:22:03,015 - INFO - Text validation for eb091496_2_pod_10: 978 characters, 1 pages
2025-09-18 14:22:03,016 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:22:03,016 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:22:03,712 - INFO - ✓ Uploaded: input_data_individual/2_pod/2_pod_4.pdf -> s3://document-extraction-logistically/temp/11472d10_2_pod_4.pdf
2025-09-18 14:22:03,713 - INFO - 🔍 [14:22:03] Starting classification: 2_pod_4.pdf
2025-09-18 14:22:03,713 - INFO - ⬆️ [14:22:03] Uploading: 2_pod_5.pdf
2025-09-18 14:22:03,714 - INFO - Initializing TextractProcessor...
2025-09-18 14:22:03,728 - INFO - Initializing BedrockProcessor...
2025-09-18 14:22:03,745 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/11472d10_2_pod_4.pdf
2025-09-18 14:22:03,745 - INFO - Processing PDF from S3...
2025-09-18 14:22:03,746 - INFO - Downloading PDF from S3 to /tmp/tmpeq5nzfsb/11472d10_2_pod_4.pdf
2025-09-18 14:22:04,411 - INFO - ✓ Uploaded: input_data_individual/2_pod/2_pod_5.pdf -> s3://document-extraction-logistically/temp/d6529741_2_pod_5.pdf
2025-09-18 14:22:04,412 - INFO - 🔍 [14:22:04] Starting classification: 2_pod_5.pdf
2025-09-18 14:22:04,412 - INFO - ⬆️ [14:22:04] Uploading: 2_pod_6.pdf
2025-09-18 14:22:04,413 - INFO - Initializing TextractProcessor...
2025-09-18 14:22:04,428 - INFO - Initializing BedrockProcessor...
2025-09-18 14:22:04,431 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d6529741_2_pod_5.pdf
2025-09-18 14:22:04,432 - INFO - Processing PDF from S3...
2025-09-18 14:22:04,432 - INFO - Downloading PDF from S3 to /tmp/tmpggvs_dsk/d6529741_2_pod_5.pdf
2025-09-18 14:22:04,577 - INFO - Page 3: Extracted 441 characters, 20 lines from e37fb697_2_pod_1_3772d569_page_003.pdf
2025-09-18 14:22:04,577 - INFO - Successfully processed page 3
2025-09-18 14:22:04,981 - INFO - Splitting PDF into individual pages...
2025-09-18 14:22:04,982 - INFO - Splitting PDF 9f9794b7_2_pod_2 into 1 pages
2025-09-18 14:22:04,984 - INFO - Split PDF into 1 pages
2025-09-18 14:22:04,984 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:22:04,984 - INFO - Expected pages: [1]
2025-09-18 14:22:05,007 - INFO - ✓ Uploaded: input_data_individual/2_pod/2_pod_6.pdf -> s3://document-extraction-logistically/temp/203c148d_2_pod_6.pdf
2025-09-18 14:22:05,007 - INFO - 🔍 [14:22:05] Starting classification: 2_pod_6.pdf
2025-09-18 14:22:05,008 - INFO - ⬆️ [14:22:05] Uploading: 2_pod_7.pdf
2025-09-18 14:22:05,010 - INFO - Initializing TextractProcessor...
2025-09-18 14:22:05,033 - INFO - Initializing BedrockProcessor...
2025-09-18 14:22:05,036 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/203c148d_2_pod_6.pdf
2025-09-18 14:22:05,036 - INFO - Processing PDF from S3...
2025-09-18 14:22:05,036 - INFO - Downloading PDF from S3 to /tmp/tmp73imw7t3/203c148d_2_pod_6.pdf
2025-09-18 14:22:05,408 - INFO - Splitting PDF into individual pages...
2025-09-18 14:22:05,408 - INFO - Splitting PDF 58586e34_2_pod_3 into 1 pages
2025-09-18 14:22:05,410 - INFO - Split PDF into 1 pages
2025-09-18 14:22:05,410 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:22:05,410 - INFO - Expected pages: [1]
2025-09-18 14:22:05,429 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/eb091496_2_pod_10.pdf
2025-09-18 14:22:05,776 - INFO - Page 1: Extracted 1597 characters, 78 lines from e37fb697_2_pod_1_3772d569_page_001.pdf
2025-09-18 14:22:05,776 - INFO - Successfully processed page 1
2025-09-18 14:22:05,826 - INFO - Splitting PDF into individual pages...
2025-09-18 14:22:05,828 - INFO - Splitting PDF 11472d10_2_pod_4 into 1 pages
2025-09-18 14:22:05,830 - INFO - Split PDF into 1 pages
2025-09-18 14:22:05,830 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:22:05,830 - INFO - Expected pages: [1]
2025-09-18 14:22:05,920 - INFO - ✓ Uploaded: input_data_individual/2_pod/2_pod_7.pdf -> s3://document-extraction-logistically/temp/baa2f2e2_2_pod_7.pdf
2025-09-18 14:22:05,920 - INFO - 🔍 [14:22:05] Starting classification: 2_pod_7.pdf
2025-09-18 14:22:05,921 - INFO - Initializing TextractProcessor...
2025-09-18 14:22:05,922 - INFO - ⬆️ [14:22:05] Uploading: 2_pod_8.pdf
2025-09-18 14:22:05,936 - INFO - Initializing BedrockProcessor...
2025-09-18 14:22:05,944 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/baa2f2e2_2_pod_7.pdf
2025-09-18 14:22:05,945 - INFO - Processing PDF from S3...
2025-09-18 14:22:05,945 - INFO - Downloading PDF from S3 to /tmp/tmpqixvhwnn/baa2f2e2_2_pod_7.pdf
2025-09-18 14:22:06,374 - INFO - Page 1: Extracted 1484 characters, 105 lines from afbb26c0_2_pod_12_e1814922_page_001.pdf
2025-09-18 14:22:06,374 - INFO - Successfully processed page 1
2025-09-18 14:22:06,375 - INFO - Combined 1 pages into final text
2025-09-18 14:22:06,375 - INFO - Text validation for afbb26c0_2_pod_12: 1501 characters, 1 pages
2025-09-18 14:22:06,375 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:22:06,375 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:22:06,478 - INFO - Splitting PDF into individual pages...
2025-09-18 14:22:06,479 - INFO - Splitting PDF d6529741_2_pod_5 into 1 pages
2025-09-18 14:22:06,481 - INFO - Split PDF into 1 pages
2025-09-18 14:22:06,482 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:22:06,482 - INFO - Expected pages: [1]
2025-09-18 14:22:06,568 - INFO - ✓ Uploaded: input_data_individual/2_pod/2_pod_8.pdf -> s3://document-extraction-logistically/temp/bc1b7842_2_pod_8.pdf
2025-09-18 14:22:06,569 - INFO - 🔍 [14:22:06] Starting classification: 2_pod_8.pdf
2025-09-18 14:22:06,569 - INFO - ⬆️ [14:22:06] Uploading: 2_pod_9.pdf
2025-09-18 14:22:06,571 - INFO - Initializing TextractProcessor...
2025-09-18 14:22:06,586 - INFO - Initializing BedrockProcessor...
2025-09-18 14:22:06,590 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bc1b7842_2_pod_8.pdf
2025-09-18 14:22:06,590 - INFO - Processing PDF from S3...
2025-09-18 14:22:06,590 - INFO - Downloading PDF from S3 to /tmp/tmpijevmbdg/bc1b7842_2_pod_8.pdf
2025-09-18 14:22:06,629 - INFO - Page 1: Extracted 797 characters, 46 lines from 14fda2ab_2_pod_11_12c253c5_page_001.pdf
2025-09-18 14:22:06,630 - INFO - Successfully processed page 1
2025-09-18 14:22:06,630 - INFO - Combined 1 pages into final text
2025-09-18 14:22:06,630 - INFO - Text validation for 14fda2ab_2_pod_11: 814 characters, 1 pages
2025-09-18 14:22:06,631 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:22:06,631 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:22:06,809 - INFO - Page 2: Extracted 1895 characters, 65 lines from e37fb697_2_pod_1_3772d569_page_002.pdf
2025-09-18 14:22:06,809 - INFO - Successfully processed page 2
2025-09-18 14:22:06,809 - INFO - Combined 3 pages into final text
2025-09-18 14:22:06,810 - INFO - Text validation for e37fb697_2_pod_1: 3988 characters, 3 pages
2025-09-18 14:22:06,810 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:22:06,810 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:22:06,812 - INFO - Splitting PDF into individual pages...
2025-09-18 14:22:06,813 - INFO - Splitting PDF 203c148d_2_pod_6 into 1 pages
2025-09-18 14:22:06,814 - INFO - Split PDF into 1 pages
2025-09-18 14:22:06,814 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:22:06,814 - INFO - Expected pages: [1]
2025-09-18 14:22:06,854 - INFO - Page 1: Extracted 983 characters, 63 lines from c052caa6_2_pod_14_12d0de39_page_001.pdf
2025-09-18 14:22:06,854 - INFO - Successfully processed page 1
2025-09-18 14:22:06,854 - INFO - Combined 1 pages into final text
2025-09-18 14:22:06,854 - INFO - Text validation for c052caa6_2_pod_14: 1000 characters, 1 pages
2025-09-18 14:22:06,855 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:22:06,855 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:22:07,239 - INFO - ✓ Uploaded: input_data_individual/2_pod/2_pod_9.pdf -> s3://document-extraction-logistically/temp/46ee4014_2_pod_9.pdf
2025-09-18 14:22:07,240 - INFO - 🔍 [14:22:07] Starting classification: 2_pod_9.pdf
2025-09-18 14:22:07,240 - INFO - Initializing TextractProcessor...
2025-09-18 14:22:07,248 - INFO - Initializing BedrockProcessor...
2025-09-18 14:22:07,252 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/46ee4014_2_pod_9.pdf
2025-09-18 14:22:07,252 - INFO - Processing PDF from S3...
2025-09-18 14:22:07,254 - INFO - Downloading PDF from S3 to /tmp/tmpcn3270g2/46ee4014_2_pod_9.pdf
2025-09-18 14:22:07,275 - INFO - 

2_pod_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 14:22:07,275 - INFO - 

✓ Saved result: output/run1_2_pod_10.json
2025-09-18 14:22:07,571 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/eb091496_2_pod_10.pdf
2025-09-18 14:22:08,053 - INFO - Page 1: Extracted 1837 characters, 158 lines from 1c999d38_2_pod_13_ce837384_page_001.pdf
2025-09-18 14:22:08,053 - INFO - Successfully processed page 1
2025-09-18 14:22:08,053 - INFO - Combined 1 pages into final text
2025-09-18 14:22:08,053 - INFO - Text validation for 1c999d38_2_pod_13: 1854 characters, 1 pages
2025-09-18 14:22:08,054 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:22:08,054 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:22:08,111 - INFO - Splitting PDF into individual pages...
2025-09-18 14:22:08,113 - INFO - Splitting PDF baa2f2e2_2_pod_7 into 1 pages
2025-09-18 14:22:08,121 - INFO - Split PDF into 1 pages
2025-09-18 14:22:08,121 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:22:08,121 - INFO - Expected pages: [1]
2025-09-18 14:22:08,148 - INFO - Splitting PDF into individual pages...
2025-09-18 14:22:08,150 - INFO - Splitting PDF bc1b7842_2_pod_8 into 1 pages
2025-09-18 14:22:08,153 - INFO - Split PDF into 1 pages
2025-09-18 14:22:08,153 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:22:08,153 - INFO - Expected pages: [1]
2025-09-18 14:22:08,322 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/14fda2ab_2_pod_11.pdf
2025-09-18 14:22:08,341 - INFO - 

2_pod_11.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 14:22:08,341 - INFO - 

✓ Saved result: output/run1_2_pod_11.json
2025-09-18 14:22:08,636 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/14fda2ab_2_pod_11.pdf
2025-09-18 14:22:08,998 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c052caa6_2_pod_14.pdf
2025-09-18 14:22:09,026 - INFO - 

2_pod_14.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 14:22:09,026 - INFO - 

✓ Saved result: output/run1_2_pod_14.json
2025-09-18 14:22:09,098 - INFO - Splitting PDF into individual pages...
2025-09-18 14:22:09,100 - INFO - Splitting PDF 46ee4014_2_pod_9 into 1 pages
2025-09-18 14:22:09,102 - INFO - Split PDF into 1 pages
2025-09-18 14:22:09,102 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:22:09,103 - INFO - Expected pages: [1]
2025-09-18 14:22:09,323 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c052caa6_2_pod_14.pdf
2025-09-18 14:22:09,583 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/afbb26c0_2_pod_12.pdf
2025-09-18 14:22:09,610 - INFO - 

2_pod_12.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 14:22:09,611 - INFO - 

✓ Saved result: output/run1_2_pod_12.json
2025-09-18 14:22:09,894 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/afbb26c0_2_pod_12.pdf
2025-09-18 14:22:10,278 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1c999d38_2_pod_13.pdf
2025-09-18 14:22:10,302 - INFO - 

2_pod_13.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 14:22:10,302 - INFO - 

✓ Saved result: output/run1_2_pod_13.json
2025-09-18 14:22:10,590 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1c999d38_2_pod_13.pdf
2025-09-18 14:22:10,898 - INFO - Page 1: Extracted 1309 characters, 74 lines from 203c148d_2_pod_6_da50410b_page_001.pdf
2025-09-18 14:22:10,898 - INFO - Successfully processed page 1
2025-09-18 14:22:10,899 - INFO - Combined 1 pages into final text
2025-09-18 14:22:10,899 - INFO - Text validation for 203c148d_2_pod_6: 1326 characters, 1 pages
2025-09-18 14:22:10,899 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:22:10,899 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:22:11,127 - INFO - Page 1: Extracted 762 characters, 53 lines from 9f9794b7_2_pod_2_b00374c3_page_001.pdf
2025-09-18 14:22:11,127 - INFO - Successfully processed page 1
2025-09-18 14:22:11,127 - INFO - Combined 1 pages into final text
2025-09-18 14:22:11,127 - INFO - Text validation for 9f9794b7_2_pod_2: 779 characters, 1 pages
2025-09-18 14:22:11,128 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:22:11,128 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:22:11,278 - INFO - Page 1: Extracted 797 characters, 55 lines from 58586e34_2_pod_3_1cf3d629_page_001.pdf
2025-09-18 14:22:11,280 - INFO - Successfully processed page 1
2025-09-18 14:22:11,281 - INFO - Combined 1 pages into final text
2025-09-18 14:22:11,281 - INFO - Text validation for 58586e34_2_pod_3: 814 characters, 1 pages
2025-09-18 14:22:11,282 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:22:11,282 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:22:11,978 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-18 14:22:11,978 - ERROR - Processing failed for s3://document-extraction-logistically/temp/e37fb697_2_pod_1.pdf: Unexpected tool response format from Bedrock
2025-09-18 14:22:11,979 - ERROR - ✗ Failed to process input_data_individual/2_pod/2_pod_1.pdf: Unexpected tool response format from Bedrock
2025-09-18 14:22:12,123 - INFO - Page 1: Extracted 1604 characters, 83 lines from 11472d10_2_pod_4_b0c54020_page_001.pdf
2025-09-18 14:22:12,124 - INFO - Successfully processed page 1
2025-09-18 14:22:12,124 - INFO - Combined 1 pages into final text
2025-09-18 14:22:12,124 - INFO - Text validation for 11472d10_2_pod_4: 1621 characters, 1 pages
2025-09-18 14:22:12,124 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:22:12,124 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:22:12,265 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e37fb697_2_pod_1.pdf
2025-09-18 14:22:12,478 - INFO - Page 1: Extracted 2131 characters, 138 lines from d6529741_2_pod_5_827261cc_page_001.pdf
2025-09-18 14:22:12,478 - INFO - Successfully processed page 1
2025-09-18 14:22:12,478 - INFO - Combined 1 pages into final text
2025-09-18 14:22:12,478 - INFO - Text validation for d6529741_2_pod_5: 2148 characters, 1 pages
2025-09-18 14:22:12,478 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:22:12,478 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:22:13,046 - INFO - Page 1: Extracted 1774 characters, 120 lines from bc1b7842_2_pod_8_e3bcfd3b_page_001.pdf
2025-09-18 14:22:13,047 - INFO - Successfully processed page 1
2025-09-18 14:22:13,047 - INFO - Combined 1 pages into final text
2025-09-18 14:22:13,047 - INFO - Text validation for bc1b7842_2_pod_8: 1791 characters, 1 pages
2025-09-18 14:22:13,047 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:22:13,047 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:22:13,117 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/203c148d_2_pod_6.pdf
2025-09-18 14:22:13,140 - INFO - 

2_pod_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 14:22:13,140 - INFO - 

✓ Saved result: output/run1_2_pod_6.json
2025-09-18 14:22:13,212 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9f9794b7_2_pod_2.pdf
2025-09-18 14:22:13,426 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/203c148d_2_pod_6.pdf
2025-09-18 14:22:13,446 - INFO - 

2_pod_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 14:22:13,446 - INFO - 

✓ Saved result: output/run1_2_pod_2.json
2025-09-18 14:22:13,713 - INFO - Page 1: Extracted 824 characters, 53 lines from 46ee4014_2_pod_9_b391fcc3_page_001.pdf
2025-09-18 14:22:13,713 - INFO - Successfully processed page 1
2025-09-18 14:22:13,713 - INFO - Combined 1 pages into final text
2025-09-18 14:22:13,713 - INFO - Text validation for 46ee4014_2_pod_9: 841 characters, 1 pages
2025-09-18 14:22:13,713 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:22:13,713 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:22:13,741 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9f9794b7_2_pod_2.pdf
2025-09-18 14:22:14,239 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/58586e34_2_pod_3.pdf
2025-09-18 14:22:14,263 - INFO - 

2_pod_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 14:22:14,263 - INFO - 

✓ Saved result: output/run1_2_pod_3.json
2025-09-18 14:22:14,291 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/11472d10_2_pod_4.pdf
2025-09-18 14:22:14,563 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/58586e34_2_pod_3.pdf
2025-09-18 14:22:14,590 - INFO - 

2_pod_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "bol"
        }
    ]
}
2025-09-18 14:22:14,590 - INFO - 

✓ Saved result: output/run1_2_pod_4.json
2025-09-18 14:22:14,886 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/11472d10_2_pod_4.pdf
2025-09-18 14:22:15,280 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bc1b7842_2_pod_8.pdf
2025-09-18 14:22:15,307 - INFO - 

2_pod_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 14:22:15,307 - INFO - 

✓ Saved result: output/run1_2_pod_8.json
2025-09-18 14:22:15,437 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d6529741_2_pod_5.pdf
2025-09-18 14:22:15,598 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bc1b7842_2_pod_8.pdf
2025-09-18 14:22:15,629 - INFO - 

2_pod_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 14:22:15,629 - INFO - 

✓ Saved result: output/run1_2_pod_5.json
2025-09-18 14:22:15,713 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/46ee4014_2_pod_9.pdf
2025-09-18 14:22:15,902 - INFO - Page 1: Extracted 1430 characters, 77 lines from baa2f2e2_2_pod_7_9caa73ce_page_001.pdf
2025-09-18 14:22:15,902 - INFO - Successfully processed page 1
2025-09-18 14:22:15,903 - INFO - Combined 1 pages into final text
2025-09-18 14:22:15,903 - INFO - Text validation for baa2f2e2_2_pod_7: 1447 characters, 1 pages
2025-09-18 14:22:15,903 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:22:15,903 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:22:15,942 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d6529741_2_pod_5.pdf
2025-09-18 14:22:15,951 - INFO - 

2_pod_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "pod"
        }
    ]
}
2025-09-18 14:22:15,951 - INFO - 

✓ Saved result: output/run1_2_pod_9.json
2025-09-18 14:22:16,245 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/46ee4014_2_pod_9.pdf
2025-09-18 14:22:17,922 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/baa2f2e2_2_pod_7.pdf
2025-09-18 14:22:17,948 - INFO - 

2_pod_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 14:22:17,948 - INFO - 

✓ Saved result: output/run1_2_pod_7.json
2025-09-18 14:22:18,234 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/baa2f2e2_2_pod_7.pdf
2025-09-18 14:22:18,235 - INFO - 
📊 Processing Summary:
2025-09-18 14:22:18,235 - INFO -    Total files: 14
2025-09-18 14:22:18,235 - INFO -    Successful: 13
2025-09-18 14:22:18,236 - INFO -    Failed: 1
2025-09-18 14:22:18,236 - INFO -    Duration: 25.12 seconds
2025-09-18 14:22:18,236 - INFO -    Output directory: output
2025-09-18 14:22:18,236 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:22:18,236 - INFO -    📄 2_pod_10.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 14:22:18,236 - INFO -    📄 2_pod_11.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 14:22:18,236 - INFO -    📄 2_pod_12.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 14:22:18,236 - INFO -    📄 2_pod_13.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 14:22:18,236 - INFO -    📄 2_pod_14.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 14:22:18,236 - INFO -    📄 2_pod_2.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 14:22:18,236 - INFO -    📄 2_pod_3.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 14:22:18,236 - INFO -    📄 2_pod_4.pdf: {"documents":[{"page_no":1,"doc_type":"bol"}]}
2025-09-18 14:22:18,237 - INFO -    📄 2_pod_5.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 14:22:18,237 - INFO -    📄 2_pod_6.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 14:22:18,237 - INFO -    📄 2_pod_7.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 14:22:18,237 - INFO -    📄 2_pod_8.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 14:22:18,237 - INFO -    📄 2_pod_9.pdf: {"documents":[{"page_no":1,"doc_type":"pod"}]}
2025-09-18 14:22:18,237 - ERROR - 
❌ Errors:
2025-09-18 14:22:18,237 - ERROR -    Failed to process input_data_individual/2_pod/2_pod_1.pdf: Unexpected tool response format from Bedrock
2025-09-18 14:22:18,238 - INFO - 
============================================================================================================================================
2025-09-18 14:22:18,238 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:22:18,238 - INFO - ============================================================================================================================================
2025-09-18 14:22:18,238 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:22:18,238 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:22:18,238 - INFO - 2_pod_10.pdf                                       1      pod                  run1_2_pod_10.json                                
2025-09-18 14:22:18,238 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/2_pod/2_pod_10.pdf
2025-09-18 14:22:18,238 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_10.json
2025-09-18 14:22:18,238 - INFO - 
2025-09-18 14:22:18,238 - INFO - 2_pod_11.pdf                                       1      pod                  run1_2_pod_11.json                                
2025-09-18 14:22:18,238 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/2_pod/2_pod_11.pdf
2025-09-18 14:22:18,238 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_11.json
2025-09-18 14:22:18,238 - INFO - 
2025-09-18 14:22:18,238 - INFO - 2_pod_12.pdf                                       1      pod                  run1_2_pod_12.json                                
2025-09-18 14:22:18,239 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/2_pod/2_pod_12.pdf
2025-09-18 14:22:18,239 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_12.json
2025-09-18 14:22:18,239 - INFO - 
2025-09-18 14:22:18,239 - INFO - 2_pod_13.pdf                                       1      pod                  run1_2_pod_13.json                                
2025-09-18 14:22:18,239 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/2_pod/2_pod_13.pdf
2025-09-18 14:22:18,239 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_13.json
2025-09-18 14:22:18,239 - INFO - 
2025-09-18 14:22:18,239 - INFO - 2_pod_14.pdf                                       1      pod                  run1_2_pod_14.json                                
2025-09-18 14:22:18,239 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/2_pod/2_pod_14.pdf
2025-09-18 14:22:18,239 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_14.json
2025-09-18 14:22:18,239 - INFO - 
2025-09-18 14:22:18,239 - INFO - 2_pod_2.pdf                                        1      pod                  run1_2_pod_2.json                                 
2025-09-18 14:22:18,239 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/2_pod/2_pod_2.pdf
2025-09-18 14:22:18,239 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_2.json
2025-09-18 14:22:18,239 - INFO - 
2025-09-18 14:22:18,239 - INFO - 2_pod_3.pdf                                        1      pod                  run1_2_pod_3.json                                 
2025-09-18 14:22:18,239 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/2_pod/2_pod_3.pdf
2025-09-18 14:22:18,239 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_3.json
2025-09-18 14:22:18,239 - INFO - 
2025-09-18 14:22:18,239 - INFO - 2_pod_4.pdf                                        1      bol                  run1_2_pod_4.json                                 
2025-09-18 14:22:18,239 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/2_pod/2_pod_4.pdf
2025-09-18 14:22:18,239 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_4.json
2025-09-18 14:22:18,239 - INFO - 
2025-09-18 14:22:18,239 - INFO - 2_pod_5.pdf                                        1      pod                  run1_2_pod_5.json                                 
2025-09-18 14:22:18,239 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/2_pod/2_pod_5.pdf
2025-09-18 14:22:18,239 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_5.json
2025-09-18 14:22:18,239 - INFO - 
2025-09-18 14:22:18,239 - INFO - 2_pod_6.pdf                                        1      pod                  run1_2_pod_6.json                                 
2025-09-18 14:22:18,239 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/2_pod/2_pod_6.pdf
2025-09-18 14:22:18,239 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_6.json
2025-09-18 14:22:18,239 - INFO - 
2025-09-18 14:22:18,239 - INFO - 2_pod_7.pdf                                        1      other                run1_2_pod_7.json                                 
2025-09-18 14:22:18,239 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/2_pod/2_pod_7.pdf
2025-09-18 14:22:18,239 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_7.json
2025-09-18 14:22:18,239 - INFO - 
2025-09-18 14:22:18,239 - INFO - 2_pod_8.pdf                                        1      pod                  run1_2_pod_8.json                                 
2025-09-18 14:22:18,239 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/2_pod/2_pod_8.pdf
2025-09-18 14:22:18,239 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_8.json
2025-09-18 14:22:18,240 - INFO - 
2025-09-18 14:22:18,240 - INFO - 2_pod_9.pdf                                        1      pod                  run1_2_pod_9.json                                 
2025-09-18 14:22:18,240 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/2_pod/2_pod_9.pdf
2025-09-18 14:22:18,240 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_2_pod_9.json
2025-09-18 14:22:18,240 - INFO - 
2025-09-18 14:22:18,240 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:22:18,240 - INFO - Total entries: 13
2025-09-18 14:22:18,240 - INFO - ============================================================================================================================================
2025-09-18 14:22:18,240 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:22:18,240 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:22:18,240 - INFO -   1. 2_pod_10.pdf                        Page 1   → pod             | run1_2_pod_10.json
2025-09-18 14:22:18,240 - INFO -   2. 2_pod_11.pdf                        Page 1   → pod             | run1_2_pod_11.json
2025-09-18 14:22:18,240 - INFO -   3. 2_pod_12.pdf                        Page 1   → pod             | run1_2_pod_12.json
2025-09-18 14:22:18,240 - INFO -   4. 2_pod_13.pdf                        Page 1   → pod             | run1_2_pod_13.json
2025-09-18 14:22:18,240 - INFO -   5. 2_pod_14.pdf                        Page 1   → pod             | run1_2_pod_14.json
2025-09-18 14:22:18,240 - INFO -   6. 2_pod_2.pdf                         Page 1   → pod             | run1_2_pod_2.json
2025-09-18 14:22:18,240 - INFO -   7. 2_pod_3.pdf                         Page 1   → pod             | run1_2_pod_3.json
2025-09-18 14:22:18,240 - INFO -   8. 2_pod_4.pdf                         Page 1   → bol             | run1_2_pod_4.json
2025-09-18 14:22:18,240 - INFO -   9. 2_pod_5.pdf                         Page 1   → pod             | run1_2_pod_5.json
2025-09-18 14:22:18,240 - INFO -  10. 2_pod_6.pdf                         Page 1   → pod             | run1_2_pod_6.json
2025-09-18 14:22:18,240 - INFO -  11. 2_pod_7.pdf                         Page 1   → other           | run1_2_pod_7.json
2025-09-18 14:22:18,240 - INFO -  12. 2_pod_8.pdf                         Page 1   → pod             | run1_2_pod_8.json
2025-09-18 14:22:18,240 - INFO -  13. 2_pod_9.pdf                         Page 1   → pod             | run1_2_pod_9.json
2025-09-18 14:22:18,240 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:22:18,240 - INFO - 
✅ Test completed: {'total_files': 14, 'processed': 13, 'failed': 1, 'errors': ['Failed to process input_data_individual/2_pod/2_pod_1.pdf: Unexpected tool response format from Bedrock'], 'duration_seconds': 25.118153, 'processed_files': [{'filename': '2_pod_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_individual/2_pod/2_pod_10.pdf'}, {'filename': '2_pod_11.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_individual/2_pod/2_pod_11.pdf'}, {'filename': '2_pod_12.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_individual/2_pod/2_pod_12.pdf'}, {'filename': '2_pod_13.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_individual/2_pod/2_pod_13.pdf'}, {'filename': '2_pod_14.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_individual/2_pod/2_pod_14.pdf'}, {'filename': '2_pod_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_individual/2_pod/2_pod_2.pdf'}, {'filename': '2_pod_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_individual/2_pod/2_pod_3.pdf'}, {'filename': '2_pod_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'bol'}]}, 'file_path': 'input_data_individual/2_pod/2_pod_4.pdf'}, {'filename': '2_pod_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_individual/2_pod/2_pod_5.pdf'}, {'filename': '2_pod_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_individual/2_pod/2_pod_6.pdf'}, {'filename': '2_pod_7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_individual/2_pod/2_pod_7.pdf'}, {'filename': '2_pod_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_individual/2_pod/2_pod_8.pdf'}, {'filename': '2_pod_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'pod'}]}, 'file_path': 'input_data_individual/2_pod/2_pod_9.pdf'}]}
