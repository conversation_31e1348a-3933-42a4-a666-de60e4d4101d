2025-09-18 14:24:13,040 - INFO - Logging initialized. Log file: logs/test_classification_20250918_142413.log
2025-09-18 14:24:13,041 - INFO - 📁 Found 2 files to process
2025-09-18 14:24:13,041 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:24:13,041 - INFO - 🚀 Processing 2 files in FORCED PARALLEL MODE...
2025-09-18 14:24:13,041 - INFO - 🚀 Creating 2 parallel tasks...
2025-09-18 14:24:13,041 - INFO - 🚀 All 2 tasks created - executing in parallel...
2025-09-18 14:24:13,041 - INFO - ⬆️ [14:24:13] Uploading: 5_clear_to_pay_1.pdf
2025-09-18 14:24:15,591 - INFO - ✓ Uploaded: input_data_individual/5_clear_to_pay/5_clear_to_pay_1.pdf -> s3://document-extraction-logistically/temp/c8e55f1e_5_clear_to_pay_1.pdf
2025-09-18 14:24:15,591 - INFO - 🔍 [14:24:15] Starting classification: 5_clear_to_pay_1.pdf
2025-09-18 14:24:15,591 - INFO - ⬆️ [14:24:15] Uploading: 5_clear_to_pay_2.pdf
2025-09-18 14:24:15,592 - INFO - Initializing TextractProcessor...
2025-09-18 14:24:15,602 - INFO - Initializing BedrockProcessor...
2025-09-18 14:24:15,606 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c8e55f1e_5_clear_to_pay_1.pdf
2025-09-18 14:24:15,606 - INFO - Processing PDF from S3...
2025-09-18 14:24:15,607 - INFO - Downloading PDF from S3 to /tmp/tmp9m8dvdur/c8e55f1e_5_clear_to_pay_1.pdf
2025-09-18 14:24:16,185 - INFO - ✓ Uploaded: input_data_individual/5_clear_to_pay/5_clear_to_pay_2.pdf -> s3://document-extraction-logistically/temp/8ac55a95_5_clear_to_pay_2.pdf
2025-09-18 14:24:16,185 - INFO - 🔍 [14:24:16] Starting classification: 5_clear_to_pay_2.pdf
2025-09-18 14:24:16,187 - INFO - Initializing TextractProcessor...
2025-09-18 14:24:16,205 - INFO - Initializing BedrockProcessor...
2025-09-18 14:24:16,208 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8ac55a95_5_clear_to_pay_2.pdf
2025-09-18 14:24:16,209 - INFO - Processing PDF from S3...
2025-09-18 14:24:16,209 - INFO - Downloading PDF from S3 to /tmp/tmpephzw31n/8ac55a95_5_clear_to_pay_2.pdf
2025-09-18 14:24:17,924 - INFO - Splitting PDF into individual pages...
2025-09-18 14:24:17,926 - INFO - Splitting PDF c8e55f1e_5_clear_to_pay_1 into 1 pages
2025-09-18 14:24:17,933 - INFO - Split PDF into 1 pages
2025-09-18 14:24:17,933 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:24:17,933 - INFO - Expected pages: [1]
2025-09-18 14:24:17,995 - INFO - Splitting PDF into individual pages...
2025-09-18 14:24:17,996 - INFO - Splitting PDF 8ac55a95_5_clear_to_pay_2 into 1 pages
2025-09-18 14:24:17,999 - INFO - Split PDF into 1 pages
2025-09-18 14:24:17,999 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:24:17,999 - INFO - Expected pages: [1]
2025-09-18 14:24:22,731 - INFO - Page 1: Extracted 721 characters, 35 lines from 8ac55a95_5_clear_to_pay_2_54fe049b_page_001.pdf
2025-09-18 14:24:22,731 - INFO - Successfully processed page 1
2025-09-18 14:24:22,732 - INFO - Combined 1 pages into final text
2025-09-18 14:24:22,732 - INFO - Text validation for 8ac55a95_5_clear_to_pay_2: 738 characters, 1 pages
2025-09-18 14:24:22,732 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:24:22,732 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:24:24,527 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8ac55a95_5_clear_to_pay_2.pdf
2025-09-18 14:24:24,536 - INFO - 

5_clear_to_pay_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-18 14:24:24,536 - INFO - 

✓ Saved result: output/run1_5_clear_to_pay_2.json
2025-09-18 14:24:24,936 - INFO - Page 1: Extracted 749 characters, 42 lines from c8e55f1e_5_clear_to_pay_1_a0dfe723_page_001.pdf
2025-09-18 14:24:24,936 - INFO - Successfully processed page 1
2025-09-18 14:24:24,936 - INFO - Combined 1 pages into final text
2025-09-18 14:24:24,937 - INFO - Text validation for c8e55f1e_5_clear_to_pay_1: 766 characters, 1 pages
2025-09-18 14:24:24,937 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:24:24,937 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:24:25,636 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8ac55a95_5_clear_to_pay_2.pdf
2025-09-18 14:24:27,583 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c8e55f1e_5_clear_to_pay_1.pdf
2025-09-18 14:24:27,606 - INFO - 

5_clear_to_pay_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "clear_to_pay"
        }
    ]
}
2025-09-18 14:24:27,606 - INFO - 

✓ Saved result: output/run1_5_clear_to_pay_1.json
2025-09-18 14:24:27,991 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c8e55f1e_5_clear_to_pay_1.pdf
2025-09-18 14:24:27,992 - INFO - 
📊 Processing Summary:
2025-09-18 14:24:27,992 - INFO -    Total files: 2
2025-09-18 14:24:27,992 - INFO -    Successful: 2
2025-09-18 14:24:27,992 - INFO -    Failed: 0
2025-09-18 14:24:27,992 - INFO -    Duration: 14.95 seconds
2025-09-18 14:24:27,993 - INFO -    Output directory: output
2025-09-18 14:24:27,993 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:24:27,993 - INFO -    📄 5_clear_to_pay_1.pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-18 14:24:27,993 - INFO -    📄 5_clear_to_pay_2.pdf: {"documents":[{"page_no":1,"doc_type":"clear_to_pay"}]}
2025-09-18 14:24:27,993 - INFO - 
============================================================================================================================================
2025-09-18 14:24:27,993 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:24:27,994 - INFO - ============================================================================================================================================
2025-09-18 14:24:27,994 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:24:27,994 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:24:27,994 - INFO - 5_clear_to_pay_1.pdf                               1      clear_to_pay         run1_5_clear_to_pay_1.json                        
2025-09-18 14:24:27,994 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/5_clear_to_pay/5_clear_to_pay_1.pdf
2025-09-18 14:24:27,994 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_5_clear_to_pay_1.json
2025-09-18 14:24:27,994 - INFO - 
2025-09-18 14:24:27,994 - INFO - 5_clear_to_pay_2.pdf                               1      clear_to_pay         run1_5_clear_to_pay_2.json                        
2025-09-18 14:24:27,994 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/5_clear_to_pay/5_clear_to_pay_2.pdf
2025-09-18 14:24:27,994 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_5_clear_to_pay_2.json
2025-09-18 14:24:27,995 - INFO - 
2025-09-18 14:24:27,995 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:24:27,995 - INFO - Total entries: 2
2025-09-18 14:24:27,995 - INFO - ============================================================================================================================================
2025-09-18 14:24:27,995 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:24:27,995 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:24:27,995 - INFO -   1. 5_clear_to_pay_1.pdf                Page 1   → clear_to_pay    | run1_5_clear_to_pay_1.json
2025-09-18 14:24:27,995 - INFO -   2. 5_clear_to_pay_2.pdf                Page 1   → clear_to_pay    | run1_5_clear_to_pay_2.json
2025-09-18 14:24:27,995 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:24:27,996 - INFO - 
✅ Test completed: {'total_files': 2, 'processed': 2, 'failed': 0, 'errors': [], 'duration_seconds': 14.950854, 'processed_files': [{'filename': '5_clear_to_pay_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': 'input_data_individual/5_clear_to_pay/5_clear_to_pay_1.pdf'}, {'filename': '5_clear_to_pay_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'clear_to_pay'}]}, 'file_path': 'input_data_individual/5_clear_to_pay/5_clear_to_pay_2.pdf'}]}
