2025-09-18 14:24:43,193 - INFO - Logging initialized. Log file: logs/test_classification_20250918_142443.log
2025-09-18 14:24:43,193 - INFO - 📁 Found 7 files to process
2025-09-18 14:24:43,193 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:24:43,193 - INFO - 🚀 Processing 7 files in FORCED PARALLEL MODE...
2025-09-18 14:24:43,193 - INFO - 🚀 Creating 7 parallel tasks...
2025-09-18 14:24:43,193 - INFO - 🚀 All 7 tasks created - executing in parallel...
2025-09-18 14:24:43,193 - INFO - ⬆️ [14:24:43] Uploading: 12_combined_carrier_documents_1.pdf
2025-09-18 14:24:45,273 - INFO - ✓ Uploaded: input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_1.pdf -> s3://document-extraction-logistically/temp/c3bcf73d_12_combined_carrier_documents_1.pdf
2025-09-18 14:24:45,273 - INFO - 🔍 [14:24:45] Starting classification: 12_combined_carrier_documents_1.pdf
2025-09-18 14:24:45,274 - INFO - Initializing TextractProcessor...
2025-09-18 14:24:45,274 - INFO - ⬆️ [14:24:45] Uploading: 12_combined_carrier_documents_2.pdf
2025-09-18 14:24:45,295 - INFO - Initializing BedrockProcessor...
2025-09-18 14:24:45,300 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c3bcf73d_12_combined_carrier_documents_1.pdf
2025-09-18 14:24:45,300 - INFO - Processing PDF from S3...
2025-09-18 14:24:45,300 - INFO - Downloading PDF from S3 to /tmp/tmpra4d4kn5/c3bcf73d_12_combined_carrier_documents_1.pdf
2025-09-18 14:24:45,875 - INFO - ✓ Uploaded: input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_2.pdf -> s3://document-extraction-logistically/temp/51eef864_12_combined_carrier_documents_2.pdf
2025-09-18 14:24:45,875 - INFO - 🔍 [14:24:45] Starting classification: 12_combined_carrier_documents_2.pdf
2025-09-18 14:24:45,875 - INFO - ⬆️ [14:24:45] Uploading: 12_combined_carrier_documents_3.pdf
2025-09-18 14:24:45,876 - INFO - Initializing TextractProcessor...
2025-09-18 14:24:45,888 - INFO - Initializing BedrockProcessor...
2025-09-18 14:24:45,890 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/51eef864_12_combined_carrier_documents_2.pdf
2025-09-18 14:24:45,890 - INFO - Processing PDF from S3...
2025-09-18 14:24:45,890 - INFO - Downloading PDF from S3 to /tmp/tmplt9r_xdd/51eef864_12_combined_carrier_documents_2.pdf
2025-09-18 14:24:46,495 - INFO - ✓ Uploaded: input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_3.pdf -> s3://document-extraction-logistically/temp/b8217f12_12_combined_carrier_documents_3.pdf
2025-09-18 14:24:46,495 - INFO - 🔍 [14:24:46] Starting classification: 12_combined_carrier_documents_3.pdf
2025-09-18 14:24:46,496 - INFO - ⬆️ [14:24:46] Uploading: 12_combined_carrier_documents_4.pdf
2025-09-18 14:24:46,497 - INFO - Initializing TextractProcessor...
2025-09-18 14:24:46,506 - INFO - Initializing BedrockProcessor...
2025-09-18 14:24:46,508 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b8217f12_12_combined_carrier_documents_3.pdf
2025-09-18 14:24:46,508 - INFO - Processing PDF from S3...
2025-09-18 14:24:46,508 - INFO - Downloading PDF from S3 to /tmp/tmpggjv8h6p/b8217f12_12_combined_carrier_documents_3.pdf
2025-09-18 14:24:47,123 - INFO - ✓ Uploaded: input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_4.pdf -> s3://document-extraction-logistically/temp/73e3ca3c_12_combined_carrier_documents_4.pdf
2025-09-18 14:24:47,124 - INFO - 🔍 [14:24:47] Starting classification: 12_combined_carrier_documents_4.pdf
2025-09-18 14:24:47,124 - INFO - ⬆️ [14:24:47] Uploading: 12_combined_carrier_documents_5.pdf
2025-09-18 14:24:47,124 - INFO - Initializing TextractProcessor...
2025-09-18 14:24:47,133 - INFO - Initializing BedrockProcessor...
2025-09-18 14:24:47,135 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/73e3ca3c_12_combined_carrier_documents_4.pdf
2025-09-18 14:24:47,135 - INFO - Processing PDF from S3...
2025-09-18 14:24:47,135 - INFO - Downloading PDF from S3 to /tmp/tmp73gpsgf4/73e3ca3c_12_combined_carrier_documents_4.pdf
2025-09-18 14:24:47,297 - INFO - Splitting PDF into individual pages...
2025-09-18 14:24:47,298 - INFO - Splitting PDF c3bcf73d_12_combined_carrier_documents_1 into 2 pages
2025-09-18 14:24:47,301 - INFO - Split PDF into 2 pages
2025-09-18 14:24:47,301 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:24:47,301 - INFO - Expected pages: [1, 2]
2025-09-18 14:24:47,668 - INFO - Splitting PDF into individual pages...
2025-09-18 14:24:47,668 - INFO - Splitting PDF 51eef864_12_combined_carrier_documents_2 into 1 pages
2025-09-18 14:24:47,679 - INFO - Split PDF into 1 pages
2025-09-18 14:24:47,679 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:24:47,679 - INFO - Expected pages: [1]
2025-09-18 14:24:47,717 - INFO - ✓ Uploaded: input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_5.pdf -> s3://document-extraction-logistically/temp/3c126f78_12_combined_carrier_documents_5.pdf
2025-09-18 14:24:47,717 - INFO - 🔍 [14:24:47] Starting classification: 12_combined_carrier_documents_5.pdf
2025-09-18 14:24:47,718 - INFO - ⬆️ [14:24:47] Uploading: 12_combined_carrier_documents_6.pdf
2025-09-18 14:24:47,719 - INFO - Initializing TextractProcessor...
2025-09-18 14:24:47,726 - INFO - Initializing BedrockProcessor...
2025-09-18 14:24:47,727 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3c126f78_12_combined_carrier_documents_5.pdf
2025-09-18 14:24:47,728 - INFO - Processing PDF from S3...
2025-09-18 14:24:47,728 - INFO - Downloading PDF from S3 to /tmp/tmpa1l3tpd_/3c126f78_12_combined_carrier_documents_5.pdf
2025-09-18 14:24:48,219 - INFO - Splitting PDF into individual pages...
2025-09-18 14:24:48,221 - INFO - Splitting PDF b8217f12_12_combined_carrier_documents_3 into 1 pages
2025-09-18 14:24:48,242 - INFO - Split PDF into 1 pages
2025-09-18 14:24:48,242 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:24:48,242 - INFO - Expected pages: [1]
2025-09-18 14:24:48,312 - INFO - ✓ Uploaded: input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_6.pdf -> s3://document-extraction-logistically/temp/fe49d29c_12_combined_carrier_documents_6.pdf
2025-09-18 14:24:48,312 - INFO - 🔍 [14:24:48] Starting classification: 12_combined_carrier_documents_6.pdf
2025-09-18 14:24:48,312 - INFO - ⬆️ [14:24:48] Uploading: 12_combined_carrier_documents_7.pdf
2025-09-18 14:24:48,313 - INFO - Initializing TextractProcessor...
2025-09-18 14:24:48,348 - INFO - Initializing BedrockProcessor...
2025-09-18 14:24:48,352 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fe49d29c_12_combined_carrier_documents_6.pdf
2025-09-18 14:24:48,352 - INFO - Processing PDF from S3...
2025-09-18 14:24:48,352 - INFO - Downloading PDF from S3 to /tmp/tmpuf1wpvaw/fe49d29c_12_combined_carrier_documents_6.pdf
2025-09-18 14:24:48,910 - INFO - Splitting PDF into individual pages...
2025-09-18 14:24:48,912 - INFO - Splitting PDF 73e3ca3c_12_combined_carrier_documents_4 into 1 pages
2025-09-18 14:24:48,952 - INFO - Split PDF into 1 pages
2025-09-18 14:24:48,952 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:24:48,952 - INFO - Expected pages: [1]
2025-09-18 14:24:48,956 - INFO - ✓ Uploaded: input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_7.pdf -> s3://document-extraction-logistically/temp/12cc49bf_12_combined_carrier_documents_7.pdf
2025-09-18 14:24:48,957 - INFO - 🔍 [14:24:48] Starting classification: 12_combined_carrier_documents_7.pdf
2025-09-18 14:24:48,957 - INFO - Initializing TextractProcessor...
2025-09-18 14:24:48,966 - INFO - Initializing BedrockProcessor...
2025-09-18 14:24:48,968 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/12cc49bf_12_combined_carrier_documents_7.pdf
2025-09-18 14:24:48,968 - INFO - Processing PDF from S3...
2025-09-18 14:24:48,968 - INFO - Downloading PDF from S3 to /tmp/tmphb5noq2s/12cc49bf_12_combined_carrier_documents_7.pdf
2025-09-18 14:24:49,480 - INFO - Splitting PDF into individual pages...
2025-09-18 14:24:49,482 - INFO - Splitting PDF 3c126f78_12_combined_carrier_documents_5 into 1 pages
2025-09-18 14:24:49,495 - INFO - Split PDF into 1 pages
2025-09-18 14:24:49,495 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:24:49,495 - INFO - Expected pages: [1]
2025-09-18 14:24:50,115 - INFO - Splitting PDF into individual pages...
2025-09-18 14:24:50,118 - INFO - Splitting PDF fe49d29c_12_combined_carrier_documents_6 into 1 pages
2025-09-18 14:24:50,139 - INFO - Split PDF into 1 pages
2025-09-18 14:24:50,139 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:24:50,139 - INFO - Expected pages: [1]
2025-09-18 14:24:50,770 - INFO - Splitting PDF into individual pages...
2025-09-18 14:24:50,773 - INFO - Splitting PDF 12cc49bf_12_combined_carrier_documents_7 into 1 pages
2025-09-18 14:24:50,796 - INFO - Split PDF into 1 pages
2025-09-18 14:24:50,797 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:24:50,797 - INFO - Expected pages: [1]
2025-09-18 14:24:50,846 - INFO - Page 2: Extracted 313 characters, 9 lines from c3bcf73d_12_combined_carrier_documents_1_ee61e596_page_002.pdf
2025-09-18 14:24:50,846 - INFO - Successfully processed page 2
2025-09-18 14:24:52,515 - INFO - Page 1: Extracted 1231 characters, 84 lines from c3bcf73d_12_combined_carrier_documents_1_ee61e596_page_001.pdf
2025-09-18 14:24:52,515 - INFO - Successfully processed page 1
2025-09-18 14:24:52,516 - INFO - Combined 2 pages into final text
2025-09-18 14:24:52,516 - INFO - Text validation for c3bcf73d_12_combined_carrier_documents_1: 1580 characters, 2 pages
2025-09-18 14:24:52,516 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:24:52,516 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:24:53,556 - INFO - Page 1: Extracted 4164 characters, 222 lines from 51eef864_12_combined_carrier_documents_2_e5967186_page_001.pdf
2025-09-18 14:24:53,556 - INFO - Successfully processed page 1
2025-09-18 14:24:53,556 - INFO - Combined 1 pages into final text
2025-09-18 14:24:53,556 - INFO - Text validation for 51eef864_12_combined_carrier_documents_2: 4181 characters, 1 pages
2025-09-18 14:24:53,557 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:24:53,557 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:24:54,029 - INFO - Page 1: Extracted 4024 characters, 186 lines from b8217f12_12_combined_carrier_documents_3_c16d9683_page_001.pdf
2025-09-18 14:24:54,029 - INFO - Successfully processed page 1
2025-09-18 14:24:54,029 - INFO - Combined 1 pages into final text
2025-09-18 14:24:54,029 - INFO - Text validation for b8217f12_12_combined_carrier_documents_3: 4041 characters, 1 pages
2025-09-18 14:24:54,030 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:24:54,030 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:24:55,361 - INFO - Page 1: Extracted 4240 characters, 215 lines from 3c126f78_12_combined_carrier_documents_5_a472e64a_page_001.pdf
2025-09-18 14:24:55,361 - INFO - Successfully processed page 1
2025-09-18 14:24:55,361 - INFO - Combined 1 pages into final text
2025-09-18 14:24:55,362 - INFO - Text validation for 3c126f78_12_combined_carrier_documents_5: 4257 characters, 1 pages
2025-09-18 14:24:55,362 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:24:55,362 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:24:55,720 - INFO - Page 1: Extracted 4367 characters, 225 lines from 73e3ca3c_12_combined_carrier_documents_4_0a2382df_page_001.pdf
2025-09-18 14:24:55,721 - INFO - Successfully processed page 1
2025-09-18 14:24:55,721 - INFO - Combined 1 pages into final text
2025-09-18 14:24:55,721 - INFO - Text validation for 73e3ca3c_12_combined_carrier_documents_4: 4384 characters, 1 pages
2025-09-18 14:24:55,721 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:24:55,721 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:24:55,938 - INFO - Page 1: Extracted 4238 characters, 216 lines from fe49d29c_12_combined_carrier_documents_6_d42f7828_page_001.pdf
2025-09-18 14:24:55,938 - INFO - Successfully processed page 1
2025-09-18 14:24:55,938 - INFO - Combined 1 pages into final text
2025-09-18 14:24:55,938 - INFO - Text validation for fe49d29c_12_combined_carrier_documents_6: 4255 characters, 1 pages
2025-09-18 14:24:55,939 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:24:55,939 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:24:56,215 - INFO - Page 1: Extracted 2583 characters, 132 lines from 12cc49bf_12_combined_carrier_documents_7_be7fc83c_page_001.pdf
2025-09-18 14:24:56,215 - INFO - Successfully processed page 1
2025-09-18 14:24:56,215 - INFO - Combined 1 pages into final text
2025-09-18 14:24:56,215 - INFO - Text validation for 12cc49bf_12_combined_carrier_documents_7: 2600 characters, 1 pages
2025-09-18 14:24:56,216 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:24:56,216 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:24:56,819 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c3bcf73d_12_combined_carrier_documents_1.pdf
2025-09-18 14:24:56,852 - INFO - 

12_combined_carrier_documents_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        },
        {
            "page_no": 2,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 14:24:56,852 - INFO - 

✓ Saved result: output/run1_12_combined_carrier_documents_1.json
2025-09-18 14:24:56,916 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/51eef864_12_combined_carrier_documents_2.pdf
2025-09-18 14:24:57,684 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3c126f78_12_combined_carrier_documents_5.pdf
2025-09-18 14:24:57,798 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c3bcf73d_12_combined_carrier_documents_1.pdf
2025-09-18 14:24:57,850 - INFO - 

12_combined_carrier_documents_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 14:24:57,851 - INFO - 

✓ Saved result: output/run1_12_combined_carrier_documents_2.json
2025-09-18 14:24:58,045 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b8217f12_12_combined_carrier_documents_3.pdf
2025-09-18 14:24:58,151 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/51eef864_12_combined_carrier_documents_2.pdf
2025-09-18 14:24:58,168 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/73e3ca3c_12_combined_carrier_documents_4.pdf
2025-09-18 14:24:58,206 - INFO - 

12_combined_carrier_documents_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 14:24:58,206 - INFO - 

✓ Saved result: output/run1_12_combined_carrier_documents_5.json
2025-09-18 14:24:58,511 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3c126f78_12_combined_carrier_documents_5.pdf
2025-09-18 14:24:58,546 - INFO - 

12_combined_carrier_documents_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 14:24:58,546 - INFO - 

✓ Saved result: output/run1_12_combined_carrier_documents_3.json
2025-09-18 14:24:58,845 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b8217f12_12_combined_carrier_documents_3.pdf
2025-09-18 14:24:58,893 - INFO - 

12_combined_carrier_documents_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:24:58,893 - INFO - 

✓ Saved result: output/run1_12_combined_carrier_documents_4.json
2025-09-18 14:24:59,196 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/73e3ca3c_12_combined_carrier_documents_4.pdf
2025-09-18 14:24:59,548 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/12cc49bf_12_combined_carrier_documents_7.pdf
2025-09-18 14:24:59,583 - INFO - 

12_combined_carrier_documents_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 14:24:59,583 - INFO - 

✓ Saved result: output/run1_12_combined_carrier_documents_7.json
2025-09-18 14:24:59,632 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fe49d29c_12_combined_carrier_documents_6.pdf
2025-09-18 14:24:59,951 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/12cc49bf_12_combined_carrier_documents_7.pdf
2025-09-18 14:24:59,995 - INFO - 

12_combined_carrier_documents_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "combined_carrier_documents"
        }
    ]
}
2025-09-18 14:24:59,995 - INFO - 

✓ Saved result: output/run1_12_combined_carrier_documents_6.json
2025-09-18 14:25:00,356 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fe49d29c_12_combined_carrier_documents_6.pdf
2025-09-18 14:25:00,356 - INFO - 
📊 Processing Summary:
2025-09-18 14:25:00,356 - INFO -    Total files: 7
2025-09-18 14:25:00,357 - INFO -    Successful: 7
2025-09-18 14:25:00,357 - INFO -    Failed: 0
2025-09-18 14:25:00,357 - INFO -    Duration: 17.16 seconds
2025-09-18 14:25:00,357 - INFO -    Output directory: output
2025-09-18 14:25:00,357 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:25:00,357 - INFO -    📄 12_combined_carrier_documents_1.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"},{"page_no":2,"doc_type":"combined_carrier_documents"}]}
2025-09-18 14:25:00,357 - INFO -    📄 12_combined_carrier_documents_2.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"}]}
2025-09-18 14:25:00,357 - INFO -    📄 12_combined_carrier_documents_3.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"}]}
2025-09-18 14:25:00,357 - INFO -    📄 12_combined_carrier_documents_4.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:25:00,357 - INFO -    📄 12_combined_carrier_documents_5.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"}]}
2025-09-18 14:25:00,357 - INFO -    📄 12_combined_carrier_documents_6.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"}]}
2025-09-18 14:25:00,357 - INFO -    📄 12_combined_carrier_documents_7.pdf: {"documents":[{"page_no":1,"doc_type":"combined_carrier_documents"}]}
2025-09-18 14:25:00,357 - INFO - 
============================================================================================================================================
2025-09-18 14:25:00,357 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:25:00,357 - INFO - ============================================================================================================================================
2025-09-18 14:25:00,357 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:25:00,357 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:25:00,357 - INFO - 12_combined_carrier_documents_1.pdf                1      combined_carrier_d... run1_12_combined_carrier_documents_1.json         
2025-09-18 14:25:00,357 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_1.pdf
2025-09-18 14:25:00,357 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_1.json
2025-09-18 14:25:00,357 - INFO - 
2025-09-18 14:25:00,357 - INFO - 12_combined_carrier_documents_1.pdf                2      combined_carrier_d... run1_12_combined_carrier_documents_1.json         
2025-09-18 14:25:00,357 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_1.pdf
2025-09-18 14:25:00,357 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_1.json
2025-09-18 14:25:00,357 - INFO - 
2025-09-18 14:25:00,357 - INFO - 12_combined_carrier_documents_2.pdf                1      combined_carrier_d... run1_12_combined_carrier_documents_2.json         
2025-09-18 14:25:00,357 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_2.pdf
2025-09-18 14:25:00,357 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_2.json
2025-09-18 14:25:00,357 - INFO - 
2025-09-18 14:25:00,357 - INFO - 12_combined_carrier_documents_3.pdf                1      combined_carrier_d... run1_12_combined_carrier_documents_3.json         
2025-09-18 14:25:00,357 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_3.pdf
2025-09-18 14:25:00,357 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_3.json
2025-09-18 14:25:00,357 - INFO - 
2025-09-18 14:25:00,357 - INFO - 12_combined_carrier_documents_4.pdf                1      invoice              run1_12_combined_carrier_documents_4.json         
2025-09-18 14:25:00,357 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_4.pdf
2025-09-18 14:25:00,357 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_4.json
2025-09-18 14:25:00,357 - INFO - 
2025-09-18 14:25:00,357 - INFO - 12_combined_carrier_documents_5.pdf                1      combined_carrier_d... run1_12_combined_carrier_documents_5.json         
2025-09-18 14:25:00,358 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_5.pdf
2025-09-18 14:25:00,358 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_5.json
2025-09-18 14:25:00,358 - INFO - 
2025-09-18 14:25:00,358 - INFO - 12_combined_carrier_documents_6.pdf                1      combined_carrier_d... run1_12_combined_carrier_documents_6.json         
2025-09-18 14:25:00,358 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_6.pdf
2025-09-18 14:25:00,358 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_6.json
2025-09-18 14:25:00,358 - INFO - 
2025-09-18 14:25:00,358 - INFO - 12_combined_carrier_documents_7.pdf                1      combined_carrier_d... run1_12_combined_carrier_documents_7.json         
2025-09-18 14:25:00,358 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_7.pdf
2025-09-18 14:25:00,358 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_12_combined_carrier_documents_7.json
2025-09-18 14:25:00,358 - INFO - 
2025-09-18 14:25:00,358 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:25:00,358 - INFO - Total entries: 8
2025-09-18 14:25:00,358 - INFO - ============================================================================================================================================
2025-09-18 14:25:00,358 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:25:00,358 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:25:00,358 - INFO -   1. 12_combined_carrier_documents_1.pdf Page 1   → combined_carrier_documents | run1_12_combined_carrier_documents_1.json
2025-09-18 14:25:00,358 - INFO -   2. 12_combined_carrier_documents_1.pdf Page 2   → combined_carrier_documents | run1_12_combined_carrier_documents_1.json
2025-09-18 14:25:00,358 - INFO -   3. 12_combined_carrier_documents_2.pdf Page 1   → combined_carrier_documents | run1_12_combined_carrier_documents_2.json
2025-09-18 14:25:00,358 - INFO -   4. 12_combined_carrier_documents_3.pdf Page 1   → combined_carrier_documents | run1_12_combined_carrier_documents_3.json
2025-09-18 14:25:00,358 - INFO -   5. 12_combined_carrier_documents_4.pdf Page 1   → invoice         | run1_12_combined_carrier_documents_4.json
2025-09-18 14:25:00,358 - INFO -   6. 12_combined_carrier_documents_5.pdf Page 1   → combined_carrier_documents | run1_12_combined_carrier_documents_5.json
2025-09-18 14:25:00,358 - INFO -   7. 12_combined_carrier_documents_6.pdf Page 1   → combined_carrier_documents | run1_12_combined_carrier_documents_6.json
2025-09-18 14:25:00,358 - INFO -   8. 12_combined_carrier_documents_7.pdf Page 1   → combined_carrier_documents | run1_12_combined_carrier_documents_7.json
2025-09-18 14:25:00,358 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:25:00,358 - INFO - 
✅ Test completed: {'total_files': 7, 'processed': 7, 'failed': 0, 'errors': [], 'duration_seconds': 17.163204, 'processed_files': [{'filename': '12_combined_carrier_documents_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}, {'page_no': 2, 'doc_type': 'combined_carrier_documents'}]}, 'file_path': 'input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_1.pdf'}, {'filename': '12_combined_carrier_documents_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}]}, 'file_path': 'input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_2.pdf'}, {'filename': '12_combined_carrier_documents_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}]}, 'file_path': 'input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_3.pdf'}, {'filename': '12_combined_carrier_documents_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_4.pdf'}, {'filename': '12_combined_carrier_documents_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}]}, 'file_path': 'input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_5.pdf'}, {'filename': '12_combined_carrier_documents_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}]}, 'file_path': 'input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_6.pdf'}, {'filename': '12_combined_carrier_documents_7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'combined_carrier_documents'}]}, 'file_path': 'input_data_individual/12_combined_carrier_documents/12_combined_carrier_documents_7.pdf'}]}
