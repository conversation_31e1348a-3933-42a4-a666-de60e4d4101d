2025-09-18 14:32:04,513 - INFO - Logging initialized. Log file: logs/test_classification_20250918_143204.log
2025-09-18 14:32:04,513 - INFO - 📁 Found 3 files to process
2025-09-18 14:32:04,513 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:32:04,513 - INFO - 🚀 Processing 3 files in FORCED PARALLEL MODE...
2025-09-18 14:32:04,513 - INFO - 🚀 Creating 3 parallel tasks...
2025-09-18 14:32:04,513 - INFO - 🚀 All 3 tasks created - executing in parallel...
2025-09-18 14:32:04,513 - INFO - ⬆️ [14:32:04] Uploading: 10_invoice_1.pdf
2025-09-18 14:32:06,057 - INFO - ✓ Uploaded: input_data_3_per_cat/10_invoice/10_invoice_1.pdf -> s3://document-extraction-logistically/temp/b203903a_10_invoice_1.pdf
2025-09-18 14:32:06,057 - INFO - 🔍 [14:32:06] Starting classification: 10_invoice_1.pdf
2025-09-18 14:32:06,058 - INFO - ⬆️ [14:32:06] Uploading: 10_invoice_10.pdf
2025-09-18 14:32:06,059 - INFO - Initializing TextractProcessor...
2025-09-18 14:32:06,090 - INFO - Initializing BedrockProcessor...
2025-09-18 14:32:06,100 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b203903a_10_invoice_1.pdf
2025-09-18 14:32:06,101 - INFO - Processing PDF from S3...
2025-09-18 14:32:06,101 - INFO - Downloading PDF from S3 to /tmp/tmp88_59wz5/b203903a_10_invoice_1.pdf
2025-09-18 14:32:06,665 - INFO - ✓ Uploaded: input_data_3_per_cat/10_invoice/10_invoice_10.pdf -> s3://document-extraction-logistically/temp/41576552_10_invoice_10.pdf
2025-09-18 14:32:06,665 - INFO - 🔍 [14:32:06] Starting classification: 10_invoice_10.pdf
2025-09-18 14:32:06,665 - INFO - ⬆️ [14:32:06] Uploading: 10_invoice_2.pdf
2025-09-18 14:32:06,666 - INFO - Initializing TextractProcessor...
2025-09-18 14:32:06,674 - INFO - Initializing BedrockProcessor...
2025-09-18 14:32:06,676 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/41576552_10_invoice_10.pdf
2025-09-18 14:32:06,676 - INFO - Processing PDF from S3...
2025-09-18 14:32:06,676 - INFO - Downloading PDF from S3 to /tmp/tmp11op3kdi/41576552_10_invoice_10.pdf
2025-09-18 14:32:07,758 - INFO - Splitting PDF into individual pages...
2025-09-18 14:32:07,760 - INFO - Splitting PDF b203903a_10_invoice_1 into 1 pages
2025-09-18 14:32:07,769 - INFO - Split PDF into 1 pages
2025-09-18 14:32:07,769 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:32:07,769 - INFO - Expected pages: [1]
2025-09-18 14:32:07,887 - INFO - Splitting PDF into individual pages...
2025-09-18 14:32:07,889 - INFO - Splitting PDF 41576552_10_invoice_10 into 1 pages
2025-09-18 14:32:07,895 - INFO - Split PDF into 1 pages
2025-09-18 14:32:07,895 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:32:07,895 - INFO - Expected pages: [1]
2025-09-18 14:32:08,423 - INFO - ✓ Uploaded: input_data_3_per_cat/10_invoice/10_invoice_2.pdf -> s3://document-extraction-logistically/temp/bbf173bc_10_invoice_2.pdf
2025-09-18 14:32:08,424 - INFO - 🔍 [14:32:08] Starting classification: 10_invoice_2.pdf
2025-09-18 14:32:08,425 - INFO - Initializing TextractProcessor...
2025-09-18 14:32:08,441 - INFO - Initializing BedrockProcessor...
2025-09-18 14:32:08,444 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bbf173bc_10_invoice_2.pdf
2025-09-18 14:32:08,445 - INFO - Processing PDF from S3...
2025-09-18 14:32:08,445 - INFO - Downloading PDF from S3 to /tmp/tmpatkk4c8x/bbf173bc_10_invoice_2.pdf
2025-09-18 14:32:10,811 - INFO - Splitting PDF into individual pages...
2025-09-18 14:32:10,819 - INFO - Splitting PDF bbf173bc_10_invoice_2 into 1 pages
2025-09-18 14:32:10,863 - INFO - Split PDF into 1 pages
2025-09-18 14:32:10,863 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:32:10,863 - INFO - Expected pages: [1]
2025-09-18 14:32:12,740 - INFO - Page 1: Extracted 659 characters, 47 lines from 41576552_10_invoice_10_83298da6_page_001.pdf
2025-09-18 14:32:12,741 - INFO - Successfully processed page 1
2025-09-18 14:32:12,741 - INFO - Combined 1 pages into final text
2025-09-18 14:32:12,742 - INFO - Text validation for 41576552_10_invoice_10: 676 characters, 1 pages
2025-09-18 14:32:12,742 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:32:12,742 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:32:13,481 - INFO - Page 1: Extracted 1418 characters, 97 lines from b203903a_10_invoice_1_dd1e2bf5_page_001.pdf
2025-09-18 14:32:13,482 - INFO - Successfully processed page 1
2025-09-18 14:32:13,482 - INFO - Combined 1 pages into final text
2025-09-18 14:32:13,482 - INFO - Text validation for b203903a_10_invoice_1: 1435 characters, 1 pages
2025-09-18 14:32:13,482 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:32:13,482 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:32:14,449 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/41576552_10_invoice_10.pdf
2025-09-18 14:32:14,468 - INFO - 

10_invoice_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:32:14,469 - INFO - 

✓ Saved result: output/run1_10_invoice_10.json
2025-09-18 14:32:15,402 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/41576552_10_invoice_10.pdf
2025-09-18 14:32:15,722 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b203903a_10_invoice_1.pdf
2025-09-18 14:32:15,746 - INFO - 

10_invoice_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:32:15,746 - INFO - 

✓ Saved result: output/run1_10_invoice_1.json
2025-09-18 14:32:16,043 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b203903a_10_invoice_1.pdf
2025-09-18 14:32:16,878 - INFO - Page 1: Extracted 940 characters, 39 lines from bbf173bc_10_invoice_2_7d267918_page_001.pdf
2025-09-18 14:32:16,879 - INFO - Successfully processed page 1
2025-09-18 14:32:16,879 - INFO - Combined 1 pages into final text
2025-09-18 14:32:16,879 - INFO - Text validation for bbf173bc_10_invoice_2: 957 characters, 1 pages
2025-09-18 14:32:16,879 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:32:16,879 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:32:18,666 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bbf173bc_10_invoice_2.pdf
2025-09-18 14:32:18,679 - INFO - 

10_invoice_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:32:18,679 - INFO - 

✓ Saved result: output/run1_10_invoice_2.json
2025-09-18 14:32:18,981 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bbf173bc_10_invoice_2.pdf
2025-09-18 14:32:18,982 - INFO - 
📊 Processing Summary:
2025-09-18 14:32:18,983 - INFO -    Total files: 3
2025-09-18 14:32:18,983 - INFO -    Successful: 3
2025-09-18 14:32:18,983 - INFO -    Failed: 0
2025-09-18 14:32:18,983 - INFO -    Duration: 14.47 seconds
2025-09-18 14:32:18,983 - INFO -    Output directory: output
2025-09-18 14:32:18,983 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:32:18,983 - INFO -    📄 10_invoice_1.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:32:18,983 - INFO -    📄 10_invoice_10.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:32:18,983 - INFO -    📄 10_invoice_2.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:32:18,984 - INFO - 
============================================================================================================================================
2025-09-18 14:32:18,984 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:32:18,984 - INFO - ============================================================================================================================================
2025-09-18 14:32:18,984 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:32:18,984 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:32:18,984 - INFO - 10_invoice_1.pdf                                   1      invoice              run1_10_invoice_1.json                            
2025-09-18 14:32:18,984 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice/10_invoice_1.pdf
2025-09-18 14:32:18,984 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_1.json
2025-09-18 14:32:18,984 - INFO - 
2025-09-18 14:32:18,984 - INFO - 10_invoice_10.pdf                                  1      invoice              run1_10_invoice_10.json                           
2025-09-18 14:32:18,984 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice/10_invoice_10.pdf
2025-09-18 14:32:18,984 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_10.json
2025-09-18 14:32:18,984 - INFO - 
2025-09-18 14:32:18,984 - INFO - 10_invoice_2.pdf                                   1      invoice              run1_10_invoice_2.json                            
2025-09-18 14:32:18,984 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice/10_invoice_2.pdf
2025-09-18 14:32:18,985 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_2.json
2025-09-18 14:32:18,985 - INFO - 
2025-09-18 14:32:18,985 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:32:18,985 - INFO - Total entries: 3
2025-09-18 14:32:18,985 - INFO - ============================================================================================================================================
2025-09-18 14:32:18,985 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:32:18,985 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:32:18,985 - INFO -   1. 10_invoice_1.pdf                    Page 1   → invoice         | run1_10_invoice_1.json
2025-09-18 14:32:18,985 - INFO -   2. 10_invoice_10.pdf                   Page 1   → invoice         | run1_10_invoice_10.json
2025-09-18 14:32:18,985 - INFO -   3. 10_invoice_2.pdf                    Page 1   → invoice         | run1_10_invoice_2.json
2025-09-18 14:32:18,985 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:32:18,985 - INFO - 
✅ Test completed: {'total_files': 3, 'processed': 3, 'failed': 0, 'errors': [], 'duration_seconds': 14.469218, 'processed_files': [{'filename': '10_invoice_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_3_per_cat/10_invoice/10_invoice_1.pdf'}, {'filename': '10_invoice_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_3_per_cat/10_invoice/10_invoice_10.pdf'}, {'filename': '10_invoice_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_3_per_cat/10_invoice/10_invoice_2.pdf'}]}
