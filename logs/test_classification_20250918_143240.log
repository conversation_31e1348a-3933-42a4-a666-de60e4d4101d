2025-09-18 14:32:40,600 - INFO - Logging initialized. Log file: logs/test_classification_20250918_143240.log
2025-09-18 14:32:40,600 - INFO - 📁 Found 10 files to process
2025-09-18 14:32:40,601 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:32:40,601 - INFO - 🚀 Processing 10 files in FORCED PARALLEL MODE...
2025-09-18 14:32:40,601 - INFO - 🚀 Creating 10 parallel tasks...
2025-09-18 14:32:40,601 - INFO - 🚀 All 10 tasks created - executing in parallel...
2025-09-18 14:32:40,601 - INFO - ⬆️ [14:32:40] Uploading: 10_invoice_1.pdf
2025-09-18 14:32:42,291 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_1.pdf -> s3://document-extraction-logistically/temp/21a21375_10_invoice_1.pdf
2025-09-18 14:32:42,292 - INFO - 🔍 [14:32:42] Starting classification: 10_invoice_1.pdf
2025-09-18 14:32:42,293 - INFO - ⬆️ [14:32:42] Uploading: 10_invoice_10.pdf
2025-09-18 14:32:42,295 - INFO - Initializing TextractProcessor...
2025-09-18 14:32:42,324 - INFO - Initializing BedrockProcessor...
2025-09-18 14:32:42,331 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/21a21375_10_invoice_1.pdf
2025-09-18 14:32:42,331 - INFO - Processing PDF from S3...
2025-09-18 14:32:42,331 - INFO - Downloading PDF from S3 to /tmp/tmpm69mzhb3/21a21375_10_invoice_1.pdf
2025-09-18 14:32:42,902 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_10.pdf -> s3://document-extraction-logistically/temp/0355cd14_10_invoice_10.pdf
2025-09-18 14:32:42,902 - INFO - 🔍 [14:32:42] Starting classification: 10_invoice_10.pdf
2025-09-18 14:32:42,903 - INFO - ⬆️ [14:32:42] Uploading: 10_invoice_2.pdf
2025-09-18 14:32:42,906 - INFO - Initializing TextractProcessor...
2025-09-18 14:32:42,928 - INFO - Initializing BedrockProcessor...
2025-09-18 14:32:42,933 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0355cd14_10_invoice_10.pdf
2025-09-18 14:32:42,933 - INFO - Processing PDF from S3...
2025-09-18 14:32:42,934 - INFO - Downloading PDF from S3 to /tmp/tmpd56mzt0j/0355cd14_10_invoice_10.pdf
2025-09-18 14:32:43,516 - INFO - Splitting PDF into individual pages...
2025-09-18 14:32:43,517 - INFO - Splitting PDF 21a21375_10_invoice_1 into 1 pages
2025-09-18 14:32:43,521 - INFO - Split PDF into 1 pages
2025-09-18 14:32:43,521 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:32:43,521 - INFO - Expected pages: [1]
2025-09-18 14:32:44,203 - INFO - Splitting PDF into individual pages...
2025-09-18 14:32:44,204 - INFO - Splitting PDF 0355cd14_10_invoice_10 into 1 pages
2025-09-18 14:32:44,208 - INFO - Split PDF into 1 pages
2025-09-18 14:32:44,209 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:32:44,209 - INFO - Expected pages: [1]
2025-09-18 14:32:44,961 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_2.pdf -> s3://document-extraction-logistically/temp/ecef7306_10_invoice_2.pdf
2025-09-18 14:32:44,962 - INFO - 🔍 [14:32:44] Starting classification: 10_invoice_2.pdf
2025-09-18 14:32:44,963 - INFO - ⬆️ [14:32:44] Uploading: 10_invoice_3.pdf
2025-09-18 14:32:44,969 - INFO - Initializing TextractProcessor...
2025-09-18 14:32:44,980 - INFO - Initializing BedrockProcessor...
2025-09-18 14:32:44,983 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ecef7306_10_invoice_2.pdf
2025-09-18 14:32:44,984 - INFO - Processing PDF from S3...
2025-09-18 14:32:44,984 - INFO - Downloading PDF from S3 to /tmp/tmp80nymgyp/ecef7306_10_invoice_2.pdf
2025-09-18 14:32:45,566 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_3.pdf -> s3://document-extraction-logistically/temp/8c9a943e_10_invoice_3.pdf
2025-09-18 14:32:45,566 - INFO - 🔍 [14:32:45] Starting classification: 10_invoice_3.pdf
2025-09-18 14:32:45,567 - INFO - ⬆️ [14:32:45] Uploading: 10_invoice_4.pdf
2025-09-18 14:32:45,567 - INFO - Initializing TextractProcessor...
2025-09-18 14:32:45,587 - INFO - Initializing BedrockProcessor...
2025-09-18 14:32:45,593 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8c9a943e_10_invoice_3.pdf
2025-09-18 14:32:45,594 - INFO - Processing PDF from S3...
2025-09-18 14:32:45,594 - INFO - Downloading PDF from S3 to /tmp/tmpa31iooq4/8c9a943e_10_invoice_3.pdf
2025-09-18 14:32:46,218 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_4.pdf -> s3://document-extraction-logistically/temp/0ce67bf5_10_invoice_4.pdf
2025-09-18 14:32:46,218 - INFO - 🔍 [14:32:46] Starting classification: 10_invoice_4.pdf
2025-09-18 14:32:46,219 - INFO - Initializing TextractProcessor...
2025-09-18 14:32:46,219 - INFO - ⬆️ [14:32:46] Uploading: 10_invoice_5.pdf
2025-09-18 14:32:46,231 - INFO - Initializing BedrockProcessor...
2025-09-18 14:32:46,242 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0ce67bf5_10_invoice_4.pdf
2025-09-18 14:32:46,242 - INFO - Processing PDF from S3...
2025-09-18 14:32:46,243 - INFO - Downloading PDF from S3 to /tmp/tmppoz6lwis/0ce67bf5_10_invoice_4.pdf
2025-09-18 14:32:46,861 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_5.pdf -> s3://document-extraction-logistically/temp/72bf0573_10_invoice_5.pdf
2025-09-18 14:32:46,861 - INFO - 🔍 [14:32:46] Starting classification: 10_invoice_5.pdf
2025-09-18 14:32:46,861 - INFO - ⬆️ [14:32:46] Uploading: 10_invoice_6.pdf
2025-09-18 14:32:46,863 - INFO - Initializing TextractProcessor...
2025-09-18 14:32:46,875 - INFO - Initializing BedrockProcessor...
2025-09-18 14:32:46,881 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/72bf0573_10_invoice_5.pdf
2025-09-18 14:32:46,881 - INFO - Processing PDF from S3...
2025-09-18 14:32:46,882 - INFO - Downloading PDF from S3 to /tmp/tmpfufn1bip/72bf0573_10_invoice_5.pdf
2025-09-18 14:32:46,899 - INFO - Splitting PDF into individual pages...
2025-09-18 14:32:46,901 - INFO - Splitting PDF 8c9a943e_10_invoice_3 into 1 pages
2025-09-18 14:32:46,903 - INFO - Split PDF into 1 pages
2025-09-18 14:32:46,903 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:32:46,903 - INFO - Expected pages: [1]
2025-09-18 14:32:47,498 - INFO - Splitting PDF into individual pages...
2025-09-18 14:32:47,505 - INFO - Splitting PDF ecef7306_10_invoice_2 into 1 pages
2025-09-18 14:32:47,565 - INFO - Split PDF into 1 pages
2025-09-18 14:32:47,566 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:32:47,566 - INFO - Expected pages: [1]
2025-09-18 14:32:47,765 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_6.pdf -> s3://document-extraction-logistically/temp/48bab12f_10_invoice_6.pdf
2025-09-18 14:32:47,766 - INFO - 🔍 [14:32:47] Starting classification: 10_invoice_6.pdf
2025-09-18 14:32:47,767 - INFO - ⬆️ [14:32:47] Uploading: 10_invoice_7.pdf
2025-09-18 14:32:47,769 - INFO - Initializing TextractProcessor...
2025-09-18 14:32:47,785 - INFO - Initializing BedrockProcessor...
2025-09-18 14:32:47,788 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/48bab12f_10_invoice_6.pdf
2025-09-18 14:32:47,789 - INFO - Processing PDF from S3...
2025-09-18 14:32:47,789 - INFO - Downloading PDF from S3 to /tmp/tmpwxns4i43/48bab12f_10_invoice_6.pdf
2025-09-18 14:32:47,933 - INFO - Page 1: Extracted 1418 characters, 97 lines from 21a21375_10_invoice_1_bb595dd1_page_001.pdf
2025-09-18 14:32:47,933 - INFO - Successfully processed page 1
2025-09-18 14:32:47,934 - INFO - Combined 1 pages into final text
2025-09-18 14:32:47,934 - INFO - Text validation for 21a21375_10_invoice_1: 1435 characters, 1 pages
2025-09-18 14:32:47,934 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:32:47,934 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:32:47,995 - INFO - Page 1: Extracted 659 characters, 47 lines from 0355cd14_10_invoice_10_0704a12c_page_001.pdf
2025-09-18 14:32:47,995 - INFO - Successfully processed page 1
2025-09-18 14:32:47,996 - INFO - Combined 1 pages into final text
2025-09-18 14:32:47,996 - INFO - Text validation for 0355cd14_10_invoice_10: 676 characters, 1 pages
2025-09-18 14:32:47,996 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:32:47,997 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:32:48,366 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_7.pdf -> s3://document-extraction-logistically/temp/6c49f953_10_invoice_7.pdf
2025-09-18 14:32:48,366 - INFO - 🔍 [14:32:48] Starting classification: 10_invoice_7.pdf
2025-09-18 14:32:48,367 - INFO - ⬆️ [14:32:48] Uploading: 10_invoice_8.pdf
2025-09-18 14:32:48,368 - INFO - Initializing TextractProcessor...
2025-09-18 14:32:48,381 - INFO - Initializing BedrockProcessor...
2025-09-18 14:32:48,385 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6c49f953_10_invoice_7.pdf
2025-09-18 14:32:48,386 - INFO - Processing PDF from S3...
2025-09-18 14:32:48,386 - INFO - Downloading PDF from S3 to /tmp/tmp249kzcww/6c49f953_10_invoice_7.pdf
2025-09-18 14:32:48,595 - INFO - Splitting PDF into individual pages...
2025-09-18 14:32:48,596 - INFO - Splitting PDF 0ce67bf5_10_invoice_4 into 1 pages
2025-09-18 14:32:48,598 - INFO - Split PDF into 1 pages
2025-09-18 14:32:48,598 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:32:48,598 - INFO - Expected pages: [1]
2025-09-18 14:32:48,980 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_8.pdf -> s3://document-extraction-logistically/temp/bcb3c0d4_10_invoice_8.pdf
2025-09-18 14:32:48,980 - INFO - 🔍 [14:32:48] Starting classification: 10_invoice_8.pdf
2025-09-18 14:32:48,981 - INFO - ⬆️ [14:32:48] Uploading: 10_invoice_9.pdf
2025-09-18 14:32:48,982 - INFO - Initializing TextractProcessor...
2025-09-18 14:32:48,999 - INFO - Initializing BedrockProcessor...
2025-09-18 14:32:49,002 - INFO - Splitting PDF into individual pages...
2025-09-18 14:32:49,003 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bcb3c0d4_10_invoice_8.pdf
2025-09-18 14:32:49,004 - INFO - Processing PDF from S3...
2025-09-18 14:32:49,005 - INFO - Downloading PDF from S3 to /tmp/tmptuxl_han/bcb3c0d4_10_invoice_8.pdf
2025-09-18 14:32:49,005 - INFO - Splitting PDF 72bf0573_10_invoice_5 into 1 pages
2025-09-18 14:32:49,016 - INFO - Split PDF into 1 pages
2025-09-18 14:32:49,017 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:32:49,017 - INFO - Expected pages: [1]
2025-09-18 14:32:49,394 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0355cd14_10_invoice_10.pdf
2025-09-18 14:32:49,608 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_9.pdf -> s3://document-extraction-logistically/temp/e289b607_10_invoice_9.pdf
2025-09-18 14:32:49,609 - INFO - 🔍 [14:32:49] Starting classification: 10_invoice_9.pdf
2025-09-18 14:32:49,610 - INFO - Initializing TextractProcessor...
2025-09-18 14:32:49,618 - INFO - Splitting PDF into individual pages...
2025-09-18 14:32:49,622 - INFO - Initializing BedrockProcessor...
2025-09-18 14:32:49,629 - INFO - Splitting PDF 6c49f953_10_invoice_7 into 1 pages
2025-09-18 14:32:49,638 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e289b607_10_invoice_9.pdf
2025-09-18 14:32:49,641 - INFO - Processing PDF from S3...
2025-09-18 14:32:49,642 - INFO - Split PDF into 1 pages
2025-09-18 14:32:49,645 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:32:49,645 - INFO - Downloading PDF from S3 to /tmp/tmps8mslym9/e289b607_10_invoice_9.pdf
2025-09-18 14:32:49,647 - INFO - Expected pages: [1]
2025-09-18 14:32:49,661 - INFO - 

10_invoice_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:32:49,661 - INFO - 

✓ Saved result: output/run1_10_invoice_10.json
2025-09-18 14:32:49,841 - INFO - Splitting PDF into individual pages...
2025-09-18 14:32:49,842 - INFO - Splitting PDF 48bab12f_10_invoice_6 into 2 pages
2025-09-18 14:32:49,855 - INFO - Split PDF into 2 pages
2025-09-18 14:32:49,855 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:32:49,855 - INFO - Expected pages: [1, 2]
2025-09-18 14:32:49,954 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/21a21375_10_invoice_1.pdf
2025-09-18 14:32:49,973 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0355cd14_10_invoice_10.pdf
2025-09-18 14:32:50,003 - INFO - 

10_invoice_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:32:50,003 - INFO - 

✓ Saved result: output/run1_10_invoice_1.json
2025-09-18 14:32:50,315 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/21a21375_10_invoice_1.pdf
2025-09-18 14:32:50,502 - INFO - Splitting PDF into individual pages...
2025-09-18 14:32:50,503 - INFO - Splitting PDF bcb3c0d4_10_invoice_8 into 1 pages
2025-09-18 14:32:50,509 - INFO - Split PDF into 1 pages
2025-09-18 14:32:50,509 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:32:50,509 - INFO - Expected pages: [1]
2025-09-18 14:32:50,938 - INFO - Page 1: Extracted 829 characters, 43 lines from 8c9a943e_10_invoice_3_4834696a_page_001.pdf
2025-09-18 14:32:50,939 - INFO - Successfully processed page 1
2025-09-18 14:32:50,939 - INFO - Combined 1 pages into final text
2025-09-18 14:32:50,939 - INFO - Text validation for 8c9a943e_10_invoice_3: 846 characters, 1 pages
2025-09-18 14:32:50,940 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:32:50,940 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:32:51,339 - INFO - Splitting PDF into individual pages...
2025-09-18 14:32:51,340 - INFO - Splitting PDF e289b607_10_invoice_9 into 1 pages
2025-09-18 14:32:51,345 - INFO - Split PDF into 1 pages
2025-09-18 14:32:51,345 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:32:51,345 - INFO - Expected pages: [1]
2025-09-18 14:32:52,762 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8c9a943e_10_invoice_3.pdf
2025-09-18 14:32:52,779 - INFO - 

10_invoice_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:32:52,780 - INFO - 

✓ Saved result: output/run1_10_invoice_3.json
2025-09-18 14:32:53,091 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8c9a943e_10_invoice_3.pdf
2025-09-18 14:32:53,514 - INFO - Page 1: Extracted 940 characters, 39 lines from ecef7306_10_invoice_2_a6542df7_page_001.pdf
2025-09-18 14:32:53,515 - INFO - Successfully processed page 1
2025-09-18 14:32:53,515 - INFO - Combined 1 pages into final text
2025-09-18 14:32:53,515 - INFO - Text validation for ecef7306_10_invoice_2: 957 characters, 1 pages
2025-09-18 14:32:53,516 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:32:53,516 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:32:53,626 - INFO - Page 1: Extracted 575 characters, 38 lines from 6c49f953_10_invoice_7_ac08bc3f_page_001.pdf
2025-09-18 14:32:53,627 - INFO - Successfully processed page 1
2025-09-18 14:32:53,627 - INFO - Combined 1 pages into final text
2025-09-18 14:32:53,627 - INFO - Text validation for 6c49f953_10_invoice_7: 592 characters, 1 pages
2025-09-18 14:32:53,627 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:32:53,628 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:32:53,862 - INFO - Page 1: Extracted 744 characters, 55 lines from 0ce67bf5_10_invoice_4_ebe730c9_page_001.pdf
2025-09-18 14:32:53,863 - INFO - Successfully processed page 1
2025-09-18 14:32:53,863 - INFO - Combined 1 pages into final text
2025-09-18 14:32:53,863 - INFO - Text validation for 0ce67bf5_10_invoice_4: 761 characters, 1 pages
2025-09-18 14:32:53,864 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:32:53,864 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:32:53,927 - INFO - Page 1: Extracted 399 characters, 30 lines from bcb3c0d4_10_invoice_8_7063e01e_page_001.pdf
2025-09-18 14:32:53,928 - INFO - Successfully processed page 1
2025-09-18 14:32:53,928 - INFO - Combined 1 pages into final text
2025-09-18 14:32:53,928 - INFO - Text validation for bcb3c0d4_10_invoice_8: 416 characters, 1 pages
2025-09-18 14:32:53,928 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:32:53,928 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:32:54,472 - INFO - Page 1: Extracted 543 characters, 38 lines from 72bf0573_10_invoice_5_8f42ea81_page_001.pdf
2025-09-18 14:32:54,472 - INFO - Successfully processed page 1
2025-09-18 14:32:54,472 - INFO - Combined 1 pages into final text
2025-09-18 14:32:54,472 - INFO - Text validation for 72bf0573_10_invoice_5: 560 characters, 1 pages
2025-09-18 14:32:54,473 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:32:54,473 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:32:55,093 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ecef7306_10_invoice_2.pdf
2025-09-18 14:32:55,115 - INFO - 

10_invoice_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:32:55,115 - INFO - 

✓ Saved result: output/run1_10_invoice_2.json
2025-09-18 14:32:55,194 - INFO - Page 1: Extracted 760 characters, 40 lines from 48bab12f_10_invoice_6_97eb1de1_page_001.pdf
2025-09-18 14:32:55,195 - INFO - Successfully processed page 1
2025-09-18 14:32:55,311 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bcb3c0d4_10_invoice_8.pdf
2025-09-18 14:32:55,321 - INFO - Page 1: Extracted 823 characters, 53 lines from e289b607_10_invoice_9_819fcaca_page_001.pdf
2025-09-18 14:32:55,321 - INFO - Successfully processed page 1
2025-09-18 14:32:55,322 - INFO - Combined 1 pages into final text
2025-09-18 14:32:55,322 - INFO - Text validation for e289b607_10_invoice_9: 840 characters, 1 pages
2025-09-18 14:32:55,322 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:32:55,322 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:32:55,419 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ecef7306_10_invoice_2.pdf
2025-09-18 14:32:55,433 - INFO - 

10_invoice_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:32:55,434 - INFO - 

✓ Saved result: output/run1_10_invoice_8.json
2025-09-18 14:32:55,641 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0ce67bf5_10_invoice_4.pdf
2025-09-18 14:32:55,654 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6c49f953_10_invoice_7.pdf
2025-09-18 14:32:55,733 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bcb3c0d4_10_invoice_8.pdf
2025-09-18 14:32:55,750 - INFO - Page 2: Extracted 561 characters, 32 lines from 48bab12f_10_invoice_6_97eb1de1_page_002.pdf
2025-09-18 14:32:55,753 - INFO - Successfully processed page 2
2025-09-18 14:32:55,758 - INFO - Combined 2 pages into final text
2025-09-18 14:32:55,760 - INFO - Text validation for 48bab12f_10_invoice_6: 1357 characters, 2 pages
2025-09-18 14:32:55,764 - INFO - 

10_invoice_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:32:55,764 - INFO - 

✓ Saved result: output/run1_10_invoice_4.json
2025-09-18 14:32:55,767 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:32:55,767 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:32:55,944 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/72bf0573_10_invoice_5.pdf
2025-09-18 14:32:56,072 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0ce67bf5_10_invoice_4.pdf
2025-09-18 14:32:56,083 - INFO - 

10_invoice_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:32:56,083 - INFO - 

✓ Saved result: output/run1_10_invoice_7.json
2025-09-18 14:32:56,394 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6c49f953_10_invoice_7.pdf
2025-09-18 14:32:56,409 - INFO - 

10_invoice_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:32:56,410 - INFO - 

✓ Saved result: output/run1_10_invoice_5.json
2025-09-18 14:32:56,718 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/72bf0573_10_invoice_5.pdf
2025-09-18 14:32:57,136 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e289b607_10_invoice_9.pdf
2025-09-18 14:32:57,160 - INFO - 

10_invoice_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:32:57,160 - INFO - 

✓ Saved result: output/run1_10_invoice_9.json
2025-09-18 14:32:57,468 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e289b607_10_invoice_9.pdf
2025-09-18 14:32:58,323 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/48bab12f_10_invoice_6.pdf
2025-09-18 14:32:58,343 - INFO - 

10_invoice_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:32:58,343 - INFO - 

✓ Saved result: output/run1_10_invoice_6.json
2025-09-18 14:32:58,655 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/48bab12f_10_invoice_6.pdf
2025-09-18 14:32:58,656 - INFO - 
📊 Processing Summary:
2025-09-18 14:32:58,656 - INFO -    Total files: 10
2025-09-18 14:32:58,656 - INFO -    Successful: 10
2025-09-18 14:32:58,657 - INFO -    Failed: 0
2025-09-18 14:32:58,657 - INFO -    Duration: 18.06 seconds
2025-09-18 14:32:58,657 - INFO -    Output directory: output
2025-09-18 14:32:58,657 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:32:58,657 - INFO -    📄 10_invoice_1.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:32:58,657 - INFO -    📄 10_invoice_10.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:32:58,657 - INFO -    📄 10_invoice_2.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:32:58,657 - INFO -    📄 10_invoice_3.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:32:58,657 - INFO -    📄 10_invoice_4.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:32:58,657 - INFO -    📄 10_invoice_5.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:32:58,658 - INFO -    📄 10_invoice_6.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"}]}
2025-09-18 14:32:58,658 - INFO -    📄 10_invoice_7.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:32:58,658 - INFO -    📄 10_invoice_8.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:32:58,658 - INFO -    📄 10_invoice_9.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:32:58,659 - INFO - 
============================================================================================================================================
2025-09-18 14:32:58,659 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:32:58,659 - INFO - ============================================================================================================================================
2025-09-18 14:32:58,659 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:32:58,659 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:32:58,659 - INFO - 10_invoice_1.pdf                                   1      invoice              run1_10_invoice_1.json                            
2025-09-18 14:32:58,659 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_1.pdf
2025-09-18 14:32:58,659 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_1.json
2025-09-18 14:32:58,659 - INFO - 
2025-09-18 14:32:58,659 - INFO - 10_invoice_10.pdf                                  1      invoice              run1_10_invoice_10.json                           
2025-09-18 14:32:58,659 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_10.pdf
2025-09-18 14:32:58,660 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_10.json
2025-09-18 14:32:58,660 - INFO - 
2025-09-18 14:32:58,660 - INFO - 10_invoice_2.pdf                                   1      invoice              run1_10_invoice_2.json                            
2025-09-18 14:32:58,660 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_2.pdf
2025-09-18 14:32:58,660 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_2.json
2025-09-18 14:32:58,660 - INFO - 
2025-09-18 14:32:58,660 - INFO - 10_invoice_3.pdf                                   1      invoice              run1_10_invoice_3.json                            
2025-09-18 14:32:58,660 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_3.pdf
2025-09-18 14:32:58,660 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_3.json
2025-09-18 14:32:58,661 - INFO - 
2025-09-18 14:32:58,661 - INFO - 10_invoice_4.pdf                                   1      invoice              run1_10_invoice_4.json                            
2025-09-18 14:32:58,661 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_4.pdf
2025-09-18 14:32:58,661 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_4.json
2025-09-18 14:32:58,661 - INFO - 
2025-09-18 14:32:58,661 - INFO - 10_invoice_5.pdf                                   1      invoice              run1_10_invoice_5.json                            
2025-09-18 14:32:58,661 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_5.pdf
2025-09-18 14:32:58,661 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_5.json
2025-09-18 14:32:58,661 - INFO - 
2025-09-18 14:32:58,661 - INFO - 10_invoice_6.pdf                                   1      invoice              run1_10_invoice_6.json                            
2025-09-18 14:32:58,661 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_6.pdf
2025-09-18 14:32:58,661 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_6.json
2025-09-18 14:32:58,661 - INFO - 
2025-09-18 14:32:58,661 - INFO - 10_invoice_6.pdf                                   2      invoice              run1_10_invoice_6.json                            
2025-09-18 14:32:58,661 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_6.pdf
2025-09-18 14:32:58,661 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_6.json
2025-09-18 14:32:58,661 - INFO - 
2025-09-18 14:32:58,661 - INFO - 10_invoice_7.pdf                                   1      invoice              run1_10_invoice_7.json                            
2025-09-18 14:32:58,661 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_7.pdf
2025-09-18 14:32:58,662 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_7.json
2025-09-18 14:32:58,662 - INFO - 
2025-09-18 14:32:58,662 - INFO - 10_invoice_8.pdf                                   1      invoice              run1_10_invoice_8.json                            
2025-09-18 14:32:58,662 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_8.pdf
2025-09-18 14:32:58,662 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_8.json
2025-09-18 14:32:58,662 - INFO - 
2025-09-18 14:32:58,662 - INFO - 10_invoice_9.pdf                                   1      invoice              run1_10_invoice_9.json                            
2025-09-18 14:32:58,662 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_9.pdf
2025-09-18 14:32:58,662 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_9.json
2025-09-18 14:32:58,662 - INFO - 
2025-09-18 14:32:58,662 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:32:58,662 - INFO - Total entries: 11
2025-09-18 14:32:58,662 - INFO - ============================================================================================================================================
2025-09-18 14:32:58,662 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:32:58,662 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:32:58,662 - INFO -   1. 10_invoice_1.pdf                    Page 1   → invoice         | run1_10_invoice_1.json
2025-09-18 14:32:58,662 - INFO -   2. 10_invoice_10.pdf                   Page 1   → invoice         | run1_10_invoice_10.json
2025-09-18 14:32:58,662 - INFO -   3. 10_invoice_2.pdf                    Page 1   → invoice         | run1_10_invoice_2.json
2025-09-18 14:32:58,662 - INFO -   4. 10_invoice_3.pdf                    Page 1   → invoice         | run1_10_invoice_3.json
2025-09-18 14:32:58,663 - INFO -   5. 10_invoice_4.pdf                    Page 1   → invoice         | run1_10_invoice_4.json
2025-09-18 14:32:58,663 - INFO -   6. 10_invoice_5.pdf                    Page 1   → invoice         | run1_10_invoice_5.json
2025-09-18 14:32:58,663 - INFO -   7. 10_invoice_6.pdf                    Page 1   → invoice         | run1_10_invoice_6.json
2025-09-18 14:32:58,663 - INFO -   8. 10_invoice_6.pdf                    Page 2   → invoice         | run1_10_invoice_6.json
2025-09-18 14:32:58,663 - INFO -   9. 10_invoice_7.pdf                    Page 1   → invoice         | run1_10_invoice_7.json
2025-09-18 14:32:58,663 - INFO -  10. 10_invoice_8.pdf                    Page 1   → invoice         | run1_10_invoice_8.json
2025-09-18 14:32:58,663 - INFO -  11. 10_invoice_9.pdf                    Page 1   → invoice         | run1_10_invoice_9.json
2025-09-18 14:32:58,663 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:32:58,663 - INFO - 
✅ Test completed: {'total_files': 10, 'processed': 10, 'failed': 0, 'errors': [], 'duration_seconds': 18.055525, 'processed_files': [{'filename': '10_invoice_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_1.pdf'}, {'filename': '10_invoice_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_10.pdf'}, {'filename': '10_invoice_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_2.pdf'}, {'filename': '10_invoice_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_3.pdf'}, {'filename': '10_invoice_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_4.pdf'}, {'filename': '10_invoice_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_5.pdf'}, {'filename': '10_invoice_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_6.pdf'}, {'filename': '10_invoice_7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_7.pdf'}, {'filename': '10_invoice_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_8.pdf'}, {'filename': '10_invoice_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_9.pdf'}]}
