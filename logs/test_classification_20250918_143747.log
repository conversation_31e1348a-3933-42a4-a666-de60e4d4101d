2025-09-18 14:37:47,744 - INFO - Logging initialized. Log file: logs/test_classification_20250918_143747.log
2025-09-18 14:37:47,745 - INFO - 📁 Found 11 files to process
2025-09-18 14:37:47,745 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:37:47,745 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 14:37:47,745 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 14:37:47,745 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 14:37:47,745 - INFO - ⬆️ [14:37:47] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 14:37:50,122 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/6ca551c7_21_lumper_receipt_1.jpeg
2025-09-18 14:37:50,122 - INFO - 🔍 [14:37:50] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 14:37:50,124 - INFO - ⬆️ [14:37:50] Uploading: 21_lumper_receipt_10.png
2025-09-18 14:37:50,126 - INFO - Initializing TextractProcessor...
2025-09-18 14:37:50,154 - INFO - Initializing BedrockProcessor...
2025-09-18 14:37:50,159 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6ca551c7_21_lumper_receipt_1.jpeg
2025-09-18 14:37:50,160 - INFO - Processing image from S3...
2025-09-18 14:37:51,070 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/99deaede_21_lumper_receipt_10.png
2025-09-18 14:37:51,070 - INFO - 🔍 [14:37:51] Starting classification: 21_lumper_receipt_10.png
2025-09-18 14:37:51,071 - INFO - ⬆️ [14:37:51] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 14:37:51,072 - INFO - Initializing TextractProcessor...
2025-09-18 14:37:51,080 - INFO - Initializing BedrockProcessor...
2025-09-18 14:37:51,082 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/99deaede_21_lumper_receipt_10.png
2025-09-18 14:37:51,082 - INFO - Processing image from S3...
2025-09-18 14:37:53,229 - INFO - S3 Image temp/6ca551c7_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 14:37:53,229 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:37:53,229 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:37:53,931 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/bf2e6b35_21_lumper_receipt_11.jpeg
2025-09-18 14:37:53,931 - INFO - 🔍 [14:37:53] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 14:37:53,932 - INFO - ⬆️ [14:37:53] Uploading: 21_lumper_receipt_2.jpg
2025-09-18 14:37:53,933 - INFO - Initializing TextractProcessor...
2025-09-18 14:37:53,942 - INFO - Initializing BedrockProcessor...
2025-09-18 14:37:53,946 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bf2e6b35_21_lumper_receipt_11.jpeg
2025-09-18 14:37:53,946 - INFO - Processing image from S3...
2025-09-18 14:37:54,042 - INFO - S3 Image temp/99deaede_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 14:37:54,042 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:37:54,043 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:37:55,709 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg -> s3://document-extraction-logistically/temp/33fa82aa_21_lumper_receipt_2.jpg
2025-09-18 14:37:55,709 - INFO - 🔍 [14:37:55] Starting classification: 21_lumper_receipt_2.jpg
2025-09-18 14:37:55,710 - INFO - ⬆️ [14:37:55] Uploading: 21_lumper_receipt_3.png
2025-09-18 14:37:55,710 - INFO - Initializing TextractProcessor...
2025-09-18 14:37:55,718 - INFO - Initializing BedrockProcessor...
2025-09-18 14:37:55,720 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/33fa82aa_21_lumper_receipt_2.jpg
2025-09-18 14:37:55,720 - INFO - Processing image from S3...
2025-09-18 14:37:56,007 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/99deaede_21_lumper_receipt_10.png
2025-09-18 14:37:57,263 - INFO - S3 Image temp/bf2e6b35_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 14:37:57,263 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:37:57,263 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:37:57,524 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png -> s3://document-extraction-logistically/temp/1134333b_21_lumper_receipt_3.png
2025-09-18 14:37:57,524 - INFO - 🔍 [14:37:57] Starting classification: 21_lumper_receipt_3.png
2025-09-18 14:37:57,525 - INFO - ⬆️ [14:37:57] Uploading: 21_lumper_receipt_4.pdf
2025-09-18 14:37:57,526 - INFO - Initializing TextractProcessor...
2025-09-18 14:37:57,538 - INFO - Initializing BedrockProcessor...
2025-09-18 14:37:57,540 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1134333b_21_lumper_receipt_3.png
2025-09-18 14:37:57,540 - INFO - Processing image from S3...
2025-09-18 14:37:57,936 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-18 14:37:57,936 - ERROR - Processing failed for s3://document-extraction-logistically/temp/6ca551c7_21_lumper_receipt_1.jpeg: Unexpected tool response format from Bedrock
2025-09-18 14:37:58,097 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf -> s3://document-extraction-logistically/temp/01bfab93_21_lumper_receipt_4.pdf
2025-09-18 14:37:58,097 - INFO - 🔍 [14:37:58] Starting classification: 21_lumper_receipt_4.pdf
2025-09-18 14:37:58,098 - INFO - ⬆️ [14:37:58] Uploading: 21_lumper_receipt_5.pdf
2025-09-18 14:37:58,100 - INFO - Initializing TextractProcessor...
2025-09-18 14:37:58,117 - INFO - Initializing BedrockProcessor...
2025-09-18 14:37:58,121 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/01bfab93_21_lumper_receipt_4.pdf
2025-09-18 14:37:58,121 - INFO - Processing PDF from S3...
2025-09-18 14:37:58,121 - INFO - Downloading PDF from S3 to /tmp/tmphx551x5e/01bfab93_21_lumper_receipt_4.pdf
2025-09-18 14:37:58,193 - INFO - S3 Image temp/33fa82aa_21_lumper_receipt_2.jpg: Extracted 184 characters, 12 lines
2025-09-18 14:37:58,194 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:37:58,194 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:37:58,676 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf -> s3://document-extraction-logistically/temp/cc9699bf_21_lumper_receipt_5.pdf
2025-09-18 14:37:58,676 - INFO - 🔍 [14:37:58] Starting classification: 21_lumper_receipt_5.pdf
2025-09-18 14:37:58,677 - INFO - ⬆️ [14:37:58] Uploading: 21_lumper_receipt_6.pdf
2025-09-18 14:37:58,679 - INFO - Initializing TextractProcessor...
2025-09-18 14:37:58,693 - INFO - Initializing BedrockProcessor...
2025-09-18 14:37:58,699 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cc9699bf_21_lumper_receipt_5.pdf
2025-09-18 14:37:58,700 - INFO - Processing PDF from S3...
2025-09-18 14:37:58,700 - INFO - Downloading PDF from S3 to /tmp/tmps275ody0/cc9699bf_21_lumper_receipt_5.pdf
2025-09-18 14:37:59,338 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf -> s3://document-extraction-logistically/temp/ed9f93a5_21_lumper_receipt_6.pdf
2025-09-18 14:37:59,338 - INFO - 🔍 [14:37:59] Starting classification: 21_lumper_receipt_6.pdf
2025-09-18 14:37:59,339 - INFO - ⬆️ [14:37:59] Uploading: 21_lumper_receipt_7.png
2025-09-18 14:37:59,339 - INFO - Initializing TextractProcessor...
2025-09-18 14:37:59,355 - INFO - Initializing BedrockProcessor...
2025-09-18 14:37:59,359 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ed9f93a5_21_lumper_receipt_6.pdf
2025-09-18 14:37:59,359 - INFO - Processing PDF from S3...
2025-09-18 14:37:59,359 - INFO - Downloading PDF from S3 to /tmp/tmpnasz0bwa/ed9f93a5_21_lumper_receipt_6.pdf
2025-09-18 14:37:59,508 - INFO - Splitting PDF into individual pages...
2025-09-18 14:37:59,510 - INFO - Splitting PDF 01bfab93_21_lumper_receipt_4 into 1 pages
2025-09-18 14:37:59,513 - INFO - Split PDF into 1 pages
2025-09-18 14:37:59,513 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:37:59,513 - INFO - Expected pages: [1]
2025-09-18 14:37:59,629 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/33fa82aa_21_lumper_receipt_2.jpg
2025-09-18 14:37:59,970 - INFO - Splitting PDF into individual pages...
2025-09-18 14:37:59,971 - INFO - Splitting PDF cc9699bf_21_lumper_receipt_5 into 1 pages
2025-09-18 14:37:59,973 - INFO - Split PDF into 1 pages
2025-09-18 14:37:59,973 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:37:59,973 - INFO - Expected pages: [1]
2025-09-18 14:37:59,978 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png -> s3://document-extraction-logistically/temp/04e76e62_21_lumper_receipt_7.png
2025-09-18 14:37:59,978 - INFO - 🔍 [14:37:59] Starting classification: 21_lumper_receipt_7.png
2025-09-18 14:37:59,978 - INFO - ⬆️ [14:37:59] Uploading: 21_lumper_receipt_8.pdf
2025-09-18 14:37:59,980 - INFO - Initializing TextractProcessor...
2025-09-18 14:37:59,995 - INFO - Initializing BedrockProcessor...
2025-09-18 14:37:59,999 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/04e76e62_21_lumper_receipt_7.png
2025-09-18 14:37:59,999 - INFO - Processing image from S3...
2025-09-18 14:38:00,545 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf -> s3://document-extraction-logistically/temp/665f2c79_21_lumper_receipt_8.pdf
2025-09-18 14:38:00,545 - INFO - 🔍 [14:38:00] Starting classification: 21_lumper_receipt_8.pdf
2025-09-18 14:38:00,546 - INFO - ⬆️ [14:38:00] Uploading: 21_lumper_receipt_9.pdf
2025-09-18 14:38:00,546 - INFO - Initializing TextractProcessor...
2025-09-18 14:38:00,561 - INFO - Initializing BedrockProcessor...
2025-09-18 14:38:00,569 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/665f2c79_21_lumper_receipt_8.pdf
2025-09-18 14:38:00,569 - INFO - Processing PDF from S3...
2025-09-18 14:38:00,569 - INFO - Downloading PDF from S3 to /tmp/tmpt57my5zo/665f2c79_21_lumper_receipt_8.pdf
2025-09-18 14:38:00,609 - INFO - S3 Image temp/1134333b_21_lumper_receipt_3.png: Extracted 663 characters, 38 lines
2025-09-18 14:38:00,609 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:38:00,609 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:38:01,430 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bf2e6b35_21_lumper_receipt_11.jpeg
2025-09-18 14:38:01,529 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf -> s3://document-extraction-logistically/temp/e24ad697_21_lumper_receipt_9.pdf
2025-09-18 14:38:01,529 - INFO - 🔍 [14:38:01] Starting classification: 21_lumper_receipt_9.pdf
2025-09-18 14:38:01,530 - INFO - Initializing TextractProcessor...
2025-09-18 14:38:01,544 - INFO - Initializing BedrockProcessor...
2025-09-18 14:38:01,548 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e24ad697_21_lumper_receipt_9.pdf
2025-09-18 14:38:01,549 - INFO - Processing PDF from S3...
2025-09-18 14:38:01,551 - INFO - Downloading PDF from S3 to /tmp/tmp302daj62/e24ad697_21_lumper_receipt_9.pdf
2025-09-18 14:38:01,559 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:38:01,559 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 14:38:01,817 - INFO - Splitting PDF into individual pages...
2025-09-18 14:38:01,818 - INFO - Splitting PDF 665f2c79_21_lumper_receipt_8 into 1 pages
2025-09-18 14:38:01,820 - INFO - Split PDF into 1 pages
2025-09-18 14:38:01,820 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:38:01,820 - INFO - Expected pages: [1]
2025-09-18 14:38:01,848 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/99deaede_21_lumper_receipt_10.png
2025-09-18 14:38:01,848 - ERROR - ✗ Failed to process input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg: Unexpected tool response format from Bedrock
2025-09-18 14:38:02,134 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6ca551c7_21_lumper_receipt_1.jpeg
2025-09-18 14:38:02,138 - INFO - 

21_lumper_receipt_2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:38:02,138 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_2.json
2025-09-18 14:38:02,332 - INFO - Page 1: Extracted 422 characters, 38 lines from 01bfab93_21_lumper_receipt_4_e37856e0_page_001.pdf
2025-09-18 14:38:02,333 - INFO - Successfully processed page 1
2025-09-18 14:38:02,333 - INFO - Combined 1 pages into final text
2025-09-18 14:38:02,333 - INFO - Text validation for 01bfab93_21_lumper_receipt_4: 439 characters, 1 pages
2025-09-18 14:38:02,334 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:38:02,334 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:38:02,358 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1134333b_21_lumper_receipt_3.png
2025-09-18 14:38:02,417 - INFO - Splitting PDF into individual pages...
2025-09-18 14:38:02,418 - INFO - Splitting PDF ed9f93a5_21_lumper_receipt_6 into 1 pages
2025-09-18 14:38:02,424 - INFO - Split PDF into 1 pages
2025-09-18 14:38:02,424 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:38:02,424 - INFO - Expected pages: [1]
2025-09-18 14:38:02,458 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/33fa82aa_21_lumper_receipt_2.jpg
2025-09-18 14:38:02,474 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 14:38:02,474 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 14:38:02,759 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bf2e6b35_21_lumper_receipt_11.jpeg
2025-09-18 14:38:02,766 - INFO - 

21_lumper_receipt_3.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:38:02,766 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_3.json
2025-09-18 14:38:02,852 - INFO - S3 Image temp/04e76e62_21_lumper_receipt_7.png: Extracted 800 characters, 70 lines
2025-09-18 14:38:02,852 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:38:02,852 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:38:03,070 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1134333b_21_lumper_receipt_3.png
2025-09-18 14:38:03,147 - INFO - Page 1: Extracted 422 characters, 38 lines from cc9699bf_21_lumper_receipt_5_1d2297f4_page_001.pdf
2025-09-18 14:38:03,148 - INFO - Successfully processed page 1
2025-09-18 14:38:03,148 - INFO - Combined 1 pages into final text
2025-09-18 14:38:03,148 - INFO - Text validation for cc9699bf_21_lumper_receipt_5: 439 characters, 1 pages
2025-09-18 14:38:03,148 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:38:03,148 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:38:03,532 - INFO - Splitting PDF into individual pages...
2025-09-18 14:38:03,533 - INFO - Splitting PDF e24ad697_21_lumper_receipt_9 into 1 pages
2025-09-18 14:38:03,536 - INFO - Split PDF into 1 pages
2025-09-18 14:38:03,536 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:38:03,537 - INFO - Expected pages: [1]
2025-09-18 14:38:04,405 - INFO - Page 1: Extracted 330 characters, 33 lines from 665f2c79_21_lumper_receipt_8_7ca7a009_page_001.pdf
2025-09-18 14:38:04,406 - INFO - Successfully processed page 1
2025-09-18 14:38:04,406 - INFO - Combined 1 pages into final text
2025-09-18 14:38:04,406 - INFO - Text validation for 665f2c79_21_lumper_receipt_8: 347 characters, 1 pages
2025-09-18 14:38:04,407 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:38:04,407 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:38:04,426 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/01bfab93_21_lumper_receipt_4.pdf
2025-09-18 14:38:04,445 - INFO - 

21_lumper_receipt_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 14:38:04,445 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_4.json
2025-09-18 14:38:04,735 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/01bfab93_21_lumper_receipt_4.pdf
2025-09-18 14:38:05,292 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/04e76e62_21_lumper_receipt_7.png
2025-09-18 14:38:05,307 - INFO - 

21_lumper_receipt_7.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:38:05,307 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_7.json
2025-09-18 14:38:05,544 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cc9699bf_21_lumper_receipt_5.pdf
2025-09-18 14:38:05,593 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/04e76e62_21_lumper_receipt_7.png
2025-09-18 14:38:05,599 - INFO - 

21_lumper_receipt_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 14:38:05,599 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_5.json
2025-09-18 14:38:05,896 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cc9699bf_21_lumper_receipt_5.pdf
2025-09-18 14:38:07,281 - INFO - Page 1: Extracted 269 characters, 22 lines from ed9f93a5_21_lumper_receipt_6_4117fa21_page_001.pdf
2025-09-18 14:38:07,282 - INFO - Successfully processed page 1
2025-09-18 14:38:07,282 - INFO - Combined 1 pages into final text
2025-09-18 14:38:07,282 - INFO - Text validation for ed9f93a5_21_lumper_receipt_6: 286 characters, 1 pages
2025-09-18 14:38:07,283 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:38:07,283 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:38:07,634 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/665f2c79_21_lumper_receipt_8.pdf
2025-09-18 14:38:07,641 - INFO - 

21_lumper_receipt_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:38:07,641 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_8.json
2025-09-18 14:38:07,953 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/665f2c79_21_lumper_receipt_8.pdf
2025-09-18 14:38:08,673 - INFO - Page 1: Extracted 645 characters, 53 lines from e24ad697_21_lumper_receipt_9_450703f2_page_001.pdf
2025-09-18 14:38:08,674 - INFO - Successfully processed page 1
2025-09-18 14:38:08,674 - INFO - Combined 1 pages into final text
2025-09-18 14:38:08,674 - INFO - Text validation for e24ad697_21_lumper_receipt_9: 662 characters, 1 pages
2025-09-18 14:38:08,675 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:38:08,675 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:38:09,419 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ed9f93a5_21_lumper_receipt_6.pdf
2025-09-18 14:38:09,431 - INFO - 

21_lumper_receipt_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:38:09,431 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_6.json
2025-09-18 14:38:09,759 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ed9f93a5_21_lumper_receipt_6.pdf
2025-09-18 14:38:11,786 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e24ad697_21_lumper_receipt_9.pdf
2025-09-18 14:38:11,800 - INFO - 

21_lumper_receipt_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:38:11,800 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_9.json
2025-09-18 14:38:12,086 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e24ad697_21_lumper_receipt_9.pdf
2025-09-18 14:38:12,086 - INFO - 
📊 Processing Summary:
2025-09-18 14:38:12,087 - INFO -    Total files: 11
2025-09-18 14:38:12,087 - INFO -    Successful: 10
2025-09-18 14:38:12,087 - INFO -    Failed: 1
2025-09-18 14:38:12,087 - INFO -    Duration: 24.34 seconds
2025-09-18 14:38:12,087 - INFO -    Output directory: output
2025-09-18 14:38:12,087 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:38:12,087 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:38:12,087 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 14:38:12,087 - INFO -    📄 21_lumper_receipt_2.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:38:12,087 - INFO -    📄 21_lumper_receipt_3.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:38:12,087 - INFO -    📄 21_lumper_receipt_4.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 14:38:12,087 - INFO -    📄 21_lumper_receipt_5.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 14:38:12,087 - INFO -    📄 21_lumper_receipt_6.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:38:12,087 - INFO -    📄 21_lumper_receipt_7.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:38:12,087 - INFO -    📄 21_lumper_receipt_8.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:38:12,087 - INFO -    📄 21_lumper_receipt_9.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:38:12,087 - ERROR - 
❌ Errors:
2025-09-18 14:38:12,087 - ERROR -    Failed to process input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg: Unexpected tool response format from Bedrock
2025-09-18 14:38:12,088 - INFO - 
============================================================================================================================================
2025-09-18 14:38:12,088 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:38:12,088 - INFO - ============================================================================================================================================
2025-09-18 14:38:12,088 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:38:12,088 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:38:12,088 - INFO - 21_lumper_receipt_10.png                           1      invoice              run1_21_lumper_receipt_10.json                    
2025-09-18 14:38:12,088 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 14:38:12,088 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 14:38:12,088 - INFO - 
2025-09-18 14:38:12,088 - INFO - 21_lumper_receipt_11.jpeg                          1      other                run1_21_lumper_receipt_11.json                    
2025-09-18 14:38:12,088 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 14:38:12,088 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 14:38:12,089 - INFO - 
2025-09-18 14:38:12,089 - INFO - 21_lumper_receipt_2.jpg                            1      lumper_receipt       run1_21_lumper_receipt_2.json                     
2025-09-18 14:38:12,089 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg
2025-09-18 14:38:12,089 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_2.json
2025-09-18 14:38:12,089 - INFO - 
2025-09-18 14:38:12,089 - INFO - 21_lumper_receipt_3.png                            1      invoice              run1_21_lumper_receipt_3.json                     
2025-09-18 14:38:12,089 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png
2025-09-18 14:38:12,089 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_3.json
2025-09-18 14:38:12,089 - INFO - 
2025-09-18 14:38:12,089 - INFO - 21_lumper_receipt_4.pdf                            1      other                run1_21_lumper_receipt_4.json                     
2025-09-18 14:38:12,089 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf
2025-09-18 14:38:12,089 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_4.json
2025-09-18 14:38:12,089 - INFO - 
2025-09-18 14:38:12,089 - INFO - 21_lumper_receipt_5.pdf                            1      other                run1_21_lumper_receipt_5.json                     
2025-09-18 14:38:12,089 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf
2025-09-18 14:38:12,089 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_5.json
2025-09-18 14:38:12,089 - INFO - 
2025-09-18 14:38:12,089 - INFO - 21_lumper_receipt_6.pdf                            1      invoice              run1_21_lumper_receipt_6.json                     
2025-09-18 14:38:12,089 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf
2025-09-18 14:38:12,089 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_6.json
2025-09-18 14:38:12,089 - INFO - 
2025-09-18 14:38:12,089 - INFO - 21_lumper_receipt_7.png                            1      invoice              run1_21_lumper_receipt_7.json                     
2025-09-18 14:38:12,089 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png
2025-09-18 14:38:12,089 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_7.json
2025-09-18 14:38:12,090 - INFO - 
2025-09-18 14:38:12,090 - INFO - 21_lumper_receipt_8.pdf                            1      lumper_receipt       run1_21_lumper_receipt_8.json                     
2025-09-18 14:38:12,090 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf
2025-09-18 14:38:12,090 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_8.json
2025-09-18 14:38:12,090 - INFO - 
2025-09-18 14:38:12,090 - INFO - 21_lumper_receipt_9.pdf                            1      invoice              run1_21_lumper_receipt_9.json                     
2025-09-18 14:38:12,090 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf
2025-09-18 14:38:12,090 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_9.json
2025-09-18 14:38:12,090 - INFO - 
2025-09-18 14:38:12,090 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:38:12,090 - INFO - Total entries: 10
2025-09-18 14:38:12,090 - INFO - ============================================================================================================================================
2025-09-18 14:38:12,090 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:38:12,090 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:38:12,090 - INFO -   1. 21_lumper_receipt_10.png            Page 1   → invoice         | run1_21_lumper_receipt_10.json
2025-09-18 14:38:12,090 - INFO -   2. 21_lumper_receipt_11.jpeg           Page 1   → other           | run1_21_lumper_receipt_11.json
2025-09-18 14:38:12,090 - INFO -   3. 21_lumper_receipt_2.jpg             Page 1   → lumper_receipt  | run1_21_lumper_receipt_2.json
2025-09-18 14:38:12,090 - INFO -   4. 21_lumper_receipt_3.png             Page 1   → invoice         | run1_21_lumper_receipt_3.json
2025-09-18 14:38:12,090 - INFO -   5. 21_lumper_receipt_4.pdf             Page 1   → other           | run1_21_lumper_receipt_4.json
2025-09-18 14:38:12,090 - INFO -   6. 21_lumper_receipt_5.pdf             Page 1   → other           | run1_21_lumper_receipt_5.json
2025-09-18 14:38:12,090 - INFO -   7. 21_lumper_receipt_6.pdf             Page 1   → invoice         | run1_21_lumper_receipt_6.json
2025-09-18 14:38:12,090 - INFO -   8. 21_lumper_receipt_7.png             Page 1   → invoice         | run1_21_lumper_receipt_7.json
2025-09-18 14:38:12,090 - INFO -   9. 21_lumper_receipt_8.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_8.json
2025-09-18 14:38:12,091 - INFO -  10. 21_lumper_receipt_9.pdf             Page 1   → invoice         | run1_21_lumper_receipt_9.json
2025-09-18 14:38:12,091 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:38:12,092 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 10, 'failed': 1, 'errors': ['Failed to process input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg: Unexpected tool response format from Bedrock'], 'duration_seconds': 24.34165, 'processed_files': [{'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '21_lumper_receipt_2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg'}, {'filename': '21_lumper_receipt_3.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png'}, {'filename': '21_lumper_receipt_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf'}, {'filename': '21_lumper_receipt_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf'}, {'filename': '21_lumper_receipt_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf'}, {'filename': '21_lumper_receipt_7.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png'}, {'filename': '21_lumper_receipt_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf'}, {'filename': '21_lumper_receipt_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf'}]}
