2025-09-18 14:49:07,114 - INFO - Logging initialized. Log file: logs/test_classification_20250918_144907.log
2025-09-18 14:49:07,114 - INFO - 📁 Found 11 files to process
2025-09-18 14:49:07,114 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:49:07,114 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 14:49:07,114 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 14:49:07,114 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 14:49:07,114 - INFO - ⬆️ [14:49:07] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 14:49:09,654 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/b9596859_21_lumper_receipt_1.jpeg
2025-09-18 14:49:09,655 - INFO - 🔍 [14:49:09] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 14:49:09,656 - INFO - ⬆️ [14:49:09] Uploading: 21_lumper_receipt_10.png
2025-09-18 14:49:09,657 - INFO - Initializing TextractProcessor...
2025-09-18 14:49:09,686 - INFO - Initializing BedrockProcessor...
2025-09-18 14:49:09,695 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b9596859_21_lumper_receipt_1.jpeg
2025-09-18 14:49:09,695 - INFO - Processing image from S3...
2025-09-18 14:49:10,332 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/514067d1_21_lumper_receipt_10.png
2025-09-18 14:49:10,333 - INFO - 🔍 [14:49:10] Starting classification: 21_lumper_receipt_10.png
2025-09-18 14:49:10,334 - INFO - ⬆️ [14:49:10] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 14:49:10,335 - INFO - Initializing TextractProcessor...
2025-09-18 14:49:10,357 - INFO - Initializing BedrockProcessor...
2025-09-18 14:49:10,360 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/514067d1_21_lumper_receipt_10.png
2025-09-18 14:49:10,360 - INFO - Processing image from S3...
2025-09-18 14:49:12,210 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/09fe7db0_21_lumper_receipt_11.jpeg
2025-09-18 14:49:12,210 - INFO - 🔍 [14:49:12] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 14:49:12,211 - INFO - ⬆️ [14:49:12] Uploading: 21_lumper_receipt_2.jpg
2025-09-18 14:49:12,213 - INFO - Initializing TextractProcessor...
2025-09-18 14:49:12,229 - INFO - Initializing BedrockProcessor...
2025-09-18 14:49:12,235 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/09fe7db0_21_lumper_receipt_11.jpeg
2025-09-18 14:49:12,235 - INFO - Processing image from S3...
2025-09-18 14:49:13,200 - INFO - S3 Image temp/b9596859_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 14:49:13,201 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:49:13,201 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:49:13,364 - INFO - S3 Image temp/514067d1_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 14:49:13,364 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:49:13,364 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:49:13,506 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg -> s3://document-extraction-logistically/temp/fe155a97_21_lumper_receipt_2.jpg
2025-09-18 14:49:13,506 - INFO - 🔍 [14:49:13] Starting classification: 21_lumper_receipt_2.jpg
2025-09-18 14:49:13,507 - INFO - ⬆️ [14:49:13] Uploading: 21_lumper_receipt_3.png
2025-09-18 14:49:13,508 - INFO - Initializing TextractProcessor...
2025-09-18 14:49:13,523 - INFO - Initializing BedrockProcessor...
2025-09-18 14:49:13,529 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fe155a97_21_lumper_receipt_2.jpg
2025-09-18 14:49:13,529 - INFO - Processing image from S3...
2025-09-18 14:49:14,951 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png -> s3://document-extraction-logistically/temp/0f7c4d57_21_lumper_receipt_3.png
2025-09-18 14:49:14,951 - INFO - 🔍 [14:49:14] Starting classification: 21_lumper_receipt_3.png
2025-09-18 14:49:14,952 - INFO - ⬆️ [14:49:14] Uploading: 21_lumper_receipt_4.pdf
2025-09-18 14:49:14,954 - INFO - Initializing TextractProcessor...
2025-09-18 14:49:14,961 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b9596859_21_lumper_receipt_1.jpeg
2025-09-18 14:49:14,972 - INFO - Initializing BedrockProcessor...
2025-09-18 14:49:14,976 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0f7c4d57_21_lumper_receipt_3.png
2025-09-18 14:49:14,977 - INFO - Processing image from S3...
2025-09-18 14:49:15,538 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf -> s3://document-extraction-logistically/temp/e67085b7_21_lumper_receipt_4.pdf
2025-09-18 14:49:15,539 - INFO - 🔍 [14:49:15] Starting classification: 21_lumper_receipt_4.pdf
2025-09-18 14:49:15,540 - INFO - ⬆️ [14:49:15] Uploading: 21_lumper_receipt_5.pdf
2025-09-18 14:49:15,542 - INFO - Initializing TextractProcessor...
2025-09-18 14:49:15,561 - INFO - Initializing BedrockProcessor...
2025-09-18 14:49:15,565 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e67085b7_21_lumper_receipt_4.pdf
2025-09-18 14:49:15,565 - INFO - Processing PDF from S3...
2025-09-18 14:49:15,566 - INFO - Downloading PDF from S3 to /tmp/tmpfkr44rdi/e67085b7_21_lumper_receipt_4.pdf
2025-09-18 14:49:15,948 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/514067d1_21_lumper_receipt_10.png
2025-09-18 14:49:15,984 - INFO - S3 Image temp/09fe7db0_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 14:49:15,984 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:49:15,984 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:49:16,137 - INFO - S3 Image temp/fe155a97_21_lumper_receipt_2.jpg: Extracted 184 characters, 12 lines
2025-09-18 14:49:16,137 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:49:16,137 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:49:16,140 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf -> s3://document-extraction-logistically/temp/0d46328e_21_lumper_receipt_5.pdf
2025-09-18 14:49:16,140 - INFO - 🔍 [14:49:16] Starting classification: 21_lumper_receipt_5.pdf
2025-09-18 14:49:16,141 - INFO - ⬆️ [14:49:16] Uploading: 21_lumper_receipt_6.pdf
2025-09-18 14:49:16,141 - INFO - Initializing TextractProcessor...
2025-09-18 14:49:16,157 - INFO - Initializing BedrockProcessor...
2025-09-18 14:49:16,160 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0d46328e_21_lumper_receipt_5.pdf
2025-09-18 14:49:16,160 - INFO - Processing PDF from S3...
2025-09-18 14:49:16,160 - INFO - Downloading PDF from S3 to /tmp/tmpr76sj988/0d46328e_21_lumper_receipt_5.pdf
2025-09-18 14:49:16,804 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf -> s3://document-extraction-logistically/temp/70c264c9_21_lumper_receipt_6.pdf
2025-09-18 14:49:16,805 - INFO - 🔍 [14:49:16] Starting classification: 21_lumper_receipt_6.pdf
2025-09-18 14:49:16,806 - INFO - ⬆️ [14:49:16] Uploading: 21_lumper_receipt_7.png
2025-09-18 14:49:16,807 - INFO - Initializing TextractProcessor...
2025-09-18 14:49:16,871 - INFO - Initializing BedrockProcessor...
2025-09-18 14:49:16,874 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/70c264c9_21_lumper_receipt_6.pdf
2025-09-18 14:49:16,874 - INFO - Processing PDF from S3...
2025-09-18 14:49:16,875 - INFO - Downloading PDF from S3 to /tmp/tmp595ntf5o/70c264c9_21_lumper_receipt_6.pdf
2025-09-18 14:49:16,965 - INFO - Splitting PDF into individual pages...
2025-09-18 14:49:16,967 - INFO - Splitting PDF e67085b7_21_lumper_receipt_4 into 1 pages
2025-09-18 14:49:16,969 - INFO - Split PDF into 1 pages
2025-09-18 14:49:16,969 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:49:16,969 - INFO - Expected pages: [1]
2025-09-18 14:49:17,477 - INFO - Splitting PDF into individual pages...
2025-09-18 14:49:17,478 - INFO - Splitting PDF 0d46328e_21_lumper_receipt_5 into 1 pages
2025-09-18 14:49:17,480 - INFO - Split PDF into 1 pages
2025-09-18 14:49:17,480 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:49:17,480 - INFO - Expected pages: [1]
2025-09-18 14:49:17,531 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png -> s3://document-extraction-logistically/temp/1168aba0_21_lumper_receipt_7.png
2025-09-18 14:49:17,532 - INFO - 🔍 [14:49:17] Starting classification: 21_lumper_receipt_7.png
2025-09-18 14:49:17,533 - INFO - ⬆️ [14:49:17] Uploading: 21_lumper_receipt_8.pdf
2025-09-18 14:49:17,534 - INFO - Initializing TextractProcessor...
2025-09-18 14:49:17,554 - INFO - Initializing BedrockProcessor...
2025-09-18 14:49:17,558 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1168aba0_21_lumper_receipt_7.png
2025-09-18 14:49:17,558 - INFO - Processing image from S3...
2025-09-18 14:49:17,655 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fe155a97_21_lumper_receipt_2.jpg
2025-09-18 14:49:18,128 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf -> s3://document-extraction-logistically/temp/5cafee74_21_lumper_receipt_8.pdf
2025-09-18 14:49:18,129 - INFO - 🔍 [14:49:18] Starting classification: 21_lumper_receipt_8.pdf
2025-09-18 14:49:18,130 - INFO - ⬆️ [14:49:18] Uploading: 21_lumper_receipt_9.pdf
2025-09-18 14:49:18,131 - INFO - Initializing TextractProcessor...
2025-09-18 14:49:18,148 - INFO - Initializing BedrockProcessor...
2025-09-18 14:49:18,153 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5cafee74_21_lumper_receipt_8.pdf
2025-09-18 14:49:18,154 - INFO - Processing PDF from S3...
2025-09-18 14:49:18,154 - INFO - Downloading PDF from S3 to /tmp/tmpw5gbgc9_/5cafee74_21_lumper_receipt_8.pdf
2025-09-18 14:49:18,498 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/09fe7db0_21_lumper_receipt_11.jpeg
2025-09-18 14:49:18,740 - INFO - Splitting PDF into individual pages...
2025-09-18 14:49:18,742 - INFO - Splitting PDF 70c264c9_21_lumper_receipt_6 into 1 pages
2025-09-18 14:49:18,747 - INFO - Split PDF into 1 pages
2025-09-18 14:49:18,747 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:49:18,747 - INFO - Expected pages: [1]
2025-09-18 14:49:18,757 - INFO - S3 Image temp/0f7c4d57_21_lumper_receipt_3.png: Extracted 663 characters, 38 lines
2025-09-18 14:49:18,758 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:49:18,758 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:49:19,026 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf -> s3://document-extraction-logistically/temp/a759f820_21_lumper_receipt_9.pdf
2025-09-18 14:49:19,027 - INFO - 🔍 [14:49:19] Starting classification: 21_lumper_receipt_9.pdf
2025-09-18 14:49:19,028 - INFO - Initializing TextractProcessor...
2025-09-18 14:49:19,043 - INFO - Initializing BedrockProcessor...
2025-09-18 14:49:19,051 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a759f820_21_lumper_receipt_9.pdf
2025-09-18 14:49:19,054 - INFO - Processing PDF from S3...
2025-09-18 14:49:19,058 - INFO - Downloading PDF from S3 to /tmp/tmpbcwxeijd/a759f820_21_lumper_receipt_9.pdf
2025-09-18 14:49:19,075 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:49:19,076 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 14:49:19,363 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b9596859_21_lumper_receipt_1.jpeg
2025-09-18 14:49:19,383 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:49:19,383 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 14:49:19,554 - INFO - Splitting PDF into individual pages...
2025-09-18 14:49:19,555 - INFO - Splitting PDF 5cafee74_21_lumper_receipt_8 into 1 pages
2025-09-18 14:49:19,556 - INFO - Split PDF into 1 pages
2025-09-18 14:49:19,556 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:49:19,556 - INFO - Expected pages: [1]
2025-09-18 14:49:19,665 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/514067d1_21_lumper_receipt_10.png
2025-09-18 14:49:19,672 - INFO - 

21_lumper_receipt_2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:49:19,672 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_2.json
2025-09-18 14:49:19,749 - INFO - Page 1: Extracted 422 characters, 38 lines from e67085b7_21_lumper_receipt_4_36890b72_page_001.pdf
2025-09-18 14:49:19,749 - INFO - Successfully processed page 1
2025-09-18 14:49:19,750 - INFO - Combined 1 pages into final text
2025-09-18 14:49:19,750 - INFO - Text validation for e67085b7_21_lumper_receipt_4: 439 characters, 1 pages
2025-09-18 14:49:19,750 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:49:19,750 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:49:19,953 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fe155a97_21_lumper_receipt_2.jpg
2025-09-18 14:49:19,960 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:49:19,960 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 14:49:20,248 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/09fe7db0_21_lumper_receipt_11.jpeg
2025-09-18 14:49:20,369 - INFO - Page 1: Extracted 422 characters, 38 lines from 0d46328e_21_lumper_receipt_5_ee797ef1_page_001.pdf
2025-09-18 14:49:20,369 - INFO - Successfully processed page 1
2025-09-18 14:49:20,369 - INFO - Combined 1 pages into final text
2025-09-18 14:49:20,369 - INFO - Text validation for 0d46328e_21_lumper_receipt_5: 439 characters, 1 pages
2025-09-18 14:49:20,369 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:49:20,369 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:49:20,966 - INFO - S3 Image temp/1168aba0_21_lumper_receipt_7.png: Extracted 800 characters, 70 lines
2025-09-18 14:49:20,966 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:49:20,966 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:49:20,989 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0f7c4d57_21_lumper_receipt_3.png
2025-09-18 14:49:20,997 - INFO - 

21_lumper_receipt_3.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:49:20,998 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_3.json
2025-09-18 14:49:21,294 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0f7c4d57_21_lumper_receipt_3.png
2025-09-18 14:49:21,371 - INFO - Splitting PDF into individual pages...
2025-09-18 14:49:21,371 - INFO - Splitting PDF a759f820_21_lumper_receipt_9 into 1 pages
2025-09-18 14:49:21,372 - INFO - Split PDF into 1 pages
2025-09-18 14:49:21,372 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:49:21,372 - INFO - Expected pages: [1]
2025-09-18 14:49:21,813 - INFO - Page 1: Extracted 330 characters, 33 lines from 5cafee74_21_lumper_receipt_8_f344adf1_page_001.pdf
2025-09-18 14:49:21,813 - INFO - Successfully processed page 1
2025-09-18 14:49:21,813 - INFO - Combined 1 pages into final text
2025-09-18 14:49:21,813 - INFO - Text validation for 5cafee74_21_lumper_receipt_8: 347 characters, 1 pages
2025-09-18 14:49:21,814 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:49:21,814 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:49:21,856 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e67085b7_21_lumper_receipt_4.pdf
2025-09-18 14:49:21,873 - INFO - 

21_lumper_receipt_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:49:21,873 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_4.json
2025-09-18 14:49:22,172 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e67085b7_21_lumper_receipt_4.pdf
2025-09-18 14:49:23,380 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0d46328e_21_lumper_receipt_5.pdf
2025-09-18 14:49:23,397 - INFO - 

21_lumper_receipt_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:49:23,397 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_5.json
2025-09-18 14:49:23,690 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0d46328e_21_lumper_receipt_5.pdf
2025-09-18 14:49:23,996 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5cafee74_21_lumper_receipt_8.pdf
2025-09-18 14:49:24,012 - INFO - 

21_lumper_receipt_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:49:24,013 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_8.json
2025-09-18 14:49:24,079 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1168aba0_21_lumper_receipt_7.png
2025-09-18 14:49:24,307 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5cafee74_21_lumper_receipt_8.pdf
2025-09-18 14:49:24,326 - INFO - 

21_lumper_receipt_7.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:49:24,327 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_7.json
2025-09-18 14:49:24,642 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1168aba0_21_lumper_receipt_7.png
2025-09-18 14:49:25,151 - INFO - Page 1: Extracted 269 characters, 22 lines from 70c264c9_21_lumper_receipt_6_add631cd_page_001.pdf
2025-09-18 14:49:25,151 - INFO - Successfully processed page 1
2025-09-18 14:49:25,152 - INFO - Combined 1 pages into final text
2025-09-18 14:49:25,152 - INFO - Text validation for 70c264c9_21_lumper_receipt_6: 286 characters, 1 pages
2025-09-18 14:49:25,152 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:49:25,152 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:49:27,940 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/70c264c9_21_lumper_receipt_6.pdf
2025-09-18 14:49:27,950 - INFO - 

21_lumper_receipt_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:49:27,950 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_6.json
2025-09-18 14:49:28,236 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/70c264c9_21_lumper_receipt_6.pdf
2025-09-18 14:49:28,402 - INFO - Page 1: Extracted 645 characters, 53 lines from a759f820_21_lumper_receipt_9_756d24a2_page_001.pdf
2025-09-18 14:49:28,402 - INFO - Successfully processed page 1
2025-09-18 14:49:28,402 - INFO - Combined 1 pages into final text
2025-09-18 14:49:28,402 - INFO - Text validation for a759f820_21_lumper_receipt_9: 662 characters, 1 pages
2025-09-18 14:49:28,403 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:49:28,403 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:49:31,076 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a759f820_21_lumper_receipt_9.pdf
2025-09-18 14:49:31,093 - INFO - 

21_lumper_receipt_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:49:31,093 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_9.json
2025-09-18 14:49:31,387 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a759f820_21_lumper_receipt_9.pdf
2025-09-18 14:49:31,388 - INFO - 
📊 Processing Summary:
2025-09-18 14:49:31,388 - INFO -    Total files: 11
2025-09-18 14:49:31,388 - INFO -    Successful: 11
2025-09-18 14:49:31,388 - INFO -    Failed: 0
2025-09-18 14:49:31,388 - INFO -    Duration: 24.27 seconds
2025-09-18 14:49:31,388 - INFO -    Output directory: output
2025-09-18 14:49:31,388 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:49:31,388 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:49:31,388 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:49:31,389 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:49:31,389 - INFO -    📄 21_lumper_receipt_2.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:49:31,389 - INFO -    📄 21_lumper_receipt_3.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:49:31,389 - INFO -    📄 21_lumper_receipt_4.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:49:31,389 - INFO -    📄 21_lumper_receipt_5.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:49:31,389 - INFO -    📄 21_lumper_receipt_6.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:49:31,389 - INFO -    📄 21_lumper_receipt_7.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:49:31,389 - INFO -    📄 21_lumper_receipt_8.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:49:31,389 - INFO -    📄 21_lumper_receipt_9.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:49:31,390 - INFO - 
============================================================================================================================================
2025-09-18 14:49:31,390 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:49:31,390 - INFO - ============================================================================================================================================
2025-09-18 14:49:31,390 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:49:31,390 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:49:31,390 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 14:49:31,390 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 14:49:31,390 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 14:49:31,390 - INFO - 
2025-09-18 14:49:31,390 - INFO - 21_lumper_receipt_10.png                           1      invoice              run1_21_lumper_receipt_10.json                    
2025-09-18 14:49:31,390 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 14:49:31,391 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 14:49:31,391 - INFO - 
2025-09-18 14:49:31,391 - INFO - 21_lumper_receipt_11.jpeg                          1      invoice              run1_21_lumper_receipt_11.json                    
2025-09-18 14:49:31,391 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 14:49:31,391 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 14:49:31,391 - INFO - 
2025-09-18 14:49:31,391 - INFO - 21_lumper_receipt_2.jpg                            1      lumper_receipt       run1_21_lumper_receipt_2.json                     
2025-09-18 14:49:31,391 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg
2025-09-18 14:49:31,391 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_2.json
2025-09-18 14:49:31,391 - INFO - 
2025-09-18 14:49:31,391 - INFO - 21_lumper_receipt_3.png                            1      invoice              run1_21_lumper_receipt_3.json                     
2025-09-18 14:49:31,391 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png
2025-09-18 14:49:31,391 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_3.json
2025-09-18 14:49:31,391 - INFO - 
2025-09-18 14:49:31,391 - INFO - 21_lumper_receipt_4.pdf                            1      lumper_receipt       run1_21_lumper_receipt_4.json                     
2025-09-18 14:49:31,391 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf
2025-09-18 14:49:31,391 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_4.json
2025-09-18 14:49:31,391 - INFO - 
2025-09-18 14:49:31,392 - INFO - 21_lumper_receipt_5.pdf                            1      lumper_receipt       run1_21_lumper_receipt_5.json                     
2025-09-18 14:49:31,392 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf
2025-09-18 14:49:31,392 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_5.json
2025-09-18 14:49:31,392 - INFO - 
2025-09-18 14:49:31,392 - INFO - 21_lumper_receipt_6.pdf                            1      lumper_receipt       run1_21_lumper_receipt_6.json                     
2025-09-18 14:49:31,392 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf
2025-09-18 14:49:31,392 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_6.json
2025-09-18 14:49:31,392 - INFO - 
2025-09-18 14:49:31,392 - INFO - 21_lumper_receipt_7.png                            1      invoice              run1_21_lumper_receipt_7.json                     
2025-09-18 14:49:31,392 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png
2025-09-18 14:49:31,392 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_7.json
2025-09-18 14:49:31,392 - INFO - 
2025-09-18 14:49:31,392 - INFO - 21_lumper_receipt_8.pdf                            1      lumper_receipt       run1_21_lumper_receipt_8.json                     
2025-09-18 14:49:31,392 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf
2025-09-18 14:49:31,392 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_8.json
2025-09-18 14:49:31,392 - INFO - 
2025-09-18 14:49:31,392 - INFO - 21_lumper_receipt_9.pdf                            1      lumper_receipt       run1_21_lumper_receipt_9.json                     
2025-09-18 14:49:31,392 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf
2025-09-18 14:49:31,392 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_9.json
2025-09-18 14:49:31,393 - INFO - 
2025-09-18 14:49:31,393 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:49:31,393 - INFO - Total entries: 11
2025-09-18 14:49:31,393 - INFO - ============================================================================================================================================
2025-09-18 14:49:31,393 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:49:31,393 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:49:31,393 - INFO -   1. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 14:49:31,393 - INFO -   2. 21_lumper_receipt_10.png            Page 1   → invoice         | run1_21_lumper_receipt_10.json
2025-09-18 14:49:31,393 - INFO -   3. 21_lumper_receipt_11.jpeg           Page 1   → invoice         | run1_21_lumper_receipt_11.json
2025-09-18 14:49:31,393 - INFO -   4. 21_lumper_receipt_2.jpg             Page 1   → lumper_receipt  | run1_21_lumper_receipt_2.json
2025-09-18 14:49:31,393 - INFO -   5. 21_lumper_receipt_3.png             Page 1   → invoice         | run1_21_lumper_receipt_3.json
2025-09-18 14:49:31,393 - INFO -   6. 21_lumper_receipt_4.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_4.json
2025-09-18 14:49:31,393 - INFO -   7. 21_lumper_receipt_5.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_5.json
2025-09-18 14:49:31,393 - INFO -   8. 21_lumper_receipt_6.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_6.json
2025-09-18 14:49:31,393 - INFO -   9. 21_lumper_receipt_7.png             Page 1   → invoice         | run1_21_lumper_receipt_7.json
2025-09-18 14:49:31,393 - INFO -  10. 21_lumper_receipt_8.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_8.json
2025-09-18 14:49:31,393 - INFO -  11. 21_lumper_receipt_9.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_9.json
2025-09-18 14:49:31,393 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:49:31,395 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 24.273562, 'processed_files': [{'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '21_lumper_receipt_2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg'}, {'filename': '21_lumper_receipt_3.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png'}, {'filename': '21_lumper_receipt_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf'}, {'filename': '21_lumper_receipt_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf'}, {'filename': '21_lumper_receipt_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf'}, {'filename': '21_lumper_receipt_7.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png'}, {'filename': '21_lumper_receipt_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf'}, {'filename': '21_lumper_receipt_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf'}]}
