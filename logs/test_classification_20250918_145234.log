2025-09-18 14:52:34,479 - INFO - Logging initialized. Log file: logs/test_classification_20250918_145234.log
2025-09-18 14:52:34,480 - INFO - 📁 Found 11 files to process
2025-09-18 14:52:34,480 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:52:34,480 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 14:52:34,480 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 14:52:34,480 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 14:52:34,480 - INFO - ⬆️ [14:52:34] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 14:52:36,912 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/f4f4ad89_21_lumper_receipt_1.jpeg
2025-09-18 14:52:36,912 - INFO - 🔍 [14:52:36] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 14:52:36,913 - INFO - ⬆️ [14:52:36] Uploading: 21_lumper_receipt_10.png
2025-09-18 14:52:36,916 - INFO - Initializing TextractProcessor...
2025-09-18 14:52:36,946 - INFO - Initializing BedrockProcessor...
2025-09-18 14:52:36,955 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f4f4ad89_21_lumper_receipt_1.jpeg
2025-09-18 14:52:36,955 - INFO - Processing image from S3...
2025-09-18 14:52:37,834 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/3830dbd7_21_lumper_receipt_10.png
2025-09-18 14:52:37,834 - INFO - 🔍 [14:52:37] Starting classification: 21_lumper_receipt_10.png
2025-09-18 14:52:37,835 - INFO - ⬆️ [14:52:37] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 14:52:37,836 - INFO - Initializing TextractProcessor...
2025-09-18 14:52:37,857 - INFO - Initializing BedrockProcessor...
2025-09-18 14:52:37,860 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3830dbd7_21_lumper_receipt_10.png
2025-09-18 14:52:37,860 - INFO - Processing image from S3...
2025-09-18 14:52:39,921 - INFO - S3 Image temp/f4f4ad89_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 14:52:39,921 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:52:39,921 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:52:40,062 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/5a752f87_21_lumper_receipt_11.jpeg
2025-09-18 14:52:40,063 - INFO - 🔍 [14:52:40] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 14:52:40,064 - INFO - ⬆️ [14:52:40] Uploading: 21_lumper_receipt_2.jpg
2025-09-18 14:52:40,066 - INFO - Initializing TextractProcessor...
2025-09-18 14:52:40,085 - INFO - Initializing BedrockProcessor...
2025-09-18 14:52:40,089 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5a752f87_21_lumper_receipt_11.jpeg
2025-09-18 14:52:40,089 - INFO - Processing image from S3...
2025-09-18 14:52:41,005 - INFO - S3 Image temp/3830dbd7_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 14:52:41,005 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:52:41,005 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:52:41,621 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg -> s3://document-extraction-logistically/temp/17db4e93_21_lumper_receipt_2.jpg
2025-09-18 14:52:41,621 - INFO - 🔍 [14:52:41] Starting classification: 21_lumper_receipt_2.jpg
2025-09-18 14:52:41,622 - INFO - Initializing TextractProcessor...
2025-09-18 14:52:41,622 - INFO - ⬆️ [14:52:41] Uploading: 21_lumper_receipt_3.png
2025-09-18 14:52:41,630 - INFO - Initializing BedrockProcessor...
2025-09-18 14:52:41,634 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/17db4e93_21_lumper_receipt_2.jpg
2025-09-18 14:52:41,634 - INFO - Processing image from S3...
2025-09-18 14:52:42,104 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f4f4ad89_21_lumper_receipt_1.jpeg
2025-09-18 14:52:43,178 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png -> s3://document-extraction-logistically/temp/6f35a1df_21_lumper_receipt_3.png
2025-09-18 14:52:43,179 - INFO - 🔍 [14:52:43] Starting classification: 21_lumper_receipt_3.png
2025-09-18 14:52:43,180 - INFO - ⬆️ [14:52:43] Uploading: 21_lumper_receipt_4.pdf
2025-09-18 14:52:43,180 - INFO - Initializing TextractProcessor...
2025-09-18 14:52:43,202 - INFO - Initializing BedrockProcessor...
2025-09-18 14:52:43,205 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6f35a1df_21_lumper_receipt_3.png
2025-09-18 14:52:43,205 - INFO - Processing image from S3...
2025-09-18 14:52:43,273 - INFO - S3 Image temp/5a752f87_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 14:52:43,273 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:52:43,273 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:52:43,774 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf -> s3://document-extraction-logistically/temp/02e44a94_21_lumper_receipt_4.pdf
2025-09-18 14:52:43,774 - INFO - 🔍 [14:52:43] Starting classification: 21_lumper_receipt_4.pdf
2025-09-18 14:52:43,775 - INFO - ⬆️ [14:52:43] Uploading: 21_lumper_receipt_5.pdf
2025-09-18 14:52:43,776 - INFO - Initializing TextractProcessor...
2025-09-18 14:52:43,797 - INFO - Initializing BedrockProcessor...
2025-09-18 14:52:43,801 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/02e44a94_21_lumper_receipt_4.pdf
2025-09-18 14:52:43,802 - INFO - Processing PDF from S3...
2025-09-18 14:52:43,802 - INFO - Downloading PDF from S3 to /tmp/tmpcww9bmux/02e44a94_21_lumper_receipt_4.pdf
2025-09-18 14:52:43,942 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3830dbd7_21_lumper_receipt_10.png
2025-09-18 14:52:44,023 - INFO - S3 Image temp/17db4e93_21_lumper_receipt_2.jpg: Extracted 184 characters, 12 lines
2025-09-18 14:52:44,023 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:52:44,023 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:52:44,362 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf -> s3://document-extraction-logistically/temp/28cd8815_21_lumper_receipt_5.pdf
2025-09-18 14:52:44,362 - INFO - 🔍 [14:52:44] Starting classification: 21_lumper_receipt_5.pdf
2025-09-18 14:52:44,363 - INFO - ⬆️ [14:52:44] Uploading: 21_lumper_receipt_6.pdf
2025-09-18 14:52:44,363 - INFO - Initializing TextractProcessor...
2025-09-18 14:52:44,376 - INFO - Initializing BedrockProcessor...
2025-09-18 14:52:44,379 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/28cd8815_21_lumper_receipt_5.pdf
2025-09-18 14:52:44,380 - INFO - Processing PDF from S3...
2025-09-18 14:52:44,380 - INFO - Downloading PDF from S3 to /tmp/tmpec8pxte_/28cd8815_21_lumper_receipt_5.pdf
2025-09-18 14:52:45,022 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf -> s3://document-extraction-logistically/temp/189c7ecd_21_lumper_receipt_6.pdf
2025-09-18 14:52:45,022 - INFO - 🔍 [14:52:45] Starting classification: 21_lumper_receipt_6.pdf
2025-09-18 14:52:45,023 - INFO - ⬆️ [14:52:45] Uploading: 21_lumper_receipt_7.png
2025-09-18 14:52:45,024 - INFO - Initializing TextractProcessor...
2025-09-18 14:52:45,046 - INFO - Initializing BedrockProcessor...
2025-09-18 14:52:45,053 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/189c7ecd_21_lumper_receipt_6.pdf
2025-09-18 14:52:45,053 - INFO - Processing PDF from S3...
2025-09-18 14:52:45,054 - INFO - Downloading PDF from S3 to /tmp/tmp0m1r3_82/189c7ecd_21_lumper_receipt_6.pdf
2025-09-18 14:52:45,071 - INFO - Splitting PDF into individual pages...
2025-09-18 14:52:45,073 - INFO - Splitting PDF 02e44a94_21_lumper_receipt_4 into 1 pages
2025-09-18 14:52:45,077 - INFO - Split PDF into 1 pages
2025-09-18 14:52:45,078 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:52:45,078 - INFO - Expected pages: [1]
2025-09-18 14:52:45,332 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5a752f87_21_lumper_receipt_11.jpeg
2025-09-18 14:52:45,515 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/17db4e93_21_lumper_receipt_2.jpg
2025-09-18 14:52:45,647 - INFO - Splitting PDF into individual pages...
2025-09-18 14:52:45,648 - INFO - Splitting PDF 28cd8815_21_lumper_receipt_5 into 1 pages
2025-09-18 14:52:45,650 - INFO - Split PDF into 1 pages
2025-09-18 14:52:45,650 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:52:45,650 - INFO - Expected pages: [1]
2025-09-18 14:52:45,719 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png -> s3://document-extraction-logistically/temp/aff24620_21_lumper_receipt_7.png
2025-09-18 14:52:45,719 - INFO - 🔍 [14:52:45] Starting classification: 21_lumper_receipt_7.png
2025-09-18 14:52:45,719 - INFO - Initializing TextractProcessor...
2025-09-18 14:52:45,720 - INFO - ⬆️ [14:52:45] Uploading: 21_lumper_receipt_8.pdf
2025-09-18 14:52:45,732 - INFO - Initializing BedrockProcessor...
2025-09-18 14:52:45,739 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/aff24620_21_lumper_receipt_7.png
2025-09-18 14:52:45,740 - INFO - Processing image from S3...
2025-09-18 14:52:46,186 - INFO - S3 Image temp/6f35a1df_21_lumper_receipt_3.png: Extracted 663 characters, 38 lines
2025-09-18 14:52:46,187 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:52:46,187 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:52:46,315 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf -> s3://document-extraction-logistically/temp/33332023_21_lumper_receipt_8.pdf
2025-09-18 14:52:46,315 - INFO - 🔍 [14:52:46] Starting classification: 21_lumper_receipt_8.pdf
2025-09-18 14:52:46,316 - INFO - ⬆️ [14:52:46] Uploading: 21_lumper_receipt_9.pdf
2025-09-18 14:52:46,316 - INFO - Initializing TextractProcessor...
2025-09-18 14:52:46,338 - INFO - Initializing BedrockProcessor...
2025-09-18 14:52:46,341 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/33332023_21_lumper_receipt_8.pdf
2025-09-18 14:52:46,341 - INFO - Processing PDF from S3...
2025-09-18 14:52:46,341 - INFO - Downloading PDF from S3 to /tmp/tmp79iivn20/33332023_21_lumper_receipt_8.pdf
2025-09-18 14:52:46,810 - INFO - Splitting PDF into individual pages...
2025-09-18 14:52:46,811 - INFO - Splitting PDF 189c7ecd_21_lumper_receipt_6 into 1 pages
2025-09-18 14:52:46,817 - INFO - Split PDF into 1 pages
2025-09-18 14:52:46,817 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:52:46,817 - INFO - Expected pages: [1]
2025-09-18 14:52:47,258 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf -> s3://document-extraction-logistically/temp/888af0a1_21_lumper_receipt_9.pdf
2025-09-18 14:52:47,259 - INFO - 🔍 [14:52:47] Starting classification: 21_lumper_receipt_9.pdf
2025-09-18 14:52:47,260 - INFO - Initializing TextractProcessor...
2025-09-18 14:52:47,275 - INFO - Initializing BedrockProcessor...
2025-09-18 14:52:47,281 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/888af0a1_21_lumper_receipt_9.pdf
2025-09-18 14:52:47,283 - INFO - Processing PDF from S3...
2025-09-18 14:52:47,285 - INFO - Downloading PDF from S3 to /tmp/tmp01n19xcx/888af0a1_21_lumper_receipt_9.pdf
2025-09-18 14:52:47,292 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:52:47,295 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 14:52:47,601 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f4f4ad89_21_lumper_receipt_1.jpeg
2025-09-18 14:52:47,608 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:52:47,608 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 14:52:47,620 - INFO - Splitting PDF into individual pages...
2025-09-18 14:52:47,621 - INFO - Splitting PDF 33332023_21_lumper_receipt_8 into 1 pages
2025-09-18 14:52:47,622 - INFO - Split PDF into 1 pages
2025-09-18 14:52:47,622 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:52:47,622 - INFO - Expected pages: [1]
2025-09-18 14:52:47,905 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3830dbd7_21_lumper_receipt_10.png
2025-09-18 14:52:47,923 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:52:47,923 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 14:52:48,010 - INFO - Page 1: Extracted 422 characters, 38 lines from 02e44a94_21_lumper_receipt_4_66c832ac_page_001.pdf
2025-09-18 14:52:48,011 - INFO - Successfully processed page 1
2025-09-18 14:52:48,011 - INFO - Combined 1 pages into final text
2025-09-18 14:52:48,011 - INFO - Text validation for 02e44a94_21_lumper_receipt_4: 439 characters, 1 pages
2025-09-18 14:52:48,012 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:52:48,012 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:52:48,228 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5a752f87_21_lumper_receipt_11.jpeg
2025-09-18 14:52:48,235 - INFO - 

21_lumper_receipt_2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:52:48,235 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_2.json
2025-09-18 14:52:48,464 - INFO - Page 1: Extracted 422 characters, 38 lines from 28cd8815_21_lumper_receipt_5_46a405d3_page_001.pdf
2025-09-18 14:52:48,464 - INFO - Successfully processed page 1
2025-09-18 14:52:48,465 - INFO - Combined 1 pages into final text
2025-09-18 14:52:48,465 - INFO - Text validation for 28cd8815_21_lumper_receipt_5: 439 characters, 1 pages
2025-09-18 14:52:48,465 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:52:48,465 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:52:48,542 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/17db4e93_21_lumper_receipt_2.jpg
2025-09-18 14:52:48,643 - INFO - S3 Image temp/aff24620_21_lumper_receipt_7.png: Extracted 800 characters, 70 lines
2025-09-18 14:52:48,643 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:52:48,643 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:52:49,137 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6f35a1df_21_lumper_receipt_3.png
2025-09-18 14:52:49,153 - INFO - 

21_lumper_receipt_3.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:52:49,153 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_3.json
2025-09-18 14:52:49,460 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6f35a1df_21_lumper_receipt_3.png
2025-09-18 14:52:49,547 - INFO - Splitting PDF into individual pages...
2025-09-18 14:52:49,548 - INFO - Splitting PDF 888af0a1_21_lumper_receipt_9 into 1 pages
2025-09-18 14:52:49,551 - INFO - Split PDF into 1 pages
2025-09-18 14:52:49,551 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:52:49,551 - INFO - Expected pages: [1]
2025-09-18 14:52:49,926 - INFO - Page 1: Extracted 330 characters, 33 lines from 33332023_21_lumper_receipt_8_5698b014_page_001.pdf
2025-09-18 14:52:49,926 - INFO - Successfully processed page 1
2025-09-18 14:52:49,926 - INFO - Combined 1 pages into final text
2025-09-18 14:52:49,927 - INFO - Text validation for 33332023_21_lumper_receipt_8: 347 characters, 1 pages
2025-09-18 14:52:49,927 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:52:49,927 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:52:50,483 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/02e44a94_21_lumper_receipt_4.pdf
2025-09-18 14:52:50,496 - INFO - 

21_lumper_receipt_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:52:50,496 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_4.json
2025-09-18 14:52:50,643 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/28cd8815_21_lumper_receipt_5.pdf
2025-09-18 14:52:50,800 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/02e44a94_21_lumper_receipt_4.pdf
2025-09-18 14:52:50,808 - INFO - 

21_lumper_receipt_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:52:50,808 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_5.json
2025-09-18 14:52:51,121 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/28cd8815_21_lumper_receipt_5.pdf
2025-09-18 14:52:51,123 - INFO - Page 1: Extracted 269 characters, 22 lines from 189c7ecd_21_lumper_receipt_6_4d5b4e96_page_001.pdf
2025-09-18 14:52:51,124 - INFO - Successfully processed page 1
2025-09-18 14:52:51,124 - INFO - Combined 1 pages into final text
2025-09-18 14:52:51,124 - INFO - Text validation for 189c7ecd_21_lumper_receipt_6: 286 characters, 1 pages
2025-09-18 14:52:51,125 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:52:51,125 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:52:51,222 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/aff24620_21_lumper_receipt_7.png
2025-09-18 14:52:51,241 - INFO - 

21_lumper_receipt_7.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:52:51,241 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_7.json
2025-09-18 14:52:51,540 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/aff24620_21_lumper_receipt_7.png
2025-09-18 14:52:52,485 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/33332023_21_lumper_receipt_8.pdf
2025-09-18 14:52:52,501 - INFO - 

21_lumper_receipt_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:52:52,501 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_8.json
2025-09-18 14:52:52,807 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/33332023_21_lumper_receipt_8.pdf
2025-09-18 14:52:53,072 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/189c7ecd_21_lumper_receipt_6.pdf
2025-09-18 14:52:53,083 - INFO - 

21_lumper_receipt_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:52:53,083 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_6.json
2025-09-18 14:52:53,393 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/189c7ecd_21_lumper_receipt_6.pdf
2025-09-18 14:52:55,119 - INFO - Page 1: Extracted 645 characters, 53 lines from 888af0a1_21_lumper_receipt_9_7e4f48a6_page_001.pdf
2025-09-18 14:52:55,119 - INFO - Successfully processed page 1
2025-09-18 14:52:55,119 - INFO - Combined 1 pages into final text
2025-09-18 14:52:55,120 - INFO - Text validation for 888af0a1_21_lumper_receipt_9: 662 characters, 1 pages
2025-09-18 14:52:55,120 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:52:55,120 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:52:59,468 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/888af0a1_21_lumper_receipt_9.pdf
2025-09-18 14:52:59,492 - INFO - 

21_lumper_receipt_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:52:59,493 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_9.json
2025-09-18 14:53:00,459 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/888af0a1_21_lumper_receipt_9.pdf
2025-09-18 14:53:00,460 - INFO - 
📊 Processing Summary:
2025-09-18 14:53:00,460 - INFO -    Total files: 11
2025-09-18 14:53:00,461 - INFO -    Successful: 11
2025-09-18 14:53:00,461 - INFO -    Failed: 0
2025-09-18 14:53:00,461 - INFO -    Duration: 25.98 seconds
2025-09-18 14:53:00,461 - INFO -    Output directory: output
2025-09-18 14:53:00,461 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:53:00,461 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:53:00,462 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:53:00,462 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:53:00,462 - INFO -    📄 21_lumper_receipt_2.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:53:00,462 - INFO -    📄 21_lumper_receipt_3.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:53:00,463 - INFO -    📄 21_lumper_receipt_4.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:53:00,463 - INFO -    📄 21_lumper_receipt_5.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:53:00,463 - INFO -    📄 21_lumper_receipt_6.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:53:00,463 - INFO -    📄 21_lumper_receipt_7.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:53:00,463 - INFO -    📄 21_lumper_receipt_8.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:53:00,463 - INFO -    📄 21_lumper_receipt_9.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:53:00,464 - INFO - 
============================================================================================================================================
2025-09-18 14:53:00,464 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:53:00,464 - INFO - ============================================================================================================================================
2025-09-18 14:53:00,464 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:53:00,464 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:53:00,464 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 14:53:00,464 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 14:53:00,464 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 14:53:00,464 - INFO - 
2025-09-18 14:53:00,464 - INFO - 21_lumper_receipt_10.png                           1      lumper_receipt       run1_21_lumper_receipt_10.json                    
2025-09-18 14:53:00,464 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 14:53:00,464 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 14:53:00,464 - INFO - 
2025-09-18 14:53:00,464 - INFO - 21_lumper_receipt_11.jpeg                          1      invoice              run1_21_lumper_receipt_11.json                    
2025-09-18 14:53:00,464 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 14:53:00,464 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 14:53:00,464 - INFO - 
2025-09-18 14:53:00,465 - INFO - 21_lumper_receipt_2.jpg                            1      lumper_receipt       run1_21_lumper_receipt_2.json                     
2025-09-18 14:53:00,465 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg
2025-09-18 14:53:00,465 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_2.json
2025-09-18 14:53:00,465 - INFO - 
2025-09-18 14:53:00,465 - INFO - 21_lumper_receipt_3.png                            1      lumper_receipt       run1_21_lumper_receipt_3.json                     
2025-09-18 14:53:00,465 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png
2025-09-18 14:53:00,465 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_3.json
2025-09-18 14:53:00,465 - INFO - 
2025-09-18 14:53:00,465 - INFO - 21_lumper_receipt_4.pdf                            1      invoice              run1_21_lumper_receipt_4.json                     
2025-09-18 14:53:00,465 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf
2025-09-18 14:53:00,465 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_4.json
2025-09-18 14:53:00,465 - INFO - 
2025-09-18 14:53:00,465 - INFO - 21_lumper_receipt_5.pdf                            1      lumper_receipt       run1_21_lumper_receipt_5.json                     
2025-09-18 14:53:00,465 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf
2025-09-18 14:53:00,465 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_5.json
2025-09-18 14:53:00,465 - INFO - 
2025-09-18 14:53:00,465 - INFO - 21_lumper_receipt_6.pdf                            1      lumper_receipt       run1_21_lumper_receipt_6.json                     
2025-09-18 14:53:00,466 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf
2025-09-18 14:53:00,466 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_6.json
2025-09-18 14:53:00,466 - INFO - 
2025-09-18 14:53:00,466 - INFO - 21_lumper_receipt_7.png                            1      lumper_receipt       run1_21_lumper_receipt_7.json                     
2025-09-18 14:53:00,466 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png
2025-09-18 14:53:00,466 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_7.json
2025-09-18 14:53:00,466 - INFO - 
2025-09-18 14:53:00,466 - INFO - 21_lumper_receipt_8.pdf                            1      lumper_receipt       run1_21_lumper_receipt_8.json                     
2025-09-18 14:53:00,466 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf
2025-09-18 14:53:00,466 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_8.json
2025-09-18 14:53:00,466 - INFO - 
2025-09-18 14:53:00,466 - INFO - 21_lumper_receipt_9.pdf                            1      lumper_receipt       run1_21_lumper_receipt_9.json                     
2025-09-18 14:53:00,466 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf
2025-09-18 14:53:00,466 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_9.json
2025-09-18 14:53:00,466 - INFO - 
2025-09-18 14:53:00,466 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:53:00,466 - INFO - Total entries: 11
2025-09-18 14:53:00,466 - INFO - ============================================================================================================================================
2025-09-18 14:53:00,466 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:53:00,466 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:53:00,467 - INFO -   1. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 14:53:00,467 - INFO -   2. 21_lumper_receipt_10.png            Page 1   → lumper_receipt  | run1_21_lumper_receipt_10.json
2025-09-18 14:53:00,467 - INFO -   3. 21_lumper_receipt_11.jpeg           Page 1   → invoice         | run1_21_lumper_receipt_11.json
2025-09-18 14:53:00,467 - INFO -   4. 21_lumper_receipt_2.jpg             Page 1   → lumper_receipt  | run1_21_lumper_receipt_2.json
2025-09-18 14:53:00,467 - INFO -   5. 21_lumper_receipt_3.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_3.json
2025-09-18 14:53:00,467 - INFO -   6. 21_lumper_receipt_4.pdf             Page 1   → invoice         | run1_21_lumper_receipt_4.json
2025-09-18 14:53:00,467 - INFO -   7. 21_lumper_receipt_5.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_5.json
2025-09-18 14:53:00,467 - INFO -   8. 21_lumper_receipt_6.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_6.json
2025-09-18 14:53:00,467 - INFO -   9. 21_lumper_receipt_7.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_7.json
2025-09-18 14:53:00,467 - INFO -  10. 21_lumper_receipt_8.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_8.json
2025-09-18 14:53:00,467 - INFO -  11. 21_lumper_receipt_9.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_9.json
2025-09-18 14:53:00,467 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:53:00,467 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 25.980029, 'processed_files': [{'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '21_lumper_receipt_2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg'}, {'filename': '21_lumper_receipt_3.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png'}, {'filename': '21_lumper_receipt_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf'}, {'filename': '21_lumper_receipt_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf'}, {'filename': '21_lumper_receipt_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf'}, {'filename': '21_lumper_receipt_7.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png'}, {'filename': '21_lumper_receipt_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf'}, {'filename': '21_lumper_receipt_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf'}]}
