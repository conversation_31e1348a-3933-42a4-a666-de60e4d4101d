2025-09-18 14:58:00,881 - INFO - Logging initialized. Log file: logs/test_classification_20250918_145800.log
2025-09-18 14:58:00,882 - INFO - 📁 Found 11 files to process
2025-09-18 14:58:00,882 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:58:00,882 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 14:58:00,882 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 14:58:00,882 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 14:58:00,882 - INFO - ⬆️ [14:58:00] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 14:58:03,366 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/bac8a654_21_lumper_receipt_1.jpeg
2025-09-18 14:58:03,367 - INFO - 🔍 [14:58:03] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 14:58:03,368 - INFO - Initializing TextractProcessor...
2025-09-18 14:58:03,368 - INFO - ⬆️ [14:58:03] Uploading: 21_lumper_receipt_10.png
2025-09-18 14:58:03,391 - INFO - Initializing BedrockProcessor...
2025-09-18 14:58:03,397 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bac8a654_21_lumper_receipt_1.jpeg
2025-09-18 14:58:03,397 - INFO - Processing image from S3...
2025-09-18 14:58:04,083 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/420227a9_21_lumper_receipt_10.png
2025-09-18 14:58:04,083 - INFO - 🔍 [14:58:04] Starting classification: 21_lumper_receipt_10.png
2025-09-18 14:58:04,084 - INFO - ⬆️ [14:58:04] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 14:58:04,084 - INFO - Initializing TextractProcessor...
2025-09-18 14:58:04,102 - INFO - Initializing BedrockProcessor...
2025-09-18 14:58:04,106 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/420227a9_21_lumper_receipt_10.png
2025-09-18 14:58:04,107 - INFO - Processing image from S3...
2025-09-18 14:58:05,512 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/73486949_21_lumper_receipt_11.jpeg
2025-09-18 14:58:05,513 - INFO - 🔍 [14:58:05] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 14:58:05,514 - INFO - ⬆️ [14:58:05] Uploading: 21_lumper_receipt_2.jpg
2025-09-18 14:58:05,514 - INFO - Initializing TextractProcessor...
2025-09-18 14:58:05,531 - INFO - Initializing BedrockProcessor...
2025-09-18 14:58:05,533 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/73486949_21_lumper_receipt_11.jpeg
2025-09-18 14:58:05,533 - INFO - Processing image from S3...
2025-09-18 14:58:06,364 - INFO - S3 Image temp/bac8a654_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 14:58:06,365 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:58:06,365 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:58:06,463 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg -> s3://document-extraction-logistically/temp/1cb67073_21_lumper_receipt_2.jpg
2025-09-18 14:58:06,463 - INFO - 🔍 [14:58:06] Starting classification: 21_lumper_receipt_2.jpg
2025-09-18 14:58:06,464 - INFO - ⬆️ [14:58:06] Uploading: 21_lumper_receipt_3.png
2025-09-18 14:58:06,466 - INFO - Initializing TextractProcessor...
2025-09-18 14:58:06,482 - INFO - Initializing BedrockProcessor...
2025-09-18 14:58:06,486 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1cb67073_21_lumper_receipt_2.jpg
2025-09-18 14:58:06,486 - INFO - Processing image from S3...
2025-09-18 14:58:07,228 - INFO - S3 Image temp/420227a9_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 14:58:07,228 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:58:07,228 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:58:07,445 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png -> s3://document-extraction-logistically/temp/a5855fd8_21_lumper_receipt_3.png
2025-09-18 14:58:07,445 - INFO - 🔍 [14:58:07] Starting classification: 21_lumper_receipt_3.png
2025-09-18 14:58:07,446 - INFO - ⬆️ [14:58:07] Uploading: 21_lumper_receipt_4.pdf
2025-09-18 14:58:07,448 - INFO - Initializing TextractProcessor...
2025-09-18 14:58:07,465 - INFO - Initializing BedrockProcessor...
2025-09-18 14:58:07,469 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a5855fd8_21_lumper_receipt_3.png
2025-09-18 14:58:07,470 - INFO - Processing image from S3...
2025-09-18 14:58:08,033 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf -> s3://document-extraction-logistically/temp/e5c1ab46_21_lumper_receipt_4.pdf
2025-09-18 14:58:08,033 - INFO - 🔍 [14:58:08] Starting classification: 21_lumper_receipt_4.pdf
2025-09-18 14:58:08,034 - INFO - ⬆️ [14:58:08] Uploading: 21_lumper_receipt_5.pdf
2025-09-18 14:58:08,036 - INFO - Initializing TextractProcessor...
2025-09-18 14:58:08,047 - INFO - Initializing BedrockProcessor...
2025-09-18 14:58:08,050 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e5c1ab46_21_lumper_receipt_4.pdf
2025-09-18 14:58:08,050 - INFO - Processing PDF from S3...
2025-09-18 14:58:08,051 - INFO - Downloading PDF from S3 to /tmp/tmpzenvr4p_/e5c1ab46_21_lumper_receipt_4.pdf
2025-09-18 14:58:08,466 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bac8a654_21_lumper_receipt_1.jpeg
2025-09-18 14:58:08,620 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf -> s3://document-extraction-logistically/temp/6b961c65_21_lumper_receipt_5.pdf
2025-09-18 14:58:08,620 - INFO - 🔍 [14:58:08] Starting classification: 21_lumper_receipt_5.pdf
2025-09-18 14:58:08,620 - INFO - ⬆️ [14:58:08] Uploading: 21_lumper_receipt_6.pdf
2025-09-18 14:58:08,621 - INFO - Initializing TextractProcessor...
2025-09-18 14:58:08,632 - INFO - Initializing BedrockProcessor...
2025-09-18 14:58:08,634 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6b961c65_21_lumper_receipt_5.pdf
2025-09-18 14:58:08,634 - INFO - Processing PDF from S3...
2025-09-18 14:58:08,634 - INFO - Downloading PDF from S3 to /tmp/tmpo2vwcg6_/6b961c65_21_lumper_receipt_5.pdf
2025-09-18 14:58:08,777 - INFO - S3 Image temp/73486949_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 14:58:08,779 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:58:08,779 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:58:09,037 - INFO - S3 Image temp/1cb67073_21_lumper_receipt_2.jpg: Extracted 184 characters, 12 lines
2025-09-18 14:58:09,037 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:58:09,037 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:58:09,133 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/420227a9_21_lumper_receipt_10.png
2025-09-18 14:58:09,243 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf -> s3://document-extraction-logistically/temp/77eb6a87_21_lumper_receipt_6.pdf
2025-09-18 14:58:09,243 - INFO - 🔍 [14:58:09] Starting classification: 21_lumper_receipt_6.pdf
2025-09-18 14:58:09,244 - INFO - ⬆️ [14:58:09] Uploading: 21_lumper_receipt_7.png
2025-09-18 14:58:09,245 - INFO - Initializing TextractProcessor...
2025-09-18 14:58:09,257 - INFO - Initializing BedrockProcessor...
2025-09-18 14:58:09,260 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/77eb6a87_21_lumper_receipt_6.pdf
2025-09-18 14:58:09,260 - INFO - Processing PDF from S3...
2025-09-18 14:58:09,261 - INFO - Downloading PDF from S3 to /tmp/tmpdw6vbp9_/77eb6a87_21_lumper_receipt_6.pdf
2025-09-18 14:58:09,372 - INFO - Splitting PDF into individual pages...
2025-09-18 14:58:09,374 - INFO - Splitting PDF e5c1ab46_21_lumper_receipt_4 into 1 pages
2025-09-18 14:58:09,377 - INFO - Split PDF into 1 pages
2025-09-18 14:58:09,377 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:58:09,377 - INFO - Expected pages: [1]
2025-09-18 14:58:09,891 - INFO - Splitting PDF into individual pages...
2025-09-18 14:58:09,892 - INFO - Splitting PDF 6b961c65_21_lumper_receipt_5 into 1 pages
2025-09-18 14:58:09,895 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png -> s3://document-extraction-logistically/temp/8e8dc4a4_21_lumper_receipt_7.png
2025-09-18 14:58:09,895 - INFO - Split PDF into 1 pages
2025-09-18 14:58:09,895 - INFO - 🔍 [14:58:09] Starting classification: 21_lumper_receipt_7.png
2025-09-18 14:58:09,895 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:58:09,896 - INFO - Expected pages: [1]
2025-09-18 14:58:09,896 - INFO - ⬆️ [14:58:09] Uploading: 21_lumper_receipt_8.pdf
2025-09-18 14:58:09,897 - INFO - Initializing TextractProcessor...
2025-09-18 14:58:09,920 - INFO - Initializing BedrockProcessor...
2025-09-18 14:58:09,924 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8e8dc4a4_21_lumper_receipt_7.png
2025-09-18 14:58:09,925 - INFO - Processing image from S3...
2025-09-18 14:58:10,434 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1cb67073_21_lumper_receipt_2.jpg
2025-09-18 14:58:10,489 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf -> s3://document-extraction-logistically/temp/3ecca325_21_lumper_receipt_8.pdf
2025-09-18 14:58:10,489 - INFO - 🔍 [14:58:10] Starting classification: 21_lumper_receipt_8.pdf
2025-09-18 14:58:10,490 - INFO - ⬆️ [14:58:10] Uploading: 21_lumper_receipt_9.pdf
2025-09-18 14:58:10,492 - INFO - Initializing TextractProcessor...
2025-09-18 14:58:10,507 - INFO - Initializing BedrockProcessor...
2025-09-18 14:58:10,511 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3ecca325_21_lumper_receipt_8.pdf
2025-09-18 14:58:10,511 - INFO - Processing PDF from S3...
2025-09-18 14:58:10,511 - INFO - Downloading PDF from S3 to /tmp/tmpe05bt3g1/3ecca325_21_lumper_receipt_8.pdf
2025-09-18 14:58:10,915 - INFO - S3 Image temp/a5855fd8_21_lumper_receipt_3.png: Extracted 663 characters, 38 lines
2025-09-18 14:58:10,915 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:58:10,915 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:58:11,050 - INFO - Splitting PDF into individual pages...
2025-09-18 14:58:11,051 - INFO - Splitting PDF 77eb6a87_21_lumper_receipt_6 into 1 pages
2025-09-18 14:58:11,053 - INFO - Split PDF into 1 pages
2025-09-18 14:58:11,053 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:58:11,053 - INFO - Expected pages: [1]
2025-09-18 14:58:11,160 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf -> s3://document-extraction-logistically/temp/eecee744_21_lumper_receipt_9.pdf
2025-09-18 14:58:11,161 - INFO - 🔍 [14:58:11] Starting classification: 21_lumper_receipt_9.pdf
2025-09-18 14:58:11,161 - INFO - Initializing TextractProcessor...
2025-09-18 14:58:11,171 - INFO - Initializing BedrockProcessor...
2025-09-18 14:58:11,175 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/eecee744_21_lumper_receipt_9.pdf
2025-09-18 14:58:11,176 - INFO - Processing PDF from S3...
2025-09-18 14:58:11,176 - INFO - Downloading PDF from S3 to /tmp/tmps1s_g1ne/eecee744_21_lumper_receipt_9.pdf
2025-09-18 14:58:11,181 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:58:11,181 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 14:58:11,479 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bac8a654_21_lumper_receipt_1.jpeg
2025-09-18 14:58:11,495 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:58:11,496 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 14:58:11,767 - INFO - Splitting PDF into individual pages...
2025-09-18 14:58:11,768 - INFO - Splitting PDF 3ecca325_21_lumper_receipt_8 into 1 pages
2025-09-18 14:58:11,769 - INFO - Split PDF into 1 pages
2025-09-18 14:58:11,769 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:58:11,769 - INFO - Expected pages: [1]
2025-09-18 14:58:11,783 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/73486949_21_lumper_receipt_11.jpeg
2025-09-18 14:58:11,794 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/420227a9_21_lumper_receipt_10.png
2025-09-18 14:58:11,798 - INFO - 

21_lumper_receipt_2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:58:11,798 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_2.json
2025-09-18 14:58:12,193 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1cb67073_21_lumper_receipt_2.jpg
2025-09-18 14:58:12,208 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:58:12,208 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 14:58:12,509 - INFO - S3 Image temp/8e8dc4a4_21_lumper_receipt_7.png: Extracted 800 characters, 70 lines
2025-09-18 14:58:12,509 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/73486949_21_lumper_receipt_11.jpeg
2025-09-18 14:58:12,510 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:58:12,510 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:58:12,672 - INFO - Page 1: Extracted 422 characters, 38 lines from 6b961c65_21_lumper_receipt_5_c44d2374_page_001.pdf
2025-09-18 14:58:12,672 - INFO - Successfully processed page 1
2025-09-18 14:58:12,672 - INFO - Combined 1 pages into final text
2025-09-18 14:58:12,672 - INFO - Text validation for 6b961c65_21_lumper_receipt_5: 439 characters, 1 pages
2025-09-18 14:58:12,672 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:58:12,673 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:58:12,854 - INFO - Page 1: Extracted 422 characters, 38 lines from e5c1ab46_21_lumper_receipt_4_1fff5046_page_001.pdf
2025-09-18 14:58:12,854 - INFO - Successfully processed page 1
2025-09-18 14:58:12,855 - INFO - Combined 1 pages into final text
2025-09-18 14:58:12,855 - INFO - Text validation for e5c1ab46_21_lumper_receipt_4: 439 characters, 1 pages
2025-09-18 14:58:12,855 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:58:12,855 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:58:13,303 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a5855fd8_21_lumper_receipt_3.png
2025-09-18 14:58:13,317 - INFO - 

21_lumper_receipt_3.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:58:13,318 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_3.json
2025-09-18 14:58:13,585 - INFO - Splitting PDF into individual pages...
2025-09-18 14:58:13,586 - INFO - Splitting PDF eecee744_21_lumper_receipt_9 into 1 pages
2025-09-18 14:58:13,588 - INFO - Split PDF into 1 pages
2025-09-18 14:58:13,588 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:58:13,589 - INFO - Expected pages: [1]
2025-09-18 14:58:13,626 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a5855fd8_21_lumper_receipt_3.png
2025-09-18 14:58:13,984 - INFO - Page 1: Extracted 330 characters, 33 lines from 3ecca325_21_lumper_receipt_8_ce2fdc75_page_001.pdf
2025-09-18 14:58:13,984 - INFO - Successfully processed page 1
2025-09-18 14:58:13,985 - INFO - Combined 1 pages into final text
2025-09-18 14:58:13,985 - INFO - Text validation for 3ecca325_21_lumper_receipt_8: 347 characters, 1 pages
2025-09-18 14:58:13,985 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:58:13,985 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:58:14,976 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e5c1ab46_21_lumper_receipt_4.pdf
2025-09-18 14:58:14,995 - INFO - 

21_lumper_receipt_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:58:14,995 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_4.json
2025-09-18 14:58:15,008 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8e8dc4a4_21_lumper_receipt_7.png
2025-09-18 14:58:15,310 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e5c1ab46_21_lumper_receipt_4.pdf
2025-09-18 14:58:15,326 - INFO - Page 1: Extracted 269 characters, 22 lines from 77eb6a87_21_lumper_receipt_6_793a0ff0_page_001.pdf
2025-09-18 14:58:15,329 - INFO - Successfully processed page 1
2025-09-18 14:58:15,331 - INFO - Combined 1 pages into final text
2025-09-18 14:58:15,332 - INFO - Text validation for 77eb6a87_21_lumper_receipt_6: 286 characters, 1 pages
2025-09-18 14:58:15,337 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:58:15,339 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:58:15,346 - INFO - 

21_lumper_receipt_7.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:58:15,346 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_7.json
2025-09-18 14:58:15,369 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6b961c65_21_lumper_receipt_5.pdf
2025-09-18 14:58:15,648 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8e8dc4a4_21_lumper_receipt_7.png
2025-09-18 14:58:15,661 - INFO - 

21_lumper_receipt_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:58:15,661 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_5.json
2025-09-18 14:58:15,989 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6b961c65_21_lumper_receipt_5.pdf
2025-09-18 14:58:16,038 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3ecca325_21_lumper_receipt_8.pdf
2025-09-18 14:58:16,052 - INFO - 

21_lumper_receipt_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:58:16,052 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_8.json
2025-09-18 14:58:16,358 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3ecca325_21_lumper_receipt_8.pdf
2025-09-18 14:58:17,622 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/77eb6a87_21_lumper_receipt_6.pdf
2025-09-18 14:58:17,633 - INFO - 

21_lumper_receipt_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:58:17,633 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_6.json
2025-09-18 14:58:17,951 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/77eb6a87_21_lumper_receipt_6.pdf
2025-09-18 14:58:18,873 - INFO - Page 1: Extracted 645 characters, 53 lines from eecee744_21_lumper_receipt_9_efe70333_page_001.pdf
2025-09-18 14:58:18,873 - INFO - Successfully processed page 1
2025-09-18 14:58:18,873 - INFO - Combined 1 pages into final text
2025-09-18 14:58:18,873 - INFO - Text validation for eecee744_21_lumper_receipt_9: 662 characters, 1 pages
2025-09-18 14:58:18,873 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:58:18,874 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:58:21,122 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/eecee744_21_lumper_receipt_9.pdf
2025-09-18 14:58:21,149 - INFO - 

21_lumper_receipt_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 14:58:21,149 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_9.json
2025-09-18 14:58:21,446 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/eecee744_21_lumper_receipt_9.pdf
2025-09-18 14:58:21,446 - INFO - 
📊 Processing Summary:
2025-09-18 14:58:21,446 - INFO -    Total files: 11
2025-09-18 14:58:21,446 - INFO -    Successful: 11
2025-09-18 14:58:21,446 - INFO -    Failed: 0
2025-09-18 14:58:21,446 - INFO -    Duration: 20.56 seconds
2025-09-18 14:58:21,446 - INFO -    Output directory: output
2025-09-18 14:58:21,446 - INFO - 
📋 Successfully Processed Files:
2025-09-18 14:58:21,447 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:58:21,447 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:58:21,447 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:58:21,447 - INFO -    📄 21_lumper_receipt_2.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:58:21,447 - INFO -    📄 21_lumper_receipt_3.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:58:21,447 - INFO -    📄 21_lumper_receipt_4.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:58:21,447 - INFO -    📄 21_lumper_receipt_5.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:58:21,447 - INFO -    📄 21_lumper_receipt_6.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:58:21,447 - INFO -    📄 21_lumper_receipt_7.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:58:21,447 - INFO -    📄 21_lumper_receipt_8.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 14:58:21,447 - INFO -    📄 21_lumper_receipt_9.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 14:58:21,447 - INFO - 
============================================================================================================================================
2025-09-18 14:58:21,447 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 14:58:21,447 - INFO - ============================================================================================================================================
2025-09-18 14:58:21,447 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 14:58:21,447 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:58:21,448 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 14:58:21,448 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 14:58:21,448 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 14:58:21,448 - INFO - 
2025-09-18 14:58:21,448 - INFO - 21_lumper_receipt_10.png                           1      invoice              run1_21_lumper_receipt_10.json                    
2025-09-18 14:58:21,448 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 14:58:21,448 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 14:58:21,448 - INFO - 
2025-09-18 14:58:21,448 - INFO - 21_lumper_receipt_11.jpeg                          1      invoice              run1_21_lumper_receipt_11.json                    
2025-09-18 14:58:21,448 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 14:58:21,448 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 14:58:21,448 - INFO - 
2025-09-18 14:58:21,448 - INFO - 21_lumper_receipt_2.jpg                            1      lumper_receipt       run1_21_lumper_receipt_2.json                     
2025-09-18 14:58:21,448 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg
2025-09-18 14:58:21,448 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_2.json
2025-09-18 14:58:21,448 - INFO - 
2025-09-18 14:58:21,448 - INFO - 21_lumper_receipt_3.png                            1      invoice              run1_21_lumper_receipt_3.json                     
2025-09-18 14:58:21,448 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png
2025-09-18 14:58:21,448 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_3.json
2025-09-18 14:58:21,448 - INFO - 
2025-09-18 14:58:21,448 - INFO - 21_lumper_receipt_4.pdf                            1      lumper_receipt       run1_21_lumper_receipt_4.json                     
2025-09-18 14:58:21,448 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf
2025-09-18 14:58:21,448 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_4.json
2025-09-18 14:58:21,448 - INFO - 
2025-09-18 14:58:21,448 - INFO - 21_lumper_receipt_5.pdf                            1      lumper_receipt       run1_21_lumper_receipt_5.json                     
2025-09-18 14:58:21,448 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf
2025-09-18 14:58:21,448 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_5.json
2025-09-18 14:58:21,448 - INFO - 
2025-09-18 14:58:21,448 - INFO - 21_lumper_receipt_6.pdf                            1      lumper_receipt       run1_21_lumper_receipt_6.json                     
2025-09-18 14:58:21,448 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf
2025-09-18 14:58:21,448 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_6.json
2025-09-18 14:58:21,448 - INFO - 
2025-09-18 14:58:21,448 - INFO - 21_lumper_receipt_7.png                            1      invoice              run1_21_lumper_receipt_7.json                     
2025-09-18 14:58:21,449 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png
2025-09-18 14:58:21,449 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_7.json
2025-09-18 14:58:21,449 - INFO - 
2025-09-18 14:58:21,449 - INFO - 21_lumper_receipt_8.pdf                            1      lumper_receipt       run1_21_lumper_receipt_8.json                     
2025-09-18 14:58:21,449 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf
2025-09-18 14:58:21,449 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_8.json
2025-09-18 14:58:21,449 - INFO - 
2025-09-18 14:58:21,449 - INFO - 21_lumper_receipt_9.pdf                            1      invoice              run1_21_lumper_receipt_9.json                     
2025-09-18 14:58:21,449 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf
2025-09-18 14:58:21,449 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_9.json
2025-09-18 14:58:21,449 - INFO - 
2025-09-18 14:58:21,449 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 14:58:21,449 - INFO - Total entries: 11
2025-09-18 14:58:21,449 - INFO - ============================================================================================================================================
2025-09-18 14:58:21,449 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 14:58:21,449 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:58:21,449 - INFO -   1. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 14:58:21,449 - INFO -   2. 21_lumper_receipt_10.png            Page 1   → invoice         | run1_21_lumper_receipt_10.json
2025-09-18 14:58:21,449 - INFO -   3. 21_lumper_receipt_11.jpeg           Page 1   → invoice         | run1_21_lumper_receipt_11.json
2025-09-18 14:58:21,449 - INFO -   4. 21_lumper_receipt_2.jpg             Page 1   → lumper_receipt  | run1_21_lumper_receipt_2.json
2025-09-18 14:58:21,449 - INFO -   5. 21_lumper_receipt_3.png             Page 1   → invoice         | run1_21_lumper_receipt_3.json
2025-09-18 14:58:21,449 - INFO -   6. 21_lumper_receipt_4.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_4.json
2025-09-18 14:58:21,449 - INFO -   7. 21_lumper_receipt_5.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_5.json
2025-09-18 14:58:21,449 - INFO -   8. 21_lumper_receipt_6.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_6.json
2025-09-18 14:58:21,449 - INFO -   9. 21_lumper_receipt_7.png             Page 1   → invoice         | run1_21_lumper_receipt_7.json
2025-09-18 14:58:21,449 - INFO -  10. 21_lumper_receipt_8.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_8.json
2025-09-18 14:58:21,449 - INFO -  11. 21_lumper_receipt_9.pdf             Page 1   → invoice         | run1_21_lumper_receipt_9.json
2025-09-18 14:58:21,449 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 14:58:21,450 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 20.564458, 'processed_files': [{'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '21_lumper_receipt_2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg'}, {'filename': '21_lumper_receipt_3.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png'}, {'filename': '21_lumper_receipt_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf'}, {'filename': '21_lumper_receipt_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf'}, {'filename': '21_lumper_receipt_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf'}, {'filename': '21_lumper_receipt_7.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png'}, {'filename': '21_lumper_receipt_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf'}, {'filename': '21_lumper_receipt_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf'}]}
