2025-09-18 14:59:48,416 - INFO - Logging initialized. Log file: logs/test_classification_20250918_145948.log
2025-09-18 14:59:48,417 - INFO - 📁 Found 11 files to process
2025-09-18 14:59:48,417 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 14:59:48,417 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 14:59:48,417 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 14:59:48,417 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 14:59:48,417 - INFO - ⬆️ [14:59:48] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 14:59:50,806 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/e7d23a17_21_lumper_receipt_1.jpeg
2025-09-18 14:59:50,807 - INFO - 🔍 [14:59:50] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 14:59:50,807 - INFO - ⬆️ [14:59:50] Uploading: 21_lumper_receipt_10.png
2025-09-18 14:59:50,810 - INFO - Initializing TextractProcessor...
2025-09-18 14:59:50,832 - INFO - Initializing BedrockProcessor...
2025-09-18 14:59:50,838 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e7d23a17_21_lumper_receipt_1.jpeg
2025-09-18 14:59:50,838 - INFO - Processing image from S3...
2025-09-18 14:59:51,607 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/6a689324_21_lumper_receipt_10.png
2025-09-18 14:59:51,608 - INFO - 🔍 [14:59:51] Starting classification: 21_lumper_receipt_10.png
2025-09-18 14:59:51,608 - INFO - ⬆️ [14:59:51] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 14:59:51,609 - INFO - Initializing TextractProcessor...
2025-09-18 14:59:51,631 - INFO - Initializing BedrockProcessor...
2025-09-18 14:59:51,635 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6a689324_21_lumper_receipt_10.png
2025-09-18 14:59:51,635 - INFO - Processing image from S3...
2025-09-18 14:59:52,889 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/99287952_21_lumper_receipt_11.jpeg
2025-09-18 14:59:52,889 - INFO - 🔍 [14:59:52] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 14:59:52,891 - INFO - ⬆️ [14:59:52] Uploading: 21_lumper_receipt_2.jpg
2025-09-18 14:59:52,893 - INFO - Initializing TextractProcessor...
2025-09-18 14:59:52,912 - INFO - Initializing BedrockProcessor...
2025-09-18 14:59:52,914 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/99287952_21_lumper_receipt_11.jpeg
2025-09-18 14:59:52,914 - INFO - Processing image from S3...
2025-09-18 14:59:53,683 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg -> s3://document-extraction-logistically/temp/0a9f83af_21_lumper_receipt_2.jpg
2025-09-18 14:59:53,683 - INFO - 🔍 [14:59:53] Starting classification: 21_lumper_receipt_2.jpg
2025-09-18 14:59:53,684 - INFO - ⬆️ [14:59:53] Uploading: 21_lumper_receipt_3.png
2025-09-18 14:59:53,686 - INFO - Initializing TextractProcessor...
2025-09-18 14:59:53,705 - INFO - Initializing BedrockProcessor...
2025-09-18 14:59:53,710 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0a9f83af_21_lumper_receipt_2.jpg
2025-09-18 14:59:53,711 - INFO - Processing image from S3...
2025-09-18 14:59:53,817 - INFO - S3 Image temp/e7d23a17_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 14:59:53,817 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:59:53,817 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:59:54,708 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png -> s3://document-extraction-logistically/temp/297e2856_21_lumper_receipt_3.png
2025-09-18 14:59:54,709 - INFO - 🔍 [14:59:54] Starting classification: 21_lumper_receipt_3.png
2025-09-18 14:59:54,711 - INFO - ⬆️ [14:59:54] Uploading: 21_lumper_receipt_4.pdf
2025-09-18 14:59:54,711 - INFO - Initializing TextractProcessor...
2025-09-18 14:59:54,737 - INFO - Initializing BedrockProcessor...
2025-09-18 14:59:54,742 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/297e2856_21_lumper_receipt_3.png
2025-09-18 14:59:54,742 - INFO - Processing image from S3...
2025-09-18 14:59:54,759 - INFO - S3 Image temp/6a689324_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 14:59:54,759 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:59:54,759 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:59:55,337 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf -> s3://document-extraction-logistically/temp/b8991ee4_21_lumper_receipt_4.pdf
2025-09-18 14:59:55,337 - INFO - 🔍 [14:59:55] Starting classification: 21_lumper_receipt_4.pdf
2025-09-18 14:59:55,338 - INFO - ⬆️ [14:59:55] Uploading: 21_lumper_receipt_5.pdf
2025-09-18 14:59:55,340 - INFO - Initializing TextractProcessor...
2025-09-18 14:59:55,364 - INFO - Initializing BedrockProcessor...
2025-09-18 14:59:55,367 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b8991ee4_21_lumper_receipt_4.pdf
2025-09-18 14:59:55,367 - INFO - Processing PDF from S3...
2025-09-18 14:59:55,368 - INFO - Downloading PDF from S3 to /tmp/tmprydrrd40/b8991ee4_21_lumper_receipt_4.pdf
2025-09-18 14:59:55,906 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e7d23a17_21_lumper_receipt_1.jpeg
2025-09-18 14:59:55,948 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf -> s3://document-extraction-logistically/temp/a454df68_21_lumper_receipt_5.pdf
2025-09-18 14:59:55,949 - INFO - 🔍 [14:59:55] Starting classification: 21_lumper_receipt_5.pdf
2025-09-18 14:59:55,949 - INFO - ⬆️ [14:59:55] Uploading: 21_lumper_receipt_6.pdf
2025-09-18 14:59:55,951 - INFO - Initializing TextractProcessor...
2025-09-18 14:59:55,971 - INFO - Initializing BedrockProcessor...
2025-09-18 14:59:55,976 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a454df68_21_lumper_receipt_5.pdf
2025-09-18 14:59:55,976 - INFO - Processing PDF from S3...
2025-09-18 14:59:55,976 - INFO - Downloading PDF from S3 to /tmp/tmpja9xso1p/a454df68_21_lumper_receipt_5.pdf
2025-09-18 14:59:56,612 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf -> s3://document-extraction-logistically/temp/6121967c_21_lumper_receipt_6.pdf
2025-09-18 14:59:56,612 - INFO - 🔍 [14:59:56] Starting classification: 21_lumper_receipt_6.pdf
2025-09-18 14:59:56,613 - INFO - ⬆️ [14:59:56] Uploading: 21_lumper_receipt_7.png
2025-09-18 14:59:56,613 - INFO - Initializing TextractProcessor...
2025-09-18 14:59:56,659 - INFO - Initializing BedrockProcessor...
2025-09-18 14:59:56,667 - INFO - S3 Image temp/99287952_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 14:59:56,670 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6121967c_21_lumper_receipt_6.pdf
2025-09-18 14:59:56,671 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:59:56,671 - INFO - Splitting PDF into individual pages...
2025-09-18 14:59:56,671 - INFO - Processing PDF from S3...
2025-09-18 14:59:56,671 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:59:56,673 - INFO - Downloading PDF from S3 to /tmp/tmpr5ekl1l2/6121967c_21_lumper_receipt_6.pdf
2025-09-18 14:59:56,674 - INFO - Splitting PDF b8991ee4_21_lumper_receipt_4 into 1 pages
2025-09-18 14:59:56,680 - INFO - Split PDF into 1 pages
2025-09-18 14:59:56,680 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:59:56,680 - INFO - Expected pages: [1]
2025-09-18 14:59:56,983 - INFO - S3 Image temp/0a9f83af_21_lumper_receipt_2.jpg: Extracted 184 characters, 12 lines
2025-09-18 14:59:56,984 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:59:56,984 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:59:57,273 - INFO - Splitting PDF into individual pages...
2025-09-18 14:59:57,274 - INFO - Splitting PDF a454df68_21_lumper_receipt_5 into 1 pages
2025-09-18 14:59:57,276 - INFO - Split PDF into 1 pages
2025-09-18 14:59:57,276 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:59:57,277 - INFO - Expected pages: [1]
2025-09-18 14:59:57,284 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png -> s3://document-extraction-logistically/temp/0d54aa1e_21_lumper_receipt_7.png
2025-09-18 14:59:57,285 - INFO - 🔍 [14:59:57] Starting classification: 21_lumper_receipt_7.png
2025-09-18 14:59:57,286 - INFO - ⬆️ [14:59:57] Uploading: 21_lumper_receipt_8.pdf
2025-09-18 14:59:57,286 - INFO - Initializing TextractProcessor...
2025-09-18 14:59:57,306 - INFO - Initializing BedrockProcessor...
2025-09-18 14:59:57,309 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0d54aa1e_21_lumper_receipt_7.png
2025-09-18 14:59:57,309 - INFO - Processing image from S3...
2025-09-18 14:59:57,671 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6a689324_21_lumper_receipt_10.png
2025-09-18 14:59:57,897 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf -> s3://document-extraction-logistically/temp/b31b976e_21_lumper_receipt_8.pdf
2025-09-18 14:59:57,897 - INFO - 🔍 [14:59:57] Starting classification: 21_lumper_receipt_8.pdf
2025-09-18 14:59:57,898 - INFO - ⬆️ [14:59:57] Uploading: 21_lumper_receipt_9.pdf
2025-09-18 14:59:57,898 - INFO - Initializing TextractProcessor...
2025-09-18 14:59:57,917 - INFO - Initializing BedrockProcessor...
2025-09-18 14:59:57,922 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b31b976e_21_lumper_receipt_8.pdf
2025-09-18 14:59:57,922 - INFO - Processing PDF from S3...
2025-09-18 14:59:57,922 - INFO - Downloading PDF from S3 to /tmp/tmpdi58twe7/b31b976e_21_lumper_receipt_8.pdf
2025-09-18 14:59:58,054 - INFO - S3 Image temp/297e2856_21_lumper_receipt_3.png: Extracted 663 characters, 38 lines
2025-09-18 14:59:58,055 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:59:58,055 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:59:58,472 - INFO - Splitting PDF into individual pages...
2025-09-18 14:59:58,473 - INFO - Splitting PDF 6121967c_21_lumper_receipt_6 into 1 pages
2025-09-18 14:59:58,480 - INFO - Split PDF into 1 pages
2025-09-18 14:59:58,480 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:59:58,480 - INFO - Expected pages: [1]
2025-09-18 14:59:58,517 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0a9f83af_21_lumper_receipt_2.jpg
2025-09-18 14:59:58,568 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf -> s3://document-extraction-logistically/temp/fca6c989_21_lumper_receipt_9.pdf
2025-09-18 14:59:58,568 - INFO - 🔍 [14:59:58] Starting classification: 21_lumper_receipt_9.pdf
2025-09-18 14:59:58,570 - INFO - Initializing TextractProcessor...
2025-09-18 14:59:58,593 - INFO - Initializing BedrockProcessor...
2025-09-18 14:59:58,600 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fca6c989_21_lumper_receipt_9.pdf
2025-09-18 14:59:58,601 - INFO - Processing PDF from S3...
2025-09-18 14:59:58,604 - INFO - Downloading PDF from S3 to /tmp/tmpjj1llnfa/fca6c989_21_lumper_receipt_9.pdf
2025-09-18 14:59:58,614 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:59:58,614 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 14:59:58,926 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e7d23a17_21_lumper_receipt_1.jpeg
2025-09-18 14:59:58,945 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:59:58,946 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 14:59:59,125 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/99287952_21_lumper_receipt_11.jpeg
2025-09-18 14:59:59,160 - INFO - Splitting PDF into individual pages...
2025-09-18 14:59:59,161 - INFO - Splitting PDF b31b976e_21_lumper_receipt_8 into 1 pages
2025-09-18 14:59:59,162 - INFO - Split PDF into 1 pages
2025-09-18 14:59:59,162 - INFO - Processing pages with Textract in parallel...
2025-09-18 14:59:59,163 - INFO - Expected pages: [1]
2025-09-18 14:59:59,254 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6a689324_21_lumper_receipt_10.png
2025-09-18 14:59:59,260 - INFO - 

21_lumper_receipt_2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:59:59,261 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_2.json
2025-09-18 14:59:59,507 - INFO - Page 1: Extracted 422 characters, 38 lines from b8991ee4_21_lumper_receipt_4_f33a4372_page_001.pdf
2025-09-18 14:59:59,508 - INFO - Successfully processed page 1
2025-09-18 14:59:59,508 - INFO - Combined 1 pages into final text
2025-09-18 14:59:59,508 - INFO - Text validation for b8991ee4_21_lumper_receipt_4: 439 characters, 1 pages
2025-09-18 14:59:59,509 - INFO - Analyzing document types with Bedrock...
2025-09-18 14:59:59,509 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 14:59:59,569 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0a9f83af_21_lumper_receipt_2.jpg
2025-09-18 14:59:59,579 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 14:59:59,580 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 14:59:59,897 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/99287952_21_lumper_receipt_11.jpeg
2025-09-18 15:00:00,461 - INFO - Page 1: Extracted 422 characters, 38 lines from a454df68_21_lumper_receipt_5_789ccece_page_001.pdf
2025-09-18 15:00:00,462 - INFO - Successfully processed page 1
2025-09-18 15:00:00,462 - INFO - Combined 1 pages into final text
2025-09-18 15:00:00,463 - INFO - Text validation for a454df68_21_lumper_receipt_5: 439 characters, 1 pages
2025-09-18 15:00:00,463 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:00,463 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:00,569 - INFO - S3 Image temp/0d54aa1e_21_lumper_receipt_7.png: Extracted 800 characters, 70 lines
2025-09-18 15:00:00,569 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:00,569 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:00,654 - INFO - Splitting PDF into individual pages...
2025-09-18 15:00:00,655 - INFO - Splitting PDF fca6c989_21_lumper_receipt_9 into 1 pages
2025-09-18 15:00:00,656 - INFO - Split PDF into 1 pages
2025-09-18 15:00:00,656 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:00:00,656 - INFO - Expected pages: [1]
2025-09-18 15:00:01,438 - INFO - Page 1: Extracted 330 characters, 33 lines from b31b976e_21_lumper_receipt_8_706b9b1a_page_001.pdf
2025-09-18 15:00:01,438 - INFO - Successfully processed page 1
2025-09-18 15:00:01,438 - INFO - Combined 1 pages into final text
2025-09-18 15:00:01,438 - INFO - Text validation for b31b976e_21_lumper_receipt_8: 347 characters, 1 pages
2025-09-18 15:00:01,439 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:01,439 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:01,579 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b8991ee4_21_lumper_receipt_4.pdf
2025-09-18 15:00:01,591 - INFO - 

21_lumper_receipt_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:01,592 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_4.json
2025-09-18 15:00:01,898 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b8991ee4_21_lumper_receipt_4.pdf
2025-09-18 15:00:01,973 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/297e2856_21_lumper_receipt_3.png
2025-09-18 15:00:01,992 - INFO - 

21_lumper_receipt_3.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:01,992 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_3.json
2025-09-18 15:00:02,310 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/297e2856_21_lumper_receipt_3.png
2025-09-18 15:00:02,741 - INFO - Page 1: Extracted 269 characters, 22 lines from 6121967c_21_lumper_receipt_6_a8767269_page_001.pdf
2025-09-18 15:00:02,742 - INFO - Successfully processed page 1
2025-09-18 15:00:02,742 - INFO - Combined 1 pages into final text
2025-09-18 15:00:02,742 - INFO - Text validation for 6121967c_21_lumper_receipt_6: 286 characters, 1 pages
2025-09-18 15:00:02,743 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:02,743 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:02,822 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a454df68_21_lumper_receipt_5.pdf
2025-09-18 15:00:02,829 - INFO - 

21_lumper_receipt_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:02,829 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_5.json
2025-09-18 15:00:03,146 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a454df68_21_lumper_receipt_5.pdf
2025-09-18 15:00:03,497 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0d54aa1e_21_lumper_receipt_7.png
2025-09-18 15:00:03,499 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b31b976e_21_lumper_receipt_8.pdf
2025-09-18 15:00:03,523 - INFO - 

21_lumper_receipt_7.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:03,523 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_7.json
2025-09-18 15:00:03,840 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0d54aa1e_21_lumper_receipt_7.png
2025-09-18 15:00:03,853 - INFO - 

21_lumper_receipt_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:03,853 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_8.json
2025-09-18 15:00:04,170 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b31b976e_21_lumper_receipt_8.pdf
2025-09-18 15:00:05,095 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6121967c_21_lumper_receipt_6.pdf
2025-09-18 15:00:05,105 - INFO - 

21_lumper_receipt_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:05,105 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_6.json
2025-09-18 15:00:05,421 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6121967c_21_lumper_receipt_6.pdf
2025-09-18 15:00:06,772 - INFO - Page 1: Extracted 645 characters, 53 lines from fca6c989_21_lumper_receipt_9_7348bba9_page_001.pdf
2025-09-18 15:00:06,772 - INFO - Successfully processed page 1
2025-09-18 15:00:06,773 - INFO - Combined 1 pages into final text
2025-09-18 15:00:06,773 - INFO - Text validation for fca6c989_21_lumper_receipt_9: 662 characters, 1 pages
2025-09-18 15:00:06,773 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:06,773 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:09,421 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fca6c989_21_lumper_receipt_9.pdf
2025-09-18 15:00:09,441 - INFO - 

21_lumper_receipt_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:09,442 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_9.json
2025-09-18 15:00:09,829 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fca6c989_21_lumper_receipt_9.pdf
2025-09-18 15:00:09,829 - INFO - 
📊 Processing Summary:
2025-09-18 15:00:09,830 - INFO -    Total files: 11
2025-09-18 15:00:09,830 - INFO -    Successful: 11
2025-09-18 15:00:09,830 - INFO -    Failed: 0
2025-09-18 15:00:09,830 - INFO -    Duration: 21.41 seconds
2025-09-18 15:00:09,830 - INFO -    Output directory: output
2025-09-18 15:00:09,830 - INFO - 
📋 Successfully Processed Files:
2025-09-18 15:00:09,830 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:09,830 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:09,830 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:09,830 - INFO -    📄 21_lumper_receipt_2.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:09,830 - INFO -    📄 21_lumper_receipt_3.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:09,830 - INFO -    📄 21_lumper_receipt_4.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:09,830 - INFO -    📄 21_lumper_receipt_5.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:09,831 - INFO -    📄 21_lumper_receipt_6.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:09,831 - INFO -    📄 21_lumper_receipt_7.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:09,831 - INFO -    📄 21_lumper_receipt_8.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:09,831 - INFO -    📄 21_lumper_receipt_9.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:09,831 - INFO - 
============================================================================================================================================
2025-09-18 15:00:09,831 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 15:00:09,831 - INFO - ============================================================================================================================================
2025-09-18 15:00:09,832 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 15:00:09,832 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:00:09,832 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 15:00:09,832 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 15:00:09,832 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 15:00:09,832 - INFO - 
2025-09-18 15:00:09,832 - INFO - 21_lumper_receipt_10.png                           1      lumper_receipt       run1_21_lumper_receipt_10.json                    
2025-09-18 15:00:09,832 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 15:00:09,832 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 15:00:09,832 - INFO - 
2025-09-18 15:00:09,832 - INFO - 21_lumper_receipt_11.jpeg                          1      lumper_receipt       run1_21_lumper_receipt_11.json                    
2025-09-18 15:00:09,832 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 15:00:09,832 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 15:00:09,832 - INFO - 
2025-09-18 15:00:09,832 - INFO - 21_lumper_receipt_2.jpg                            1      lumper_receipt       run1_21_lumper_receipt_2.json                     
2025-09-18 15:00:09,832 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg
2025-09-18 15:00:09,832 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_2.json
2025-09-18 15:00:09,832 - INFO - 
2025-09-18 15:00:09,833 - INFO - 21_lumper_receipt_3.png                            1      lumper_receipt       run1_21_lumper_receipt_3.json                     
2025-09-18 15:00:09,833 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png
2025-09-18 15:00:09,833 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_3.json
2025-09-18 15:00:09,833 - INFO - 
2025-09-18 15:00:09,833 - INFO - 21_lumper_receipt_4.pdf                            1      lumper_receipt       run1_21_lumper_receipt_4.json                     
2025-09-18 15:00:09,833 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf
2025-09-18 15:00:09,833 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_4.json
2025-09-18 15:00:09,833 - INFO - 
2025-09-18 15:00:09,833 - INFO - 21_lumper_receipt_5.pdf                            1      lumper_receipt       run1_21_lumper_receipt_5.json                     
2025-09-18 15:00:09,833 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf
2025-09-18 15:00:09,833 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_5.json
2025-09-18 15:00:09,833 - INFO - 
2025-09-18 15:00:09,833 - INFO - 21_lumper_receipt_6.pdf                            1      lumper_receipt       run1_21_lumper_receipt_6.json                     
2025-09-18 15:00:09,833 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf
2025-09-18 15:00:09,833 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_6.json
2025-09-18 15:00:09,833 - INFO - 
2025-09-18 15:00:09,833 - INFO - 21_lumper_receipt_7.png                            1      lumper_receipt       run1_21_lumper_receipt_7.json                     
2025-09-18 15:00:09,833 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png
2025-09-18 15:00:09,833 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_7.json
2025-09-18 15:00:09,833 - INFO - 
2025-09-18 15:00:09,833 - INFO - 21_lumper_receipt_8.pdf                            1      lumper_receipt       run1_21_lumper_receipt_8.json                     
2025-09-18 15:00:09,834 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf
2025-09-18 15:00:09,834 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_8.json
2025-09-18 15:00:09,834 - INFO - 
2025-09-18 15:00:09,834 - INFO - 21_lumper_receipt_9.pdf                            1      lumper_receipt       run1_21_lumper_receipt_9.json                     
2025-09-18 15:00:09,834 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf
2025-09-18 15:00:09,834 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_9.json
2025-09-18 15:00:09,834 - INFO - 
2025-09-18 15:00:09,834 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:00:09,834 - INFO - Total entries: 11
2025-09-18 15:00:09,834 - INFO - ============================================================================================================================================
2025-09-18 15:00:09,834 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 15:00:09,834 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:00:09,834 - INFO -   1. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 15:00:09,834 - INFO -   2. 21_lumper_receipt_10.png            Page 1   → lumper_receipt  | run1_21_lumper_receipt_10.json
2025-09-18 15:00:09,834 - INFO -   3. 21_lumper_receipt_11.jpeg           Page 1   → lumper_receipt  | run1_21_lumper_receipt_11.json
2025-09-18 15:00:09,834 - INFO -   4. 21_lumper_receipt_2.jpg             Page 1   → lumper_receipt  | run1_21_lumper_receipt_2.json
2025-09-18 15:00:09,834 - INFO -   5. 21_lumper_receipt_3.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_3.json
2025-09-18 15:00:09,834 - INFO -   6. 21_lumper_receipt_4.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_4.json
2025-09-18 15:00:09,834 - INFO -   7. 21_lumper_receipt_5.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_5.json
2025-09-18 15:00:09,835 - INFO -   8. 21_lumper_receipt_6.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_6.json
2025-09-18 15:00:09,835 - INFO -   9. 21_lumper_receipt_7.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_7.json
2025-09-18 15:00:09,835 - INFO -  10. 21_lumper_receipt_8.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_8.json
2025-09-18 15:00:09,835 - INFO -  11. 21_lumper_receipt_9.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_9.json
2025-09-18 15:00:09,835 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:00:09,835 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 21.412317, 'processed_files': [{'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '21_lumper_receipt_2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg'}, {'filename': '21_lumper_receipt_3.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png'}, {'filename': '21_lumper_receipt_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf'}, {'filename': '21_lumper_receipt_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf'}, {'filename': '21_lumper_receipt_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf'}, {'filename': '21_lumper_receipt_7.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png'}, {'filename': '21_lumper_receipt_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf'}, {'filename': '21_lumper_receipt_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf'}]}
