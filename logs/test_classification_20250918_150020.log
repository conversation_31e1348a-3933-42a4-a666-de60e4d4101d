2025-09-18 15:00:20,057 - INFO - Logging initialized. Log file: logs/test_classification_20250918_150020.log
2025-09-18 15:00:20,057 - INFO - 📁 Found 11 files to process
2025-09-18 15:00:20,057 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 15:00:20,057 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 15:00:20,058 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 15:00:20,058 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 15:00:20,058 - INFO - ⬆️ [15:00:20] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 15:00:22,424 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/b75cad24_21_lumper_receipt_1.jpeg
2025-09-18 15:00:22,425 - INFO - 🔍 [15:00:22] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 15:00:22,426 - INFO - Initializing TextractProcessor...
2025-09-18 15:00:22,426 - INFO - ⬆️ [15:00:22] Uploading: 21_lumper_receipt_10.png
2025-09-18 15:00:22,446 - INFO - Initializing BedrockProcessor...
2025-09-18 15:00:22,451 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b75cad24_21_lumper_receipt_1.jpeg
2025-09-18 15:00:22,451 - INFO - Processing image from S3...
2025-09-18 15:00:23,357 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/89ee08af_21_lumper_receipt_10.png
2025-09-18 15:00:23,357 - INFO - 🔍 [15:00:23] Starting classification: 21_lumper_receipt_10.png
2025-09-18 15:00:23,358 - INFO - ⬆️ [15:00:23] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 15:00:23,361 - INFO - Initializing TextractProcessor...
2025-09-18 15:00:23,376 - INFO - Initializing BedrockProcessor...
2025-09-18 15:00:23,380 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/89ee08af_21_lumper_receipt_10.png
2025-09-18 15:00:23,380 - INFO - Processing image from S3...
2025-09-18 15:00:24,578 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/b5a5a84b_21_lumper_receipt_11.jpeg
2025-09-18 15:00:24,578 - INFO - 🔍 [15:00:24] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 15:00:24,579 - INFO - ⬆️ [15:00:24] Uploading: 21_lumper_receipt_2.jpg
2025-09-18 15:00:24,581 - INFO - Initializing TextractProcessor...
2025-09-18 15:00:24,594 - INFO - Initializing BedrockProcessor...
2025-09-18 15:00:24,597 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b5a5a84b_21_lumper_receipt_11.jpeg
2025-09-18 15:00:24,597 - INFO - Processing image from S3...
2025-09-18 15:00:25,392 - INFO - S3 Image temp/b75cad24_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 15:00:25,392 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:25,392 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:25,518 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg -> s3://document-extraction-logistically/temp/dedf9077_21_lumper_receipt_2.jpg
2025-09-18 15:00:25,519 - INFO - 🔍 [15:00:25] Starting classification: 21_lumper_receipt_2.jpg
2025-09-18 15:00:25,520 - INFO - ⬆️ [15:00:25] Uploading: 21_lumper_receipt_3.png
2025-09-18 15:00:25,526 - INFO - Initializing TextractProcessor...
2025-09-18 15:00:25,543 - INFO - Initializing BedrockProcessor...
2025-09-18 15:00:25,549 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dedf9077_21_lumper_receipt_2.jpg
2025-09-18 15:00:25,550 - INFO - Processing image from S3...
2025-09-18 15:00:26,350 - INFO - S3 Image temp/89ee08af_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 15:00:26,350 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:26,350 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:26,486 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png -> s3://document-extraction-logistically/temp/9b841de7_21_lumper_receipt_3.png
2025-09-18 15:00:26,486 - INFO - 🔍 [15:00:26] Starting classification: 21_lumper_receipt_3.png
2025-09-18 15:00:26,487 - INFO - ⬆️ [15:00:26] Uploading: 21_lumper_receipt_4.pdf
2025-09-18 15:00:26,492 - INFO - Initializing TextractProcessor...
2025-09-18 15:00:26,506 - INFO - Initializing BedrockProcessor...
2025-09-18 15:00:26,509 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9b841de7_21_lumper_receipt_3.png
2025-09-18 15:00:26,509 - INFO - Processing image from S3...
2025-09-18 15:00:27,055 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf -> s3://document-extraction-logistically/temp/b8024f1f_21_lumper_receipt_4.pdf
2025-09-18 15:00:27,055 - INFO - 🔍 [15:00:27] Starting classification: 21_lumper_receipt_4.pdf
2025-09-18 15:00:27,056 - INFO - ⬆️ [15:00:27] Uploading: 21_lumper_receipt_5.pdf
2025-09-18 15:00:27,057 - INFO - Initializing TextractProcessor...
2025-09-18 15:00:27,073 - INFO - Initializing BedrockProcessor...
2025-09-18 15:00:27,079 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b8024f1f_21_lumper_receipt_4.pdf
2025-09-18 15:00:27,079 - INFO - Processing PDF from S3...
2025-09-18 15:00:27,080 - INFO - Downloading PDF from S3 to /tmp/tmpd2we6ca4/b8024f1f_21_lumper_receipt_4.pdf
2025-09-18 15:00:27,633 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf -> s3://document-extraction-logistically/temp/920e53f8_21_lumper_receipt_5.pdf
2025-09-18 15:00:27,633 - INFO - 🔍 [15:00:27] Starting classification: 21_lumper_receipt_5.pdf
2025-09-18 15:00:27,633 - INFO - ⬆️ [15:00:27] Uploading: 21_lumper_receipt_6.pdf
2025-09-18 15:00:27,638 - INFO - Initializing TextractProcessor...
2025-09-18 15:00:27,649 - INFO - Initializing BedrockProcessor...
2025-09-18 15:00:27,655 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/920e53f8_21_lumper_receipt_5.pdf
2025-09-18 15:00:27,655 - INFO - Processing PDF from S3...
2025-09-18 15:00:27,656 - INFO - Downloading PDF from S3 to /tmp/tmp31mw643c/920e53f8_21_lumper_receipt_5.pdf
2025-09-18 15:00:27,818 - INFO - S3 Image temp/b5a5a84b_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 15:00:27,818 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:27,818 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:28,219 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf -> s3://document-extraction-logistically/temp/c8d32188_21_lumper_receipt_6.pdf
2025-09-18 15:00:28,220 - INFO - 🔍 [15:00:28] Starting classification: 21_lumper_receipt_6.pdf
2025-09-18 15:00:28,221 - INFO - ⬆️ [15:00:28] Uploading: 21_lumper_receipt_7.png
2025-09-18 15:00:28,221 - INFO - Initializing TextractProcessor...
2025-09-18 15:00:28,236 - INFO - Initializing BedrockProcessor...
2025-09-18 15:00:28,241 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c8d32188_21_lumper_receipt_6.pdf
2025-09-18 15:00:28,241 - INFO - Processing PDF from S3...
2025-09-18 15:00:28,241 - INFO - Downloading PDF from S3 to /tmp/tmppefb8bre/c8d32188_21_lumper_receipt_6.pdf
2025-09-18 15:00:28,244 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b75cad24_21_lumper_receipt_1.jpeg
2025-09-18 15:00:28,367 - INFO - Splitting PDF into individual pages...
2025-09-18 15:00:28,368 - INFO - Splitting PDF b8024f1f_21_lumper_receipt_4 into 1 pages
2025-09-18 15:00:28,370 - INFO - Split PDF into 1 pages
2025-09-18 15:00:28,370 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:00:28,371 - INFO - Expected pages: [1]
2025-09-18 15:00:28,833 - INFO - S3 Image temp/dedf9077_21_lumper_receipt_2.jpg: Extracted 184 characters, 12 lines
2025-09-18 15:00:28,833 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:28,833 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:28,856 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png -> s3://document-extraction-logistically/temp/51c1a112_21_lumper_receipt_7.png
2025-09-18 15:00:28,857 - INFO - 🔍 [15:00:28] Starting classification: 21_lumper_receipt_7.png
2025-09-18 15:00:28,857 - INFO - ⬆️ [15:00:28] Uploading: 21_lumper_receipt_8.pdf
2025-09-18 15:00:28,858 - INFO - Initializing TextractProcessor...
2025-09-18 15:00:28,880 - INFO - Initializing BedrockProcessor...
2025-09-18 15:00:28,883 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/51c1a112_21_lumper_receipt_7.png
2025-09-18 15:00:28,883 - INFO - Processing image from S3...
2025-09-18 15:00:28,999 - INFO - Splitting PDF into individual pages...
2025-09-18 15:00:29,000 - INFO - Splitting PDF 920e53f8_21_lumper_receipt_5 into 1 pages
2025-09-18 15:00:29,002 - INFO - Split PDF into 1 pages
2025-09-18 15:00:29,002 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:00:29,002 - INFO - Expected pages: [1]
2025-09-18 15:00:29,432 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf -> s3://document-extraction-logistically/temp/0e9ad948_21_lumper_receipt_8.pdf
2025-09-18 15:00:29,432 - INFO - 🔍 [15:00:29] Starting classification: 21_lumper_receipt_8.pdf
2025-09-18 15:00:29,433 - INFO - ⬆️ [15:00:29] Uploading: 21_lumper_receipt_9.pdf
2025-09-18 15:00:29,435 - INFO - Initializing TextractProcessor...
2025-09-18 15:00:29,449 - INFO - Initializing BedrockProcessor...
2025-09-18 15:00:29,451 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0e9ad948_21_lumper_receipt_8.pdf
2025-09-18 15:00:29,451 - INFO - Processing PDF from S3...
2025-09-18 15:00:29,452 - INFO - Downloading PDF from S3 to /tmp/tmpejdmtrav/0e9ad948_21_lumper_receipt_8.pdf
2025-09-18 15:00:29,886 - INFO - S3 Image temp/9b841de7_21_lumper_receipt_3.png: Extracted 663 characters, 38 lines
2025-09-18 15:00:29,886 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:29,886 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:30,040 - INFO - Splitting PDF into individual pages...
2025-09-18 15:00:30,041 - INFO - Splitting PDF c8d32188_21_lumper_receipt_6 into 1 pages
2025-09-18 15:00:30,046 - INFO - Split PDF into 1 pages
2025-09-18 15:00:30,046 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:00:30,046 - INFO - Expected pages: [1]
2025-09-18 15:00:30,082 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf -> s3://document-extraction-logistically/temp/95566a44_21_lumper_receipt_9.pdf
2025-09-18 15:00:30,083 - INFO - 🔍 [15:00:30] Starting classification: 21_lumper_receipt_9.pdf
2025-09-18 15:00:30,084 - INFO - Initializing TextractProcessor...
2025-09-18 15:00:30,107 - INFO - Initializing BedrockProcessor...
2025-09-18 15:00:30,116 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/95566a44_21_lumper_receipt_9.pdf
2025-09-18 15:00:30,118 - INFO - Processing PDF from S3...
2025-09-18 15:00:30,121 - INFO - Downloading PDF from S3 to /tmp/tmpmf39y86j/95566a44_21_lumper_receipt_9.pdf
2025-09-18 15:00:30,133 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:30,134 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 15:00:30,425 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b75cad24_21_lumper_receipt_1.jpeg
2025-09-18 15:00:30,446 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dedf9077_21_lumper_receipt_2.jpg
2025-09-18 15:00:30,452 - INFO - 

21_lumper_receipt_2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:30,452 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_2.json
2025-09-18 15:00:30,691 - INFO - Splitting PDF into individual pages...
2025-09-18 15:00:30,693 - INFO - Splitting PDF 0e9ad948_21_lumper_receipt_8 into 1 pages
2025-09-18 15:00:30,694 - INFO - Split PDF into 1 pages
2025-09-18 15:00:30,694 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:00:30,694 - INFO - Expected pages: [1]
2025-09-18 15:00:30,746 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dedf9077_21_lumper_receipt_2.jpg
2025-09-18 15:00:31,005 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b5a5a84b_21_lumper_receipt_11.jpeg
2025-09-18 15:00:31,027 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:31,027 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 15:00:31,224 - INFO - Page 1: Extracted 422 characters, 38 lines from b8024f1f_21_lumper_receipt_4_e78535dd_page_001.pdf
2025-09-18 15:00:31,225 - INFO - Successfully processed page 1
2025-09-18 15:00:31,225 - INFO - Combined 1 pages into final text
2025-09-18 15:00:31,225 - INFO - Text validation for b8024f1f_21_lumper_receipt_4: 439 characters, 1 pages
2025-09-18 15:00:31,225 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:31,225 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:31,321 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b5a5a84b_21_lumper_receipt_11.jpeg
2025-09-18 15:00:31,672 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/89ee08af_21_lumper_receipt_10.png
2025-09-18 15:00:31,693 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 15:00:31,693 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 15:00:31,832 - INFO - Page 1: Extracted 422 characters, 38 lines from 920e53f8_21_lumper_receipt_5_5cd337dc_page_001.pdf
2025-09-18 15:00:31,832 - INFO - Successfully processed page 1
2025-09-18 15:00:31,832 - INFO - Combined 1 pages into final text
2025-09-18 15:00:31,832 - INFO - Text validation for 920e53f8_21_lumper_receipt_5: 439 characters, 1 pages
2025-09-18 15:00:31,833 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:31,833 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:31,990 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/89ee08af_21_lumper_receipt_10.png
2025-09-18 15:00:32,002 - INFO - S3 Image temp/51c1a112_21_lumper_receipt_7.png: Extracted 800 characters, 70 lines
2025-09-18 15:00:32,002 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:32,002 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:32,295 - INFO - Splitting PDF into individual pages...
2025-09-18 15:00:32,297 - INFO - Splitting PDF 95566a44_21_lumper_receipt_9 into 1 pages
2025-09-18 15:00:32,299 - INFO - Split PDF into 1 pages
2025-09-18 15:00:32,299 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:00:32,299 - INFO - Expected pages: [1]
2025-09-18 15:00:33,226 - INFO - Page 1: Extracted 330 characters, 33 lines from 0e9ad948_21_lumper_receipt_8_6c4d9af0_page_001.pdf
2025-09-18 15:00:33,227 - INFO - Successfully processed page 1
2025-09-18 15:00:33,227 - INFO - Combined 1 pages into final text
2025-09-18 15:00:33,227 - INFO - Text validation for 0e9ad948_21_lumper_receipt_8: 347 characters, 1 pages
2025-09-18 15:00:33,228 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:33,228 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:34,070 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9b841de7_21_lumper_receipt_3.png
2025-09-18 15:00:34,089 - INFO - 

21_lumper_receipt_3.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:34,089 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_3.json
2025-09-18 15:00:34,388 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9b841de7_21_lumper_receipt_3.png
2025-09-18 15:00:34,610 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/920e53f8_21_lumper_receipt_5.pdf
2025-09-18 15:00:34,624 - INFO - 

21_lumper_receipt_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 15:00:34,625 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_5.json
2025-09-18 15:00:34,723 - INFO - Page 1: Extracted 269 characters, 22 lines from c8d32188_21_lumper_receipt_6_94c2179f_page_001.pdf
2025-09-18 15:00:34,723 - INFO - Successfully processed page 1
2025-09-18 15:00:34,723 - INFO - Combined 1 pages into final text
2025-09-18 15:00:34,724 - INFO - Text validation for c8d32188_21_lumper_receipt_6: 286 characters, 1 pages
2025-09-18 15:00:34,724 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:34,724 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:34,789 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0e9ad948_21_lumper_receipt_8.pdf
2025-09-18 15:00:34,870 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b8024f1f_21_lumper_receipt_4.pdf
2025-09-18 15:00:34,924 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/920e53f8_21_lumper_receipt_5.pdf
2025-09-18 15:00:34,935 - INFO - 

21_lumper_receipt_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:34,935 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_8.json
2025-09-18 15:00:34,976 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/51c1a112_21_lumper_receipt_7.png
2025-09-18 15:00:35,223 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0e9ad948_21_lumper_receipt_8.pdf
2025-09-18 15:00:35,235 - INFO - 

21_lumper_receipt_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:35,235 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_4.json
2025-09-18 15:00:35,522 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b8024f1f_21_lumper_receipt_4.pdf
2025-09-18 15:00:35,531 - INFO - 

21_lumper_receipt_7.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:35,531 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_7.json
2025-09-18 15:00:35,816 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/51c1a112_21_lumper_receipt_7.png
2025-09-18 15:00:37,635 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c8d32188_21_lumper_receipt_6.pdf
2025-09-18 15:00:37,648 - INFO - 

21_lumper_receipt_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-18 15:00:37,648 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_6.json
2025-09-18 15:00:37,787 - INFO - Page 1: Extracted 645 characters, 53 lines from 95566a44_21_lumper_receipt_9_2052b845_page_001.pdf
2025-09-18 15:00:37,787 - INFO - Successfully processed page 1
2025-09-18 15:00:37,788 - INFO - Combined 1 pages into final text
2025-09-18 15:00:37,788 - INFO - Text validation for 95566a44_21_lumper_receipt_9: 662 characters, 1 pages
2025-09-18 15:00:37,788 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:00:37,788 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:00:37,948 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c8d32188_21_lumper_receipt_6.pdf
2025-09-18 15:00:40,922 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/95566a44_21_lumper_receipt_9.pdf
2025-09-18 15:00:40,943 - INFO - 

21_lumper_receipt_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:00:40,943 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_9.json
2025-09-18 15:00:41,231 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/95566a44_21_lumper_receipt_9.pdf
2025-09-18 15:00:41,231 - INFO - 
📊 Processing Summary:
2025-09-18 15:00:41,231 - INFO -    Total files: 11
2025-09-18 15:00:41,231 - INFO -    Successful: 11
2025-09-18 15:00:41,231 - INFO -    Failed: 0
2025-09-18 15:00:41,232 - INFO -    Duration: 21.17 seconds
2025-09-18 15:00:41,232 - INFO -    Output directory: output
2025-09-18 15:00:41,232 - INFO - 
📋 Successfully Processed Files:
2025-09-18 15:00:41,232 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:41,232 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 15:00:41,232 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:41,232 - INFO -    📄 21_lumper_receipt_2.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:41,232 - INFO -    📄 21_lumper_receipt_3.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:41,232 - INFO -    📄 21_lumper_receipt_4.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:41,232 - INFO -    📄 21_lumper_receipt_5.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 15:00:41,232 - INFO -    📄 21_lumper_receipt_6.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-18 15:00:41,232 - INFO -    📄 21_lumper_receipt_7.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:41,232 - INFO -    📄 21_lumper_receipt_8.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:41,232 - INFO -    📄 21_lumper_receipt_9.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:00:41,233 - INFO - 
============================================================================================================================================
2025-09-18 15:00:41,233 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 15:00:41,233 - INFO - ============================================================================================================================================
2025-09-18 15:00:41,233 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 15:00:41,233 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:00:41,233 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 15:00:41,233 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 15:00:41,233 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 15:00:41,233 - INFO - 
2025-09-18 15:00:41,233 - INFO - 21_lumper_receipt_10.png                           1      other                run1_21_lumper_receipt_10.json                    
2025-09-18 15:00:41,233 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 15:00:41,233 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 15:00:41,234 - INFO - 
2025-09-18 15:00:41,234 - INFO - 21_lumper_receipt_11.jpeg                          1      lumper_receipt       run1_21_lumper_receipt_11.json                    
2025-09-18 15:00:41,234 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 15:00:41,234 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 15:00:41,234 - INFO - 
2025-09-18 15:00:41,234 - INFO - 21_lumper_receipt_2.jpg                            1      lumper_receipt       run1_21_lumper_receipt_2.json                     
2025-09-18 15:00:41,234 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg
2025-09-18 15:00:41,234 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_2.json
2025-09-18 15:00:41,234 - INFO - 
2025-09-18 15:00:41,234 - INFO - 21_lumper_receipt_3.png                            1      lumper_receipt       run1_21_lumper_receipt_3.json                     
2025-09-18 15:00:41,234 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png
2025-09-18 15:00:41,234 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_3.json
2025-09-18 15:00:41,234 - INFO - 
2025-09-18 15:00:41,234 - INFO - 21_lumper_receipt_4.pdf                            1      lumper_receipt       run1_21_lumper_receipt_4.json                     
2025-09-18 15:00:41,234 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf
2025-09-18 15:00:41,234 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_4.json
2025-09-18 15:00:41,234 - INFO - 
2025-09-18 15:00:41,234 - INFO - 21_lumper_receipt_5.pdf                            1      other                run1_21_lumper_receipt_5.json                     
2025-09-18 15:00:41,234 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf
2025-09-18 15:00:41,234 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_5.json
2025-09-18 15:00:41,234 - INFO - 
2025-09-18 15:00:41,235 - INFO - 21_lumper_receipt_6.pdf                            1      po                   run1_21_lumper_receipt_6.json                     
2025-09-18 15:00:41,235 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf
2025-09-18 15:00:41,235 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_6.json
2025-09-18 15:00:41,235 - INFO - 
2025-09-18 15:00:41,235 - INFO - 21_lumper_receipt_7.png                            1      lumper_receipt       run1_21_lumper_receipt_7.json                     
2025-09-18 15:00:41,235 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png
2025-09-18 15:00:41,235 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_7.json
2025-09-18 15:00:41,235 - INFO - 
2025-09-18 15:00:41,235 - INFO - 21_lumper_receipt_8.pdf                            1      lumper_receipt       run1_21_lumper_receipt_8.json                     
2025-09-18 15:00:41,235 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf
2025-09-18 15:00:41,235 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_8.json
2025-09-18 15:00:41,235 - INFO - 
2025-09-18 15:00:41,235 - INFO - 21_lumper_receipt_9.pdf                            1      lumper_receipt       run1_21_lumper_receipt_9.json                     
2025-09-18 15:00:41,235 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf
2025-09-18 15:00:41,235 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_9.json
2025-09-18 15:00:41,235 - INFO - 
2025-09-18 15:00:41,235 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:00:41,235 - INFO - Total entries: 11
2025-09-18 15:00:41,235 - INFO - ============================================================================================================================================
2025-09-18 15:00:41,235 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 15:00:41,235 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:00:41,236 - INFO -   1. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 15:00:41,236 - INFO -   2. 21_lumper_receipt_10.png            Page 1   → other           | run1_21_lumper_receipt_10.json
2025-09-18 15:00:41,236 - INFO -   3. 21_lumper_receipt_11.jpeg           Page 1   → lumper_receipt  | run1_21_lumper_receipt_11.json
2025-09-18 15:00:41,236 - INFO -   4. 21_lumper_receipt_2.jpg             Page 1   → lumper_receipt  | run1_21_lumper_receipt_2.json
2025-09-18 15:00:41,236 - INFO -   5. 21_lumper_receipt_3.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_3.json
2025-09-18 15:00:41,236 - INFO -   6. 21_lumper_receipt_4.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_4.json
2025-09-18 15:00:41,236 - INFO -   7. 21_lumper_receipt_5.pdf             Page 1   → other           | run1_21_lumper_receipt_5.json
2025-09-18 15:00:41,236 - INFO -   8. 21_lumper_receipt_6.pdf             Page 1   → po              | run1_21_lumper_receipt_6.json
2025-09-18 15:00:41,236 - INFO -   9. 21_lumper_receipt_7.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_7.json
2025-09-18 15:00:41,236 - INFO -  10. 21_lumper_receipt_8.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_8.json
2025-09-18 15:00:41,236 - INFO -  11. 21_lumper_receipt_9.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_9.json
2025-09-18 15:00:41,236 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:00:41,236 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 21.173629, 'processed_files': [{'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '21_lumper_receipt_2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg'}, {'filename': '21_lumper_receipt_3.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png'}, {'filename': '21_lumper_receipt_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf'}, {'filename': '21_lumper_receipt_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf'}, {'filename': '21_lumper_receipt_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf'}, {'filename': '21_lumper_receipt_7.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png'}, {'filename': '21_lumper_receipt_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf'}, {'filename': '21_lumper_receipt_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf'}]}
