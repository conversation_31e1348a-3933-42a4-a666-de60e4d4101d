2025-09-18 15:02:30,607 - INFO - Logging initialized. Log file: logs/test_classification_20250918_150230.log
2025-09-18 15:02:30,608 - INFO - 📁 Found 11 files to process
2025-09-18 15:02:30,608 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 15:02:30,608 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 15:02:30,608 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 15:02:30,608 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 15:02:30,608 - INFO - ⬆️ [15:02:30] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 15:02:32,981 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/9b1f3ad6_21_lumper_receipt_1.jpeg
2025-09-18 15:02:32,981 - INFO - 🔍 [15:02:32] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 15:02:32,982 - INFO - ⬆️ [15:02:32] Uploading: 21_lumper_receipt_10.png
2025-09-18 15:02:32,985 - INFO - Initializing TextractProcessor...
2025-09-18 15:02:33,008 - INFO - Initializing BedrockProcessor...
2025-09-18 15:02:33,014 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9b1f3ad6_21_lumper_receipt_1.jpeg
2025-09-18 15:02:33,015 - INFO - Processing image from S3...
2025-09-18 15:02:33,896 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/c717afac_21_lumper_receipt_10.png
2025-09-18 15:02:33,896 - INFO - 🔍 [15:02:33] Starting classification: 21_lumper_receipt_10.png
2025-09-18 15:02:33,897 - INFO - ⬆️ [15:02:33] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 15:02:33,898 - INFO - Initializing TextractProcessor...
2025-09-18 15:02:33,914 - INFO - Initializing BedrockProcessor...
2025-09-18 15:02:33,919 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c717afac_21_lumper_receipt_10.png
2025-09-18 15:02:33,919 - INFO - Processing image from S3...
2025-09-18 15:02:35,594 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/60ff847b_21_lumper_receipt_11.jpeg
2025-09-18 15:02:35,594 - INFO - 🔍 [15:02:35] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 15:02:35,595 - INFO - Initializing TextractProcessor...
2025-09-18 15:02:35,595 - INFO - ⬆️ [15:02:35] Uploading: 21_lumper_receipt_2.jpg
2025-09-18 15:02:35,608 - INFO - Initializing BedrockProcessor...
2025-09-18 15:02:35,617 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/60ff847b_21_lumper_receipt_11.jpeg
2025-09-18 15:02:35,617 - INFO - Processing image from S3...
2025-09-18 15:02:35,727 - INFO - S3 Image temp/9b1f3ad6_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 15:02:35,727 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:02:35,727 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:02:36,752 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg -> s3://document-extraction-logistically/temp/a89bea9f_21_lumper_receipt_2.jpg
2025-09-18 15:02:36,752 - INFO - 🔍 [15:02:36] Starting classification: 21_lumper_receipt_2.jpg
2025-09-18 15:02:36,754 - INFO - ⬆️ [15:02:36] Uploading: 21_lumper_receipt_3.png
2025-09-18 15:02:36,754 - INFO - Initializing TextractProcessor...
2025-09-18 15:02:36,774 - INFO - Initializing BedrockProcessor...
2025-09-18 15:02:36,779 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a89bea9f_21_lumper_receipt_2.jpg
2025-09-18 15:02:36,780 - INFO - Processing image from S3...
2025-09-18 15:02:37,020 - INFO - S3 Image temp/c717afac_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 15:02:37,020 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:02:37,020 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:02:37,480 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9b1f3ad6_21_lumper_receipt_1.jpeg
2025-09-18 15:02:37,707 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png -> s3://document-extraction-logistically/temp/563fea3c_21_lumper_receipt_3.png
2025-09-18 15:02:37,707 - INFO - 🔍 [15:02:37] Starting classification: 21_lumper_receipt_3.png
2025-09-18 15:02:37,708 - INFO - ⬆️ [15:02:37] Uploading: 21_lumper_receipt_4.pdf
2025-09-18 15:02:37,711 - INFO - Initializing TextractProcessor...
2025-09-18 15:02:37,730 - INFO - Initializing BedrockProcessor...
2025-09-18 15:02:37,735 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/563fea3c_21_lumper_receipt_3.png
2025-09-18 15:02:37,735 - INFO - Processing image from S3...
2025-09-18 15:02:38,276 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf -> s3://document-extraction-logistically/temp/4eb3a424_21_lumper_receipt_4.pdf
2025-09-18 15:02:38,276 - INFO - 🔍 [15:02:38] Starting classification: 21_lumper_receipt_4.pdf
2025-09-18 15:02:38,277 - INFO - ⬆️ [15:02:38] Uploading: 21_lumper_receipt_5.pdf
2025-09-18 15:02:38,284 - INFO - Initializing TextractProcessor...
2025-09-18 15:02:38,300 - INFO - Initializing BedrockProcessor...
2025-09-18 15:02:38,304 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4eb3a424_21_lumper_receipt_4.pdf
2025-09-18 15:02:38,304 - INFO - Processing PDF from S3...
2025-09-18 15:02:38,305 - INFO - Downloading PDF from S3 to /tmp/tmpvere66dj/4eb3a424_21_lumper_receipt_4.pdf
2025-09-18 15:02:38,595 - INFO - S3 Image temp/60ff847b_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 15:02:38,595 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:02:38,596 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:02:38,842 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf -> s3://document-extraction-logistically/temp/919ceedd_21_lumper_receipt_5.pdf
2025-09-18 15:02:38,843 - INFO - 🔍 [15:02:38] Starting classification: 21_lumper_receipt_5.pdf
2025-09-18 15:02:38,844 - INFO - ⬆️ [15:02:38] Uploading: 21_lumper_receipt_6.pdf
2025-09-18 15:02:38,845 - INFO - Initializing TextractProcessor...
2025-09-18 15:02:38,863 - INFO - Initializing BedrockProcessor...
2025-09-18 15:02:38,868 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/919ceedd_21_lumper_receipt_5.pdf
2025-09-18 15:02:38,869 - INFO - Processing PDF from S3...
2025-09-18 15:02:38,869 - INFO - Downloading PDF from S3 to /tmp/tmpixyot_ah/919ceedd_21_lumper_receipt_5.pdf
2025-09-18 15:02:39,360 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c717afac_21_lumper_receipt_10.png
2025-09-18 15:02:39,409 - INFO - S3 Image temp/a89bea9f_21_lumper_receipt_2.jpg: Extracted 184 characters, 12 lines
2025-09-18 15:02:39,409 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:02:39,409 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:02:39,435 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf -> s3://document-extraction-logistically/temp/dd4c566b_21_lumper_receipt_6.pdf
2025-09-18 15:02:39,436 - INFO - 🔍 [15:02:39] Starting classification: 21_lumper_receipt_6.pdf
2025-09-18 15:02:39,436 - INFO - ⬆️ [15:02:39] Uploading: 21_lumper_receipt_7.png
2025-09-18 15:02:39,442 - INFO - Initializing TextractProcessor...
2025-09-18 15:02:39,458 - INFO - Initializing BedrockProcessor...
2025-09-18 15:02:39,463 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dd4c566b_21_lumper_receipt_6.pdf
2025-09-18 15:02:39,463 - INFO - Processing PDF from S3...
2025-09-18 15:02:39,463 - INFO - Downloading PDF from S3 to /tmp/tmplrocvth3/dd4c566b_21_lumper_receipt_6.pdf
2025-09-18 15:02:39,616 - INFO - Splitting PDF into individual pages...
2025-09-18 15:02:39,617 - INFO - Splitting PDF 4eb3a424_21_lumper_receipt_4 into 1 pages
2025-09-18 15:02:39,619 - INFO - Split PDF into 1 pages
2025-09-18 15:02:39,619 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:02:39,619 - INFO - Expected pages: [1]
2025-09-18 15:02:40,063 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png -> s3://document-extraction-logistically/temp/eb2f784c_21_lumper_receipt_7.png
2025-09-18 15:02:40,063 - INFO - 🔍 [15:02:40] Starting classification: 21_lumper_receipt_7.png
2025-09-18 15:02:40,064 - INFO - ⬆️ [15:02:40] Uploading: 21_lumper_receipt_8.pdf
2025-09-18 15:02:40,067 - INFO - Initializing TextractProcessor...
2025-09-18 15:02:40,088 - INFO - Initializing BedrockProcessor...
2025-09-18 15:02:40,095 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/eb2f784c_21_lumper_receipt_7.png
2025-09-18 15:02:40,096 - INFO - Processing image from S3...
2025-09-18 15:02:40,102 - INFO - Splitting PDF into individual pages...
2025-09-18 15:02:40,104 - INFO - Splitting PDF 919ceedd_21_lumper_receipt_5 into 1 pages
2025-09-18 15:02:40,107 - INFO - Split PDF into 1 pages
2025-09-18 15:02:40,107 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:02:40,107 - INFO - Expected pages: [1]
2025-09-18 15:02:40,330 - INFO - S3 Image temp/563fea3c_21_lumper_receipt_3.png: Extracted 663 characters, 38 lines
2025-09-18 15:02:40,330 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:02:40,330 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:02:40,635 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf -> s3://document-extraction-logistically/temp/58e963ba_21_lumper_receipt_8.pdf
2025-09-18 15:02:40,635 - INFO - 🔍 [15:02:40] Starting classification: 21_lumper_receipt_8.pdf
2025-09-18 15:02:40,636 - INFO - ⬆️ [15:02:40] Uploading: 21_lumper_receipt_9.pdf
2025-09-18 15:02:40,639 - INFO - Initializing TextractProcessor...
2025-09-18 15:02:40,656 - INFO - Initializing BedrockProcessor...
2025-09-18 15:02:40,660 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/58e963ba_21_lumper_receipt_8.pdf
2025-09-18 15:02:40,660 - INFO - Processing PDF from S3...
2025-09-18 15:02:40,660 - INFO - Downloading PDF from S3 to /tmp/tmpmeo7f2e9/58e963ba_21_lumper_receipt_8.pdf
2025-09-18 15:02:41,066 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a89bea9f_21_lumper_receipt_2.jpg
2025-09-18 15:02:41,102 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/60ff847b_21_lumper_receipt_11.jpeg
2025-09-18 15:02:41,283 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf -> s3://document-extraction-logistically/temp/c8841964_21_lumper_receipt_9.pdf
2025-09-18 15:02:41,284 - INFO - 🔍 [15:02:41] Starting classification: 21_lumper_receipt_9.pdf
2025-09-18 15:02:41,285 - INFO - Initializing TextractProcessor...
2025-09-18 15:02:41,307 - INFO - Initializing BedrockProcessor...
2025-09-18 15:02:41,313 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c8841964_21_lumper_receipt_9.pdf
2025-09-18 15:02:41,315 - INFO - Processing PDF from S3...
2025-09-18 15:02:41,317 - INFO - Downloading PDF from S3 to /tmp/tmpmnm6ewts/c8841964_21_lumper_receipt_9.pdf
2025-09-18 15:02:41,318 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:02:41,324 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 15:02:41,324 - INFO - Splitting PDF into individual pages...
2025-09-18 15:02:41,329 - INFO - Splitting PDF dd4c566b_21_lumper_receipt_6 into 1 pages
2025-09-18 15:02:41,336 - INFO - Split PDF into 1 pages
2025-09-18 15:02:41,336 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:02:41,336 - INFO - Expected pages: [1]
2025-09-18 15:02:41,609 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9b1f3ad6_21_lumper_receipt_1.jpeg
2025-09-18 15:02:41,625 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:02:41,625 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 15:02:41,870 - INFO - Splitting PDF into individual pages...
2025-09-18 15:02:41,871 - INFO - Splitting PDF 58e963ba_21_lumper_receipt_8 into 1 pages
2025-09-18 15:02:41,872 - INFO - Split PDF into 1 pages
2025-09-18 15:02:41,872 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:02:41,873 - INFO - Expected pages: [1]
2025-09-18 15:02:41,911 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c717afac_21_lumper_receipt_10.png
2025-09-18 15:02:41,918 - INFO - 

21_lumper_receipt_2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:02:41,919 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_2.json
2025-09-18 15:02:42,206 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a89bea9f_21_lumper_receipt_2.jpg
2025-09-18 15:02:42,214 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:02:42,214 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 15:02:42,495 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/60ff847b_21_lumper_receipt_11.jpeg
2025-09-18 15:02:42,507 - INFO - Page 1: Extracted 422 characters, 38 lines from 4eb3a424_21_lumper_receipt_4_3e70437b_page_001.pdf
2025-09-18 15:02:42,508 - INFO - Successfully processed page 1
2025-09-18 15:02:42,508 - INFO - Combined 1 pages into final text
2025-09-18 15:02:42,508 - INFO - Text validation for 4eb3a424_21_lumper_receipt_4: 439 characters, 1 pages
2025-09-18 15:02:42,509 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:02:42,509 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:02:43,013 - INFO - Page 1: Extracted 422 characters, 38 lines from 919ceedd_21_lumper_receipt_5_d4e3ab5c_page_001.pdf
2025-09-18 15:02:43,014 - INFO - Successfully processed page 1
2025-09-18 15:02:43,014 - INFO - Combined 1 pages into final text
2025-09-18 15:02:43,014 - INFO - Text validation for 919ceedd_21_lumper_receipt_5: 439 characters, 1 pages
2025-09-18 15:02:43,014 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:02:43,014 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:02:43,230 - INFO - S3 Image temp/eb2f784c_21_lumper_receipt_7.png: Extracted 800 characters, 70 lines
2025-09-18 15:02:43,230 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:02:43,230 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:02:43,339 - INFO - Splitting PDF into individual pages...
2025-09-18 15:02:43,339 - INFO - Splitting PDF c8841964_21_lumper_receipt_9 into 1 pages
2025-09-18 15:02:43,340 - INFO - Split PDF into 1 pages
2025-09-18 15:02:43,340 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:02:43,340 - INFO - Expected pages: [1]
2025-09-18 15:02:43,983 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/563fea3c_21_lumper_receipt_3.png
2025-09-18 15:02:43,997 - INFO - 

21_lumper_receipt_3.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:02:43,998 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_3.json
2025-09-18 15:02:44,291 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/563fea3c_21_lumper_receipt_3.png
2025-09-18 15:02:44,473 - INFO - Page 1: Extracted 330 characters, 33 lines from 58e963ba_21_lumper_receipt_8_7ca03337_page_001.pdf
2025-09-18 15:02:44,473 - INFO - Successfully processed page 1
2025-09-18 15:02:44,473 - INFO - Combined 1 pages into final text
2025-09-18 15:02:44,474 - INFO - Text validation for 58e963ba_21_lumper_receipt_8: 347 characters, 1 pages
2025-09-18 15:02:44,474 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:02:44,474 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:02:44,502 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/919ceedd_21_lumper_receipt_5.pdf
2025-09-18 15:02:44,521 - INFO - 

21_lumper_receipt_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:02:44,521 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_5.json
2025-09-18 15:02:44,549 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4eb3a424_21_lumper_receipt_4.pdf
2025-09-18 15:02:44,812 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/919ceedd_21_lumper_receipt_5.pdf
2025-09-18 15:02:44,826 - INFO - 

21_lumper_receipt_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:02:44,826 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_4.json
2025-09-18 15:02:45,118 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4eb3a424_21_lumper_receipt_4.pdf
2025-09-18 15:02:45,554 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/eb2f784c_21_lumper_receipt_7.png
2025-09-18 15:02:45,575 - INFO - 

21_lumper_receipt_7.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:02:45,575 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_7.json
2025-09-18 15:02:45,869 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/eb2f784c_21_lumper_receipt_7.png
2025-09-18 15:02:45,899 - INFO - Page 1: Extracted 269 characters, 22 lines from dd4c566b_21_lumper_receipt_6_90b48223_page_001.pdf
2025-09-18 15:02:45,899 - INFO - Successfully processed page 1
2025-09-18 15:02:45,899 - INFO - Combined 1 pages into final text
2025-09-18 15:02:45,899 - INFO - Text validation for dd4c566b_21_lumper_receipt_6: 286 characters, 1 pages
2025-09-18 15:02:45,900 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:02:45,900 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:02:46,517 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/58e963ba_21_lumper_receipt_8.pdf
2025-09-18 15:02:46,526 - INFO - 

21_lumper_receipt_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:02:46,526 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_8.json
2025-09-18 15:02:46,826 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/58e963ba_21_lumper_receipt_8.pdf
2025-09-18 15:02:48,104 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dd4c566b_21_lumper_receipt_6.pdf
2025-09-18 15:02:48,116 - INFO - 

21_lumper_receipt_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:02:48,116 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_6.json
2025-09-18 15:02:48,411 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dd4c566b_21_lumper_receipt_6.pdf
2025-09-18 15:02:48,499 - INFO - Page 1: Extracted 645 characters, 53 lines from c8841964_21_lumper_receipt_9_e696d24d_page_001.pdf
2025-09-18 15:02:48,499 - INFO - Successfully processed page 1
2025-09-18 15:02:48,499 - INFO - Combined 1 pages into final text
2025-09-18 15:02:48,499 - INFO - Text validation for c8841964_21_lumper_receipt_9: 662 characters, 1 pages
2025-09-18 15:02:48,500 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:02:48,500 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:02:51,516 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c8841964_21_lumper_receipt_9.pdf
2025-09-18 15:02:51,531 - INFO - 

21_lumper_receipt_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:02:51,532 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_9.json
2025-09-18 15:02:51,826 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c8841964_21_lumper_receipt_9.pdf
2025-09-18 15:02:51,827 - INFO - 
📊 Processing Summary:
2025-09-18 15:02:51,827 - INFO -    Total files: 11
2025-09-18 15:02:51,827 - INFO -    Successful: 11
2025-09-18 15:02:51,828 - INFO -    Failed: 0
2025-09-18 15:02:51,828 - INFO -    Duration: 21.22 seconds
2025-09-18 15:02:51,828 - INFO -    Output directory: output
2025-09-18 15:02:51,828 - INFO - 
📋 Successfully Processed Files:
2025-09-18 15:02:51,828 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:02:51,828 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:02:51,828 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:02:51,828 - INFO -    📄 21_lumper_receipt_2.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:02:51,828 - INFO -    📄 21_lumper_receipt_3.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:02:51,829 - INFO -    📄 21_lumper_receipt_4.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:02:51,829 - INFO -    📄 21_lumper_receipt_5.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:02:51,829 - INFO -    📄 21_lumper_receipt_6.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:02:51,829 - INFO -    📄 21_lumper_receipt_7.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:02:51,829 - INFO -    📄 21_lumper_receipt_8.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:02:51,829 - INFO -    📄 21_lumper_receipt_9.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:02:51,830 - INFO - 
============================================================================================================================================
2025-09-18 15:02:51,830 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 15:02:51,830 - INFO - ============================================================================================================================================
2025-09-18 15:02:51,830 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 15:02:51,830 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:02:51,830 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 15:02:51,830 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 15:02:51,830 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 15:02:51,830 - INFO - 
2025-09-18 15:02:51,830 - INFO - 21_lumper_receipt_10.png                           1      lumper_receipt       run1_21_lumper_receipt_10.json                    
2025-09-18 15:02:51,830 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 15:02:51,831 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 15:02:51,831 - INFO - 
2025-09-18 15:02:51,831 - INFO - 21_lumper_receipt_11.jpeg                          1      invoice              run1_21_lumper_receipt_11.json                    
2025-09-18 15:02:51,831 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 15:02:51,831 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 15:02:51,831 - INFO - 
2025-09-18 15:02:51,831 - INFO - 21_lumper_receipt_2.jpg                            1      lumper_receipt       run1_21_lumper_receipt_2.json                     
2025-09-18 15:02:51,831 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg
2025-09-18 15:02:51,831 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_2.json
2025-09-18 15:02:51,831 - INFO - 
2025-09-18 15:02:51,831 - INFO - 21_lumper_receipt_3.png                            1      lumper_receipt       run1_21_lumper_receipt_3.json                     
2025-09-18 15:02:51,831 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png
2025-09-18 15:02:51,831 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_3.json
2025-09-18 15:02:51,831 - INFO - 
2025-09-18 15:02:51,831 - INFO - 21_lumper_receipt_4.pdf                            1      lumper_receipt       run1_21_lumper_receipt_4.json                     
2025-09-18 15:02:51,832 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf
2025-09-18 15:02:51,832 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_4.json
2025-09-18 15:02:51,832 - INFO - 
2025-09-18 15:02:51,832 - INFO - 21_lumper_receipt_5.pdf                            1      lumper_receipt       run1_21_lumper_receipt_5.json                     
2025-09-18 15:02:51,832 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf
2025-09-18 15:02:51,832 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_5.json
2025-09-18 15:02:51,832 - INFO - 
2025-09-18 15:02:51,832 - INFO - 21_lumper_receipt_6.pdf                            1      lumper_receipt       run1_21_lumper_receipt_6.json                     
2025-09-18 15:02:51,832 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf
2025-09-18 15:02:51,832 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_6.json
2025-09-18 15:02:51,832 - INFO - 
2025-09-18 15:02:51,832 - INFO - 21_lumper_receipt_7.png                            1      lumper_receipt       run1_21_lumper_receipt_7.json                     
2025-09-18 15:02:51,832 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png
2025-09-18 15:02:51,832 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_7.json
2025-09-18 15:02:51,832 - INFO - 
2025-09-18 15:02:51,832 - INFO - 21_lumper_receipt_8.pdf                            1      lumper_receipt       run1_21_lumper_receipt_8.json                     
2025-09-18 15:02:51,832 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf
2025-09-18 15:02:51,832 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_8.json
2025-09-18 15:02:51,833 - INFO - 
2025-09-18 15:02:51,833 - INFO - 21_lumper_receipt_9.pdf                            1      lumper_receipt       run1_21_lumper_receipt_9.json                     
2025-09-18 15:02:51,833 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf
2025-09-18 15:02:51,833 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_9.json
2025-09-18 15:02:51,833 - INFO - 
2025-09-18 15:02:51,833 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:02:51,833 - INFO - Total entries: 11
2025-09-18 15:02:51,833 - INFO - ============================================================================================================================================
2025-09-18 15:02:51,833 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 15:02:51,833 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:02:51,833 - INFO -   1. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 15:02:51,833 - INFO -   2. 21_lumper_receipt_10.png            Page 1   → lumper_receipt  | run1_21_lumper_receipt_10.json
2025-09-18 15:02:51,833 - INFO -   3. 21_lumper_receipt_11.jpeg           Page 1   → invoice         | run1_21_lumper_receipt_11.json
2025-09-18 15:02:51,833 - INFO -   4. 21_lumper_receipt_2.jpg             Page 1   → lumper_receipt  | run1_21_lumper_receipt_2.json
2025-09-18 15:02:51,833 - INFO -   5. 21_lumper_receipt_3.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_3.json
2025-09-18 15:02:51,833 - INFO -   6. 21_lumper_receipt_4.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_4.json
2025-09-18 15:02:51,833 - INFO -   7. 21_lumper_receipt_5.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_5.json
2025-09-18 15:02:51,833 - INFO -   8. 21_lumper_receipt_6.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_6.json
2025-09-18 15:02:51,834 - INFO -   9. 21_lumper_receipt_7.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_7.json
2025-09-18 15:02:51,834 - INFO -  10. 21_lumper_receipt_8.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_8.json
2025-09-18 15:02:51,834 - INFO -  11. 21_lumper_receipt_9.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_9.json
2025-09-18 15:02:51,834 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:02:51,834 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 21.219201, 'processed_files': [{'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '21_lumper_receipt_2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg'}, {'filename': '21_lumper_receipt_3.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png'}, {'filename': '21_lumper_receipt_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf'}, {'filename': '21_lumper_receipt_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf'}, {'filename': '21_lumper_receipt_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf'}, {'filename': '21_lumper_receipt_7.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png'}, {'filename': '21_lumper_receipt_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf'}, {'filename': '21_lumper_receipt_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf'}]}
