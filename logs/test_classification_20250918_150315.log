2025-09-18 15:03:15,137 - INFO - Logging initialized. Log file: logs/test_classification_20250918_150315.log
2025-09-18 15:03:15,138 - INFO - 📁 Found 11 files to process
2025-09-18 15:03:15,138 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 15:03:15,138 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 15:03:15,138 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 15:03:15,138 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 15:03:15,138 - INFO - ⬆️ [15:03:15] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 15:03:17,521 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/8f6c5aef_21_lumper_receipt_1.jpeg
2025-09-18 15:03:17,522 - INFO - 🔍 [15:03:17] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 15:03:17,523 - INFO - ⬆️ [15:03:17] Uploading: 21_lumper_receipt_10.png
2025-09-18 15:03:17,524 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:17,546 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:17,558 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8f6c5aef_21_lumper_receipt_1.jpeg
2025-09-18 15:03:17,559 - INFO - Processing image from S3...
2025-09-18 15:03:18,444 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/47ff9f9d_21_lumper_receipt_10.png
2025-09-18 15:03:18,445 - INFO - 🔍 [15:03:18] Starting classification: 21_lumper_receipt_10.png
2025-09-18 15:03:18,446 - INFO - ⬆️ [15:03:18] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 15:03:18,448 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:18,465 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:18,470 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/47ff9f9d_21_lumper_receipt_10.png
2025-09-18 15:03:18,471 - INFO - Processing image from S3...
2025-09-18 15:03:19,708 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/a79a14f7_21_lumper_receipt_11.jpeg
2025-09-18 15:03:19,708 - INFO - 🔍 [15:03:19] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 15:03:19,709 - INFO - ⬆️ [15:03:19] Uploading: 21_lumper_receipt_2.jpg
2025-09-18 15:03:19,709 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:19,722 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:19,726 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a79a14f7_21_lumper_receipt_11.jpeg
2025-09-18 15:03:19,726 - INFO - Processing image from S3...
2025-09-18 15:03:20,326 - INFO - S3 Image temp/8f6c5aef_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 15:03:20,326 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:03:20,326 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:03:21,803 - INFO - S3 Image temp/47ff9f9d_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 15:03:21,803 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:03:21,803 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:03:22,134 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8f6c5aef_21_lumper_receipt_1.jpeg
2025-09-18 15:03:22,296 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg -> s3://document-extraction-logistically/temp/7484c4e4_21_lumper_receipt_2.jpg
2025-09-18 15:03:22,296 - INFO - 🔍 [15:03:22] Starting classification: 21_lumper_receipt_2.jpg
2025-09-18 15:03:22,297 - INFO - ⬆️ [15:03:22] Uploading: 21_lumper_receipt_3.png
2025-09-18 15:03:22,300 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:22,320 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:22,324 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7484c4e4_21_lumper_receipt_2.jpg
2025-09-18 15:03:22,324 - INFO - Processing image from S3...
2025-09-18 15:03:23,195 - INFO - S3 Image temp/a79a14f7_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 15:03:23,195 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:03:23,195 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:03:23,299 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png -> s3://document-extraction-logistically/temp/b14769a2_21_lumper_receipt_3.png
2025-09-18 15:03:23,300 - INFO - 🔍 [15:03:23] Starting classification: 21_lumper_receipt_3.png
2025-09-18 15:03:23,301 - INFO - ⬆️ [15:03:23] Uploading: 21_lumper_receipt_4.pdf
2025-09-18 15:03:23,303 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:23,323 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:23,328 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b14769a2_21_lumper_receipt_3.png
2025-09-18 15:03:23,329 - INFO - Processing image from S3...
2025-09-18 15:03:23,897 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf -> s3://document-extraction-logistically/temp/55748866_21_lumper_receipt_4.pdf
2025-09-18 15:03:23,897 - INFO - 🔍 [15:03:23] Starting classification: 21_lumper_receipt_4.pdf
2025-09-18 15:03:23,898 - INFO - ⬆️ [15:03:23] Uploading: 21_lumper_receipt_5.pdf
2025-09-18 15:03:23,899 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:23,918 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:23,921 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/55748866_21_lumper_receipt_4.pdf
2025-09-18 15:03:23,921 - INFO - Processing PDF from S3...
2025-09-18 15:03:23,921 - INFO - Downloading PDF from S3 to /tmp/tmpqv7ssj87/55748866_21_lumper_receipt_4.pdf
2025-09-18 15:03:24,480 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf -> s3://document-extraction-logistically/temp/b37d6e07_21_lumper_receipt_5.pdf
2025-09-18 15:03:24,480 - INFO - 🔍 [15:03:24] Starting classification: 21_lumper_receipt_5.pdf
2025-09-18 15:03:24,481 - INFO - ⬆️ [15:03:24] Uploading: 21_lumper_receipt_6.pdf
2025-09-18 15:03:24,483 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:24,500 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:24,507 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b37d6e07_21_lumper_receipt_5.pdf
2025-09-18 15:03:24,507 - INFO - Processing PDF from S3...
2025-09-18 15:03:24,508 - INFO - Downloading PDF from S3 to /tmp/tmphyyylarn/b37d6e07_21_lumper_receipt_5.pdf
2025-09-18 15:03:25,093 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf -> s3://document-extraction-logistically/temp/22555a8c_21_lumper_receipt_6.pdf
2025-09-18 15:03:25,093 - INFO - 🔍 [15:03:25] Starting classification: 21_lumper_receipt_6.pdf
2025-09-18 15:03:25,094 - INFO - ⬆️ [15:03:25] Uploading: 21_lumper_receipt_7.png
2025-09-18 15:03:25,095 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:25,114 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:25,119 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/22555a8c_21_lumper_receipt_6.pdf
2025-09-18 15:03:25,119 - INFO - Processing PDF from S3...
2025-09-18 15:03:25,120 - INFO - Downloading PDF from S3 to /tmp/tmpzbf4q_cc/22555a8c_21_lumper_receipt_6.pdf
2025-09-18 15:03:25,178 - INFO - Splitting PDF into individual pages...
2025-09-18 15:03:25,179 - INFO - Splitting PDF 55748866_21_lumper_receipt_4 into 1 pages
2025-09-18 15:03:25,182 - INFO - Split PDF into 1 pages
2025-09-18 15:03:25,182 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:03:25,182 - INFO - Expected pages: [1]
2025-09-18 15:03:25,374 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/47ff9f9d_21_lumper_receipt_10.png
2025-09-18 15:03:25,511 - INFO - S3 Image temp/7484c4e4_21_lumper_receipt_2.jpg: Extracted 184 characters, 12 lines
2025-09-18 15:03:25,511 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:03:25,511 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:03:25,657 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a79a14f7_21_lumper_receipt_11.jpeg
2025-09-18 15:03:25,789 - INFO - Splitting PDF into individual pages...
2025-09-18 15:03:25,790 - INFO - Splitting PDF b37d6e07_21_lumper_receipt_5 into 1 pages
2025-09-18 15:03:25,792 - INFO - Split PDF into 1 pages
2025-09-18 15:03:25,792 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:03:25,792 - INFO - Expected pages: [1]
2025-09-18 15:03:25,958 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png -> s3://document-extraction-logistically/temp/9662b271_21_lumper_receipt_7.png
2025-09-18 15:03:25,958 - INFO - 🔍 [15:03:25] Starting classification: 21_lumper_receipt_7.png
2025-09-18 15:03:25,959 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:25,960 - INFO - ⬆️ [15:03:25] Uploading: 21_lumper_receipt_8.pdf
2025-09-18 15:03:25,971 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:25,979 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9662b271_21_lumper_receipt_7.png
2025-09-18 15:03:25,979 - INFO - Processing image from S3...
2025-09-18 15:03:26,013 - INFO - S3 Image temp/b14769a2_21_lumper_receipt_3.png: Extracted 663 characters, 38 lines
2025-09-18 15:03:26,013 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:03:26,013 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:03:26,551 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf -> s3://document-extraction-logistically/temp/407123cc_21_lumper_receipt_8.pdf
2025-09-18 15:03:26,551 - INFO - 🔍 [15:03:26] Starting classification: 21_lumper_receipt_8.pdf
2025-09-18 15:03:26,552 - INFO - ⬆️ [15:03:26] Uploading: 21_lumper_receipt_9.pdf
2025-09-18 15:03:26,554 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:26,573 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:26,580 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/407123cc_21_lumper_receipt_8.pdf
2025-09-18 15:03:26,580 - INFO - Processing PDF from S3...
2025-09-18 15:03:26,581 - INFO - Downloading PDF from S3 to /tmp/tmpggu78ixg/407123cc_21_lumper_receipt_8.pdf
2025-09-18 15:03:27,132 - INFO - Splitting PDF into individual pages...
2025-09-18 15:03:27,134 - INFO - Splitting PDF 22555a8c_21_lumper_receipt_6 into 1 pages
2025-09-18 15:03:27,139 - INFO - Split PDF into 1 pages
2025-09-18 15:03:27,139 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:03:27,139 - INFO - Expected pages: [1]
2025-09-18 15:03:27,216 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf -> s3://document-extraction-logistically/temp/656842fa_21_lumper_receipt_9.pdf
2025-09-18 15:03:27,216 - INFO - 🔍 [15:03:27] Starting classification: 21_lumper_receipt_9.pdf
2025-09-18 15:03:27,218 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:27,237 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:27,246 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/656842fa_21_lumper_receipt_9.pdf
2025-09-18 15:03:27,248 - INFO - Processing PDF from S3...
2025-09-18 15:03:27,250 - INFO - Downloading PDF from S3 to /tmp/tmp8mtbso6z/656842fa_21_lumper_receipt_9.pdf
2025-09-18 15:03:27,258 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:03:27,258 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 15:03:27,355 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7484c4e4_21_lumper_receipt_2.jpg
2025-09-18 15:03:27,557 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8f6c5aef_21_lumper_receipt_1.jpeg
2025-09-18 15:03:27,571 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 15:03:27,571 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 15:03:27,842 - INFO - Splitting PDF into individual pages...
2025-09-18 15:03:27,843 - INFO - Splitting PDF 407123cc_21_lumper_receipt_8 into 1 pages
2025-09-18 15:03:27,844 - INFO - Split PDF into 1 pages
2025-09-18 15:03:27,844 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:03:27,844 - INFO - Expected pages: [1]
2025-09-18 15:03:27,906 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/47ff9f9d_21_lumper_receipt_10.png
2025-09-18 15:03:27,921 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:03:27,922 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 15:03:27,936 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b14769a2_21_lumper_receipt_3.png
2025-09-18 15:03:28,174 - INFO - Page 1: Extracted 422 characters, 38 lines from 55748866_21_lumper_receipt_4_93048ca8_page_001.pdf
2025-09-18 15:03:28,174 - INFO - Successfully processed page 1
2025-09-18 15:03:28,174 - INFO - Combined 1 pages into final text
2025-09-18 15:03:28,175 - INFO - Text validation for 55748866_21_lumper_receipt_4: 439 characters, 1 pages
2025-09-18 15:03:28,175 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:03:28,175 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:03:28,212 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a79a14f7_21_lumper_receipt_11.jpeg
2025-09-18 15:03:28,215 - INFO - 

21_lumper_receipt_2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:03:28,215 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_2.json
2025-09-18 15:03:28,505 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7484c4e4_21_lumper_receipt_2.jpg
2025-09-18 15:03:28,519 - INFO - 

21_lumper_receipt_3.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:03:28,519 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_3.json
2025-09-18 15:03:28,837 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b14769a2_21_lumper_receipt_3.png
2025-09-18 15:03:28,867 - INFO - S3 Image temp/9662b271_21_lumper_receipt_7.png: Extracted 800 characters, 70 lines
2025-09-18 15:03:28,867 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:03:28,867 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:03:29,448 - INFO - Splitting PDF into individual pages...
2025-09-18 15:03:29,449 - INFO - Splitting PDF 656842fa_21_lumper_receipt_9 into 1 pages
2025-09-18 15:03:29,451 - INFO - Split PDF into 1 pages
2025-09-18 15:03:29,451 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:03:29,451 - INFO - Expected pages: [1]
2025-09-18 15:03:29,705 - INFO - Page 1: Extracted 422 characters, 38 lines from b37d6e07_21_lumper_receipt_5_f0e85e55_page_001.pdf
2025-09-18 15:03:29,705 - INFO - Successfully processed page 1
2025-09-18 15:03:29,705 - INFO - Combined 1 pages into final text
2025-09-18 15:03:29,706 - INFO - Text validation for b37d6e07_21_lumper_receipt_5: 439 characters, 1 pages
2025-09-18 15:03:29,706 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:03:29,706 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:03:30,694 - INFO - Page 1: Extracted 330 characters, 33 lines from 407123cc_21_lumper_receipt_8_b6f4f2d9_page_001.pdf
2025-09-18 15:03:30,695 - INFO - Successfully processed page 1
2025-09-18 15:03:30,695 - INFO - Combined 1 pages into final text
2025-09-18 15:03:30,695 - INFO - Text validation for 407123cc_21_lumper_receipt_8: 347 characters, 1 pages
2025-09-18 15:03:30,696 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:03:30,696 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:03:31,147 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/55748866_21_lumper_receipt_4.pdf
2025-09-18 15:03:31,162 - INFO - 

21_lumper_receipt_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 15:03:31,163 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_4.json
2025-09-18 15:03:31,467 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/55748866_21_lumper_receipt_4.pdf
2025-09-18 15:03:31,478 - INFO - Page 1: Extracted 269 characters, 22 lines from 22555a8c_21_lumper_receipt_6_e4216139_page_001.pdf
2025-09-18 15:03:31,479 - INFO - Successfully processed page 1
2025-09-18 15:03:31,479 - INFO - Combined 1 pages into final text
2025-09-18 15:03:31,479 - INFO - Text validation for 22555a8c_21_lumper_receipt_6: 286 characters, 1 pages
2025-09-18 15:03:31,479 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:03:31,479 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:03:31,839 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9662b271_21_lumper_receipt_7.png
2025-09-18 15:03:31,849 - INFO - 

21_lumper_receipt_7.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:03:31,849 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_7.json
2025-09-18 15:03:32,150 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9662b271_21_lumper_receipt_7.png
2025-09-18 15:03:32,464 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b37d6e07_21_lumper_receipt_5.pdf
2025-09-18 15:03:32,474 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/407123cc_21_lumper_receipt_8.pdf
2025-09-18 15:03:32,488 - INFO - 

21_lumper_receipt_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:03:32,488 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_5.json
2025-09-18 15:03:32,790 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b37d6e07_21_lumper_receipt_5.pdf
2025-09-18 15:03:32,801 - INFO - 

21_lumper_receipt_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:03:32,801 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_8.json
2025-09-18 15:03:33,104 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/407123cc_21_lumper_receipt_8.pdf
2025-09-18 15:03:33,529 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/22555a8c_21_lumper_receipt_6.pdf
2025-09-18 15:03:33,539 - INFO - 

21_lumper_receipt_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:03:33,539 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_6.json
2025-09-18 15:03:33,843 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/22555a8c_21_lumper_receipt_6.pdf
2025-09-18 15:03:34,689 - INFO - Page 1: Extracted 645 characters, 53 lines from 656842fa_21_lumper_receipt_9_4d4398d8_page_001.pdf
2025-09-18 15:03:34,689 - INFO - Successfully processed page 1
2025-09-18 15:03:34,689 - INFO - Combined 1 pages into final text
2025-09-18 15:03:34,689 - INFO - Text validation for 656842fa_21_lumper_receipt_9: 662 characters, 1 pages
2025-09-18 15:03:34,690 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:03:34,690 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:03:39,233 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/656842fa_21_lumper_receipt_9.pdf
2025-09-18 15:03:39,258 - INFO - 

21_lumper_receipt_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:03:39,258 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_9.json
2025-09-18 15:03:39,624 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/656842fa_21_lumper_receipt_9.pdf
2025-09-18 15:03:39,624 - INFO - 
📊 Processing Summary:
2025-09-18 15:03:39,624 - INFO -    Total files: 11
2025-09-18 15:03:39,624 - INFO -    Successful: 11
2025-09-18 15:03:39,625 - INFO -    Failed: 0
2025-09-18 15:03:39,625 - INFO -    Duration: 24.49 seconds
2025-09-18 15:03:39,625 - INFO -    Output directory: output
2025-09-18 15:03:39,625 - INFO - 
📋 Successfully Processed Files:
2025-09-18 15:03:39,625 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:03:39,625 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 15:03:39,625 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:03:39,625 - INFO -    📄 21_lumper_receipt_2.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:03:39,625 - INFO -    📄 21_lumper_receipt_3.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:03:39,625 - INFO -    📄 21_lumper_receipt_4.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 15:03:39,625 - INFO -    📄 21_lumper_receipt_5.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:03:39,625 - INFO -    📄 21_lumper_receipt_6.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:03:39,625 - INFO -    📄 21_lumper_receipt_7.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:03:39,625 - INFO -    📄 21_lumper_receipt_8.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:03:39,626 - INFO -    📄 21_lumper_receipt_9.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:03:39,626 - INFO - 
============================================================================================================================================
2025-09-18 15:03:39,626 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 15:03:39,626 - INFO - ============================================================================================================================================
2025-09-18 15:03:39,626 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 15:03:39,626 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:03:39,626 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 15:03:39,626 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 15:03:39,626 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 15:03:39,627 - INFO - 
2025-09-18 15:03:39,627 - INFO - 21_lumper_receipt_10.png                           1      other                run1_21_lumper_receipt_10.json                    
2025-09-18 15:03:39,627 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 15:03:39,627 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 15:03:39,627 - INFO - 
2025-09-18 15:03:39,627 - INFO - 21_lumper_receipt_11.jpeg                          1      invoice              run1_21_lumper_receipt_11.json                    
2025-09-18 15:03:39,627 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 15:03:39,627 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 15:03:39,627 - INFO - 
2025-09-18 15:03:39,627 - INFO - 21_lumper_receipt_2.jpg                            1      lumper_receipt       run1_21_lumper_receipt_2.json                     
2025-09-18 15:03:39,627 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg
2025-09-18 15:03:39,627 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_2.json
2025-09-18 15:03:39,627 - INFO - 
2025-09-18 15:03:39,627 - INFO - 21_lumper_receipt_3.png                            1      invoice              run1_21_lumper_receipt_3.json                     
2025-09-18 15:03:39,627 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png
2025-09-18 15:03:39,627 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_3.json
2025-09-18 15:03:39,627 - INFO - 
2025-09-18 15:03:39,627 - INFO - 21_lumper_receipt_4.pdf                            1      other                run1_21_lumper_receipt_4.json                     
2025-09-18 15:03:39,627 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf
2025-09-18 15:03:39,628 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_4.json
2025-09-18 15:03:39,628 - INFO - 
2025-09-18 15:03:39,628 - INFO - 21_lumper_receipt_5.pdf                            1      lumper_receipt       run1_21_lumper_receipt_5.json                     
2025-09-18 15:03:39,628 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf
2025-09-18 15:03:39,628 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_5.json
2025-09-18 15:03:39,628 - INFO - 
2025-09-18 15:03:39,628 - INFO - 21_lumper_receipt_6.pdf                            1      lumper_receipt       run1_21_lumper_receipt_6.json                     
2025-09-18 15:03:39,628 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf
2025-09-18 15:03:39,628 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_6.json
2025-09-18 15:03:39,628 - INFO - 
2025-09-18 15:03:39,628 - INFO - 21_lumper_receipt_7.png                            1      lumper_receipt       run1_21_lumper_receipt_7.json                     
2025-09-18 15:03:39,628 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png
2025-09-18 15:03:39,628 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_7.json
2025-09-18 15:03:39,628 - INFO - 
2025-09-18 15:03:39,628 - INFO - 21_lumper_receipt_8.pdf                            1      lumper_receipt       run1_21_lumper_receipt_8.json                     
2025-09-18 15:03:39,628 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf
2025-09-18 15:03:39,628 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_8.json
2025-09-18 15:03:39,628 - INFO - 
2025-09-18 15:03:39,628 - INFO - 21_lumper_receipt_9.pdf                            1      lumper_receipt       run1_21_lumper_receipt_9.json                     
2025-09-18 15:03:39,628 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf
2025-09-18 15:03:39,628 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_9.json
2025-09-18 15:03:39,629 - INFO - 
2025-09-18 15:03:39,629 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:03:39,629 - INFO - Total entries: 11
2025-09-18 15:03:39,629 - INFO - ============================================================================================================================================
2025-09-18 15:03:39,629 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 15:03:39,629 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:03:39,629 - INFO -   1. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 15:03:39,629 - INFO -   2. 21_lumper_receipt_10.png            Page 1   → other           | run1_21_lumper_receipt_10.json
2025-09-18 15:03:39,629 - INFO -   3. 21_lumper_receipt_11.jpeg           Page 1   → invoice         | run1_21_lumper_receipt_11.json
2025-09-18 15:03:39,629 - INFO -   4. 21_lumper_receipt_2.jpg             Page 1   → lumper_receipt  | run1_21_lumper_receipt_2.json
2025-09-18 15:03:39,629 - INFO -   5. 21_lumper_receipt_3.png             Page 1   → invoice         | run1_21_lumper_receipt_3.json
2025-09-18 15:03:39,629 - INFO -   6. 21_lumper_receipt_4.pdf             Page 1   → other           | run1_21_lumper_receipt_4.json
2025-09-18 15:03:39,629 - INFO -   7. 21_lumper_receipt_5.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_5.json
2025-09-18 15:03:39,629 - INFO -   8. 21_lumper_receipt_6.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_6.json
2025-09-18 15:03:39,629 - INFO -   9. 21_lumper_receipt_7.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_7.json
2025-09-18 15:03:39,629 - INFO -  10. 21_lumper_receipt_8.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_8.json
2025-09-18 15:03:39,629 - INFO -  11. 21_lumper_receipt_9.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_9.json
2025-09-18 15:03:39,629 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:03:39,630 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 24.486453, 'processed_files': [{'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '21_lumper_receipt_2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg'}, {'filename': '21_lumper_receipt_3.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png'}, {'filename': '21_lumper_receipt_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf'}, {'filename': '21_lumper_receipt_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf'}, {'filename': '21_lumper_receipt_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf'}, {'filename': '21_lumper_receipt_7.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png'}, {'filename': '21_lumper_receipt_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf'}, {'filename': '21_lumper_receipt_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf'}]}
