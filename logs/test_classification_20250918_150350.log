2025-09-18 15:03:50,537 - INFO - Logging initialized. Log file: logs/test_classification_20250918_150350.log
2025-09-18 15:03:50,538 - INFO - 📁 Found 11 files to process
2025-09-18 15:03:50,538 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 15:03:50,538 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 15:03:50,538 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 15:03:50,538 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 15:03:50,538 - INFO - ⬆️ [15:03:50] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 15:03:53,174 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/33d4261d_21_lumper_receipt_1.jpeg
2025-09-18 15:03:53,174 - INFO - 🔍 [15:03:53] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 15:03:53,175 - INFO - ⬆️ [15:03:53] Uploading: 21_lumper_receipt_10.png
2025-09-18 15:03:53,176 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:53,197 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:53,203 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/33d4261d_21_lumper_receipt_1.jpeg
2025-09-18 15:03:53,203 - INFO - Processing image from S3...
2025-09-18 15:03:54,188 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/68b015c0_21_lumper_receipt_10.png
2025-09-18 15:03:54,189 - INFO - 🔍 [15:03:54] Starting classification: 21_lumper_receipt_10.png
2025-09-18 15:03:54,190 - INFO - ⬆️ [15:03:54] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 15:03:54,191 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:54,213 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:54,215 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/68b015c0_21_lumper_receipt_10.png
2025-09-18 15:03:54,216 - INFO - Processing image from S3...
2025-09-18 15:03:56,844 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/837e9160_21_lumper_receipt_11.jpeg
2025-09-18 15:03:56,845 - INFO - 🔍 [15:03:56] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 15:03:56,845 - INFO - ⬆️ [15:03:56] Uploading: 21_lumper_receipt_2.jpg
2025-09-18 15:03:56,846 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:56,855 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:56,858 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/837e9160_21_lumper_receipt_11.jpeg
2025-09-18 15:03:56,858 - INFO - Processing image from S3...
2025-09-18 15:03:57,045 - INFO - S3 Image temp/33d4261d_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 15:03:57,045 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:03:57,045 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:03:57,092 - INFO - S3 Image temp/68b015c0_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 15:03:57,093 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:03:57,093 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:03:58,556 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg -> s3://document-extraction-logistically/temp/590bcf11_21_lumper_receipt_2.jpg
2025-09-18 15:03:58,557 - INFO - 🔍 [15:03:58] Starting classification: 21_lumper_receipt_2.jpg
2025-09-18 15:03:58,558 - INFO - ⬆️ [15:03:58] Uploading: 21_lumper_receipt_3.png
2025-09-18 15:03:58,560 - INFO - Initializing TextractProcessor...
2025-09-18 15:03:58,576 - INFO - Initializing BedrockProcessor...
2025-09-18 15:03:58,580 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/590bcf11_21_lumper_receipt_2.jpg
2025-09-18 15:03:58,580 - INFO - Processing image from S3...
2025-09-18 15:03:59,141 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/33d4261d_21_lumper_receipt_1.jpeg
2025-09-18 15:03:59,544 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/68b015c0_21_lumper_receipt_10.png
2025-09-18 15:04:00,373 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png -> s3://document-extraction-logistically/temp/03a1a192_21_lumper_receipt_3.png
2025-09-18 15:04:00,374 - INFO - 🔍 [15:04:00] Starting classification: 21_lumper_receipt_3.png
2025-09-18 15:04:00,375 - INFO - ⬆️ [15:04:00] Uploading: 21_lumper_receipt_4.pdf
2025-09-18 15:04:00,375 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:00,397 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:00,400 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/03a1a192_21_lumper_receipt_3.png
2025-09-18 15:04:00,400 - INFO - Processing image from S3...
2025-09-18 15:04:00,555 - INFO - S3 Image temp/837e9160_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 15:04:00,556 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:00,556 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:00,951 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf -> s3://document-extraction-logistically/temp/2f98adeb_21_lumper_receipt_4.pdf
2025-09-18 15:04:00,952 - INFO - 🔍 [15:04:00] Starting classification: 21_lumper_receipt_4.pdf
2025-09-18 15:04:00,952 - INFO - ⬆️ [15:04:00] Uploading: 21_lumper_receipt_5.pdf
2025-09-18 15:04:00,954 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:00,971 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:00,975 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2f98adeb_21_lumper_receipt_4.pdf
2025-09-18 15:04:00,975 - INFO - Processing PDF from S3...
2025-09-18 15:04:00,975 - INFO - Downloading PDF from S3 to /tmp/tmpef9kwfur/2f98adeb_21_lumper_receipt_4.pdf
2025-09-18 15:04:01,143 - INFO - S3 Image temp/590bcf11_21_lumper_receipt_2.jpg: Extracted 184 characters, 12 lines
2025-09-18 15:04:01,143 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:01,143 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:01,522 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf -> s3://document-extraction-logistically/temp/faee3af5_21_lumper_receipt_5.pdf
2025-09-18 15:04:01,522 - INFO - 🔍 [15:04:01] Starting classification: 21_lumper_receipt_5.pdf
2025-09-18 15:04:01,522 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:01,523 - INFO - ⬆️ [15:04:01] Uploading: 21_lumper_receipt_6.pdf
2025-09-18 15:04:01,531 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:01,569 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/faee3af5_21_lumper_receipt_5.pdf
2025-09-18 15:04:01,569 - INFO - Processing PDF from S3...
2025-09-18 15:04:01,569 - INFO - Downloading PDF from S3 to /tmp/tmpgi5424c4/faee3af5_21_lumper_receipt_5.pdf
2025-09-18 15:04:02,193 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf -> s3://document-extraction-logistically/temp/9bea3d5b_21_lumper_receipt_6.pdf
2025-09-18 15:04:02,193 - INFO - 🔍 [15:04:02] Starting classification: 21_lumper_receipt_6.pdf
2025-09-18 15:04:02,195 - INFO - ⬆️ [15:04:02] Uploading: 21_lumper_receipt_7.png
2025-09-18 15:04:02,195 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:02,210 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:02,213 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9bea3d5b_21_lumper_receipt_6.pdf
2025-09-18 15:04:02,213 - INFO - Processing PDF from S3...
2025-09-18 15:04:02,213 - INFO - Downloading PDF from S3 to /tmp/tmpnrg5fme7/9bea3d5b_21_lumper_receipt_6.pdf
2025-09-18 15:04:02,364 - INFO - Splitting PDF into individual pages...
2025-09-18 15:04:02,365 - INFO - Splitting PDF 2f98adeb_21_lumper_receipt_4 into 1 pages
2025-09-18 15:04:02,369 - INFO - Split PDF into 1 pages
2025-09-18 15:04:02,369 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:04:02,369 - INFO - Expected pages: [1]
2025-09-18 15:04:02,598 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/590bcf11_21_lumper_receipt_2.jpg
2025-09-18 15:04:02,715 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/837e9160_21_lumper_receipt_11.jpeg
2025-09-18 15:04:02,833 - INFO - Splitting PDF into individual pages...
2025-09-18 15:04:02,835 - INFO - Splitting PDF faee3af5_21_lumper_receipt_5 into 1 pages
2025-09-18 15:04:02,836 - INFO - Split PDF into 1 pages
2025-09-18 15:04:02,837 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:04:02,837 - INFO - Expected pages: [1]
2025-09-18 15:04:02,884 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png -> s3://document-extraction-logistically/temp/9377ee68_21_lumper_receipt_7.png
2025-09-18 15:04:02,884 - INFO - 🔍 [15:04:02] Starting classification: 21_lumper_receipt_7.png
2025-09-18 15:04:02,885 - INFO - ⬆️ [15:04:02] Uploading: 21_lumper_receipt_8.pdf
2025-09-18 15:04:02,886 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:02,899 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:02,902 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9377ee68_21_lumper_receipt_7.png
2025-09-18 15:04:02,902 - INFO - Processing image from S3...
2025-09-18 15:04:03,452 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf -> s3://document-extraction-logistically/temp/93c4b69e_21_lumper_receipt_8.pdf
2025-09-18 15:04:03,452 - INFO - 🔍 [15:04:03] Starting classification: 21_lumper_receipt_8.pdf
2025-09-18 15:04:03,453 - INFO - ⬆️ [15:04:03] Uploading: 21_lumper_receipt_9.pdf
2025-09-18 15:04:03,459 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:03,473 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:03,478 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/93c4b69e_21_lumper_receipt_8.pdf
2025-09-18 15:04:03,478 - INFO - Processing PDF from S3...
2025-09-18 15:04:03,478 - INFO - Downloading PDF from S3 to /tmp/tmp1f2_6kct/93c4b69e_21_lumper_receipt_8.pdf
2025-09-18 15:04:04,111 - INFO - Splitting PDF into individual pages...
2025-09-18 15:04:04,112 - INFO - Splitting PDF 9bea3d5b_21_lumper_receipt_6 into 1 pages
2025-09-18 15:04:04,118 - INFO - Split PDF into 1 pages
2025-09-18 15:04:04,118 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:04:04,118 - INFO - Expected pages: [1]
2025-09-18 15:04:04,420 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf -> s3://document-extraction-logistically/temp/2a3f090e_21_lumper_receipt_9.pdf
2025-09-18 15:04:04,421 - INFO - 🔍 [15:04:04] Starting classification: 21_lumper_receipt_9.pdf
2025-09-18 15:04:04,421 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:04,433 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:04,437 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2a3f090e_21_lumper_receipt_9.pdf
2025-09-18 15:04:04,438 - INFO - Processing PDF from S3...
2025-09-18 15:04:04,440 - INFO - Downloading PDF from S3 to /tmp/tmp_0wj9pom/2a3f090e_21_lumper_receipt_9.pdf
2025-09-18 15:04:04,450 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:04,450 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 15:04:04,742 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/33d4261d_21_lumper_receipt_1.jpeg
2025-09-18 15:04:04,752 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:04,752 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 15:04:04,819 - INFO - Splitting PDF into individual pages...
2025-09-18 15:04:04,820 - INFO - Splitting PDF 93c4b69e_21_lumper_receipt_8 into 1 pages
2025-09-18 15:04:04,821 - INFO - Split PDF into 1 pages
2025-09-18 15:04:04,821 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:04:04,821 - INFO - Expected pages: [1]
2025-09-18 15:04:05,041 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/68b015c0_21_lumper_receipt_10.png
2025-09-18 15:04:05,045 - INFO - 

21_lumper_receipt_2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:05,046 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_2.json
2025-09-18 15:04:05,086 - INFO - S3 Image temp/03a1a192_21_lumper_receipt_3.png: Extracted 663 characters, 38 lines
2025-09-18 15:04:05,086 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:05,086 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:05,335 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/590bcf11_21_lumper_receipt_2.jpg
2025-09-18 15:04:05,350 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:04:05,350 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 15:04:05,642 - INFO - Page 1: Extracted 422 characters, 38 lines from 2f98adeb_21_lumper_receipt_4_14181448_page_001.pdf
2025-09-18 15:04:05,642 - INFO - Successfully processed page 1
2025-09-18 15:04:05,642 - INFO - Combined 1 pages into final text
2025-09-18 15:04:05,642 - INFO - Text validation for 2f98adeb_21_lumper_receipt_4: 439 characters, 1 pages
2025-09-18 15:04:05,643 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:05,643 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:05,699 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/837e9160_21_lumper_receipt_11.jpeg
2025-09-18 15:04:05,870 - INFO - Page 1: Extracted 422 characters, 38 lines from faee3af5_21_lumper_receipt_5_2d53d51b_page_001.pdf
2025-09-18 15:04:05,870 - INFO - Successfully processed page 1
2025-09-18 15:04:05,870 - INFO - Combined 1 pages into final text
2025-09-18 15:04:05,871 - INFO - Text validation for faee3af5_21_lumper_receipt_5: 439 characters, 1 pages
2025-09-18 15:04:05,871 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:05,871 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:06,562 - INFO - Splitting PDF into individual pages...
2025-09-18 15:04:06,563 - INFO - Splitting PDF 2a3f090e_21_lumper_receipt_9 into 1 pages
2025-09-18 15:04:06,565 - INFO - Split PDF into 1 pages
2025-09-18 15:04:06,566 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:04:06,566 - INFO - Expected pages: [1]
2025-09-18 15:04:07,018 - INFO - S3 Image temp/9377ee68_21_lumper_receipt_7.png: Extracted 800 characters, 70 lines
2025-09-18 15:04:07,018 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:07,018 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:07,840 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/03a1a192_21_lumper_receipt_3.png
2025-09-18 15:04:07,856 - INFO - 

21_lumper_receipt_3.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:07,856 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_3.json
2025-09-18 15:04:07,898 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2f98adeb_21_lumper_receipt_4.pdf
2025-09-18 15:04:08,152 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/03a1a192_21_lumper_receipt_3.png
2025-09-18 15:04:08,166 - INFO - 

21_lumper_receipt_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:08,166 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_4.json
2025-09-18 15:04:08,179 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/faee3af5_21_lumper_receipt_5.pdf
2025-09-18 15:04:08,478 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2f98adeb_21_lumper_receipt_4.pdf
2025-09-18 15:04:08,489 - INFO - 

21_lumper_receipt_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:08,489 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_5.json
2025-09-18 15:04:08,845 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/faee3af5_21_lumper_receipt_5.pdf
2025-09-18 15:04:08,925 - INFO - Page 1: Extracted 330 characters, 33 lines from 93c4b69e_21_lumper_receipt_8_422cd6a4_page_001.pdf
2025-09-18 15:04:08,925 - INFO - Successfully processed page 1
2025-09-18 15:04:08,926 - INFO - Combined 1 pages into final text
2025-09-18 15:04:08,926 - INFO - Text validation for 93c4b69e_21_lumper_receipt_8: 347 characters, 1 pages
2025-09-18 15:04:08,927 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:08,927 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:09,719 - INFO - Page 1: Extracted 269 characters, 22 lines from 9bea3d5b_21_lumper_receipt_6_12b10b11_page_001.pdf
2025-09-18 15:04:09,720 - INFO - Successfully processed page 1
2025-09-18 15:04:09,720 - INFO - Combined 1 pages into final text
2025-09-18 15:04:09,721 - INFO - Text validation for 9bea3d5b_21_lumper_receipt_6: 286 characters, 1 pages
2025-09-18 15:04:09,721 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:09,721 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:09,882 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9377ee68_21_lumper_receipt_7.png
2025-09-18 15:04:09,906 - INFO - 

21_lumper_receipt_7.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:09,906 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_7.json
2025-09-18 15:04:10,203 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9377ee68_21_lumper_receipt_7.png
2025-09-18 15:04:10,871 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/93c4b69e_21_lumper_receipt_8.pdf
2025-09-18 15:04:10,884 - INFO - 

21_lumper_receipt_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:10,884 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_8.json
2025-09-18 15:04:11,183 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/93c4b69e_21_lumper_receipt_8.pdf
2025-09-18 15:04:11,801 - INFO - Page 1: Extracted 645 characters, 53 lines from 2a3f090e_21_lumper_receipt_9_18174f31_page_001.pdf
2025-09-18 15:04:11,801 - INFO - Successfully processed page 1
2025-09-18 15:04:11,802 - INFO - Combined 1 pages into final text
2025-09-18 15:04:11,802 - INFO - Text validation for 2a3f090e_21_lumper_receipt_9: 662 characters, 1 pages
2025-09-18 15:04:11,802 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:11,802 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:12,157 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9bea3d5b_21_lumper_receipt_6.pdf
2025-09-18 15:04:12,171 - INFO - 

21_lumper_receipt_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:12,171 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_6.json
2025-09-18 15:04:12,469 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9bea3d5b_21_lumper_receipt_6.pdf
2025-09-18 15:04:15,180 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2a3f090e_21_lumper_receipt_9.pdf
2025-09-18 15:04:15,203 - INFO - 

21_lumper_receipt_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:15,203 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_9.json
2025-09-18 15:04:15,560 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2a3f090e_21_lumper_receipt_9.pdf
2025-09-18 15:04:15,561 - INFO - 
📊 Processing Summary:
2025-09-18 15:04:15,561 - INFO -    Total files: 11
2025-09-18 15:04:15,561 - INFO -    Successful: 11
2025-09-18 15:04:15,561 - INFO -    Failed: 0
2025-09-18 15:04:15,562 - INFO -    Duration: 25.02 seconds
2025-09-18 15:04:15,562 - INFO -    Output directory: output
2025-09-18 15:04:15,562 - INFO - 
📋 Successfully Processed Files:
2025-09-18 15:04:15,562 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:15,562 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:15,562 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:04:15,562 - INFO -    📄 21_lumper_receipt_2.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:15,562 - INFO -    📄 21_lumper_receipt_3.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:15,562 - INFO -    📄 21_lumper_receipt_4.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:15,562 - INFO -    📄 21_lumper_receipt_5.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:15,562 - INFO -    📄 21_lumper_receipt_6.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:15,563 - INFO -    📄 21_lumper_receipt_7.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:15,563 - INFO -    📄 21_lumper_receipt_8.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:15,563 - INFO -    📄 21_lumper_receipt_9.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:15,563 - INFO - 
============================================================================================================================================
2025-09-18 15:04:15,563 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 15:04:15,564 - INFO - ============================================================================================================================================
2025-09-18 15:04:15,564 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 15:04:15,564 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:04:15,564 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 15:04:15,564 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 15:04:15,564 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 15:04:15,564 - INFO - 
2025-09-18 15:04:15,564 - INFO - 21_lumper_receipt_10.png                           1      lumper_receipt       run1_21_lumper_receipt_10.json                    
2025-09-18 15:04:15,564 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 15:04:15,564 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 15:04:15,564 - INFO - 
2025-09-18 15:04:15,564 - INFO - 21_lumper_receipt_11.jpeg                          1      invoice              run1_21_lumper_receipt_11.json                    
2025-09-18 15:04:15,564 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 15:04:15,564 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 15:04:15,564 - INFO - 
2025-09-18 15:04:15,564 - INFO - 21_lumper_receipt_2.jpg                            1      lumper_receipt       run1_21_lumper_receipt_2.json                     
2025-09-18 15:04:15,565 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg
2025-09-18 15:04:15,565 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_2.json
2025-09-18 15:04:15,565 - INFO - 
2025-09-18 15:04:15,565 - INFO - 21_lumper_receipt_3.png                            1      lumper_receipt       run1_21_lumper_receipt_3.json                     
2025-09-18 15:04:15,565 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png
2025-09-18 15:04:15,565 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_3.json
2025-09-18 15:04:15,565 - INFO - 
2025-09-18 15:04:15,565 - INFO - 21_lumper_receipt_4.pdf                            1      lumper_receipt       run1_21_lumper_receipt_4.json                     
2025-09-18 15:04:15,565 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf
2025-09-18 15:04:15,565 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_4.json
2025-09-18 15:04:15,565 - INFO - 
2025-09-18 15:04:15,565 - INFO - 21_lumper_receipt_5.pdf                            1      lumper_receipt       run1_21_lumper_receipt_5.json                     
2025-09-18 15:04:15,565 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf
2025-09-18 15:04:15,565 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_5.json
2025-09-18 15:04:15,565 - INFO - 
2025-09-18 15:04:15,565 - INFO - 21_lumper_receipt_6.pdf                            1      lumper_receipt       run1_21_lumper_receipt_6.json                     
2025-09-18 15:04:15,565 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf
2025-09-18 15:04:15,565 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_6.json
2025-09-18 15:04:15,566 - INFO - 
2025-09-18 15:04:15,566 - INFO - 21_lumper_receipt_7.png                            1      lumper_receipt       run1_21_lumper_receipt_7.json                     
2025-09-18 15:04:15,566 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png
2025-09-18 15:04:15,566 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_7.json
2025-09-18 15:04:15,566 - INFO - 
2025-09-18 15:04:15,566 - INFO - 21_lumper_receipt_8.pdf                            1      lumper_receipt       run1_21_lumper_receipt_8.json                     
2025-09-18 15:04:15,566 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf
2025-09-18 15:04:15,566 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_8.json
2025-09-18 15:04:15,566 - INFO - 
2025-09-18 15:04:15,566 - INFO - 21_lumper_receipt_9.pdf                            1      lumper_receipt       run1_21_lumper_receipt_9.json                     
2025-09-18 15:04:15,566 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf
2025-09-18 15:04:15,566 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_9.json
2025-09-18 15:04:15,566 - INFO - 
2025-09-18 15:04:15,566 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:04:15,566 - INFO - Total entries: 11
2025-09-18 15:04:15,566 - INFO - ============================================================================================================================================
2025-09-18 15:04:15,566 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 15:04:15,566 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:04:15,566 - INFO -   1. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 15:04:15,566 - INFO -   2. 21_lumper_receipt_10.png            Page 1   → lumper_receipt  | run1_21_lumper_receipt_10.json
2025-09-18 15:04:15,567 - INFO -   3. 21_lumper_receipt_11.jpeg           Page 1   → invoice         | run1_21_lumper_receipt_11.json
2025-09-18 15:04:15,567 - INFO -   4. 21_lumper_receipt_2.jpg             Page 1   → lumper_receipt  | run1_21_lumper_receipt_2.json
2025-09-18 15:04:15,567 - INFO -   5. 21_lumper_receipt_3.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_3.json
2025-09-18 15:04:15,567 - INFO -   6. 21_lumper_receipt_4.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_4.json
2025-09-18 15:04:15,567 - INFO -   7. 21_lumper_receipt_5.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_5.json
2025-09-18 15:04:15,567 - INFO -   8. 21_lumper_receipt_6.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_6.json
2025-09-18 15:04:15,567 - INFO -   9. 21_lumper_receipt_7.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_7.json
2025-09-18 15:04:15,567 - INFO -  10. 21_lumper_receipt_8.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_8.json
2025-09-18 15:04:15,567 - INFO -  11. 21_lumper_receipt_9.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_9.json
2025-09-18 15:04:15,567 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:04:15,567 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 25.023106, 'processed_files': [{'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '21_lumper_receipt_2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg'}, {'filename': '21_lumper_receipt_3.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png'}, {'filename': '21_lumper_receipt_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf'}, {'filename': '21_lumper_receipt_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf'}, {'filename': '21_lumper_receipt_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf'}, {'filename': '21_lumper_receipt_7.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png'}, {'filename': '21_lumper_receipt_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf'}, {'filename': '21_lumper_receipt_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf'}]}
