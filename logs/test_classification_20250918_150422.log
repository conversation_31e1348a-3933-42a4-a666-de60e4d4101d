2025-09-18 15:04:22,305 - INFO - Logging initialized. Log file: logs/test_classification_20250918_150422.log
2025-09-18 15:04:22,305 - INFO - 📁 Found 11 files to process
2025-09-18 15:04:22,305 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 15:04:22,305 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 15:04:22,306 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 15:04:22,306 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 15:04:22,306 - INFO - ⬆️ [15:04:22] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 15:04:24,904 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/ad1fd2fe_21_lumper_receipt_1.jpeg
2025-09-18 15:04:24,904 - INFO - 🔍 [15:04:24] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 15:04:24,904 - INFO - ⬆️ [15:04:24] Uploading: 21_lumper_receipt_10.png
2025-09-18 15:04:24,905 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:24,921 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:24,926 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ad1fd2fe_21_lumper_receipt_1.jpeg
2025-09-18 15:04:24,926 - INFO - Processing image from S3...
2025-09-18 15:04:25,831 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/9c82660d_21_lumper_receipt_10.png
2025-09-18 15:04:25,832 - INFO - 🔍 [15:04:25] Starting classification: 21_lumper_receipt_10.png
2025-09-18 15:04:25,833 - INFO - ⬆️ [15:04:25] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 15:04:25,833 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:25,851 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:25,855 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9c82660d_21_lumper_receipt_10.png
2025-09-18 15:04:25,855 - INFO - Processing image from S3...
2025-09-18 15:04:28,074 - INFO - S3 Image temp/ad1fd2fe_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 15:04:28,074 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:28,074 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:28,916 - INFO - S3 Image temp/9c82660d_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 15:04:28,916 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:28,916 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:29,059 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/b2d2a021_21_lumper_receipt_11.jpeg
2025-09-18 15:04:29,059 - INFO - 🔍 [15:04:29] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 15:04:29,060 - INFO - ⬆️ [15:04:29] Uploading: 21_lumper_receipt_2.jpg
2025-09-18 15:04:29,061 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:29,077 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:29,081 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b2d2a021_21_lumper_receipt_11.jpeg
2025-09-18 15:04:29,081 - INFO - Processing image from S3...
2025-09-18 15:04:30,634 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg -> s3://document-extraction-logistically/temp/568fe3c3_21_lumper_receipt_2.jpg
2025-09-18 15:04:30,634 - INFO - 🔍 [15:04:30] Starting classification: 21_lumper_receipt_2.jpg
2025-09-18 15:04:30,635 - INFO - ⬆️ [15:04:30] Uploading: 21_lumper_receipt_3.png
2025-09-18 15:04:30,636 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:30,658 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:30,663 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/568fe3c3_21_lumper_receipt_2.jpg
2025-09-18 15:04:30,664 - INFO - Processing image from S3...
2025-09-18 15:04:30,793 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ad1fd2fe_21_lumper_receipt_1.jpeg
2025-09-18 15:04:31,411 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9c82660d_21_lumper_receipt_10.png
2025-09-18 15:04:32,178 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png -> s3://document-extraction-logistically/temp/58c7e2cc_21_lumper_receipt_3.png
2025-09-18 15:04:32,179 - INFO - 🔍 [15:04:32] Starting classification: 21_lumper_receipt_3.png
2025-09-18 15:04:32,180 - INFO - ⬆️ [15:04:32] Uploading: 21_lumper_receipt_4.pdf
2025-09-18 15:04:32,180 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:32,201 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:32,205 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/58c7e2cc_21_lumper_receipt_3.png
2025-09-18 15:04:32,206 - INFO - Processing image from S3...
2025-09-18 15:04:32,382 - INFO - S3 Image temp/b2d2a021_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 15:04:32,382 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:32,382 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:32,757 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf -> s3://document-extraction-logistically/temp/3ff2e5dd_21_lumper_receipt_4.pdf
2025-09-18 15:04:32,757 - INFO - 🔍 [15:04:32] Starting classification: 21_lumper_receipt_4.pdf
2025-09-18 15:04:32,757 - INFO - ⬆️ [15:04:32] Uploading: 21_lumper_receipt_5.pdf
2025-09-18 15:04:32,758 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:32,765 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:32,768 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3ff2e5dd_21_lumper_receipt_4.pdf
2025-09-18 15:04:32,768 - INFO - Processing PDF from S3...
2025-09-18 15:04:32,768 - INFO - Downloading PDF from S3 to /tmp/tmpet71lojt/3ff2e5dd_21_lumper_receipt_4.pdf
2025-09-18 15:04:33,014 - INFO - S3 Image temp/568fe3c3_21_lumper_receipt_2.jpg: Extracted 184 characters, 12 lines
2025-09-18 15:04:33,014 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:33,014 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:33,329 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf -> s3://document-extraction-logistically/temp/fe46e9b7_21_lumper_receipt_5.pdf
2025-09-18 15:04:33,330 - INFO - 🔍 [15:04:33] Starting classification: 21_lumper_receipt_5.pdf
2025-09-18 15:04:33,331 - INFO - ⬆️ [15:04:33] Uploading: 21_lumper_receipt_6.pdf
2025-09-18 15:04:33,333 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:33,348 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:33,352 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fe46e9b7_21_lumper_receipt_5.pdf
2025-09-18 15:04:33,352 - INFO - Processing PDF from S3...
2025-09-18 15:04:33,353 - INFO - Downloading PDF from S3 to /tmp/tmp4tgz168u/fe46e9b7_21_lumper_receipt_5.pdf
2025-09-18 15:04:33,971 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf -> s3://document-extraction-logistically/temp/1acd1902_21_lumper_receipt_6.pdf
2025-09-18 15:04:33,971 - INFO - 🔍 [15:04:33] Starting classification: 21_lumper_receipt_6.pdf
2025-09-18 15:04:33,972 - INFO - ⬆️ [15:04:33] Uploading: 21_lumper_receipt_7.png
2025-09-18 15:04:33,977 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:33,988 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:33,994 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1acd1902_21_lumper_receipt_6.pdf
2025-09-18 15:04:33,994 - INFO - Processing PDF from S3...
2025-09-18 15:04:33,995 - INFO - Downloading PDF from S3 to /tmp/tmphlr9lu8r/1acd1902_21_lumper_receipt_6.pdf
2025-09-18 15:04:34,027 - INFO - Splitting PDF into individual pages...
2025-09-18 15:04:34,029 - INFO - Splitting PDF 3ff2e5dd_21_lumper_receipt_4 into 1 pages
2025-09-18 15:04:34,032 - INFO - Split PDF into 1 pages
2025-09-18 15:04:34,033 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:04:34,033 - INFO - Expected pages: [1]
2025-09-18 15:04:34,614 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png -> s3://document-extraction-logistically/temp/a5bee2b9_21_lumper_receipt_7.png
2025-09-18 15:04:34,614 - INFO - 🔍 [15:04:34] Starting classification: 21_lumper_receipt_7.png
2025-09-18 15:04:34,615 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:34,616 - INFO - ⬆️ [15:04:34] Uploading: 21_lumper_receipt_8.pdf
2025-09-18 15:04:34,629 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:34,638 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a5bee2b9_21_lumper_receipt_7.png
2025-09-18 15:04:34,639 - INFO - Processing image from S3...
2025-09-18 15:04:34,643 - INFO - Splitting PDF into individual pages...
2025-09-18 15:04:34,644 - INFO - Splitting PDF fe46e9b7_21_lumper_receipt_5 into 1 pages
2025-09-18 15:04:34,646 - INFO - Split PDF into 1 pages
2025-09-18 15:04:34,646 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:04:34,646 - INFO - Expected pages: [1]
2025-09-18 15:04:34,741 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/568fe3c3_21_lumper_receipt_2.jpg
2025-09-18 15:04:35,202 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf -> s3://document-extraction-logistically/temp/2f73953b_21_lumper_receipt_8.pdf
2025-09-18 15:04:35,203 - INFO - 🔍 [15:04:35] Starting classification: 21_lumper_receipt_8.pdf
2025-09-18 15:04:35,204 - INFO - ⬆️ [15:04:35] Uploading: 21_lumper_receipt_9.pdf
2025-09-18 15:04:35,206 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:35,230 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:35,232 - INFO - S3 Image temp/58c7e2cc_21_lumper_receipt_3.png: Extracted 663 characters, 38 lines
2025-09-18 15:04:35,236 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:35,236 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:35,242 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2f73953b_21_lumper_receipt_8.pdf
2025-09-18 15:04:35,244 - INFO - Processing PDF from S3...
2025-09-18 15:04:35,244 - INFO - Downloading PDF from S3 to /tmp/tmp2dyenh1j/2f73953b_21_lumper_receipt_8.pdf
2025-09-18 15:04:35,296 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b2d2a021_21_lumper_receipt_11.jpeg
2025-09-18 15:04:35,797 - INFO - Splitting PDF into individual pages...
2025-09-18 15:04:35,798 - INFO - Splitting PDF 1acd1902_21_lumper_receipt_6 into 1 pages
2025-09-18 15:04:35,804 - INFO - Split PDF into 1 pages
2025-09-18 15:04:35,804 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:04:35,804 - INFO - Expected pages: [1]
2025-09-18 15:04:36,105 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf -> s3://document-extraction-logistically/temp/fd48a1df_21_lumper_receipt_9.pdf
2025-09-18 15:04:36,106 - INFO - 🔍 [15:04:36] Starting classification: 21_lumper_receipt_9.pdf
2025-09-18 15:04:36,108 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:36,127 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:36,134 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fd48a1df_21_lumper_receipt_9.pdf
2025-09-18 15:04:36,135 - INFO - Processing PDF from S3...
2025-09-18 15:04:36,138 - INFO - Downloading PDF from S3 to /tmp/tmp18l9fph9/fd48a1df_21_lumper_receipt_9.pdf
2025-09-18 15:04:36,140 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:36,144 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 15:04:36,448 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ad1fd2fe_21_lumper_receipt_1.jpeg
2025-09-18 15:04:36,463 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:36,463 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 15:04:36,518 - INFO - Splitting PDF into individual pages...
2025-09-18 15:04:36,518 - INFO - Splitting PDF 2f73953b_21_lumper_receipt_8 into 1 pages
2025-09-18 15:04:36,519 - INFO - Split PDF into 1 pages
2025-09-18 15:04:36,519 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:04:36,519 - INFO - Expected pages: [1]
2025-09-18 15:04:36,756 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9c82660d_21_lumper_receipt_10.png
2025-09-18 15:04:36,762 - INFO - 

21_lumper_receipt_2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:36,762 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_2.json
2025-09-18 15:04:37,061 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/568fe3c3_21_lumper_receipt_2.jpg
2025-09-18 15:04:37,077 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:37,077 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 15:04:37,104 - INFO - Page 1: Extracted 422 characters, 38 lines from 3ff2e5dd_21_lumper_receipt_4_9001a0e9_page_001.pdf
2025-09-18 15:04:37,108 - INFO - Successfully processed page 1
2025-09-18 15:04:37,116 - INFO - Combined 1 pages into final text
2025-09-18 15:04:37,117 - INFO - Text validation for 3ff2e5dd_21_lumper_receipt_4: 439 characters, 1 pages
2025-09-18 15:04:37,118 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:37,118 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:37,373 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b2d2a021_21_lumper_receipt_11.jpeg
2025-09-18 15:04:37,380 - INFO - Page 1: Extracted 422 characters, 38 lines from fe46e9b7_21_lumper_receipt_5_95023cdf_page_001.pdf
2025-09-18 15:04:37,381 - INFO - Successfully processed page 1
2025-09-18 15:04:37,381 - INFO - Combined 1 pages into final text
2025-09-18 15:04:37,381 - INFO - Text validation for fe46e9b7_21_lumper_receipt_5: 439 characters, 1 pages
2025-09-18 15:04:37,382 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:37,382 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:37,502 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/58c7e2cc_21_lumper_receipt_3.png
2025-09-18 15:04:37,506 - INFO - S3 Image temp/a5bee2b9_21_lumper_receipt_7.png: Extracted 800 characters, 70 lines
2025-09-18 15:04:37,506 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:37,507 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:37,519 - INFO - 

21_lumper_receipt_3.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:37,519 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_3.json
2025-09-18 15:04:37,819 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/58c7e2cc_21_lumper_receipt_3.png
2025-09-18 15:04:38,235 - INFO - Splitting PDF into individual pages...
2025-09-18 15:04:38,236 - INFO - Splitting PDF fd48a1df_21_lumper_receipt_9 into 1 pages
2025-09-18 15:04:38,238 - INFO - Split PDF into 1 pages
2025-09-18 15:04:38,238 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:04:38,238 - INFO - Expected pages: [1]
2025-09-18 15:04:39,100 - INFO - Page 1: Extracted 330 characters, 33 lines from 2f73953b_21_lumper_receipt_8_a80b42c9_page_001.pdf
2025-09-18 15:04:39,100 - INFO - Successfully processed page 1
2025-09-18 15:04:39,100 - INFO - Combined 1 pages into final text
2025-09-18 15:04:39,100 - INFO - Text validation for 2f73953b_21_lumper_receipt_8: 347 characters, 1 pages
2025-09-18 15:04:39,101 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:39,101 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:39,737 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3ff2e5dd_21_lumper_receipt_4.pdf
2025-09-18 15:04:39,754 - INFO - 

21_lumper_receipt_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:39,754 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_4.json
2025-09-18 15:04:40,025 - INFO - Page 1: Extracted 269 characters, 22 lines from 1acd1902_21_lumper_receipt_6_2395146c_page_001.pdf
2025-09-18 15:04:40,025 - INFO - Successfully processed page 1
2025-09-18 15:04:40,025 - INFO - Combined 1 pages into final text
2025-09-18 15:04:40,026 - INFO - Text validation for 1acd1902_21_lumper_receipt_6: 286 characters, 1 pages
2025-09-18 15:04:40,026 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:40,026 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:40,051 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3ff2e5dd_21_lumper_receipt_4.pdf
2025-09-18 15:04:41,070 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a5bee2b9_21_lumper_receipt_7.png
2025-09-18 15:04:41,092 - INFO - 

21_lumper_receipt_7.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:41,092 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_7.json
2025-09-18 15:04:41,243 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2f73953b_21_lumper_receipt_8.pdf
2025-09-18 15:04:41,393 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a5bee2b9_21_lumper_receipt_7.png
2025-09-18 15:04:41,406 - INFO - 

21_lumper_receipt_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:41,406 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_8.json
2025-09-18 15:04:41,706 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2f73953b_21_lumper_receipt_8.pdf
2025-09-18 15:04:42,121 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1acd1902_21_lumper_receipt_6.pdf
2025-09-18 15:04:42,133 - INFO - 

21_lumper_receipt_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:42,133 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_6.json
2025-09-18 15:04:42,395 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fe46e9b7_21_lumper_receipt_5.pdf
2025-09-18 15:04:42,430 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1acd1902_21_lumper_receipt_6.pdf
2025-09-18 15:04:42,446 - INFO - 

21_lumper_receipt_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:42,446 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_5.json
2025-09-18 15:04:42,749 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fe46e9b7_21_lumper_receipt_5.pdf
2025-09-18 15:04:43,155 - INFO - Page 1: Extracted 645 characters, 53 lines from fd48a1df_21_lumper_receipt_9_01b2f83f_page_001.pdf
2025-09-18 15:04:43,155 - INFO - Successfully processed page 1
2025-09-18 15:04:43,155 - INFO - Combined 1 pages into final text
2025-09-18 15:04:43,156 - INFO - Text validation for fd48a1df_21_lumper_receipt_9: 662 characters, 1 pages
2025-09-18 15:04:43,156 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:43,156 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:46,080 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fd48a1df_21_lumper_receipt_9.pdf
2025-09-18 15:04:46,095 - INFO - 

21_lumper_receipt_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:04:46,096 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_9.json
2025-09-18 15:04:46,388 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fd48a1df_21_lumper_receipt_9.pdf
2025-09-18 15:04:46,389 - INFO - 
📊 Processing Summary:
2025-09-18 15:04:46,389 - INFO -    Total files: 11
2025-09-18 15:04:46,389 - INFO -    Successful: 11
2025-09-18 15:04:46,389 - INFO -    Failed: 0
2025-09-18 15:04:46,390 - INFO -    Duration: 24.08 seconds
2025-09-18 15:04:46,390 - INFO -    Output directory: output
2025-09-18 15:04:46,390 - INFO - 
📋 Successfully Processed Files:
2025-09-18 15:04:46,390 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:46,390 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:46,390 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:46,390 - INFO -    📄 21_lumper_receipt_2.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:46,390 - INFO -    📄 21_lumper_receipt_3.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:46,390 - INFO -    📄 21_lumper_receipt_4.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:46,390 - INFO -    📄 21_lumper_receipt_5.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:46,390 - INFO -    📄 21_lumper_receipt_6.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:46,390 - INFO -    📄 21_lumper_receipt_7.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:46,390 - INFO -    📄 21_lumper_receipt_8.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:46,390 - INFO -    📄 21_lumper_receipt_9.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:04:46,391 - INFO - 
============================================================================================================================================
2025-09-18 15:04:46,391 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 15:04:46,391 - INFO - ============================================================================================================================================
2025-09-18 15:04:46,391 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 15:04:46,391 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:04:46,391 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 15:04:46,391 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 15:04:46,391 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 15:04:46,391 - INFO - 
2025-09-18 15:04:46,392 - INFO - 21_lumper_receipt_10.png                           1      lumper_receipt       run1_21_lumper_receipt_10.json                    
2025-09-18 15:04:46,392 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 15:04:46,392 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 15:04:46,392 - INFO - 
2025-09-18 15:04:46,392 - INFO - 21_lumper_receipt_11.jpeg                          1      lumper_receipt       run1_21_lumper_receipt_11.json                    
2025-09-18 15:04:46,392 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 15:04:46,392 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 15:04:46,392 - INFO - 
2025-09-18 15:04:46,392 - INFO - 21_lumper_receipt_2.jpg                            1      lumper_receipt       run1_21_lumper_receipt_2.json                     
2025-09-18 15:04:46,392 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg
2025-09-18 15:04:46,392 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_2.json
2025-09-18 15:04:46,392 - INFO - 
2025-09-18 15:04:46,392 - INFO - 21_lumper_receipt_3.png                            1      lumper_receipt       run1_21_lumper_receipt_3.json                     
2025-09-18 15:04:46,392 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png
2025-09-18 15:04:46,392 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_3.json
2025-09-18 15:04:46,392 - INFO - 
2025-09-18 15:04:46,392 - INFO - 21_lumper_receipt_4.pdf                            1      lumper_receipt       run1_21_lumper_receipt_4.json                     
2025-09-18 15:04:46,392 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf
2025-09-18 15:04:46,392 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_4.json
2025-09-18 15:04:46,392 - INFO - 
2025-09-18 15:04:46,393 - INFO - 21_lumper_receipt_5.pdf                            1      lumper_receipt       run1_21_lumper_receipt_5.json                     
2025-09-18 15:04:46,393 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf
2025-09-18 15:04:46,393 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_5.json
2025-09-18 15:04:46,393 - INFO - 
2025-09-18 15:04:46,393 - INFO - 21_lumper_receipt_6.pdf                            1      lumper_receipt       run1_21_lumper_receipt_6.json                     
2025-09-18 15:04:46,393 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf
2025-09-18 15:04:46,393 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_6.json
2025-09-18 15:04:46,393 - INFO - 
2025-09-18 15:04:46,393 - INFO - 21_lumper_receipt_7.png                            1      lumper_receipt       run1_21_lumper_receipt_7.json                     
2025-09-18 15:04:46,393 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png
2025-09-18 15:04:46,393 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_7.json
2025-09-18 15:04:46,393 - INFO - 
2025-09-18 15:04:46,393 - INFO - 21_lumper_receipt_8.pdf                            1      lumper_receipt       run1_21_lumper_receipt_8.json                     
2025-09-18 15:04:46,393 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf
2025-09-18 15:04:46,393 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_8.json
2025-09-18 15:04:46,393 - INFO - 
2025-09-18 15:04:46,393 - INFO - 21_lumper_receipt_9.pdf                            1      lumper_receipt       run1_21_lumper_receipt_9.json                     
2025-09-18 15:04:46,393 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf
2025-09-18 15:04:46,393 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_9.json
2025-09-18 15:04:46,393 - INFO - 
2025-09-18 15:04:46,394 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:04:46,394 - INFO - Total entries: 11
2025-09-18 15:04:46,394 - INFO - ============================================================================================================================================
2025-09-18 15:04:46,394 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 15:04:46,394 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:04:46,394 - INFO -   1. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 15:04:46,394 - INFO -   2. 21_lumper_receipt_10.png            Page 1   → lumper_receipt  | run1_21_lumper_receipt_10.json
2025-09-18 15:04:46,394 - INFO -   3. 21_lumper_receipt_11.jpeg           Page 1   → lumper_receipt  | run1_21_lumper_receipt_11.json
2025-09-18 15:04:46,394 - INFO -   4. 21_lumper_receipt_2.jpg             Page 1   → lumper_receipt  | run1_21_lumper_receipt_2.json
2025-09-18 15:04:46,394 - INFO -   5. 21_lumper_receipt_3.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_3.json
2025-09-18 15:04:46,394 - INFO -   6. 21_lumper_receipt_4.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_4.json
2025-09-18 15:04:46,394 - INFO -   7. 21_lumper_receipt_5.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_5.json
2025-09-18 15:04:46,394 - INFO -   8. 21_lumper_receipt_6.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_6.json
2025-09-18 15:04:46,394 - INFO -   9. 21_lumper_receipt_7.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_7.json
2025-09-18 15:04:46,394 - INFO -  10. 21_lumper_receipt_8.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_8.json
2025-09-18 15:04:46,394 - INFO -  11. 21_lumper_receipt_9.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_9.json
2025-09-18 15:04:46,394 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:04:46,394 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 24.083609, 'processed_files': [{'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '21_lumper_receipt_2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg'}, {'filename': '21_lumper_receipt_3.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png'}, {'filename': '21_lumper_receipt_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf'}, {'filename': '21_lumper_receipt_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf'}, {'filename': '21_lumper_receipt_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf'}, {'filename': '21_lumper_receipt_7.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png'}, {'filename': '21_lumper_receipt_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf'}, {'filename': '21_lumper_receipt_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf'}]}
