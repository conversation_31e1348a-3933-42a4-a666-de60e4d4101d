2025-09-18 15:04:52,741 - INFO - Logging initialized. Log file: logs/test_classification_20250918_150452.log
2025-09-18 15:04:52,742 - INFO - 📁 Found 11 files to process
2025-09-18 15:04:52,742 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 15:04:52,742 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 15:04:52,742 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 15:04:52,742 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 15:04:52,742 - INFO - ⬆️ [15:04:52] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 15:04:55,220 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/92c0c8b1_21_lumper_receipt_1.jpeg
2025-09-18 15:04:55,220 - INFO - 🔍 [15:04:55] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 15:04:55,221 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:55,222 - INFO - ⬆️ [15:04:55] Uploading: 21_lumper_receipt_10.png
2025-09-18 15:04:55,244 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:55,250 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/92c0c8b1_21_lumper_receipt_1.jpeg
2025-09-18 15:04:55,250 - INFO - Processing image from S3...
2025-09-18 15:04:56,242 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/17bffb3c_21_lumper_receipt_10.png
2025-09-18 15:04:56,242 - INFO - 🔍 [15:04:56] Starting classification: 21_lumper_receipt_10.png
2025-09-18 15:04:56,243 - INFO - ⬆️ [15:04:56] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 15:04:56,243 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:56,259 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:56,263 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/17bffb3c_21_lumper_receipt_10.png
2025-09-18 15:04:56,263 - INFO - Processing image from S3...
2025-09-18 15:04:58,133 - INFO - S3 Image temp/92c0c8b1_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 15:04:58,133 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:58,133 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:04:58,755 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/e6da8236_21_lumper_receipt_11.jpeg
2025-09-18 15:04:58,755 - INFO - 🔍 [15:04:58] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 15:04:58,756 - INFO - ⬆️ [15:04:58] Uploading: 21_lumper_receipt_2.jpg
2025-09-18 15:04:58,758 - INFO - Initializing TextractProcessor...
2025-09-18 15:04:58,776 - INFO - Initializing BedrockProcessor...
2025-09-18 15:04:58,778 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e6da8236_21_lumper_receipt_11.jpeg
2025-09-18 15:04:58,778 - INFO - Processing image from S3...
2025-09-18 15:04:59,213 - INFO - S3 Image temp/17bffb3c_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 15:04:59,214 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:04:59,214 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:05:00,320 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg -> s3://document-extraction-logistically/temp/4df15875_21_lumper_receipt_2.jpg
2025-09-18 15:05:00,320 - INFO - 🔍 [15:05:00] Starting classification: 21_lumper_receipt_2.jpg
2025-09-18 15:05:00,321 - INFO - ⬆️ [15:05:00] Uploading: 21_lumper_receipt_3.png
2025-09-18 15:05:00,323 - INFO - Initializing TextractProcessor...
2025-09-18 15:05:00,341 - INFO - Initializing BedrockProcessor...
2025-09-18 15:05:00,344 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4df15875_21_lumper_receipt_2.jpg
2025-09-18 15:05:00,344 - INFO - Processing image from S3...
2025-09-18 15:05:00,509 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/92c0c8b1_21_lumper_receipt_1.jpeg
2025-09-18 15:05:01,788 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png -> s3://document-extraction-logistically/temp/6f894b2a_21_lumper_receipt_3.png
2025-09-18 15:05:01,788 - INFO - 🔍 [15:05:01] Starting classification: 21_lumper_receipt_3.png
2025-09-18 15:05:01,789 - INFO - ⬆️ [15:05:01] Uploading: 21_lumper_receipt_4.pdf
2025-09-18 15:05:01,795 - INFO - Initializing TextractProcessor...
2025-09-18 15:05:01,810 - INFO - Initializing BedrockProcessor...
2025-09-18 15:05:01,816 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6f894b2a_21_lumper_receipt_3.png
2025-09-18 15:05:01,817 - INFO - Processing image from S3...
2025-09-18 15:05:01,825 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/17bffb3c_21_lumper_receipt_10.png
2025-09-18 15:05:02,024 - INFO - S3 Image temp/e6da8236_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 15:05:02,025 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:05:02,025 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:05:02,357 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf -> s3://document-extraction-logistically/temp/81f9fb4d_21_lumper_receipt_4.pdf
2025-09-18 15:05:02,357 - INFO - 🔍 [15:05:02] Starting classification: 21_lumper_receipt_4.pdf
2025-09-18 15:05:02,358 - INFO - ⬆️ [15:05:02] Uploading: 21_lumper_receipt_5.pdf
2025-09-18 15:05:02,360 - INFO - Initializing TextractProcessor...
2025-09-18 15:05:02,378 - INFO - Initializing BedrockProcessor...
2025-09-18 15:05:02,382 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/81f9fb4d_21_lumper_receipt_4.pdf
2025-09-18 15:05:02,382 - INFO - Processing PDF from S3...
2025-09-18 15:05:02,383 - INFO - Downloading PDF from S3 to /tmp/tmputupe55d/81f9fb4d_21_lumper_receipt_4.pdf
2025-09-18 15:05:02,947 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf -> s3://document-extraction-logistically/temp/aceeaea9_21_lumper_receipt_5.pdf
2025-09-18 15:05:02,948 - INFO - 🔍 [15:05:02] Starting classification: 21_lumper_receipt_5.pdf
2025-09-18 15:05:02,949 - INFO - ⬆️ [15:05:02] Uploading: 21_lumper_receipt_6.pdf
2025-09-18 15:05:02,949 - INFO - Initializing TextractProcessor...
2025-09-18 15:05:02,968 - INFO - Initializing BedrockProcessor...
2025-09-18 15:05:02,973 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/aceeaea9_21_lumper_receipt_5.pdf
2025-09-18 15:05:02,974 - INFO - Processing PDF from S3...
2025-09-18 15:05:02,974 - INFO - Downloading PDF from S3 to /tmp/tmpe89b_whr/aceeaea9_21_lumper_receipt_5.pdf
2025-09-18 15:05:02,983 - INFO - S3 Image temp/4df15875_21_lumper_receipt_2.jpg: Extracted 184 characters, 12 lines
2025-09-18 15:05:02,983 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:05:02,984 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:05:03,574 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf -> s3://document-extraction-logistically/temp/ba31a1e0_21_lumper_receipt_6.pdf
2025-09-18 15:05:03,575 - INFO - 🔍 [15:05:03] Starting classification: 21_lumper_receipt_6.pdf
2025-09-18 15:05:03,576 - INFO - ⬆️ [15:05:03] Uploading: 21_lumper_receipt_7.png
2025-09-18 15:05:03,576 - INFO - Initializing TextractProcessor...
2025-09-18 15:05:03,628 - INFO - Initializing BedrockProcessor...
2025-09-18 15:05:03,630 - INFO - Splitting PDF into individual pages...
2025-09-18 15:05:03,630 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ba31a1e0_21_lumper_receipt_6.pdf
2025-09-18 15:05:03,631 - INFO - Processing PDF from S3...
2025-09-18 15:05:03,631 - INFO - Splitting PDF 81f9fb4d_21_lumper_receipt_4 into 1 pages
2025-09-18 15:05:03,631 - INFO - Downloading PDF from S3 to /tmp/tmpf0juetdy/ba31a1e0_21_lumper_receipt_6.pdf
2025-09-18 15:05:03,635 - INFO - Split PDF into 1 pages
2025-09-18 15:05:03,635 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:05:03,635 - INFO - Expected pages: [1]
2025-09-18 15:05:03,910 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e6da8236_21_lumper_receipt_11.jpeg
2025-09-18 15:05:04,238 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png -> s3://document-extraction-logistically/temp/e51cc17f_21_lumper_receipt_7.png
2025-09-18 15:05:04,239 - INFO - 🔍 [15:05:04] Starting classification: 21_lumper_receipt_7.png
2025-09-18 15:05:04,240 - INFO - ⬆️ [15:05:04] Uploading: 21_lumper_receipt_8.pdf
2025-09-18 15:05:04,242 - INFO - Initializing TextractProcessor...
2025-09-18 15:05:04,259 - INFO - Initializing BedrockProcessor...
2025-09-18 15:05:04,263 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e51cc17f_21_lumper_receipt_7.png
2025-09-18 15:05:04,264 - INFO - Processing image from S3...
2025-09-18 15:05:04,272 - INFO - Splitting PDF into individual pages...
2025-09-18 15:05:04,273 - INFO - Splitting PDF aceeaea9_21_lumper_receipt_5 into 1 pages
2025-09-18 15:05:04,275 - INFO - Split PDF into 1 pages
2025-09-18 15:05:04,275 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:05:04,275 - INFO - Expected pages: [1]
2025-09-18 15:05:04,460 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4df15875_21_lumper_receipt_2.jpg
2025-09-18 15:05:04,709 - INFO - S3 Image temp/6f894b2a_21_lumper_receipt_3.png: Extracted 663 characters, 38 lines
2025-09-18 15:05:04,709 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:05:04,710 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:05:04,812 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf -> s3://document-extraction-logistically/temp/43e0d2ca_21_lumper_receipt_8.pdf
2025-09-18 15:05:04,813 - INFO - 🔍 [15:05:04] Starting classification: 21_lumper_receipt_8.pdf
2025-09-18 15:05:04,814 - INFO - ⬆️ [15:05:04] Uploading: 21_lumper_receipt_9.pdf
2025-09-18 15:05:04,814 - INFO - Initializing TextractProcessor...
2025-09-18 15:05:04,834 - INFO - Initializing BedrockProcessor...
2025-09-18 15:05:04,840 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/43e0d2ca_21_lumper_receipt_8.pdf
2025-09-18 15:05:04,841 - INFO - Processing PDF from S3...
2025-09-18 15:05:04,841 - INFO - Downloading PDF from S3 to /tmp/tmp5rzmlizd/43e0d2ca_21_lumper_receipt_8.pdf
2025-09-18 15:05:05,498 - INFO - Splitting PDF into individual pages...
2025-09-18 15:05:05,499 - INFO - Splitting PDF ba31a1e0_21_lumper_receipt_6 into 1 pages
2025-09-18 15:05:05,504 - INFO - Split PDF into 1 pages
2025-09-18 15:05:05,504 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:05:05,504 - INFO - Expected pages: [1]
2025-09-18 15:05:05,711 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf -> s3://document-extraction-logistically/temp/b46c3de5_21_lumper_receipt_9.pdf
2025-09-18 15:05:05,711 - INFO - 🔍 [15:05:05] Starting classification: 21_lumper_receipt_9.pdf
2025-09-18 15:05:05,713 - INFO - Initializing TextractProcessor...
2025-09-18 15:05:05,729 - INFO - Initializing BedrockProcessor...
2025-09-18 15:05:05,736 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b46c3de5_21_lumper_receipt_9.pdf
2025-09-18 15:05:05,738 - INFO - Processing PDF from S3...
2025-09-18 15:05:05,740 - INFO - Downloading PDF from S3 to /tmp/tmpp2_3r_0m/b46c3de5_21_lumper_receipt_9.pdf
2025-09-18 15:05:05,749 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:05:05,749 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 15:05:06,043 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/92c0c8b1_21_lumper_receipt_1.jpeg
2025-09-18 15:05:06,058 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:05:06,058 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 15:05:06,080 - INFO - Splitting PDF into individual pages...
2025-09-18 15:05:06,082 - INFO - Splitting PDF 43e0d2ca_21_lumper_receipt_8 into 1 pages
2025-09-18 15:05:06,084 - INFO - Split PDF into 1 pages
2025-09-18 15:05:06,084 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:05:06,084 - INFO - Expected pages: [1]
2025-09-18 15:05:06,353 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/17bffb3c_21_lumper_receipt_10.png
2025-09-18 15:05:06,369 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:05:06,369 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 15:05:06,516 - INFO - Page 1: Extracted 422 characters, 38 lines from 81f9fb4d_21_lumper_receipt_4_3cce07a7_page_001.pdf
2025-09-18 15:05:06,516 - INFO - Successfully processed page 1
2025-09-18 15:05:06,517 - INFO - Combined 1 pages into final text
2025-09-18 15:05:06,517 - INFO - Text validation for 81f9fb4d_21_lumper_receipt_4: 439 characters, 1 pages
2025-09-18 15:05:06,517 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:05:06,517 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:05:06,664 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e6da8236_21_lumper_receipt_11.jpeg
2025-09-18 15:05:06,671 - INFO - 

21_lumper_receipt_2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:05:06,671 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_2.json
2025-09-18 15:05:06,740 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6f894b2a_21_lumper_receipt_3.png
2025-09-18 15:05:06,941 - INFO - S3 Image temp/e51cc17f_21_lumper_receipt_7.png: Extracted 800 characters, 70 lines
2025-09-18 15:05:06,941 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:05:06,941 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:05:06,961 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4df15875_21_lumper_receipt_2.jpg
2025-09-18 15:05:06,978 - INFO - 

21_lumper_receipt_3.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:05:06,979 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_3.json
2025-09-18 15:05:07,010 - INFO - Page 1: Extracted 422 characters, 38 lines from aceeaea9_21_lumper_receipt_5_12c6962c_page_001.pdf
2025-09-18 15:05:07,011 - INFO - Successfully processed page 1
2025-09-18 15:05:07,014 - INFO - Combined 1 pages into final text
2025-09-18 15:05:07,015 - INFO - Text validation for aceeaea9_21_lumper_receipt_5: 439 characters, 1 pages
2025-09-18 15:05:07,015 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:05:07,016 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:05:07,281 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6f894b2a_21_lumper_receipt_3.png
2025-09-18 15:05:07,885 - INFO - Splitting PDF into individual pages...
2025-09-18 15:05:07,886 - INFO - Splitting PDF b46c3de5_21_lumper_receipt_9 into 1 pages
2025-09-18 15:05:07,889 - INFO - Split PDF into 1 pages
2025-09-18 15:05:07,889 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:05:07,889 - INFO - Expected pages: [1]
2025-09-18 15:05:08,551 - INFO - Page 1: Extracted 330 characters, 33 lines from 43e0d2ca_21_lumper_receipt_8_f115557e_page_001.pdf
2025-09-18 15:05:08,552 - INFO - Successfully processed page 1
2025-09-18 15:05:08,552 - INFO - Combined 1 pages into final text
2025-09-18 15:05:08,552 - INFO - Text validation for 43e0d2ca_21_lumper_receipt_8: 347 characters, 1 pages
2025-09-18 15:05:08,552 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:05:08,552 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:05:08,946 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e51cc17f_21_lumper_receipt_7.png
2025-09-18 15:05:08,962 - INFO - 

21_lumper_receipt_7.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:05:08,962 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_7.json
2025-09-18 15:05:09,263 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e51cc17f_21_lumper_receipt_7.png
2025-09-18 15:05:09,449 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/81f9fb4d_21_lumper_receipt_4.pdf
2025-09-18 15:05:09,464 - INFO - 

21_lumper_receipt_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:05:09,464 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_4.json
2025-09-18 15:05:09,639 - INFO - Page 1: Extracted 269 characters, 22 lines from ba31a1e0_21_lumper_receipt_6_2de48c8f_page_001.pdf
2025-09-18 15:05:09,639 - INFO - Successfully processed page 1
2025-09-18 15:05:09,640 - INFO - Combined 1 pages into final text
2025-09-18 15:05:09,640 - INFO - Text validation for ba31a1e0_21_lumper_receipt_6: 286 characters, 1 pages
2025-09-18 15:05:09,640 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:05:09,640 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:05:09,725 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/aceeaea9_21_lumper_receipt_5.pdf
2025-09-18 15:05:09,822 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/81f9fb4d_21_lumper_receipt_4.pdf
2025-09-18 15:05:09,835 - INFO - 

21_lumper_receipt_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:05:09,835 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_5.json
2025-09-18 15:05:10,134 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/aceeaea9_21_lumper_receipt_5.pdf
2025-09-18 15:05:10,776 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/43e0d2ca_21_lumper_receipt_8.pdf
2025-09-18 15:05:10,789 - INFO - 

21_lumper_receipt_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:05:10,789 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_8.json
2025-09-18 15:05:11,092 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/43e0d2ca_21_lumper_receipt_8.pdf
2025-09-18 15:05:12,900 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ba31a1e0_21_lumper_receipt_6.pdf
2025-09-18 15:05:12,912 - INFO - 

21_lumper_receipt_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:05:12,912 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_6.json
2025-09-18 15:05:13,203 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ba31a1e0_21_lumper_receipt_6.pdf
2025-09-18 15:05:13,636 - INFO - Page 1: Extracted 645 characters, 53 lines from b46c3de5_21_lumper_receipt_9_935c3784_page_001.pdf
2025-09-18 15:05:13,636 - INFO - Successfully processed page 1
2025-09-18 15:05:13,636 - INFO - Combined 1 pages into final text
2025-09-18 15:05:13,636 - INFO - Text validation for b46c3de5_21_lumper_receipt_9: 662 characters, 1 pages
2025-09-18 15:05:13,637 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:05:13,637 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:05:15,969 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b46c3de5_21_lumper_receipt_9.pdf
2025-09-18 15:05:15,995 - INFO - 

21_lumper_receipt_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:05:15,995 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_9.json
2025-09-18 15:05:16,284 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b46c3de5_21_lumper_receipt_9.pdf
2025-09-18 15:05:16,285 - INFO - 
📊 Processing Summary:
2025-09-18 15:05:16,285 - INFO -    Total files: 11
2025-09-18 15:05:16,285 - INFO -    Successful: 11
2025-09-18 15:05:16,285 - INFO -    Failed: 0
2025-09-18 15:05:16,285 - INFO -    Duration: 23.54 seconds
2025-09-18 15:05:16,285 - INFO -    Output directory: output
2025-09-18 15:05:16,285 - INFO - 
📋 Successfully Processed Files:
2025-09-18 15:05:16,286 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:05:16,286 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:05:16,286 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:05:16,286 - INFO -    📄 21_lumper_receipt_2.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:05:16,286 - INFO -    📄 21_lumper_receipt_3.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:05:16,286 - INFO -    📄 21_lumper_receipt_4.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:05:16,286 - INFO -    📄 21_lumper_receipt_5.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:05:16,286 - INFO -    📄 21_lumper_receipt_6.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:05:16,286 - INFO -    📄 21_lumper_receipt_7.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:05:16,286 - INFO -    📄 21_lumper_receipt_8.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:05:16,286 - INFO -    📄 21_lumper_receipt_9.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:05:16,287 - INFO - 
============================================================================================================================================
2025-09-18 15:05:16,287 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 15:05:16,287 - INFO - ============================================================================================================================================
2025-09-18 15:05:16,287 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 15:05:16,287 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:05:16,287 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 15:05:16,287 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 15:05:16,287 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 15:05:16,287 - INFO - 
2025-09-18 15:05:16,288 - INFO - 21_lumper_receipt_10.png                           1      lumper_receipt       run1_21_lumper_receipt_10.json                    
2025-09-18 15:05:16,288 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 15:05:16,288 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 15:05:16,288 - INFO - 
2025-09-18 15:05:16,288 - INFO - 21_lumper_receipt_11.jpeg                          1      invoice              run1_21_lumper_receipt_11.json                    
2025-09-18 15:05:16,288 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 15:05:16,288 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 15:05:16,288 - INFO - 
2025-09-18 15:05:16,288 - INFO - 21_lumper_receipt_2.jpg                            1      lumper_receipt       run1_21_lumper_receipt_2.json                     
2025-09-18 15:05:16,288 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg
2025-09-18 15:05:16,288 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_2.json
2025-09-18 15:05:16,288 - INFO - 
2025-09-18 15:05:16,288 - INFO - 21_lumper_receipt_3.png                            1      lumper_receipt       run1_21_lumper_receipt_3.json                     
2025-09-18 15:05:16,288 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png
2025-09-18 15:05:16,288 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_3.json
2025-09-18 15:05:16,288 - INFO - 
2025-09-18 15:05:16,288 - INFO - 21_lumper_receipt_4.pdf                            1      lumper_receipt       run1_21_lumper_receipt_4.json                     
2025-09-18 15:05:16,288 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf
2025-09-18 15:05:16,288 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_4.json
2025-09-18 15:05:16,289 - INFO - 
2025-09-18 15:05:16,289 - INFO - 21_lumper_receipt_5.pdf                            1      lumper_receipt       run1_21_lumper_receipt_5.json                     
2025-09-18 15:05:16,289 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf
2025-09-18 15:05:16,289 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_5.json
2025-09-18 15:05:16,289 - INFO - 
2025-09-18 15:05:16,289 - INFO - 21_lumper_receipt_6.pdf                            1      lumper_receipt       run1_21_lumper_receipt_6.json                     
2025-09-18 15:05:16,289 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf
2025-09-18 15:05:16,289 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_6.json
2025-09-18 15:05:16,289 - INFO - 
2025-09-18 15:05:16,289 - INFO - 21_lumper_receipt_7.png                            1      invoice              run1_21_lumper_receipt_7.json                     
2025-09-18 15:05:16,289 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png
2025-09-18 15:05:16,289 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_7.json
2025-09-18 15:05:16,289 - INFO - 
2025-09-18 15:05:16,289 - INFO - 21_lumper_receipt_8.pdf                            1      lumper_receipt       run1_21_lumper_receipt_8.json                     
2025-09-18 15:05:16,289 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf
2025-09-18 15:05:16,289 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_8.json
2025-09-18 15:05:16,289 - INFO - 
2025-09-18 15:05:16,290 - INFO - 21_lumper_receipt_9.pdf                            1      lumper_receipt       run1_21_lumper_receipt_9.json                     
2025-09-18 15:05:16,290 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf
2025-09-18 15:05:16,290 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_9.json
2025-09-18 15:05:16,290 - INFO - 
2025-09-18 15:05:16,290 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:05:16,290 - INFO - Total entries: 11
2025-09-18 15:05:16,290 - INFO - ============================================================================================================================================
2025-09-18 15:05:16,290 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 15:05:16,290 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:05:16,290 - INFO -   1. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 15:05:16,290 - INFO -   2. 21_lumper_receipt_10.png            Page 1   → lumper_receipt  | run1_21_lumper_receipt_10.json
2025-09-18 15:05:16,290 - INFO -   3. 21_lumper_receipt_11.jpeg           Page 1   → invoice         | run1_21_lumper_receipt_11.json
2025-09-18 15:05:16,290 - INFO -   4. 21_lumper_receipt_2.jpg             Page 1   → lumper_receipt  | run1_21_lumper_receipt_2.json
2025-09-18 15:05:16,290 - INFO -   5. 21_lumper_receipt_3.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_3.json
2025-09-18 15:05:16,290 - INFO -   6. 21_lumper_receipt_4.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_4.json
2025-09-18 15:05:16,290 - INFO -   7. 21_lumper_receipt_5.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_5.json
2025-09-18 15:05:16,290 - INFO -   8. 21_lumper_receipt_6.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_6.json
2025-09-18 15:05:16,290 - INFO -   9. 21_lumper_receipt_7.png             Page 1   → invoice         | run1_21_lumper_receipt_7.json
2025-09-18 15:05:16,291 - INFO -  10. 21_lumper_receipt_8.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_8.json
2025-09-18 15:05:16,291 - INFO -  11. 21_lumper_receipt_9.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_9.json
2025-09-18 15:05:16,291 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:05:16,291 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 23.543083, 'processed_files': [{'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '21_lumper_receipt_2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg'}, {'filename': '21_lumper_receipt_3.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png'}, {'filename': '21_lumper_receipt_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf'}, {'filename': '21_lumper_receipt_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf'}, {'filename': '21_lumper_receipt_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf'}, {'filename': '21_lumper_receipt_7.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png'}, {'filename': '21_lumper_receipt_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf'}, {'filename': '21_lumper_receipt_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf'}]}
