2025-09-18 15:18:41,778 - INFO - Logging initialized. Log file: logs/test_classification_20250918_151841.log
2025-09-18 15:18:41,779 - INFO - 📁 Found 11 files to process
2025-09-18 15:18:41,779 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 15:18:41,779 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 15:18:41,779 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 15:18:41,779 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 15:18:41,779 - INFO - ⬆️ [15:18:41] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 15:18:44,049 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/56cb7317_21_lumper_receipt_1.jpeg
2025-09-18 15:18:44,049 - INFO - 🔍 [15:18:44] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 15:18:44,050 - INFO - ⬆️ [15:18:44] Uploading: 21_lumper_receipt_10.png
2025-09-18 15:18:44,051 - INFO - Initializing TextractProcessor...
2025-09-18 15:18:44,075 - INFO - Initializing BedrockProcessor...
2025-09-18 15:18:44,081 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/56cb7317_21_lumper_receipt_1.jpeg
2025-09-18 15:18:44,081 - INFO - Processing image from S3...
2025-09-18 15:18:44,748 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/770d058d_21_lumper_receipt_10.png
2025-09-18 15:18:44,748 - INFO - 🔍 [15:18:44] Starting classification: 21_lumper_receipt_10.png
2025-09-18 15:18:44,749 - INFO - Initializing TextractProcessor...
2025-09-18 15:18:44,750 - INFO - ⬆️ [15:18:44] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 15:18:44,763 - INFO - Initializing BedrockProcessor...
2025-09-18 15:18:44,769 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/770d058d_21_lumper_receipt_10.png
2025-09-18 15:18:44,770 - INFO - Processing image from S3...
2025-09-18 15:18:46,083 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/531ac506_21_lumper_receipt_11.jpeg
2025-09-18 15:18:46,083 - INFO - 🔍 [15:18:46] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 15:18:46,085 - INFO - ⬆️ [15:18:46] Uploading: 21_lumper_receipt_2.jpg
2025-09-18 15:18:46,087 - INFO - Initializing TextractProcessor...
2025-09-18 15:18:46,107 - INFO - Initializing BedrockProcessor...
2025-09-18 15:18:46,114 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/531ac506_21_lumper_receipt_11.jpeg
2025-09-18 15:18:46,114 - INFO - Processing image from S3...
2025-09-18 15:18:46,846 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg -> s3://document-extraction-logistically/temp/e2f4ec3d_21_lumper_receipt_2.jpg
2025-09-18 15:18:46,846 - INFO - 🔍 [15:18:46] Starting classification: 21_lumper_receipt_2.jpg
2025-09-18 15:18:46,847 - INFO - ⬆️ [15:18:46] Uploading: 21_lumper_receipt_3.png
2025-09-18 15:18:46,848 - INFO - Initializing TextractProcessor...
2025-09-18 15:18:46,870 - INFO - Initializing BedrockProcessor...
2025-09-18 15:18:46,876 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e2f4ec3d_21_lumper_receipt_2.jpg
2025-09-18 15:18:46,876 - INFO - Processing image from S3...
2025-09-18 15:18:47,250 - INFO - S3 Image temp/56cb7317_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 15:18:47,250 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:18:47,250 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:18:47,500 - INFO - S3 Image temp/770d058d_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 15:18:47,500 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:18:47,500 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:18:48,315 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png -> s3://document-extraction-logistically/temp/8189ac59_21_lumper_receipt_3.png
2025-09-18 15:18:48,315 - INFO - 🔍 [15:18:48] Starting classification: 21_lumper_receipt_3.png
2025-09-18 15:18:48,316 - INFO - ⬆️ [15:18:48] Uploading: 21_lumper_receipt_4.pdf
2025-09-18 15:18:48,316 - INFO - Initializing TextractProcessor...
2025-09-18 15:18:48,330 - INFO - Initializing BedrockProcessor...
2025-09-18 15:18:48,334 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8189ac59_21_lumper_receipt_3.png
2025-09-18 15:18:48,334 - INFO - Processing image from S3...
2025-09-18 15:18:48,894 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf -> s3://document-extraction-logistically/temp/0943a441_21_lumper_receipt_4.pdf
2025-09-18 15:18:48,894 - INFO - 🔍 [15:18:48] Starting classification: 21_lumper_receipt_4.pdf
2025-09-18 15:18:48,896 - INFO - ⬆️ [15:18:48] Uploading: 21_lumper_receipt_5.pdf
2025-09-18 15:18:48,898 - INFO - Initializing TextractProcessor...
2025-09-18 15:18:48,914 - INFO - Initializing BedrockProcessor...
2025-09-18 15:18:48,920 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0943a441_21_lumper_receipt_4.pdf
2025-09-18 15:18:48,920 - INFO - Processing PDF from S3...
2025-09-18 15:18:48,921 - INFO - Downloading PDF from S3 to /tmp/tmpmpuak1r1/0943a441_21_lumper_receipt_4.pdf
2025-09-18 15:18:48,969 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/56cb7317_21_lumper_receipt_1.jpeg
2025-09-18 15:18:49,398 - INFO - S3 Image temp/e2f4ec3d_21_lumper_receipt_2.jpg: Extracted 184 characters, 12 lines
2025-09-18 15:18:49,398 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:18:49,398 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:18:49,495 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf -> s3://document-extraction-logistically/temp/84937b22_21_lumper_receipt_5.pdf
2025-09-18 15:18:49,495 - INFO - 🔍 [15:18:49] Starting classification: 21_lumper_receipt_5.pdf
2025-09-18 15:18:49,496 - INFO - ⬆️ [15:18:49] Uploading: 21_lumper_receipt_6.pdf
2025-09-18 15:18:49,496 - INFO - Initializing TextractProcessor...
2025-09-18 15:18:49,512 - INFO - Initializing BedrockProcessor...
2025-09-18 15:18:49,518 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/84937b22_21_lumper_receipt_5.pdf
2025-09-18 15:18:49,518 - INFO - Processing PDF from S3...
2025-09-18 15:18:49,518 - INFO - Downloading PDF from S3 to /tmp/tmpub7976v3/84937b22_21_lumper_receipt_5.pdf
2025-09-18 15:18:50,118 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf -> s3://document-extraction-logistically/temp/9e478db5_21_lumper_receipt_6.pdf
2025-09-18 15:18:50,119 - INFO - 🔍 [15:18:50] Starting classification: 21_lumper_receipt_6.pdf
2025-09-18 15:18:50,120 - INFO - ⬆️ [15:18:50] Uploading: 21_lumper_receipt_7.png
2025-09-18 15:18:50,122 - INFO - Initializing TextractProcessor...
2025-09-18 15:18:50,174 - INFO - Initializing BedrockProcessor...
2025-09-18 15:18:50,176 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9e478db5_21_lumper_receipt_6.pdf
2025-09-18 15:18:50,176 - INFO - Processing PDF from S3...
2025-09-18 15:18:50,176 - INFO - Downloading PDF from S3 to /tmp/tmpg5r8aues/9e478db5_21_lumper_receipt_6.pdf
2025-09-18 15:18:50,235 - INFO - Splitting PDF into individual pages...
2025-09-18 15:18:50,236 - INFO - Splitting PDF 0943a441_21_lumper_receipt_4 into 1 pages
2025-09-18 15:18:50,237 - INFO - Split PDF into 1 pages
2025-09-18 15:18:50,237 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:18:50,237 - INFO - Expected pages: [1]
2025-09-18 15:18:50,404 - INFO - S3 Image temp/531ac506_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 15:18:50,404 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:18:50,404 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:18:50,752 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png -> s3://document-extraction-logistically/temp/9f2a518e_21_lumper_receipt_7.png
2025-09-18 15:18:50,752 - INFO - 🔍 [15:18:50] Starting classification: 21_lumper_receipt_7.png
2025-09-18 15:18:50,753 - INFO - ⬆️ [15:18:50] Uploading: 21_lumper_receipt_8.pdf
2025-09-18 15:18:50,755 - INFO - Initializing TextractProcessor...
2025-09-18 15:18:50,769 - INFO - Initializing BedrockProcessor...
2025-09-18 15:18:50,775 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9f2a518e_21_lumper_receipt_7.png
2025-09-18 15:18:50,775 - INFO - Processing image from S3...
2025-09-18 15:18:50,831 - INFO - Splitting PDF into individual pages...
2025-09-18 15:18:50,832 - INFO - Splitting PDF 84937b22_21_lumper_receipt_5 into 1 pages
2025-09-18 15:18:50,832 - INFO - Split PDF into 1 pages
2025-09-18 15:18:50,832 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:18:50,832 - INFO - Expected pages: [1]
2025-09-18 15:18:50,988 - INFO - S3 Image temp/8189ac59_21_lumper_receipt_3.png: Extracted 663 characters, 38 lines
2025-09-18 15:18:50,988 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:18:50,988 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:18:51,016 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e2f4ec3d_21_lumper_receipt_2.jpg
2025-09-18 15:18:51,356 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/770d058d_21_lumper_receipt_10.png
2025-09-18 15:18:51,359 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf -> s3://document-extraction-logistically/temp/303582a1_21_lumper_receipt_8.pdf
2025-09-18 15:18:51,359 - INFO - 🔍 [15:18:51] Starting classification: 21_lumper_receipt_8.pdf
2025-09-18 15:18:51,360 - INFO - ⬆️ [15:18:51] Uploading: 21_lumper_receipt_9.pdf
2025-09-18 15:18:51,361 - INFO - Initializing TextractProcessor...
2025-09-18 15:18:51,374 - INFO - Initializing BedrockProcessor...
2025-09-18 15:18:51,376 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/303582a1_21_lumper_receipt_8.pdf
2025-09-18 15:18:51,376 - INFO - Processing PDF from S3...
2025-09-18 15:18:51,376 - INFO - Downloading PDF from S3 to /tmp/tmphh5we1cx/303582a1_21_lumper_receipt_8.pdf
2025-09-18 15:18:52,002 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf -> s3://document-extraction-logistically/temp/33b62480_21_lumper_receipt_9.pdf
2025-09-18 15:18:52,002 - INFO - 🔍 [15:18:52] Starting classification: 21_lumper_receipt_9.pdf
2025-09-18 15:18:52,003 - INFO - Initializing TextractProcessor...
2025-09-18 15:18:52,025 - INFO - Initializing BedrockProcessor...
2025-09-18 15:18:52,034 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/33b62480_21_lumper_receipt_9.pdf
2025-09-18 15:18:52,034 - INFO - Processing PDF from S3...
2025-09-18 15:18:52,036 - INFO - Downloading PDF from S3 to /tmp/tmpnwfqqz54/33b62480_21_lumper_receipt_9.pdf
2025-09-18 15:18:52,038 - INFO - Splitting PDF into individual pages...
2025-09-18 15:18:52,051 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:18:52,052 - INFO - Splitting PDF 9e478db5_21_lumper_receipt_6 into 1 pages
2025-09-18 15:18:52,053 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 15:18:52,067 - INFO - Split PDF into 1 pages
2025-09-18 15:18:52,067 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:18:52,068 - INFO - Expected pages: [1]
2025-09-18 15:18:52,364 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/56cb7317_21_lumper_receipt_1.jpeg
2025-09-18 15:18:52,376 - INFO - 

21_lumper_receipt_2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:18:52,377 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_2.json
2025-09-18 15:18:52,673 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e2f4ec3d_21_lumper_receipt_2.jpg
2025-09-18 15:18:52,679 - INFO - Splitting PDF into individual pages...
2025-09-18 15:18:52,683 - INFO - Splitting PDF 303582a1_21_lumper_receipt_8 into 1 pages
2025-09-18 15:18:52,689 - INFO - Split PDF into 1 pages
2025-09-18 15:18:52,690 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:18:52,691 - INFO - Expected pages: [1]
2025-09-18 15:18:52,696 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:18:52,696 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 15:18:52,945 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8189ac59_21_lumper_receipt_3.png
2025-09-18 15:18:52,987 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/770d058d_21_lumper_receipt_10.png
2025-09-18 15:18:53,005 - INFO - 

21_lumper_receipt_3.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:18:53,005 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_3.json
2025-09-18 15:18:53,043 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/531ac506_21_lumper_receipt_11.jpeg
2025-09-18 15:18:53,297 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8189ac59_21_lumper_receipt_3.png
2025-09-18 15:18:53,312 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:18:53,312 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 15:18:53,319 - INFO - Page 1: Extracted 422 characters, 38 lines from 0943a441_21_lumper_receipt_4_cf42aaa8_page_001.pdf
2025-09-18 15:18:53,320 - INFO - Successfully processed page 1
2025-09-18 15:18:53,320 - INFO - Combined 1 pages into final text
2025-09-18 15:18:53,320 - INFO - Text validation for 0943a441_21_lumper_receipt_4: 439 characters, 1 pages
2025-09-18 15:18:53,321 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:18:53,321 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:18:53,577 - INFO - Page 1: Extracted 422 characters, 38 lines from 84937b22_21_lumper_receipt_5_abd4a10d_page_001.pdf
2025-09-18 15:18:53,577 - INFO - Successfully processed page 1
2025-09-18 15:18:53,577 - INFO - Combined 1 pages into final text
2025-09-18 15:18:53,577 - INFO - Text validation for 84937b22_21_lumper_receipt_5: 439 characters, 1 pages
2025-09-18 15:18:53,577 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:18:53,577 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:18:53,616 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/531ac506_21_lumper_receipt_11.jpeg
2025-09-18 15:18:53,663 - INFO - S3 Image temp/9f2a518e_21_lumper_receipt_7.png: Extracted 800 characters, 70 lines
2025-09-18 15:18:53,663 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:18:53,663 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:18:54,150 - INFO - Splitting PDF into individual pages...
2025-09-18 15:18:54,152 - INFO - Splitting PDF 33b62480_21_lumper_receipt_9 into 1 pages
2025-09-18 15:18:54,154 - INFO - Split PDF into 1 pages
2025-09-18 15:18:54,154 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:18:54,154 - INFO - Expected pages: [1]
2025-09-18 15:18:55,258 - INFO - Page 1: Extracted 330 characters, 33 lines from 303582a1_21_lumper_receipt_8_dd116fc9_page_001.pdf
2025-09-18 15:18:55,258 - INFO - Successfully processed page 1
2025-09-18 15:18:55,259 - INFO - Combined 1 pages into final text
2025-09-18 15:18:55,259 - INFO - Text validation for 303582a1_21_lumper_receipt_8: 347 characters, 1 pages
2025-09-18 15:18:55,259 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:18:55,259 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:18:55,741 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/84937b22_21_lumper_receipt_5.pdf
2025-09-18 15:18:55,757 - INFO - 

21_lumper_receipt_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:18:55,757 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_5.json
2025-09-18 15:18:56,067 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/84937b22_21_lumper_receipt_5.pdf
2025-09-18 15:18:56,270 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9f2a518e_21_lumper_receipt_7.png
2025-09-18 15:18:56,285 - INFO - 

21_lumper_receipt_7.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:18:56,286 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_7.json
2025-09-18 15:18:56,386 - INFO - Page 1: Extracted 269 characters, 22 lines from 9e478db5_21_lumper_receipt_6_257c695c_page_001.pdf
2025-09-18 15:18:56,386 - INFO - Successfully processed page 1
2025-09-18 15:18:56,387 - INFO - Combined 1 pages into final text
2025-09-18 15:18:56,387 - INFO - Text validation for 9e478db5_21_lumper_receipt_6: 286 characters, 1 pages
2025-09-18 15:18:56,387 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:18:56,387 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:18:56,578 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9f2a518e_21_lumper_receipt_7.png
2025-09-18 15:18:57,149 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/303582a1_21_lumper_receipt_8.pdf
2025-09-18 15:18:57,159 - INFO - 

21_lumper_receipt_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:18:57,159 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_8.json
2025-09-18 15:18:57,458 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/303582a1_21_lumper_receipt_8.pdf
2025-09-18 15:18:59,031 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9e478db5_21_lumper_receipt_6.pdf
2025-09-18 15:18:59,044 - INFO - 

21_lumper_receipt_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:18:59,045 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_6.json
2025-09-18 15:18:59,346 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9e478db5_21_lumper_receipt_6.pdf
2025-09-18 15:18:59,596 - INFO - Page 1: Extracted 645 characters, 53 lines from 33b62480_21_lumper_receipt_9_2e67bb78_page_001.pdf
2025-09-18 15:18:59,597 - INFO - Successfully processed page 1
2025-09-18 15:18:59,597 - INFO - Combined 1 pages into final text
2025-09-18 15:18:59,597 - INFO - Text validation for 33b62480_21_lumper_receipt_9: 662 characters, 1 pages
2025-09-18 15:18:59,598 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:18:59,598 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:18:59,659 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0943a441_21_lumper_receipt_4.pdf
2025-09-18 15:18:59,676 - INFO - 

21_lumper_receipt_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:18:59,676 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_4.json
2025-09-18 15:18:59,975 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0943a441_21_lumper_receipt_4.pdf
2025-09-18 15:19:02,518 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/33b62480_21_lumper_receipt_9.pdf
2025-09-18 15:19:02,536 - INFO - 

21_lumper_receipt_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:19:02,536 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_9.json
2025-09-18 15:19:02,825 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/33b62480_21_lumper_receipt_9.pdf
2025-09-18 15:19:02,825 - INFO - 
📊 Processing Summary:
2025-09-18 15:19:02,826 - INFO -    Total files: 11
2025-09-18 15:19:02,826 - INFO -    Successful: 11
2025-09-18 15:19:02,826 - INFO -    Failed: 0
2025-09-18 15:19:02,826 - INFO -    Duration: 21.05 seconds
2025-09-18 15:19:02,826 - INFO -    Output directory: output
2025-09-18 15:19:02,826 - INFO - 
📋 Successfully Processed Files:
2025-09-18 15:19:02,827 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:19:02,827 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:19:02,827 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:19:02,827 - INFO -    📄 21_lumper_receipt_2.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:19:02,827 - INFO -    📄 21_lumper_receipt_3.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:19:02,827 - INFO -    📄 21_lumper_receipt_4.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:19:02,827 - INFO -    📄 21_lumper_receipt_5.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:19:02,827 - INFO -    📄 21_lumper_receipt_6.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:19:02,827 - INFO -    📄 21_lumper_receipt_7.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:19:02,827 - INFO -    📄 21_lumper_receipt_8.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:19:02,827 - INFO -    📄 21_lumper_receipt_9.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:19:02,828 - INFO - 
============================================================================================================================================
2025-09-18 15:19:02,828 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 15:19:02,828 - INFO - ============================================================================================================================================
2025-09-18 15:19:02,828 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 15:19:02,828 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:19:02,828 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 15:19:02,828 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 15:19:02,828 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 15:19:02,828 - INFO - 
2025-09-18 15:19:02,828 - INFO - 21_lumper_receipt_10.png                           1      invoice              run1_21_lumper_receipt_10.json                    
2025-09-18 15:19:02,828 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 15:19:02,828 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 15:19:02,829 - INFO - 
2025-09-18 15:19:02,829 - INFO - 21_lumper_receipt_11.jpeg                          1      lumper_receipt       run1_21_lumper_receipt_11.json                    
2025-09-18 15:19:02,829 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 15:19:02,829 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 15:19:02,829 - INFO - 
2025-09-18 15:19:02,829 - INFO - 21_lumper_receipt_2.jpg                            1      lumper_receipt       run1_21_lumper_receipt_2.json                     
2025-09-18 15:19:02,829 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg
2025-09-18 15:19:02,829 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_2.json
2025-09-18 15:19:02,829 - INFO - 
2025-09-18 15:19:02,829 - INFO - 21_lumper_receipt_3.png                            1      invoice              run1_21_lumper_receipt_3.json                     
2025-09-18 15:19:02,829 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png
2025-09-18 15:19:02,829 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_3.json
2025-09-18 15:19:02,829 - INFO - 
2025-09-18 15:19:02,829 - INFO - 21_lumper_receipt_4.pdf                            1      invoice              run1_21_lumper_receipt_4.json                     
2025-09-18 15:19:02,829 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf
2025-09-18 15:19:02,829 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_4.json
2025-09-18 15:19:02,829 - INFO - 
2025-09-18 15:19:02,829 - INFO - 21_lumper_receipt_5.pdf                            1      lumper_receipt       run1_21_lumper_receipt_5.json                     
2025-09-18 15:19:02,829 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf
2025-09-18 15:19:02,830 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_5.json
2025-09-18 15:19:02,830 - INFO - 
2025-09-18 15:19:02,830 - INFO - 21_lumper_receipt_6.pdf                            1      lumper_receipt       run1_21_lumper_receipt_6.json                     
2025-09-18 15:19:02,830 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf
2025-09-18 15:19:02,830 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_6.json
2025-09-18 15:19:02,830 - INFO - 
2025-09-18 15:19:02,830 - INFO - 21_lumper_receipt_7.png                            1      lumper_receipt       run1_21_lumper_receipt_7.json                     
2025-09-18 15:19:02,830 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png
2025-09-18 15:19:02,830 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_7.json
2025-09-18 15:19:02,830 - INFO - 
2025-09-18 15:19:02,830 - INFO - 21_lumper_receipt_8.pdf                            1      lumper_receipt       run1_21_lumper_receipt_8.json                     
2025-09-18 15:19:02,830 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf
2025-09-18 15:19:02,830 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_8.json
2025-09-18 15:19:02,830 - INFO - 
2025-09-18 15:19:02,830 - INFO - 21_lumper_receipt_9.pdf                            1      lumper_receipt       run1_21_lumper_receipt_9.json                     
2025-09-18 15:19:02,830 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf
2025-09-18 15:19:02,830 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_9.json
2025-09-18 15:19:02,830 - INFO - 
2025-09-18 15:19:02,830 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:19:02,830 - INFO - Total entries: 11
2025-09-18 15:19:02,830 - INFO - ============================================================================================================================================
2025-09-18 15:19:02,830 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 15:19:02,831 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:19:02,831 - INFO -   1. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 15:19:02,831 - INFO -   2. 21_lumper_receipt_10.png            Page 1   → invoice         | run1_21_lumper_receipt_10.json
2025-09-18 15:19:02,831 - INFO -   3. 21_lumper_receipt_11.jpeg           Page 1   → lumper_receipt  | run1_21_lumper_receipt_11.json
2025-09-18 15:19:02,831 - INFO -   4. 21_lumper_receipt_2.jpg             Page 1   → lumper_receipt  | run1_21_lumper_receipt_2.json
2025-09-18 15:19:02,831 - INFO -   5. 21_lumper_receipt_3.png             Page 1   → invoice         | run1_21_lumper_receipt_3.json
2025-09-18 15:19:02,831 - INFO -   6. 21_lumper_receipt_4.pdf             Page 1   → invoice         | run1_21_lumper_receipt_4.json
2025-09-18 15:19:02,831 - INFO -   7. 21_lumper_receipt_5.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_5.json
2025-09-18 15:19:02,831 - INFO -   8. 21_lumper_receipt_6.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_6.json
2025-09-18 15:19:02,831 - INFO -   9. 21_lumper_receipt_7.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_7.json
2025-09-18 15:19:02,831 - INFO -  10. 21_lumper_receipt_8.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_8.json
2025-09-18 15:19:02,831 - INFO -  11. 21_lumper_receipt_9.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_9.json
2025-09-18 15:19:02,831 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:19:02,831 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 21.046693, 'processed_files': [{'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '21_lumper_receipt_2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg'}, {'filename': '21_lumper_receipt_3.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png'}, {'filename': '21_lumper_receipt_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf'}, {'filename': '21_lumper_receipt_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf'}, {'filename': '21_lumper_receipt_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf'}, {'filename': '21_lumper_receipt_7.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png'}, {'filename': '21_lumper_receipt_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf'}, {'filename': '21_lumper_receipt_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf'}]}
