2025-09-18 15:19:44,214 - INFO - Logging initialized. Log file: logs/test_classification_20250918_151944.log
2025-09-18 15:19:44,214 - INFO - 📁 Found 10 files to process
2025-09-18 15:19:44,214 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 15:19:44,214 - INFO - 🚀 Processing 10 files in FORCED PARALLEL MODE...
2025-09-18 15:19:44,215 - INFO - 🚀 Creating 10 parallel tasks...
2025-09-18 15:19:44,215 - INFO - 🚀 All 10 tasks created - executing in parallel...
2025-09-18 15:19:44,215 - INFO - ⬆️ [15:19:44] Uploading: 10_invoice_1.pdf
2025-09-18 15:19:45,794 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_1.pdf -> s3://document-extraction-logistically/temp/9e6bc1f1_10_invoice_1.pdf
2025-09-18 15:19:45,794 - INFO - 🔍 [15:19:45] Starting classification: 10_invoice_1.pdf
2025-09-18 15:19:45,795 - INFO - ⬆️ [15:19:45] Uploading: 10_invoice_10.pdf
2025-09-18 15:19:45,796 - INFO - Initializing TextractProcessor...
2025-09-18 15:19:45,829 - INFO - Initializing BedrockProcessor...
2025-09-18 15:19:45,834 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9e6bc1f1_10_invoice_1.pdf
2025-09-18 15:19:45,834 - INFO - Processing PDF from S3...
2025-09-18 15:19:45,835 - INFO - Downloading PDF from S3 to /tmp/tmp5x5a4p27/9e6bc1f1_10_invoice_1.pdf
2025-09-18 15:19:46,410 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_10.pdf -> s3://document-extraction-logistically/temp/080d3ad6_10_invoice_10.pdf
2025-09-18 15:19:46,410 - INFO - 🔍 [15:19:46] Starting classification: 10_invoice_10.pdf
2025-09-18 15:19:46,411 - INFO - ⬆️ [15:19:46] Uploading: 10_invoice_2.pdf
2025-09-18 15:19:46,415 - INFO - Initializing TextractProcessor...
2025-09-18 15:19:46,439 - INFO - Initializing BedrockProcessor...
2025-09-18 15:19:46,442 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/080d3ad6_10_invoice_10.pdf
2025-09-18 15:19:46,442 - INFO - Processing PDF from S3...
2025-09-18 15:19:46,442 - INFO - Downloading PDF from S3 to /tmp/tmp0wws7151/080d3ad6_10_invoice_10.pdf
2025-09-18 15:19:47,226 - INFO - Splitting PDF into individual pages...
2025-09-18 15:19:47,229 - INFO - Splitting PDF 9e6bc1f1_10_invoice_1 into 1 pages
2025-09-18 15:19:47,234 - INFO - Split PDF into 1 pages
2025-09-18 15:19:47,235 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:19:47,235 - INFO - Expected pages: [1]
2025-09-18 15:19:47,820 - INFO - Splitting PDF into individual pages...
2025-09-18 15:19:47,823 - INFO - Splitting PDF 080d3ad6_10_invoice_10 into 1 pages
2025-09-18 15:19:47,833 - INFO - Split PDF into 1 pages
2025-09-18 15:19:47,833 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:19:47,833 - INFO - Expected pages: [1]
2025-09-18 15:19:49,093 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_2.pdf -> s3://document-extraction-logistically/temp/fc83d3c1_10_invoice_2.pdf
2025-09-18 15:19:49,093 - INFO - 🔍 [15:19:49] Starting classification: 10_invoice_2.pdf
2025-09-18 15:19:49,094 - INFO - ⬆️ [15:19:49] Uploading: 10_invoice_3.pdf
2025-09-18 15:19:49,099 - INFO - Initializing TextractProcessor...
2025-09-18 15:19:49,115 - INFO - Initializing BedrockProcessor...
2025-09-18 15:19:49,119 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fc83d3c1_10_invoice_2.pdf
2025-09-18 15:19:49,119 - INFO - Processing PDF from S3...
2025-09-18 15:19:49,119 - INFO - Downloading PDF from S3 to /tmp/tmpucnvl27y/fc83d3c1_10_invoice_2.pdf
2025-09-18 15:19:49,670 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_3.pdf -> s3://document-extraction-logistically/temp/4db0274d_10_invoice_3.pdf
2025-09-18 15:19:49,670 - INFO - 🔍 [15:19:49] Starting classification: 10_invoice_3.pdf
2025-09-18 15:19:49,671 - INFO - ⬆️ [15:19:49] Uploading: 10_invoice_4.pdf
2025-09-18 15:19:49,673 - INFO - Initializing TextractProcessor...
2025-09-18 15:19:49,693 - INFO - Initializing BedrockProcessor...
2025-09-18 15:19:49,695 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4db0274d_10_invoice_3.pdf
2025-09-18 15:19:49,695 - INFO - Processing PDF from S3...
2025-09-18 15:19:49,696 - INFO - Downloading PDF from S3 to /tmp/tmprl30hqhe/4db0274d_10_invoice_3.pdf
2025-09-18 15:19:50,886 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_4.pdf -> s3://document-extraction-logistically/temp/a14a8ffe_10_invoice_4.pdf
2025-09-18 15:19:50,886 - INFO - 🔍 [15:19:50] Starting classification: 10_invoice_4.pdf
2025-09-18 15:19:50,887 - INFO - ⬆️ [15:19:50] Uploading: 10_invoice_5.pdf
2025-09-18 15:19:50,889 - INFO - Initializing TextractProcessor...
2025-09-18 15:19:50,905 - INFO - Initializing BedrockProcessor...
2025-09-18 15:19:50,909 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a14a8ffe_10_invoice_4.pdf
2025-09-18 15:19:50,909 - INFO - Processing PDF from S3...
2025-09-18 15:19:50,909 - INFO - Downloading PDF from S3 to /tmp/tmphiiwk84m/a14a8ffe_10_invoice_4.pdf
2025-09-18 15:19:50,966 - INFO - Splitting PDF into individual pages...
2025-09-18 15:19:50,968 - INFO - Splitting PDF 4db0274d_10_invoice_3 into 1 pages
2025-09-18 15:19:50,971 - INFO - Split PDF into 1 pages
2025-09-18 15:19:50,971 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:19:50,971 - INFO - Expected pages: [1]
2025-09-18 15:19:51,414 - INFO - Splitting PDF into individual pages...
2025-09-18 15:19:51,422 - INFO - Splitting PDF fc83d3c1_10_invoice_2 into 1 pages
2025-09-18 15:19:51,462 - INFO - Split PDF into 1 pages
2025-09-18 15:19:51,462 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:19:51,462 - INFO - Expected pages: [1]
2025-09-18 15:19:51,512 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_5.pdf -> s3://document-extraction-logistically/temp/2c72b0ad_10_invoice_5.pdf
2025-09-18 15:19:51,512 - INFO - 🔍 [15:19:51] Starting classification: 10_invoice_5.pdf
2025-09-18 15:19:51,513 - INFO - ⬆️ [15:19:51] Uploading: 10_invoice_6.pdf
2025-09-18 15:19:51,514 - INFO - Initializing TextractProcessor...
2025-09-18 15:19:51,564 - INFO - Initializing BedrockProcessor...
2025-09-18 15:19:51,567 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2c72b0ad_10_invoice_5.pdf
2025-09-18 15:19:51,567 - INFO - Processing PDF from S3...
2025-09-18 15:19:51,567 - INFO - Downloading PDF from S3 to /tmp/tmpt1z308gu/2c72b0ad_10_invoice_5.pdf
2025-09-18 15:19:51,876 - INFO - Page 1: Extracted 659 characters, 47 lines from 080d3ad6_10_invoice_10_e9e1487a_page_001.pdf
2025-09-18 15:19:51,877 - INFO - Successfully processed page 1
2025-09-18 15:19:51,877 - INFO - Combined 1 pages into final text
2025-09-18 15:19:51,877 - INFO - Text validation for 080d3ad6_10_invoice_10: 676 characters, 1 pages
2025-09-18 15:19:51,878 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:19:51,878 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:19:52,048 - INFO - Page 1: Extracted 1418 characters, 97 lines from 9e6bc1f1_10_invoice_1_ffe06954_page_001.pdf
2025-09-18 15:19:52,048 - INFO - Successfully processed page 1
2025-09-18 15:19:52,049 - INFO - Combined 1 pages into final text
2025-09-18 15:19:52,049 - INFO - Text validation for 9e6bc1f1_10_invoice_1: 1435 characters, 1 pages
2025-09-18 15:19:52,049 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:19:52,049 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:19:52,930 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_6.pdf -> s3://document-extraction-logistically/temp/69b50405_10_invoice_6.pdf
2025-09-18 15:19:52,931 - INFO - 🔍 [15:19:52] Starting classification: 10_invoice_6.pdf
2025-09-18 15:19:52,932 - INFO - ⬆️ [15:19:52] Uploading: 10_invoice_7.pdf
2025-09-18 15:19:52,932 - INFO - Initializing TextractProcessor...
2025-09-18 15:19:52,949 - INFO - Initializing BedrockProcessor...
2025-09-18 15:19:52,954 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/69b50405_10_invoice_6.pdf
2025-09-18 15:19:52,954 - INFO - Processing PDF from S3...
2025-09-18 15:19:52,954 - INFO - Downloading PDF from S3 to /tmp/tmp3g3fsutr/69b50405_10_invoice_6.pdf
2025-09-18 15:19:52,968 - INFO - Splitting PDF into individual pages...
2025-09-18 15:19:52,970 - INFO - Splitting PDF a14a8ffe_10_invoice_4 into 1 pages
2025-09-18 15:19:52,972 - INFO - Split PDF into 1 pages
2025-09-18 15:19:52,972 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:19:52,972 - INFO - Expected pages: [1]
2025-09-18 15:19:53,114 - INFO - Splitting PDF into individual pages...
2025-09-18 15:19:53,116 - INFO - Splitting PDF 2c72b0ad_10_invoice_5 into 1 pages
2025-09-18 15:19:53,122 - INFO - Split PDF into 1 pages
2025-09-18 15:19:53,122 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:19:53,122 - INFO - Expected pages: [1]
2025-09-18 15:19:53,514 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_7.pdf -> s3://document-extraction-logistically/temp/ab7f2926_10_invoice_7.pdf
2025-09-18 15:19:53,515 - INFO - 🔍 [15:19:53] Starting classification: 10_invoice_7.pdf
2025-09-18 15:19:53,516 - INFO - Initializing TextractProcessor...
2025-09-18 15:19:53,516 - INFO - ⬆️ [15:19:53] Uploading: 10_invoice_8.pdf
2025-09-18 15:19:53,534 - INFO - Initializing BedrockProcessor...
2025-09-18 15:19:53,538 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ab7f2926_10_invoice_7.pdf
2025-09-18 15:19:53,538 - INFO - Processing PDF from S3...
2025-09-18 15:19:53,539 - INFO - Downloading PDF from S3 to /tmp/tmp6gh8un4w/ab7f2926_10_invoice_7.pdf
2025-09-18 15:19:53,988 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/080d3ad6_10_invoice_10.pdf
2025-09-18 15:19:54,135 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_8.pdf -> s3://document-extraction-logistically/temp/83b64f8d_10_invoice_8.pdf
2025-09-18 15:19:54,135 - INFO - 🔍 [15:19:54] Starting classification: 10_invoice_8.pdf
2025-09-18 15:19:54,136 - INFO - Initializing TextractProcessor...
2025-09-18 15:19:54,136 - INFO - ⬆️ [15:19:54] Uploading: 10_invoice_9.pdf
2025-09-18 15:19:54,148 - INFO - Initializing BedrockProcessor...
2025-09-18 15:19:54,157 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/83b64f8d_10_invoice_8.pdf
2025-09-18 15:19:54,158 - INFO - Processing PDF from S3...
2025-09-18 15:19:54,159 - INFO - Downloading PDF from S3 to /tmp/tmp0f0dphrb/83b64f8d_10_invoice_8.pdf
2025-09-18 15:19:54,752 - INFO - ✓ Uploaded: input_data_individual/10_invoice/10_invoice_9.pdf -> s3://document-extraction-logistically/temp/8aaa2698_10_invoice_9.pdf
2025-09-18 15:19:54,752 - INFO - 🔍 [15:19:54] Starting classification: 10_invoice_9.pdf
2025-09-18 15:19:54,754 - INFO - Initializing TextractProcessor...
2025-09-18 15:19:54,764 - INFO - Initializing BedrockProcessor...
2025-09-18 15:19:54,776 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8aaa2698_10_invoice_9.pdf
2025-09-18 15:19:54,778 - INFO - Processing PDF from S3...
2025-09-18 15:19:54,779 - INFO - Downloading PDF from S3 to /tmp/tmp0535zg6j/8aaa2698_10_invoice_9.pdf
2025-09-18 15:19:54,803 - INFO - 

10_invoice_10.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:19:54,803 - INFO - 

✓ Saved result: output/run1_10_invoice_10.json
2025-09-18 15:19:54,828 - INFO - Splitting PDF into individual pages...
2025-09-18 15:19:54,830 - INFO - Splitting PDF ab7f2926_10_invoice_7 into 1 pages
2025-09-18 15:19:54,835 - INFO - Split PDF into 1 pages
2025-09-18 15:19:54,836 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:19:54,836 - INFO - Expected pages: [1]
2025-09-18 15:19:54,848 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9e6bc1f1_10_invoice_1.pdf
2025-09-18 15:19:55,044 - INFO - Splitting PDF into individual pages...
2025-09-18 15:19:55,045 - INFO - Splitting PDF 69b50405_10_invoice_6 into 2 pages
2025-09-18 15:19:55,060 - INFO - Split PDF into 2 pages
2025-09-18 15:19:55,060 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:19:55,060 - INFO - Expected pages: [1, 2]
2025-09-18 15:19:55,081 - INFO - Page 1: Extracted 829 characters, 43 lines from 4db0274d_10_invoice_3_81e8b219_page_001.pdf
2025-09-18 15:19:55,081 - INFO - Successfully processed page 1
2025-09-18 15:19:55,082 - INFO - Combined 1 pages into final text
2025-09-18 15:19:55,082 - INFO - Text validation for 4db0274d_10_invoice_3: 846 characters, 1 pages
2025-09-18 15:19:55,082 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:19:55,082 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:19:55,105 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/080d3ad6_10_invoice_10.pdf
2025-09-18 15:19:55,142 - INFO - 

10_invoice_1.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:19:55,143 - INFO - 

✓ Saved result: output/run1_10_invoice_1.json
2025-09-18 15:19:55,442 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9e6bc1f1_10_invoice_1.pdf
2025-09-18 15:19:55,685 - INFO - Splitting PDF into individual pages...
2025-09-18 15:19:55,687 - INFO - Splitting PDF 83b64f8d_10_invoice_8 into 1 pages
2025-09-18 15:19:55,693 - INFO - Split PDF into 1 pages
2025-09-18 15:19:55,694 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:19:55,694 - INFO - Expected pages: [1]
2025-09-18 15:19:56,055 - INFO - Splitting PDF into individual pages...
2025-09-18 15:19:56,055 - INFO - Splitting PDF 8aaa2698_10_invoice_9 into 1 pages
2025-09-18 15:19:56,058 - INFO - Split PDF into 1 pages
2025-09-18 15:19:56,058 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:19:56,058 - INFO - Expected pages: [1]
2025-09-18 15:19:57,173 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4db0274d_10_invoice_3.pdf
2025-09-18 15:19:57,194 - INFO - 

10_invoice_3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:19:57,194 - INFO - 

✓ Saved result: output/run1_10_invoice_3.json
2025-09-18 15:19:57,358 - INFO - Page 1: Extracted 940 characters, 39 lines from fc83d3c1_10_invoice_2_1247de29_page_001.pdf
2025-09-18 15:19:57,359 - INFO - Successfully processed page 1
2025-09-18 15:19:57,359 - INFO - Combined 1 pages into final text
2025-09-18 15:19:57,360 - INFO - Text validation for fc83d3c1_10_invoice_2: 957 characters, 1 pages
2025-09-18 15:19:57,360 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:19:57,360 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:19:57,387 - INFO - Page 1: Extracted 543 characters, 38 lines from 2c72b0ad_10_invoice_5_352cb921_page_001.pdf
2025-09-18 15:19:57,388 - INFO - Successfully processed page 1
2025-09-18 15:19:57,388 - INFO - Combined 1 pages into final text
2025-09-18 15:19:57,388 - INFO - Text validation for 2c72b0ad_10_invoice_5: 560 characters, 1 pages
2025-09-18 15:19:57,389 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:19:57,389 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:19:57,497 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4db0274d_10_invoice_3.pdf
2025-09-18 15:19:57,966 - INFO - Page 1: Extracted 744 characters, 55 lines from a14a8ffe_10_invoice_4_bdc39fbe_page_001.pdf
2025-09-18 15:19:57,966 - INFO - Successfully processed page 1
2025-09-18 15:19:57,966 - INFO - Combined 1 pages into final text
2025-09-18 15:19:57,967 - INFO - Text validation for a14a8ffe_10_invoice_4: 761 characters, 1 pages
2025-09-18 15:19:57,967 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:19:57,967 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:19:58,990 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2c72b0ad_10_invoice_5.pdf
2025-09-18 15:19:59,010 - INFO - 

10_invoice_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:19:59,011 - INFO - 

✓ Saved result: output/run1_10_invoice_5.json
2025-09-18 15:19:59,051 - INFO - Page 1: Extracted 575 characters, 38 lines from ab7f2926_10_invoice_7_1fb21988_page_001.pdf
2025-09-18 15:19:59,051 - INFO - Successfully processed page 1
2025-09-18 15:19:59,053 - INFO - Combined 1 pages into final text
2025-09-18 15:19:59,053 - INFO - Text validation for ab7f2926_10_invoice_7: 592 characters, 1 pages
2025-09-18 15:19:59,053 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:19:59,053 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:19:59,235 - INFO - Page 1: Extracted 399 characters, 30 lines from 83b64f8d_10_invoice_8_4d47ca8d_page_001.pdf
2025-09-18 15:19:59,235 - INFO - Successfully processed page 1
2025-09-18 15:19:59,237 - INFO - Combined 1 pages into final text
2025-09-18 15:19:59,237 - INFO - Text validation for 83b64f8d_10_invoice_8: 416 characters, 1 pages
2025-09-18 15:19:59,237 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:19:59,237 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:19:59,305 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2c72b0ad_10_invoice_5.pdf
2025-09-18 15:19:59,393 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fc83d3c1_10_invoice_2.pdf
2025-09-18 15:19:59,406 - INFO - 

10_invoice_2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:19:59,406 - INFO - 

✓ Saved result: output/run1_10_invoice_2.json
2025-09-18 15:19:59,704 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fc83d3c1_10_invoice_2.pdf
2025-09-18 15:19:59,744 - INFO - Page 1: Extracted 760 characters, 40 lines from 69b50405_10_invoice_6_bdde3601_page_001.pdf
2025-09-18 15:19:59,745 - INFO - Successfully processed page 1
2025-09-18 15:19:59,818 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a14a8ffe_10_invoice_4.pdf
2025-09-18 15:19:59,840 - INFO - 

10_invoice_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:19:59,841 - INFO - 

✓ Saved result: output/run1_10_invoice_4.json
2025-09-18 15:20:00,139 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a14a8ffe_10_invoice_4.pdf
2025-09-18 15:20:00,145 - INFO - Page 2: Extracted 561 characters, 32 lines from 69b50405_10_invoice_6_bdde3601_page_002.pdf
2025-09-18 15:20:00,145 - INFO - Successfully processed page 2
2025-09-18 15:20:00,145 - INFO - Combined 2 pages into final text
2025-09-18 15:20:00,145 - INFO - Text validation for 69b50405_10_invoice_6: 1357 characters, 2 pages
2025-09-18 15:20:00,145 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:20:00,145 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:20:00,254 - INFO - Page 1: Extracted 823 characters, 53 lines from 8aaa2698_10_invoice_9_6a489f8a_page_001.pdf
2025-09-18 15:20:00,254 - INFO - Successfully processed page 1
2025-09-18 15:20:00,255 - INFO - Combined 1 pages into final text
2025-09-18 15:20:00,255 - INFO - Text validation for 8aaa2698_10_invoice_9: 840 characters, 1 pages
2025-09-18 15:20:00,255 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:20:00,255 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:20:00,697 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/83b64f8d_10_invoice_8.pdf
2025-09-18 15:20:00,711 - INFO - 

10_invoice_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:20:00,711 - INFO - 

✓ Saved result: output/run1_10_invoice_8.json
2025-09-18 15:20:01,015 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/83b64f8d_10_invoice_8.pdf
2025-09-18 15:20:01,180 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ab7f2926_10_invoice_7.pdf
2025-09-18 15:20:01,196 - INFO - 

10_invoice_7.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:20:01,196 - INFO - 

✓ Saved result: output/run1_10_invoice_7.json
2025-09-18 15:20:01,500 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ab7f2926_10_invoice_7.pdf
2025-09-18 15:20:02,200 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8aaa2698_10_invoice_9.pdf
2025-09-18 15:20:02,222 - INFO - 

10_invoice_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:20:02,222 - INFO - 

✓ Saved result: output/run1_10_invoice_9.json
2025-09-18 15:20:02,530 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8aaa2698_10_invoice_9.pdf
2025-09-18 15:20:03,052 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/69b50405_10_invoice_6.pdf
2025-09-18 15:20:03,080 - INFO - 

10_invoice_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:20:03,080 - INFO - 

✓ Saved result: output/run1_10_invoice_6.json
2025-09-18 15:20:03,373 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/69b50405_10_invoice_6.pdf
2025-09-18 15:20:03,375 - INFO - 
📊 Processing Summary:
2025-09-18 15:20:03,375 - INFO -    Total files: 10
2025-09-18 15:20:03,375 - INFO -    Successful: 10
2025-09-18 15:20:03,375 - INFO -    Failed: 0
2025-09-18 15:20:03,375 - INFO -    Duration: 19.16 seconds
2025-09-18 15:20:03,375 - INFO -    Output directory: output
2025-09-18 15:20:03,375 - INFO - 
📋 Successfully Processed Files:
2025-09-18 15:20:03,376 - INFO -    📄 10_invoice_1.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:20:03,376 - INFO -    📄 10_invoice_10.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:20:03,376 - INFO -    📄 10_invoice_2.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:20:03,377 - INFO -    📄 10_invoice_3.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:20:03,377 - INFO -    📄 10_invoice_4.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:20:03,377 - INFO -    📄 10_invoice_5.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:20:03,377 - INFO -    📄 10_invoice_6.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"},{"page_no":2,"doc_type":"invoice"}]}
2025-09-18 15:20:03,377 - INFO -    📄 10_invoice_7.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:20:03,377 - INFO -    📄 10_invoice_8.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:20:03,377 - INFO -    📄 10_invoice_9.pdf: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:20:03,378 - INFO - 
============================================================================================================================================
2025-09-18 15:20:03,378 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 15:20:03,378 - INFO - ============================================================================================================================================
2025-09-18 15:20:03,378 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 15:20:03,378 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:20:03,378 - INFO - 10_invoice_1.pdf                                   1      invoice              run1_10_invoice_1.json                            
2025-09-18 15:20:03,378 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_1.pdf
2025-09-18 15:20:03,378 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_1.json
2025-09-18 15:20:03,378 - INFO - 
2025-09-18 15:20:03,378 - INFO - 10_invoice_10.pdf                                  1      invoice              run1_10_invoice_10.json                           
2025-09-18 15:20:03,378 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_10.pdf
2025-09-18 15:20:03,378 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_10.json
2025-09-18 15:20:03,378 - INFO - 
2025-09-18 15:20:03,378 - INFO - 10_invoice_2.pdf                                   1      invoice              run1_10_invoice_2.json                            
2025-09-18 15:20:03,378 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_2.pdf
2025-09-18 15:20:03,379 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_2.json
2025-09-18 15:20:03,379 - INFO - 
2025-09-18 15:20:03,379 - INFO - 10_invoice_3.pdf                                   1      invoice              run1_10_invoice_3.json                            
2025-09-18 15:20:03,379 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_3.pdf
2025-09-18 15:20:03,379 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_3.json
2025-09-18 15:20:03,379 - INFO - 
2025-09-18 15:20:03,379 - INFO - 10_invoice_4.pdf                                   1      invoice              run1_10_invoice_4.json                            
2025-09-18 15:20:03,379 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_4.pdf
2025-09-18 15:20:03,379 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_4.json
2025-09-18 15:20:03,379 - INFO - 
2025-09-18 15:20:03,379 - INFO - 10_invoice_5.pdf                                   1      invoice              run1_10_invoice_5.json                            
2025-09-18 15:20:03,379 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_5.pdf
2025-09-18 15:20:03,379 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_5.json
2025-09-18 15:20:03,379 - INFO - 
2025-09-18 15:20:03,379 - INFO - 10_invoice_6.pdf                                   1      invoice              run1_10_invoice_6.json                            
2025-09-18 15:20:03,379 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_6.pdf
2025-09-18 15:20:03,379 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_6.json
2025-09-18 15:20:03,379 - INFO - 
2025-09-18 15:20:03,379 - INFO - 10_invoice_6.pdf                                   2      invoice              run1_10_invoice_6.json                            
2025-09-18 15:20:03,380 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_6.pdf
2025-09-18 15:20:03,380 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_6.json
2025-09-18 15:20:03,380 - INFO - 
2025-09-18 15:20:03,380 - INFO - 10_invoice_7.pdf                                   1      invoice              run1_10_invoice_7.json                            
2025-09-18 15:20:03,380 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_7.pdf
2025-09-18 15:20:03,380 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_7.json
2025-09-18 15:20:03,380 - INFO - 
2025-09-18 15:20:03,380 - INFO - 10_invoice_8.pdf                                   1      invoice              run1_10_invoice_8.json                            
2025-09-18 15:20:03,380 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_8.pdf
2025-09-18 15:20:03,380 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_8.json
2025-09-18 15:20:03,380 - INFO - 
2025-09-18 15:20:03,380 - INFO - 10_invoice_9.pdf                                   1      invoice              run1_10_invoice_9.json                            
2025-09-18 15:20:03,380 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/10_invoice/10_invoice_9.pdf
2025-09-18 15:20:03,380 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_10_invoice_9.json
2025-09-18 15:20:03,380 - INFO - 
2025-09-18 15:20:03,380 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:20:03,380 - INFO - Total entries: 11
2025-09-18 15:20:03,381 - INFO - ============================================================================================================================================
2025-09-18 15:20:03,381 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 15:20:03,381 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:20:03,381 - INFO -   1. 10_invoice_1.pdf                    Page 1   → invoice         | run1_10_invoice_1.json
2025-09-18 15:20:03,381 - INFO -   2. 10_invoice_10.pdf                   Page 1   → invoice         | run1_10_invoice_10.json
2025-09-18 15:20:03,381 - INFO -   3. 10_invoice_2.pdf                    Page 1   → invoice         | run1_10_invoice_2.json
2025-09-18 15:20:03,381 - INFO -   4. 10_invoice_3.pdf                    Page 1   → invoice         | run1_10_invoice_3.json
2025-09-18 15:20:03,381 - INFO -   5. 10_invoice_4.pdf                    Page 1   → invoice         | run1_10_invoice_4.json
2025-09-18 15:20:03,381 - INFO -   6. 10_invoice_5.pdf                    Page 1   → invoice         | run1_10_invoice_5.json
2025-09-18 15:20:03,381 - INFO -   7. 10_invoice_6.pdf                    Page 1   → invoice         | run1_10_invoice_6.json
2025-09-18 15:20:03,381 - INFO -   8. 10_invoice_6.pdf                    Page 2   → invoice         | run1_10_invoice_6.json
2025-09-18 15:20:03,381 - INFO -   9. 10_invoice_7.pdf                    Page 1   → invoice         | run1_10_invoice_7.json
2025-09-18 15:20:03,381 - INFO -  10. 10_invoice_8.pdf                    Page 1   → invoice         | run1_10_invoice_8.json
2025-09-18 15:20:03,381 - INFO -  11. 10_invoice_9.pdf                    Page 1   → invoice         | run1_10_invoice_9.json
2025-09-18 15:20:03,381 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:20:03,381 - INFO - 
✅ Test completed: {'total_files': 10, 'processed': 10, 'failed': 0, 'errors': [], 'duration_seconds': 19.159968, 'processed_files': [{'filename': '10_invoice_1.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_1.pdf'}, {'filename': '10_invoice_10.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_10.pdf'}, {'filename': '10_invoice_2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_2.pdf'}, {'filename': '10_invoice_3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_3.pdf'}, {'filename': '10_invoice_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_4.pdf'}, {'filename': '10_invoice_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_5.pdf'}, {'filename': '10_invoice_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}, {'page_no': 2, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_6.pdf'}, {'filename': '10_invoice_7.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_7.pdf'}, {'filename': '10_invoice_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_8.pdf'}, {'filename': '10_invoice_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/10_invoice/10_invoice_9.pdf'}]}
