2025-09-18 15:20:16,941 - INFO - Logging initialized. Log file: logs/test_classification_20250918_152016.log
2025-09-18 15:20:16,941 - INFO - 📁 Found 11 files to process
2025-09-18 15:20:16,941 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 15:20:16,941 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 15:20:16,941 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 15:20:16,942 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 15:20:16,942 - INFO - ⬆️ [15:20:16] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 15:20:19,201 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/6e43ff49_21_lumper_receipt_1.jpeg
2025-09-18 15:20:19,201 - INFO - 🔍 [15:20:19] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 15:20:19,201 - INFO - ⬆️ [15:20:19] Uploading: 21_lumper_receipt_10.png
2025-09-18 15:20:19,203 - INFO - Initializing TextractProcessor...
2025-09-18 15:20:19,222 - INFO - Initializing BedrockProcessor...
2025-09-18 15:20:19,228 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6e43ff49_21_lumper_receipt_1.jpeg
2025-09-18 15:20:19,228 - INFO - Processing image from S3...
2025-09-18 15:20:19,852 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/d13d6a1f_21_lumper_receipt_10.png
2025-09-18 15:20:19,852 - INFO - 🔍 [15:20:19] Starting classification: 21_lumper_receipt_10.png
2025-09-18 15:20:19,853 - INFO - ⬆️ [15:20:19] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 15:20:19,855 - INFO - Initializing TextractProcessor...
2025-09-18 15:20:19,871 - INFO - Initializing BedrockProcessor...
2025-09-18 15:20:19,875 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d13d6a1f_21_lumper_receipt_10.png
2025-09-18 15:20:19,875 - INFO - Processing image from S3...
2025-09-18 15:20:21,098 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/81d9433c_21_lumper_receipt_11.jpeg
2025-09-18 15:20:21,098 - INFO - 🔍 [15:20:21] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 15:20:21,099 - INFO - ⬆️ [15:20:21] Uploading: 21_lumper_receipt_2.jpg
2025-09-18 15:20:21,100 - INFO - Initializing TextractProcessor...
2025-09-18 15:20:21,117 - INFO - Initializing BedrockProcessor...
2025-09-18 15:20:21,120 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/81d9433c_21_lumper_receipt_11.jpeg
2025-09-18 15:20:21,121 - INFO - Processing image from S3...
2025-09-18 15:20:21,939 - INFO - S3 Image temp/6e43ff49_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 15:20:21,940 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:20:21,940 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:20:22,092 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg -> s3://document-extraction-logistically/temp/db8591f5_21_lumper_receipt_2.jpg
2025-09-18 15:20:22,092 - INFO - 🔍 [15:20:22] Starting classification: 21_lumper_receipt_2.jpg
2025-09-18 15:20:22,093 - INFO - ⬆️ [15:20:22] Uploading: 21_lumper_receipt_3.png
2025-09-18 15:20:22,095 - INFO - Initializing TextractProcessor...
2025-09-18 15:20:22,107 - INFO - Initializing BedrockProcessor...
2025-09-18 15:20:22,111 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/db8591f5_21_lumper_receipt_2.jpg
2025-09-18 15:20:22,111 - INFO - Processing image from S3...
2025-09-18 15:20:23,100 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png -> s3://document-extraction-logistically/temp/a0fcb8a4_21_lumper_receipt_3.png
2025-09-18 15:20:23,100 - INFO - 🔍 [15:20:23] Starting classification: 21_lumper_receipt_3.png
2025-09-18 15:20:23,101 - INFO - ⬆️ [15:20:23] Uploading: 21_lumper_receipt_4.pdf
2025-09-18 15:20:23,103 - INFO - Initializing TextractProcessor...
2025-09-18 15:20:23,123 - INFO - Initializing BedrockProcessor...
2025-09-18 15:20:23,127 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a0fcb8a4_21_lumper_receipt_3.png
2025-09-18 15:20:23,127 - INFO - Processing image from S3...
2025-09-18 15:20:23,695 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf -> s3://document-extraction-logistically/temp/21b6cbbb_21_lumper_receipt_4.pdf
2025-09-18 15:20:23,695 - INFO - 🔍 [15:20:23] Starting classification: 21_lumper_receipt_4.pdf
2025-09-18 15:20:23,696 - INFO - ⬆️ [15:20:23] Uploading: 21_lumper_receipt_5.pdf
2025-09-18 15:20:23,697 - INFO - Initializing TextractProcessor...
2025-09-18 15:20:23,713 - INFO - Initializing BedrockProcessor...
2025-09-18 15:20:23,718 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/21b6cbbb_21_lumper_receipt_4.pdf
2025-09-18 15:20:23,723 - INFO - Processing PDF from S3...
2025-09-18 15:20:23,726 - INFO - S3 Image temp/d13d6a1f_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 15:20:23,726 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:20:23,726 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:20:23,731 - INFO - Downloading PDF from S3 to /tmp/tmpvom6kyyd/21b6cbbb_21_lumper_receipt_4.pdf
2025-09-18 15:20:23,957 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6e43ff49_21_lumper_receipt_1.jpeg
2025-09-18 15:20:24,149 - INFO - S3 Image temp/81d9433c_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 15:20:24,149 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:20:24,150 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:20:24,296 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf -> s3://document-extraction-logistically/temp/60657dcb_21_lumper_receipt_5.pdf
2025-09-18 15:20:24,297 - INFO - 🔍 [15:20:24] Starting classification: 21_lumper_receipt_5.pdf
2025-09-18 15:20:24,298 - INFO - ⬆️ [15:20:24] Uploading: 21_lumper_receipt_6.pdf
2025-09-18 15:20:24,300 - INFO - Initializing TextractProcessor...
2025-09-18 15:20:24,314 - INFO - Initializing BedrockProcessor...
2025-09-18 15:20:24,317 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/60657dcb_21_lumper_receipt_5.pdf
2025-09-18 15:20:24,317 - INFO - Processing PDF from S3...
2025-09-18 15:20:24,317 - INFO - Downloading PDF from S3 to /tmp/tmp0ihnpmbs/60657dcb_21_lumper_receipt_5.pdf
2025-09-18 15:20:24,666 - INFO - S3 Image temp/db8591f5_21_lumper_receipt_2.jpg: Extracted 184 characters, 12 lines
2025-09-18 15:20:24,666 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:20:24,666 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:20:24,928 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf -> s3://document-extraction-logistically/temp/eb383b38_21_lumper_receipt_6.pdf
2025-09-18 15:20:24,929 - INFO - 🔍 [15:20:24] Starting classification: 21_lumper_receipt_6.pdf
2025-09-18 15:20:24,930 - INFO - Initializing TextractProcessor...
2025-09-18 15:20:24,930 - INFO - ⬆️ [15:20:24] Uploading: 21_lumper_receipt_7.png
2025-09-18 15:20:24,942 - INFO - Initializing BedrockProcessor...
2025-09-18 15:20:24,955 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/eb383b38_21_lumper_receipt_6.pdf
2025-09-18 15:20:24,955 - INFO - Processing PDF from S3...
2025-09-18 15:20:24,956 - INFO - Downloading PDF from S3 to /tmp/tmp1jpjgzco/eb383b38_21_lumper_receipt_6.pdf
2025-09-18 15:20:25,046 - INFO - Splitting PDF into individual pages...
2025-09-18 15:20:25,047 - INFO - Splitting PDF 21b6cbbb_21_lumper_receipt_4 into 1 pages
2025-09-18 15:20:25,048 - INFO - Split PDF into 1 pages
2025-09-18 15:20:25,048 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:20:25,048 - INFO - Expected pages: [1]
2025-09-18 15:20:25,573 - INFO - Splitting PDF into individual pages...
2025-09-18 15:20:25,574 - INFO - Splitting PDF 60657dcb_21_lumper_receipt_5 into 1 pages
2025-09-18 15:20:25,574 - INFO - Split PDF into 1 pages
2025-09-18 15:20:25,574 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:20:25,575 - INFO - Expected pages: [1]
2025-09-18 15:20:25,591 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png -> s3://document-extraction-logistically/temp/10670f3e_21_lumper_receipt_7.png
2025-09-18 15:20:25,592 - INFO - 🔍 [15:20:25] Starting classification: 21_lumper_receipt_7.png
2025-09-18 15:20:25,592 - INFO - ⬆️ [15:20:25] Uploading: 21_lumper_receipt_8.pdf
2025-09-18 15:20:25,600 - INFO - Initializing TextractProcessor...
2025-09-18 15:20:25,611 - INFO - Initializing BedrockProcessor...
2025-09-18 15:20:25,614 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/10670f3e_21_lumper_receipt_7.png
2025-09-18 15:20:25,614 - INFO - Processing image from S3...
2025-09-18 15:20:25,988 - INFO - S3 Image temp/a0fcb8a4_21_lumper_receipt_3.png: Extracted 663 characters, 38 lines
2025-09-18 15:20:25,988 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:20:25,988 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:20:26,087 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/db8591f5_21_lumper_receipt_2.jpg
2025-09-18 15:20:26,188 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf -> s3://document-extraction-logistically/temp/36629b36_21_lumper_receipt_8.pdf
2025-09-18 15:20:26,188 - INFO - 🔍 [15:20:26] Starting classification: 21_lumper_receipt_8.pdf
2025-09-18 15:20:26,188 - INFO - ⬆️ [15:20:26] Uploading: 21_lumper_receipt_9.pdf
2025-09-18 15:20:26,189 - INFO - Initializing TextractProcessor...
2025-09-18 15:20:26,201 - INFO - Initializing BedrockProcessor...
2025-09-18 15:20:26,204 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/36629b36_21_lumper_receipt_8.pdf
2025-09-18 15:20:26,205 - INFO - Processing PDF from S3...
2025-09-18 15:20:26,205 - INFO - Downloading PDF from S3 to /tmp/tmpfnc77sez/36629b36_21_lumper_receipt_8.pdf
2025-09-18 15:20:26,533 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/81d9433c_21_lumper_receipt_11.jpeg
2025-09-18 15:20:26,618 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d13d6a1f_21_lumper_receipt_10.png
2025-09-18 15:20:26,688 - INFO - Splitting PDF into individual pages...
2025-09-18 15:20:26,689 - INFO - Splitting PDF eb383b38_21_lumper_receipt_6 into 1 pages
2025-09-18 15:20:26,695 - INFO - Split PDF into 1 pages
2025-09-18 15:20:26,695 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:20:26,695 - INFO - Expected pages: [1]
2025-09-18 15:20:26,834 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf -> s3://document-extraction-logistically/temp/f5604878_21_lumper_receipt_9.pdf
2025-09-18 15:20:26,835 - INFO - 🔍 [15:20:26] Starting classification: 21_lumper_receipt_9.pdf
2025-09-18 15:20:26,836 - INFO - Initializing TextractProcessor...
2025-09-18 15:20:26,855 - INFO - Initializing BedrockProcessor...
2025-09-18 15:20:26,859 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f5604878_21_lumper_receipt_9.pdf
2025-09-18 15:20:26,860 - INFO - Processing PDF from S3...
2025-09-18 15:20:26,861 - INFO - Downloading PDF from S3 to /tmp/tmpguxq1ima/f5604878_21_lumper_receipt_9.pdf
2025-09-18 15:20:26,865 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:20:26,866 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 15:20:27,175 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6e43ff49_21_lumper_receipt_1.jpeg
2025-09-18 15:20:27,182 - INFO - 

21_lumper_receipt_2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:20:27,182 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_2.json
2025-09-18 15:20:27,445 - INFO - Splitting PDF into individual pages...
2025-09-18 15:20:27,446 - INFO - Splitting PDF 36629b36_21_lumper_receipt_8 into 1 pages
2025-09-18 15:20:27,448 - INFO - Split PDF into 1 pages
2025-09-18 15:20:27,448 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:20:27,448 - INFO - Expected pages: [1]
2025-09-18 15:20:27,493 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/db8591f5_21_lumper_receipt_2.jpg
2025-09-18 15:20:27,510 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:20:27,510 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 15:20:27,818 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/81d9433c_21_lumper_receipt_11.jpeg
2025-09-18 15:20:27,825 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:20:27,825 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 15:20:27,985 - INFO - Page 1: Extracted 422 characters, 38 lines from 21b6cbbb_21_lumper_receipt_4_235bd16f_page_001.pdf
2025-09-18 15:20:27,985 - INFO - Successfully processed page 1
2025-09-18 15:20:27,985 - INFO - Combined 1 pages into final text
2025-09-18 15:20:27,985 - INFO - Text validation for 21b6cbbb_21_lumper_receipt_4: 439 characters, 1 pages
2025-09-18 15:20:27,986 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:20:27,986 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:20:28,163 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d13d6a1f_21_lumper_receipt_10.png
2025-09-18 15:20:28,165 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a0fcb8a4_21_lumper_receipt_3.png
2025-09-18 15:20:28,181 - INFO - 

21_lumper_receipt_3.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "invoice"
        }
    ]
}
2025-09-18 15:20:28,181 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_3.json
2025-09-18 15:20:28,280 - INFO - Page 1: Extracted 422 characters, 38 lines from 60657dcb_21_lumper_receipt_5_8d7f866e_page_001.pdf
2025-09-18 15:20:28,280 - INFO - Successfully processed page 1
2025-09-18 15:20:28,280 - INFO - Combined 1 pages into final text
2025-09-18 15:20:28,281 - INFO - Text validation for 60657dcb_21_lumper_receipt_5: 439 characters, 1 pages
2025-09-18 15:20:28,281 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:20:28,281 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:20:28,494 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a0fcb8a4_21_lumper_receipt_3.png
2025-09-18 15:20:28,643 - INFO - S3 Image temp/10670f3e_21_lumper_receipt_7.png: Extracted 800 characters, 70 lines
2025-09-18 15:20:28,643 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:20:28,643 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:20:28,910 - INFO - Splitting PDF into individual pages...
2025-09-18 15:20:28,911 - INFO - Splitting PDF f5604878_21_lumper_receipt_9 into 1 pages
2025-09-18 15:20:28,913 - INFO - Split PDF into 1 pages
2025-09-18 15:20:28,913 - INFO - Processing pages with Textract in parallel...
2025-09-18 15:20:28,913 - INFO - Expected pages: [1]
2025-09-18 15:20:30,619 - INFO - Page 1: Extracted 330 characters, 33 lines from 36629b36_21_lumper_receipt_8_d6119b30_page_001.pdf
2025-09-18 15:20:30,619 - INFO - Successfully processed page 1
2025-09-18 15:20:30,620 - INFO - Combined 1 pages into final text
2025-09-18 15:20:30,620 - INFO - Text validation for 36629b36_21_lumper_receipt_8: 347 characters, 1 pages
2025-09-18 15:20:30,621 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:20:30,621 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:20:30,706 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/21b6cbbb_21_lumper_receipt_4.pdf
2025-09-18 15:20:30,722 - INFO - 

21_lumper_receipt_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:20:30,722 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_4.json
2025-09-18 15:20:31,029 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/21b6cbbb_21_lumper_receipt_4.pdf
2025-09-18 15:20:31,086 - INFO - Page 1: Extracted 269 characters, 22 lines from eb383b38_21_lumper_receipt_6_edd2d9b6_page_001.pdf
2025-09-18 15:20:31,086 - INFO - Successfully processed page 1
2025-09-18 15:20:31,087 - INFO - Combined 1 pages into final text
2025-09-18 15:20:31,087 - INFO - Text validation for eb383b38_21_lumper_receipt_6: 286 characters, 1 pages
2025-09-18 15:20:31,087 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:20:31,088 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:20:31,159 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/10670f3e_21_lumper_receipt_7.png
2025-09-18 15:20:31,181 - INFO - 

21_lumper_receipt_7.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:20:31,181 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_7.json
2025-09-18 15:20:31,492 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/10670f3e_21_lumper_receipt_7.png
2025-09-18 15:20:31,841 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/60657dcb_21_lumper_receipt_5.pdf
2025-09-18 15:20:31,853 - INFO - 

21_lumper_receipt_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:20:31,853 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_5.json
2025-09-18 15:20:32,165 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/60657dcb_21_lumper_receipt_5.pdf
2025-09-18 15:20:32,693 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/36629b36_21_lumper_receipt_8.pdf
2025-09-18 15:20:32,704 - INFO - 

21_lumper_receipt_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:20:32,704 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_8.json
2025-09-18 15:20:33,017 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/36629b36_21_lumper_receipt_8.pdf
2025-09-18 15:20:33,106 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/eb383b38_21_lumper_receipt_6.pdf
2025-09-18 15:20:33,118 - INFO - 

21_lumper_receipt_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:20:33,118 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_6.json
2025-09-18 15:20:33,431 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/eb383b38_21_lumper_receipt_6.pdf
2025-09-18 15:20:34,843 - INFO - Page 1: Extracted 645 characters, 53 lines from f5604878_21_lumper_receipt_9_5b9b1e8e_page_001.pdf
2025-09-18 15:20:34,843 - INFO - Successfully processed page 1
2025-09-18 15:20:34,843 - INFO - Combined 1 pages into final text
2025-09-18 15:20:34,844 - INFO - Text validation for f5604878_21_lumper_receipt_9: 662 characters, 1 pages
2025-09-18 15:20:34,844 - INFO - Analyzing document types with Bedrock...
2025-09-18 15:20:34,844 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 15:20:37,877 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f5604878_21_lumper_receipt_9.pdf
2025-09-18 15:20:37,892 - INFO - 

21_lumper_receipt_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 15:20:37,892 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_9.json
2025-09-18 15:20:38,194 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f5604878_21_lumper_receipt_9.pdf
2025-09-18 15:20:38,195 - INFO - 
📊 Processing Summary:
2025-09-18 15:20:38,195 - INFO -    Total files: 11
2025-09-18 15:20:38,195 - INFO -    Successful: 11
2025-09-18 15:20:38,195 - INFO -    Failed: 0
2025-09-18 15:20:38,196 - INFO -    Duration: 21.25 seconds
2025-09-18 15:20:38,196 - INFO -    Output directory: output
2025-09-18 15:20:38,196 - INFO - 
📋 Successfully Processed Files:
2025-09-18 15:20:38,196 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:20:38,196 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:20:38,196 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:20:38,196 - INFO -    📄 21_lumper_receipt_2.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:20:38,196 - INFO -    📄 21_lumper_receipt_3.png: {"documents":[{"page_no":1,"doc_type":"invoice"}]}
2025-09-18 15:20:38,196 - INFO -    📄 21_lumper_receipt_4.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:20:38,196 - INFO -    📄 21_lumper_receipt_5.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:20:38,196 - INFO -    📄 21_lumper_receipt_6.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:20:38,196 - INFO -    📄 21_lumper_receipt_7.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:20:38,197 - INFO -    📄 21_lumper_receipt_8.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:20:38,197 - INFO -    📄 21_lumper_receipt_9.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 15:20:38,197 - INFO - 
============================================================================================================================================
2025-09-18 15:20:38,197 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 15:20:38,197 - INFO - ============================================================================================================================================
2025-09-18 15:20:38,197 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 15:20:38,197 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:20:38,198 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 15:20:38,198 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 15:20:38,198 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 15:20:38,198 - INFO - 
2025-09-18 15:20:38,198 - INFO - 21_lumper_receipt_10.png                           1      lumper_receipt       run1_21_lumper_receipt_10.json                    
2025-09-18 15:20:38,198 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 15:20:38,198 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 15:20:38,198 - INFO - 
2025-09-18 15:20:38,198 - INFO - 21_lumper_receipt_11.jpeg                          1      invoice              run1_21_lumper_receipt_11.json                    
2025-09-18 15:20:38,198 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 15:20:38,198 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 15:20:38,198 - INFO - 
2025-09-18 15:20:38,198 - INFO - 21_lumper_receipt_2.jpg                            1      lumper_receipt       run1_21_lumper_receipt_2.json                     
2025-09-18 15:20:38,198 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg
2025-09-18 15:20:38,198 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_2.json
2025-09-18 15:20:38,198 - INFO - 
2025-09-18 15:20:38,198 - INFO - 21_lumper_receipt_3.png                            1      invoice              run1_21_lumper_receipt_3.json                     
2025-09-18 15:20:38,199 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png
2025-09-18 15:20:38,199 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_3.json
2025-09-18 15:20:38,199 - INFO - 
2025-09-18 15:20:38,199 - INFO - 21_lumper_receipt_4.pdf                            1      lumper_receipt       run1_21_lumper_receipt_4.json                     
2025-09-18 15:20:38,199 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf
2025-09-18 15:20:38,199 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_4.json
2025-09-18 15:20:38,199 - INFO - 
2025-09-18 15:20:38,199 - INFO - 21_lumper_receipt_5.pdf                            1      lumper_receipt       run1_21_lumper_receipt_5.json                     
2025-09-18 15:20:38,199 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf
2025-09-18 15:20:38,199 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_5.json
2025-09-18 15:20:38,199 - INFO - 
2025-09-18 15:20:38,199 - INFO - 21_lumper_receipt_6.pdf                            1      lumper_receipt       run1_21_lumper_receipt_6.json                     
2025-09-18 15:20:38,199 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf
2025-09-18 15:20:38,199 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_6.json
2025-09-18 15:20:38,199 - INFO - 
2025-09-18 15:20:38,199 - INFO - 21_lumper_receipt_7.png                            1      lumper_receipt       run1_21_lumper_receipt_7.json                     
2025-09-18 15:20:38,199 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png
2025-09-18 15:20:38,199 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_7.json
2025-09-18 15:20:38,200 - INFO - 
2025-09-18 15:20:38,200 - INFO - 21_lumper_receipt_8.pdf                            1      lumper_receipt       run1_21_lumper_receipt_8.json                     
2025-09-18 15:20:38,200 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf
2025-09-18 15:20:38,200 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_8.json
2025-09-18 15:20:38,200 - INFO - 
2025-09-18 15:20:38,200 - INFO - 21_lumper_receipt_9.pdf                            1      lumper_receipt       run1_21_lumper_receipt_9.json                     
2025-09-18 15:20:38,200 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf
2025-09-18 15:20:38,200 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_9.json
2025-09-18 15:20:38,200 - INFO - 
2025-09-18 15:20:38,200 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 15:20:38,200 - INFO - Total entries: 11
2025-09-18 15:20:38,200 - INFO - ============================================================================================================================================
2025-09-18 15:20:38,200 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 15:20:38,200 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:20:38,200 - INFO -   1. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 15:20:38,200 - INFO -   2. 21_lumper_receipt_10.png            Page 1   → lumper_receipt  | run1_21_lumper_receipt_10.json
2025-09-18 15:20:38,200 - INFO -   3. 21_lumper_receipt_11.jpeg           Page 1   → invoice         | run1_21_lumper_receipt_11.json
2025-09-18 15:20:38,200 - INFO -   4. 21_lumper_receipt_2.jpg             Page 1   → lumper_receipt  | run1_21_lumper_receipt_2.json
2025-09-18 15:20:38,200 - INFO -   5. 21_lumper_receipt_3.png             Page 1   → invoice         | run1_21_lumper_receipt_3.json
2025-09-18 15:20:38,200 - INFO -   6. 21_lumper_receipt_4.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_4.json
2025-09-18 15:20:38,201 - INFO -   7. 21_lumper_receipt_5.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_5.json
2025-09-18 15:20:38,201 - INFO -   8. 21_lumper_receipt_6.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_6.json
2025-09-18 15:20:38,201 - INFO -   9. 21_lumper_receipt_7.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_7.json
2025-09-18 15:20:38,201 - INFO -  10. 21_lumper_receipt_8.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_8.json
2025-09-18 15:20:38,201 - INFO -  11. 21_lumper_receipt_9.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_9.json
2025-09-18 15:20:38,201 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 15:20:38,201 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 21.25368, 'processed_files': [{'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '21_lumper_receipt_2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg'}, {'filename': '21_lumper_receipt_3.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'invoice'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png'}, {'filename': '21_lumper_receipt_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf'}, {'filename': '21_lumper_receipt_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf'}, {'filename': '21_lumper_receipt_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf'}, {'filename': '21_lumper_receipt_7.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png'}, {'filename': '21_lumper_receipt_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf'}, {'filename': '21_lumper_receipt_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf'}]}
