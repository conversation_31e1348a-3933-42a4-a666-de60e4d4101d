2025-09-18 16:03:45,105 - INFO - Logging initialized. Log file: logs/test_classification_20250918_160345.log
2025-09-18 16:03:45,105 - INFO - 📁 Found 11 files to process
2025-09-18 16:03:45,105 - INFO - 🚀 Starting processing with run number: 1
2025-09-18 16:03:45,105 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-18 16:03:45,105 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-18 16:03:45,106 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-18 16:03:45,106 - INFO - ⬆️ [16:03:45] Uploading: 21_lumper_receipt_1.jpeg
2025-09-18 16:03:47,474 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg -> s3://document-extraction-logistically/temp/b0e748fc_21_lumper_receipt_1.jpeg
2025-09-18 16:03:47,474 - INFO - 🔍 [16:03:47] Starting classification: 21_lumper_receipt_1.jpeg
2025-09-18 16:03:47,475 - INFO - ⬆️ [16:03:47] Uploading: 21_lumper_receipt_10.png
2025-09-18 16:03:47,476 - INFO - Initializing TextractProcessor...
2025-09-18 16:03:47,496 - INFO - Initializing BedrockProcessor...
2025-09-18 16:03:47,502 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b0e748fc_21_lumper_receipt_1.jpeg
2025-09-18 16:03:47,502 - INFO - Processing image from S3...
2025-09-18 16:03:48,606 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png -> s3://document-extraction-logistically/temp/2ee23cde_21_lumper_receipt_10.png
2025-09-18 16:03:48,606 - INFO - 🔍 [16:03:48] Starting classification: 21_lumper_receipt_10.png
2025-09-18 16:03:48,607 - INFO - ⬆️ [16:03:48] Uploading: 21_lumper_receipt_11.jpeg
2025-09-18 16:03:48,608 - INFO - Initializing TextractProcessor...
2025-09-18 16:03:48,625 - INFO - Initializing BedrockProcessor...
2025-09-18 16:03:48,628 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2ee23cde_21_lumper_receipt_10.png
2025-09-18 16:03:48,629 - INFO - Processing image from S3...
2025-09-18 16:03:50,680 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg -> s3://document-extraction-logistically/temp/b9afb946_21_lumper_receipt_11.jpeg
2025-09-18 16:03:50,680 - INFO - 🔍 [16:03:50] Starting classification: 21_lumper_receipt_11.jpeg
2025-09-18 16:03:50,682 - INFO - ⬆️ [16:03:50] Uploading: 21_lumper_receipt_2.jpg
2025-09-18 16:03:50,682 - INFO - Initializing TextractProcessor...
2025-09-18 16:03:50,700 - INFO - Initializing BedrockProcessor...
2025-09-18 16:03:50,705 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b9afb946_21_lumper_receipt_11.jpeg
2025-09-18 16:03:50,706 - INFO - Processing image from S3...
2025-09-18 16:03:52,167 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg -> s3://document-extraction-logistically/temp/ac2d8ed7_21_lumper_receipt_2.jpg
2025-09-18 16:03:52,167 - INFO - 🔍 [16:03:52] Starting classification: 21_lumper_receipt_2.jpg
2025-09-18 16:03:52,168 - INFO - ⬆️ [16:03:52] Uploading: 21_lumper_receipt_3.png
2025-09-18 16:03:52,170 - INFO - Initializing TextractProcessor...
2025-09-18 16:03:52,186 - INFO - Initializing BedrockProcessor...
2025-09-18 16:03:52,190 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ac2d8ed7_21_lumper_receipt_2.jpg
2025-09-18 16:03:52,191 - INFO - Processing image from S3...
2025-09-18 16:03:53,604 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png -> s3://document-extraction-logistically/temp/26dbc265_21_lumper_receipt_3.png
2025-09-18 16:03:53,605 - INFO - 🔍 [16:03:53] Starting classification: 21_lumper_receipt_3.png
2025-09-18 16:03:53,606 - INFO - ⬆️ [16:03:53] Uploading: 21_lumper_receipt_4.pdf
2025-09-18 16:03:53,606 - INFO - Initializing TextractProcessor...
2025-09-18 16:03:53,630 - INFO - Initializing BedrockProcessor...
2025-09-18 16:03:53,633 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/26dbc265_21_lumper_receipt_3.png
2025-09-18 16:03:53,634 - INFO - Processing image from S3...
2025-09-18 16:03:54,186 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf -> s3://document-extraction-logistically/temp/36116464_21_lumper_receipt_4.pdf
2025-09-18 16:03:54,187 - INFO - 🔍 [16:03:54] Starting classification: 21_lumper_receipt_4.pdf
2025-09-18 16:03:54,188 - INFO - ⬆️ [16:03:54] Uploading: 21_lumper_receipt_5.pdf
2025-09-18 16:03:54,188 - INFO - Initializing TextractProcessor...
2025-09-18 16:03:54,208 - INFO - Initializing BedrockProcessor...
2025-09-18 16:03:54,214 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/36116464_21_lumper_receipt_4.pdf
2025-09-18 16:03:54,215 - INFO - Processing PDF from S3...
2025-09-18 16:03:54,215 - INFO - Downloading PDF from S3 to /tmp/tmpcla37zsd/36116464_21_lumper_receipt_4.pdf
2025-09-18 16:03:54,760 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf -> s3://document-extraction-logistically/temp/83f8a698_21_lumper_receipt_5.pdf
2025-09-18 16:03:54,760 - INFO - 🔍 [16:03:54] Starting classification: 21_lumper_receipt_5.pdf
2025-09-18 16:03:54,761 - INFO - ⬆️ [16:03:54] Uploading: 21_lumper_receipt_6.pdf
2025-09-18 16:03:54,761 - INFO - Initializing TextractProcessor...
2025-09-18 16:03:54,780 - INFO - Initializing BedrockProcessor...
2025-09-18 16:03:54,786 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/83f8a698_21_lumper_receipt_5.pdf
2025-09-18 16:03:54,786 - INFO - Processing PDF from S3...
2025-09-18 16:03:54,786 - INFO - Downloading PDF from S3 to /tmp/tmp9z53pyvc/83f8a698_21_lumper_receipt_5.pdf
2025-09-18 16:03:55,341 - INFO - S3 Image temp/ac2d8ed7_21_lumper_receipt_2.jpg: Extracted 184 characters, 12 lines
2025-09-18 16:03:55,341 - INFO - Analyzing document types with Bedrock...
2025-09-18 16:03:55,341 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 16:03:55,394 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf -> s3://document-extraction-logistically/temp/529200bc_21_lumper_receipt_6.pdf
2025-09-18 16:03:55,395 - INFO - 🔍 [16:03:55] Starting classification: 21_lumper_receipt_6.pdf
2025-09-18 16:03:55,395 - INFO - ⬆️ [16:03:55] Uploading: 21_lumper_receipt_7.png
2025-09-18 16:03:55,398 - INFO - Initializing TextractProcessor...
2025-09-18 16:03:55,415 - INFO - Initializing BedrockProcessor...
2025-09-18 16:03:55,420 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/529200bc_21_lumper_receipt_6.pdf
2025-09-18 16:03:55,421 - INFO - Processing PDF from S3...
2025-09-18 16:03:55,421 - INFO - Downloading PDF from S3 to /tmp/tmpe_7vjplz/529200bc_21_lumper_receipt_6.pdf
2025-09-18 16:03:55,486 - INFO - S3 Image temp/b0e748fc_21_lumper_receipt_1.jpeg: Extracted 369 characters, 37 lines
2025-09-18 16:03:55,486 - INFO - Analyzing document types with Bedrock...
2025-09-18 16:03:55,486 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 16:03:55,515 - INFO - Splitting PDF into individual pages...
2025-09-18 16:03:55,516 - INFO - Splitting PDF 36116464_21_lumper_receipt_4 into 1 pages
2025-09-18 16:03:55,519 - INFO - Split PDF into 1 pages
2025-09-18 16:03:55,520 - INFO - Processing pages with Textract in parallel...
2025-09-18 16:03:55,520 - INFO - Expected pages: [1]
2025-09-18 16:03:55,569 - INFO - S3 Image temp/2ee23cde_21_lumper_receipt_10.png: Extracted 594 characters, 44 lines
2025-09-18 16:03:55,569 - INFO - Analyzing document types with Bedrock...
2025-09-18 16:03:55,569 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 16:03:56,069 - INFO - Splitting PDF into individual pages...
2025-09-18 16:03:56,070 - INFO - Splitting PDF 83f8a698_21_lumper_receipt_5 into 1 pages
2025-09-18 16:03:56,072 - INFO - Split PDF into 1 pages
2025-09-18 16:03:56,072 - INFO - Processing pages with Textract in parallel...
2025-09-18 16:03:56,072 - INFO - Expected pages: [1]
2025-09-18 16:03:56,083 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png -> s3://document-extraction-logistically/temp/84c01b37_21_lumper_receipt_7.png
2025-09-18 16:03:56,084 - INFO - 🔍 [16:03:56] Starting classification: 21_lumper_receipt_7.png
2025-09-18 16:03:56,085 - INFO - ⬆️ [16:03:56] Uploading: 21_lumper_receipt_8.pdf
2025-09-18 16:03:56,086 - INFO - Initializing TextractProcessor...
2025-09-18 16:03:56,098 - INFO - Initializing BedrockProcessor...
2025-09-18 16:03:56,100 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/84c01b37_21_lumper_receipt_7.png
2025-09-18 16:03:56,100 - INFO - Processing image from S3...
2025-09-18 16:03:56,411 - INFO - S3 Image temp/b9afb946_21_lumper_receipt_11.jpeg: Extracted 520 characters, 49 lines
2025-09-18 16:03:56,411 - INFO - Analyzing document types with Bedrock...
2025-09-18 16:03:56,411 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 16:03:56,418 - INFO - S3 Image temp/26dbc265_21_lumper_receipt_3.png: Extracted 663 characters, 38 lines
2025-09-18 16:03:56,418 - INFO - Analyzing document types with Bedrock...
2025-09-18 16:03:56,418 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 16:03:56,640 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf -> s3://document-extraction-logistically/temp/afb69ac1_21_lumper_receipt_8.pdf
2025-09-18 16:03:56,640 - INFO - 🔍 [16:03:56] Starting classification: 21_lumper_receipt_8.pdf
2025-09-18 16:03:56,641 - INFO - Initializing TextractProcessor...
2025-09-18 16:03:56,641 - INFO - ⬆️ [16:03:56] Uploading: 21_lumper_receipt_9.pdf
2025-09-18 16:03:56,653 - INFO - Initializing BedrockProcessor...
2025-09-18 16:03:56,661 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/afb69ac1_21_lumper_receipt_8.pdf
2025-09-18 16:03:56,662 - INFO - Processing PDF from S3...
2025-09-18 16:03:56,662 - INFO - Downloading PDF from S3 to /tmp/tmp60j_9dl2/afb69ac1_21_lumper_receipt_8.pdf
2025-09-18 16:03:57,000 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ac2d8ed7_21_lumper_receipt_2.jpg
2025-09-18 16:03:57,313 - INFO - Splitting PDF into individual pages...
2025-09-18 16:03:57,315 - INFO - Splitting PDF 529200bc_21_lumper_receipt_6 into 1 pages
2025-09-18 16:03:57,319 - INFO - Split PDF into 1 pages
2025-09-18 16:03:57,320 - INFO - Processing pages with Textract in parallel...
2025-09-18 16:03:57,320 - INFO - Expected pages: [1]
2025-09-18 16:03:57,550 - INFO - ✓ Uploaded: input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf -> s3://document-extraction-logistically/temp/fe5708ac_21_lumper_receipt_9.pdf
2025-09-18 16:03:57,551 - INFO - 🔍 [16:03:57] Starting classification: 21_lumper_receipt_9.pdf
2025-09-18 16:03:57,553 - INFO - Initializing TextractProcessor...
2025-09-18 16:03:57,567 - INFO - 

21_lumper_receipt_2.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 16:03:57,567 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_2.json
2025-09-18 16:03:57,574 - INFO - Initializing BedrockProcessor...
2025-09-18 16:03:57,579 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fe5708ac_21_lumper_receipt_9.pdf
2025-09-18 16:03:57,579 - INFO - Processing PDF from S3...
2025-09-18 16:03:57,580 - INFO - Downloading PDF from S3 to /tmp/tmpfg963z15/fe5708ac_21_lumper_receipt_9.pdf
2025-09-18 16:03:57,860 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ac2d8ed7_21_lumper_receipt_2.jpg
2025-09-18 16:03:57,922 - INFO - Splitting PDF into individual pages...
2025-09-18 16:03:57,923 - INFO - Splitting PDF afb69ac1_21_lumper_receipt_8 into 1 pages
2025-09-18 16:03:57,925 - INFO - Split PDF into 1 pages
2025-09-18 16:03:57,925 - INFO - Processing pages with Textract in parallel...
2025-09-18 16:03:57,925 - INFO - Expected pages: [1]
2025-09-18 16:03:58,116 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b0e748fc_21_lumper_receipt_1.jpeg
2025-09-18 16:03:58,130 - INFO - 

21_lumper_receipt_1.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 16:03:58,130 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_1.json
2025-09-18 16:03:58,422 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b0e748fc_21_lumper_receipt_1.jpeg
2025-09-18 16:03:58,494 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2ee23cde_21_lumper_receipt_10.png
2025-09-18 16:03:58,512 - INFO - 

21_lumper_receipt_10.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 16:03:58,513 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_10.json
2025-09-18 16:03:58,580 - INFO - Page 1: Extracted 422 characters, 38 lines from 36116464_21_lumper_receipt_4_c0dfa89c_page_001.pdf
2025-09-18 16:03:58,581 - INFO - Successfully processed page 1
2025-09-18 16:03:58,581 - INFO - Combined 1 pages into final text
2025-09-18 16:03:58,581 - INFO - Text validation for 36116464_21_lumper_receipt_4: 439 characters, 1 pages
2025-09-18 16:03:58,582 - INFO - Analyzing document types with Bedrock...
2025-09-18 16:03:58,582 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 16:03:58,808 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2ee23cde_21_lumper_receipt_10.png
2025-09-18 16:03:58,872 - INFO - Page 1: Extracted 422 characters, 38 lines from 83f8a698_21_lumper_receipt_5_cea23910_page_001.pdf
2025-09-18 16:03:58,872 - INFO - Successfully processed page 1
2025-09-18 16:03:58,873 - INFO - Combined 1 pages into final text
2025-09-18 16:03:58,873 - INFO - Text validation for 83f8a698_21_lumper_receipt_5: 439 characters, 1 pages
2025-09-18 16:03:58,873 - INFO - Analyzing document types with Bedrock...
2025-09-18 16:03:58,874 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 16:03:59,250 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b9afb946_21_lumper_receipt_11.jpeg
2025-09-18 16:03:59,259 - INFO - 

21_lumper_receipt_11.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 16:03:59,259 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_11.json
2025-09-18 16:03:59,412 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/26dbc265_21_lumper_receipt_3.png
2025-09-18 16:03:59,523 - INFO - S3 Image temp/84c01b37_21_lumper_receipt_7.png: Extracted 800 characters, 70 lines
2025-09-18 16:03:59,524 - INFO - Analyzing document types with Bedrock...
2025-09-18 16:03:59,524 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 16:03:59,542 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b9afb946_21_lumper_receipt_11.jpeg
2025-09-18 16:03:59,563 - INFO - 

21_lumper_receipt_3.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 16:03:59,564 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_3.json
2025-09-18 16:03:59,735 - INFO - Splitting PDF into individual pages...
2025-09-18 16:03:59,737 - INFO - Splitting PDF fe5708ac_21_lumper_receipt_9 into 1 pages
2025-09-18 16:03:59,739 - INFO - Split PDF into 1 pages
2025-09-18 16:03:59,740 - INFO - Processing pages with Textract in parallel...
2025-09-18 16:03:59,740 - INFO - Expected pages: [1]
2025-09-18 16:03:59,851 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/26dbc265_21_lumper_receipt_3.png
2025-09-18 16:04:00,456 - INFO - Page 1: Extracted 330 characters, 33 lines from afb69ac1_21_lumper_receipt_8_1608c67d_page_001.pdf
2025-09-18 16:04:00,457 - INFO - Successfully processed page 1
2025-09-18 16:04:00,457 - INFO - Combined 1 pages into final text
2025-09-18 16:04:00,457 - INFO - Text validation for afb69ac1_21_lumper_receipt_8: 347 characters, 1 pages
2025-09-18 16:04:00,458 - INFO - Analyzing document types with Bedrock...
2025-09-18 16:04:00,458 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 16:04:00,967 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/36116464_21_lumper_receipt_4.pdf
2025-09-18 16:04:00,982 - INFO - 

21_lumper_receipt_4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 16:04:00,982 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_4.json
2025-09-18 16:04:01,268 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/36116464_21_lumper_receipt_4.pdf
2025-09-18 16:04:01,667 - INFO - Page 1: Extracted 269 characters, 22 lines from 529200bc_21_lumper_receipt_6_6520a678_page_001.pdf
2025-09-18 16:04:01,668 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/83f8a698_21_lumper_receipt_5.pdf
2025-09-18 16:04:01,672 - INFO - Successfully processed page 1
2025-09-18 16:04:01,672 - INFO - Combined 1 pages into final text
2025-09-18 16:04:01,673 - INFO - Text validation for 529200bc_21_lumper_receipt_6: 286 characters, 1 pages
2025-09-18 16:04:01,679 - INFO - Analyzing document types with Bedrock...
2025-09-18 16:04:01,680 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 16:04:01,688 - INFO - 

21_lumper_receipt_5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-18 16:04:01,688 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_5.json
2025-09-18 16:04:01,980 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/83f8a698_21_lumper_receipt_5.pdf
2025-09-18 16:04:02,432 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/84c01b37_21_lumper_receipt_7.png
2025-09-18 16:04:02,447 - INFO - 

21_lumper_receipt_7.png

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 16:04:02,447 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_7.json
2025-09-18 16:04:02,588 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/afb69ac1_21_lumper_receipt_8.pdf
2025-09-18 16:04:02,745 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/84c01b37_21_lumper_receipt_7.png
2025-09-18 16:04:02,757 - INFO - 

21_lumper_receipt_8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 16:04:02,757 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_8.json
2025-09-18 16:04:03,050 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/afb69ac1_21_lumper_receipt_8.pdf
2025-09-18 16:04:03,812 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/529200bc_21_lumper_receipt_6.pdf
2025-09-18 16:04:03,823 - INFO - 

21_lumper_receipt_6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 16:04:03,823 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_6.json
2025-09-18 16:04:04,119 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/529200bc_21_lumper_receipt_6.pdf
2025-09-18 16:04:04,641 - INFO - Page 1: Extracted 645 characters, 53 lines from fe5708ac_21_lumper_receipt_9_44e5994d_page_001.pdf
2025-09-18 16:04:04,642 - INFO - Successfully processed page 1
2025-09-18 16:04:04,642 - INFO - Combined 1 pages into final text
2025-09-18 16:04:04,642 - INFO - Text validation for fe5708ac_21_lumper_receipt_9: 662 characters, 1 pages
2025-09-18 16:04:04,642 - INFO - Analyzing document types with Bedrock...
2025-09-18 16:04:04,643 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-18 16:04:07,989 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fe5708ac_21_lumper_receipt_9.pdf
2025-09-18 16:04:08,010 - INFO - 

21_lumper_receipt_9.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "lumper_receipt"
        }
    ]
}
2025-09-18 16:04:08,010 - INFO - 

✓ Saved result: output/run1_21_lumper_receipt_9.json
2025-09-18 16:04:08,290 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fe5708ac_21_lumper_receipt_9.pdf
2025-09-18 16:04:08,290 - INFO - 
📊 Processing Summary:
2025-09-18 16:04:08,291 - INFO -    Total files: 11
2025-09-18 16:04:08,291 - INFO -    Successful: 11
2025-09-18 16:04:08,291 - INFO -    Failed: 0
2025-09-18 16:04:08,291 - INFO -    Duration: 23.18 seconds
2025-09-18 16:04:08,291 - INFO -    Output directory: output
2025-09-18 16:04:08,291 - INFO - 
📋 Successfully Processed Files:
2025-09-18 16:04:08,291 - INFO -    📄 21_lumper_receipt_1.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 16:04:08,291 - INFO -    📄 21_lumper_receipt_10.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 16:04:08,292 - INFO -    📄 21_lumper_receipt_11.jpeg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 16:04:08,292 - INFO -    📄 21_lumper_receipt_2.jpg: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 16:04:08,292 - INFO -    📄 21_lumper_receipt_3.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 16:04:08,292 - INFO -    📄 21_lumper_receipt_4.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 16:04:08,292 - INFO -    📄 21_lumper_receipt_5.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-18 16:04:08,292 - INFO -    📄 21_lumper_receipt_6.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 16:04:08,292 - INFO -    📄 21_lumper_receipt_7.png: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 16:04:08,292 - INFO -    📄 21_lumper_receipt_8.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 16:04:08,292 - INFO -    📄 21_lumper_receipt_9.pdf: {"documents":[{"page_no":1,"doc_type":"lumper_receipt"}]}
2025-09-18 16:04:08,293 - INFO - 
============================================================================================================================================
2025-09-18 16:04:08,293 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-18 16:04:08,293 - INFO - ============================================================================================================================================
2025-09-18 16:04:08,293 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-18 16:04:08,293 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 16:04:08,293 - INFO - 21_lumper_receipt_1.jpeg                           1      lumper_receipt       run1_21_lumper_receipt_1.json                     
2025-09-18 16:04:08,293 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg
2025-09-18 16:04:08,293 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_1.json
2025-09-18 16:04:08,293 - INFO - 
2025-09-18 16:04:08,293 - INFO - 21_lumper_receipt_10.png                           1      lumper_receipt       run1_21_lumper_receipt_10.json                    
2025-09-18 16:04:08,294 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png
2025-09-18 16:04:08,294 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_10.json
2025-09-18 16:04:08,294 - INFO - 
2025-09-18 16:04:08,294 - INFO - 21_lumper_receipt_11.jpeg                          1      lumper_receipt       run1_21_lumper_receipt_11.json                    
2025-09-18 16:04:08,294 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg
2025-09-18 16:04:08,294 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_11.json
2025-09-18 16:04:08,294 - INFO - 
2025-09-18 16:04:08,294 - INFO - 21_lumper_receipt_2.jpg                            1      lumper_receipt       run1_21_lumper_receipt_2.json                     
2025-09-18 16:04:08,294 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg
2025-09-18 16:04:08,294 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_2.json
2025-09-18 16:04:08,294 - INFO - 
2025-09-18 16:04:08,294 - INFO - 21_lumper_receipt_3.png                            1      lumper_receipt       run1_21_lumper_receipt_3.json                     
2025-09-18 16:04:08,294 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png
2025-09-18 16:04:08,294 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_3.json
2025-09-18 16:04:08,294 - INFO - 
2025-09-18 16:04:08,294 - INFO - 21_lumper_receipt_4.pdf                            1      lumper_receipt       run1_21_lumper_receipt_4.json                     
2025-09-18 16:04:08,294 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf
2025-09-18 16:04:08,294 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_4.json
2025-09-18 16:04:08,294 - INFO - 
2025-09-18 16:04:08,294 - INFO - 21_lumper_receipt_5.pdf                            1      other                run1_21_lumper_receipt_5.json                     
2025-09-18 16:04:08,294 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf
2025-09-18 16:04:08,294 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_5.json
2025-09-18 16:04:08,294 - INFO - 
2025-09-18 16:04:08,294 - INFO - 21_lumper_receipt_6.pdf                            1      lumper_receipt       run1_21_lumper_receipt_6.json                     
2025-09-18 16:04:08,294 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf
2025-09-18 16:04:08,294 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_6.json
2025-09-18 16:04:08,294 - INFO - 
2025-09-18 16:04:08,294 - INFO - 21_lumper_receipt_7.png                            1      lumper_receipt       run1_21_lumper_receipt_7.json                     
2025-09-18 16:04:08,295 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png
2025-09-18 16:04:08,295 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_7.json
2025-09-18 16:04:08,295 - INFO - 
2025-09-18 16:04:08,295 - INFO - 21_lumper_receipt_8.pdf                            1      lumper_receipt       run1_21_lumper_receipt_8.json                     
2025-09-18 16:04:08,295 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf
2025-09-18 16:04:08,295 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_8.json
2025-09-18 16:04:08,295 - INFO - 
2025-09-18 16:04:08,295 - INFO - 21_lumper_receipt_9.pdf                            1      lumper_receipt       run1_21_lumper_receipt_9.json                     
2025-09-18 16:04:08,295 - INFO -   PDF → file:///home/<USER>/Desktop/test_ligistically/input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf
2025-09-18 16:04:08,295 - INFO -   JSON→ file:///home/<USER>/Desktop/test_ligistically/output/run1_21_lumper_receipt_9.json
2025-09-18 16:04:08,295 - INFO - 
2025-09-18 16:04:08,295 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-18 16:04:08,295 - INFO - Total entries: 11
2025-09-18 16:04:08,295 - INFO - ============================================================================================================================================
2025-09-18 16:04:08,295 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-18 16:04:08,295 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 16:04:08,295 - INFO -   1. 21_lumper_receipt_1.jpeg            Page 1   → lumper_receipt  | run1_21_lumper_receipt_1.json
2025-09-18 16:04:08,295 - INFO -   2. 21_lumper_receipt_10.png            Page 1   → lumper_receipt  | run1_21_lumper_receipt_10.json
2025-09-18 16:04:08,295 - INFO -   3. 21_lumper_receipt_11.jpeg           Page 1   → lumper_receipt  | run1_21_lumper_receipt_11.json
2025-09-18 16:04:08,295 - INFO -   4. 21_lumper_receipt_2.jpg             Page 1   → lumper_receipt  | run1_21_lumper_receipt_2.json
2025-09-18 16:04:08,295 - INFO -   5. 21_lumper_receipt_3.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_3.json
2025-09-18 16:04:08,295 - INFO -   6. 21_lumper_receipt_4.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_4.json
2025-09-18 16:04:08,295 - INFO -   7. 21_lumper_receipt_5.pdf             Page 1   → other           | run1_21_lumper_receipt_5.json
2025-09-18 16:04:08,295 - INFO -   8. 21_lumper_receipt_6.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_6.json
2025-09-18 16:04:08,295 - INFO -   9. 21_lumper_receipt_7.png             Page 1   → lumper_receipt  | run1_21_lumper_receipt_7.json
2025-09-18 16:04:08,295 - INFO -  10. 21_lumper_receipt_8.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_8.json
2025-09-18 16:04:08,295 - INFO -  11. 21_lumper_receipt_9.pdf             Page 1   → lumper_receipt  | run1_21_lumper_receipt_9.json
2025-09-18 16:04:08,295 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-18 16:04:08,295 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 23.184971, 'processed_files': [{'filename': '21_lumper_receipt_1.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_1.jpeg'}, {'filename': '21_lumper_receipt_10.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_10.png'}, {'filename': '21_lumper_receipt_11.jpeg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_11.jpeg'}, {'filename': '21_lumper_receipt_2.jpg', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_2.jpg'}, {'filename': '21_lumper_receipt_3.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_3.png'}, {'filename': '21_lumper_receipt_4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_4.pdf'}, {'filename': '21_lumper_receipt_5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_5.pdf'}, {'filename': '21_lumper_receipt_6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_6.pdf'}, {'filename': '21_lumper_receipt_7.png', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_7.png'}, {'filename': '21_lumper_receipt_8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_8.pdf'}, {'filename': '21_lumper_receipt_9.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'lumper_receipt'}]}, 'file_path': 'input_data_individual/21_lumper_receipt/21_lumper_receipt_9.pdf'}]}
