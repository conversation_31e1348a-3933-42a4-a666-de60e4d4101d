{"classification_result": {"documents": [{"page_no": 1, "doc_type": "invoice"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 4.742424408732404e-08, "Y": 1.0}]}, "Id": "d9ed07fe-8de3-4ce0-999e-6eff3dffe042", "Relationships": [{"Type": "CHILD", "Ids": ["490ed5d6-68aa-4858-83cf-a2d42fdb5bbd", "0fe2c1b1-2b03-4ec1-8750-0608509467a6", "dab7092e-4705-4d5f-8897-04397d292ac2", "b1b2db07-b30b-4f19-bf8d-71a9b59f990b", "24605e00-e7da-47fc-a661-6675f87cefe7", "03132284-ae63-4b6f-991d-8d563d9d1c77", "290c1f60-a367-4887-b1a7-f8010aff4208", "9acaa072-decf-4638-9121-541b055cca40", "06b73196-29cc-4750-a149-f1c41a0d5605", "5f0e283d-3a81-4c54-8418-669b338eeb8c", "b711bac4-86d4-4f51-830c-2fc43cd6d766", "7e1be3d9-6af8-4f12-85d9-060431c4833f", "8a7f3961-67b4-4e27-8b95-fecf1d6240c9", "10b147c4-ccb9-49e3-8ace-8e660b05880a", "9ceaa32c-fe5d-4005-bc5f-64712f13a62e", "6efef566-1edb-4f17-bf21-c2f3f415cc85", "96554e26-59a4-4c4d-82b6-3290fb1ec6a5", "da786c0b-31c0-43c9-8258-fe7654f404cf", "f2917dfd-4d1c-4148-9d85-84be992db3d2", "5f4787ba-63fb-4634-8bcc-a3a956a15c89", "906285f7-7eaf-4d30-98c6-29273729ceed", "c54bd03a-1e6f-4bec-8b16-4e6f10a4d5a7", "121ebae1-1b71-48a7-bab9-cffa6b184ccf", "131a4d9b-6d35-45f8-9969-21afcebc8203", "af6be203-1d06-4457-b468-8cc937b71014", "b68aa216-d859-440c-9e25-2f5ad9352e0d", "38016811-f355-4be7-aef3-ad0094e052fc", "3a6c9f8c-2d65-460d-aad7-47146defb065", "2be40438-405e-4b60-b66d-c10c6d8a7f94", "182f91df-d829-4e34-a0c6-1e3ff62ba32a"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "INVOICE", "Geometry": {"BoundingBox": {"Width": 0.0891040563583374, "Height": 0.011822395958006382, "Left": 0.05322177708148956, "Top": 0.059693578630685806}, "Polygon": [{"X": 0.05322177708148956, "Y": 0.059693727642297745}, {"X": 0.14232578873634338, "Y": 0.059693578630685806}, {"X": 0.14232583343982697, "Y": 0.07151582092046738}, {"X": 0.05322182923555374, "Y": 0.07151597738265991}]}, "Id": "490ed5d6-68aa-4858-83cf-a2d42fdb5bbd", "Relationships": [{"Type": "CHILD", "Ids": ["f9daab6b-c199-4201-9005-58c5375471bb"]}]}, {"BlockType": "LINE", "Confidence": 99.0977783203125, "Text": "R LOGISTICS LLC", "Geometry": {"BoundingBox": {"Width": 0.10400407016277313, "Height": 0.007861665450036526, "Left": 0.053084589540958405, "Top": 0.07863394170999527}, "Polygon": [{"X": 0.053084589540958405, "Y": 0.07863412797451019}, {"X": 0.15708863735198975, "Y": 0.07863394170999527}, {"X": 0.15708866715431213, "Y": 0.08649542182683945}, {"X": 0.05308462306857109, "Y": 0.08649560809135437}]}, "Id": "0fe2c1b1-2b03-4ec1-8750-0608509467a6", "Relationships": [{"Type": "CHILD", "Ids": ["84c418d7-5a17-4d20-8fb7-baad18248cf9", "81c5969d-e5e4-4d18-867e-36b5841f6057", "761b9a68-1848-48ce-90c9-a04bf6191890"]}]}, {"BlockType": "LINE", "Confidence": 99.91131591796875, "Text": "<EMAIL>", "Geometry": {"BoundingBox": {"Width": 0.1157127097249031, "Height": 0.009510430507361889, "Left": 0.2847188711166382, "Top": 0.07771598547697067}, "Polygon": [{"X": 0.2847188711166382, "Y": 0.07771619409322739}, {"X": 0.4004315733909607, "Y": 0.07771598547697067}, {"X": 0.4004315733909607, "Y": 0.08722620457410812}, {"X": 0.28471890091896057, "Y": 0.08722641319036484}]}, "Id": "dab7092e-4705-4d5f-8897-04397d292ac2", "Relationships": [{"Type": "CHILD", "Ids": ["d85660ad-64e2-4a17-aae8-928c66f63c06"]}]}, {"BlockType": "LINE", "Confidence": 99.35553741455078, "Text": "E7986 770th Ave", "Geometry": {"BoundingBox": {"Width": 0.09675782918930054, "Height": 0.007770573254674673, "Left": 0.05298956483602524, "Top": 0.09299353510141373}, "Polygon": [{"X": 0.05298956483602524, "Y": 0.09299371391534805}, {"X": 0.1497473567724228, "Y": 0.09299353510141373}, {"X": 0.14974738657474518, "Y": 0.10076392441987991}, {"X": 0.052989598363637924, "Y": 0.10076410323381424}]}, "Id": "b1b2db07-b30b-4f19-bf8d-71a9b59f990b", "Relationships": [{"Type": "CHILD", "Ids": ["eb1fc7b8-b279-46c2-9b73-e2ed203f1582", "a8e100b5-704e-44da-a5c0-6360847d24cf", "febbc16b-34fd-4b6d-8ca2-a23e89ca16e2"]}]}, {"BlockType": "LINE", "Confidence": 87.38371276855469, "Text": "+1 (715) 962-3202", "Geometry": {"BoundingBox": {"Width": 0.10712525993585587, "Height": 0.009917793795466423, "Left": 0.2849085032939911, "Top": 0.09193120151758194}, "Polygon": [{"X": 0.2849085032939911, "Y": 0.09193140268325806}, {"X": 0.39203375577926636, "Y": 0.09193120151758194}, {"X": 0.39203375577926636, "Y": 0.1018487885594368}, {"X": 0.2849085330963135, "Y": 0.10184899717569351}]}, "Id": "24605e00-e7da-47fc-a661-6675f87cefe7", "Relationships": [{"Type": "CHILD", "Ids": ["526609db-91a0-4ae1-933f-9305ac3a7759", "3bdca395-c626-40fd-b4b4-914c9d7f4836"]}]}, {"BlockType": "LINE", "Confidence": 99.44893646240234, "Text": "Colfax, WI 54730", "Geometry": {"BoundingBox": {"Width": 0.09587335586547852, "Height": 0.008848017081618309, "Left": 0.052837274968624115, "Top": 0.10716897994279861}, "Polygon": [{"X": 0.052837274968624115, "Y": 0.10716916620731354}, {"X": 0.14871059358119965, "Y": 0.10716897994279861}, {"X": 0.14871062338352203, "Y": 0.11601680517196655}, {"X": 0.0528373122215271, "Y": 0.11601699888706207}]}, "Id": "03132284-ae63-4b6f-991d-8d563d9d1c77", "Relationships": [{"Type": "CHILD", "Ids": ["7b4671e0-217c-4c9e-833f-d1edf91eec1e", "4fc193e6-24aa-4079-9fbd-3bd3706eb66b", "fe7e5a3b-77a1-4856-a712-b8f2bedab929"]}]}, {"BlockType": "LINE", "Confidence": 99.6044921875, "Text": "Bill to", "Geometry": {"BoundingBox": {"Width": 0.034764427691698074, "Height": 0.007827646099030972, "Left": 0.053165141493082047, "Top": 0.1886564940214157}, "Polygon": [{"X": 0.053165141493082047, "Y": 0.18865656852722168}, {"X": 0.08792953938245773, "Y": 0.1886564940214157}, {"X": 0.08792956918478012, "Y": 0.1964840441942215}, {"X": 0.05316517502069473, "Y": 0.19648413360118866}]}, "Id": "290c1f60-a367-4887-b1a7-f8010aff4208", "Relationships": [{"Type": "CHILD", "Ids": ["298607f0-2173-40d9-a210-778374767c74", "fb7f2e48-5e34-4d4c-9ac2-32ca46b0ddad"]}]}, {"BlockType": "LINE", "Confidence": 99.9560546875, "Text": "PACKER FREIGHT", "Geometry": {"BoundingBox": {"Width": 0.12178805470466614, "Height": 0.008943850174546242, "Left": 0.053285129368305206, "Top": 0.20444712042808533}, "Polygon": [{"X": 0.053285129368305206, "Y": 0.2044474184513092}, {"X": 0.17507314682006836, "Y": 0.20444712042808533}, {"X": 0.17507317662239075, "Y": 0.21339064836502075}, {"X": 0.05328516662120819, "Y": 0.21339096128940582}]}, "Id": "9acaa072-decf-4638-9121-541b055cca40", "Relationships": [{"Type": "CHILD", "Ids": ["a68c6727-7c9f-4ddb-bbef-c895a4a1d439", "95c30bfc-1a2e-41fa-a49e-1e3683f97b7b"]}]}, {"BlockType": "LINE", "Confidence": 99.5307846069336, "Text": "P.O. BOX 28407", "Geometry": {"BoundingBox": {"Width": 0.10519292205572128, "Height": 0.008912495337426662, "Left": 0.05327608808875084, "Top": 0.2214394211769104}, "Polygon": [{"X": 0.05327608808875084, "Y": 0.2214396893978119}, {"X": 0.15846897661685944, "Y": 0.2214394211769104}, {"X": 0.15846900641918182, "Y": 0.23035162687301636}, {"X": 0.053276125341653824, "Y": 0.23035190999507904}]}, "Id": "06b73196-29cc-4750-a149-f1c41a0d5605", "Relationships": [{"Type": "CHILD", "Ids": ["1022c1e6-cd2c-4007-ac10-8bfac4ad9219", "127fea3d-817b-4915-83e9-61ec7fb3e151", "e5cc4f8e-42f6-41af-9e60-d41abec13175"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "<EMAIL>", "Geometry": {"BoundingBox": {"Width": 0.145509272813797, "Height": 0.011515108868479729, "Left": 0.052981358021497726, "Top": 0.23838292062282562}, "Polygon": [{"X": 0.052981358021497726, "Y": 0.23838332295417786}, {"X": 0.19849058985710144, "Y": 0.23838292062282562}, {"X": 0.19849061965942383, "Y": 0.24989762902259827}, {"X": 0.052981406450271606, "Y": 0.2498980313539505}]}, "Id": "5f0e283d-3a81-4c54-8418-669b338eeb8c", "Relationships": [{"Type": "CHILD", "Ids": ["3b51e2ef-6ca3-4d73-b00d-48996a6325ad"]}]}, {"BlockType": "LINE", "Confidence": 99.77100372314453, "Text": "GREEN BAY, WI 54324 US", "Geometry": {"BoundingBox": {"Width": 0.17432211339473724, "Height": 0.010309629142284393, "Left": 0.053000226616859436, "Top": 0.2556881308555603}, "Polygon": [{"X": 0.053000226616859436, "Y": 0.2556886076927185}, {"X": 0.2273222953081131, "Y": 0.2556881308555603}, {"X": 0.22732234001159668, "Y": 0.2659972608089447}, {"X": 0.05300027132034302, "Y": 0.2659977674484253}]}, "Id": "b711bac4-86d4-4f51-830c-2fc43cd6d766", "Relationships": [{"Type": "CHILD", "Ids": ["1701af4a-5b03-47f7-8b47-5f18c0d752a0", "9587cdfa-5c91-4bdb-872a-eaff68756167", "c947d1d0-9798-465d-aff8-054dae75caaa", "300bd4e9-7ac3-4bca-b1a7-8e5313c2fea2", "fde7f196-13d5-4414-ac3c-c440900282f2"]}]}, {"BlockType": "LINE", "Confidence": 99.9755859375, "Text": "Invoice details", "Geometry": {"BoundingBox": {"Width": 0.09904959797859192, "Height": 0.009068777784705162, "Left": 0.05334409326314926, "Top": 0.34474682807922363}, "Polygon": [{"X": 0.05334409326314926, "Y": 0.3447471559047699}, {"X": 0.1523936539888382, "Y": 0.34474682807922363}, {"X": 0.15239368379116058, "Y": 0.3538152873516083}, {"X": 0.053344130516052246, "Y": 0.35381561517715454}]}, "Id": "7e1be3d9-6af8-4f12-85d9-060431c4833f", "Relationships": [{"Type": "CHILD", "Ids": ["d8dd18e0-d9c8-4e44-970a-0eeddaec1270", "ef3fdabe-4b9f-4844-8269-055fcb64319f"]}]}, {"BlockType": "LINE", "Confidence": 99.8009033203125, "Text": "Invoice no.: 9975", "Geometry": {"BoundingBox": {"Width": 0.11096329241991043, "Height": 0.008991815149784088, "Left": 0.05350954458117485, "Top": 0.3636644780635834}, "Polygon": [{"X": 0.05350954458117485, "Y": 0.363664835691452}, {"X": 0.1644728034734726, "Y": 0.3636644780635834}, {"X": 0.16447283327579498, "Y": 0.3726558983325958}, {"X": 0.053509581834077835, "Y": 0.37265628576278687}]}, "Id": "8a7f3961-67b4-4e27-8b95-fecf1d6240c9", "Relationships": [{"Type": "CHILD", "Ids": ["9a790547-3395-4033-a2ae-6930d669d172", "2dae76fb-2ce7-4478-b641-3e394291cf4a", "04f3ce66-f6b3-4dd1-a098-319ee3281aae"]}]}, {"BlockType": "LINE", "Confidence": 99.96745300292969, "Text": "Invoice date: 08/06/2025", "Geometry": {"BoundingBox": {"Width": 0.16263139247894287, "Height": 0.00884860660880804, "Left": 0.05349475517868996, "Top": 0.3806251883506775}, "Polygon": [{"X": 0.05349475517868996, "Y": 0.38062575459480286}, {"X": 0.21612611413002014, "Y": 0.3806251883506775}, {"X": 0.21612614393234253, "Y": 0.38947319984436035}, {"X": 0.05349479243159294, "Y": 0.3894737958908081}]}, "Id": "10b147c4-ccb9-49e3-8ace-8e660b05880a", "Relationships": [{"Type": "CHILD", "Ids": ["65c7fda2-11fb-4085-a348-ec0303a76149", "99d7f653-efc2-4d0c-9196-06726b7ac14c", "0c1b3f28-2d26-4721-9cef-a4ca773093ce"]}]}, {"BlockType": "LINE", "Confidence": 99.96745300292969, "Text": "Due date: 09/05/2025", "Geometry": {"BoundingBox": {"Width": 0.14181961119174957, "Height": 0.009018728509545326, "Left": 0.05342615395784378, "Top": 0.39786189794540405}, "Polygon": [{"X": 0.05342615395784378, "Y": 0.39786240458488464}, {"X": 0.19524572789669037, "Y": 0.39786189794540405}, {"X": 0.19524575769901276, "Y": 0.40688008069992065}, {"X": 0.053426191210746765, "Y": 0.40688061714172363}]}, "Id": "9ceaa32c-fe5d-4005-bc5f-64712f13a62e", "Relationships": [{"Type": "CHILD", "Ids": ["a5fe5835-f26d-4e95-8676-0d0566397b81", "5180c52d-f040-4031-bf50-ba5236e7af54", "8a237f73-04f6-4e4b-9bc5-5728b5936702"]}]}, {"BlockType": "LINE", "Confidence": 99.4921875, "Text": "#", "Geometry": {"BoundingBox": {"Width": 0.007260054815560579, "Height": 0.007916828617453575, "Left": 0.04401132091879845, "Top": 0.459294855594635}, "Polygon": [{"X": 0.04401132091879845, "Y": 0.4592948853969574}, {"X": 0.05127134174108505, "Y": 0.459294855594635}, {"X": 0.05127137526869774, "Y": 0.46721166372299194}, {"X": 0.04401135444641113, "Y": 0.46721169352531433}]}, "Id": "6efef566-1edb-4f17-bf21-c2f3f415cc85", "Relationships": [{"Type": "CHILD", "Ids": ["1f854d5e-a491-4dfe-bb28-fa76b3bd667f"]}]}, {"BlockType": "LINE", "Confidence": 99.9180908203125, "Text": "Product or service", "Geometry": {"BoundingBox": {"Width": 0.11878730356693268, "Height": 0.008870680816471577, "Left": 0.07526688277721405, "Top": 0.46125441789627075}, "Polygon": [{"X": 0.07526688277721405, "Y": 0.46125489473342896}, {"X": 0.19405415654182434, "Y": 0.46125441789627075}, {"X": 0.19405418634414673, "Y": 0.47012463212013245}, {"X": 0.07526691257953644, "Y": 0.47012510895729065}]}, "Id": "96554e26-59a4-4c4d-82b6-3290fb1ec6a5", "Relationships": [{"Type": "CHILD", "Ids": ["77455441-fc6f-47d4-93aa-a991725a839e", "94a17a60-0737-490b-8c14-75255364cb9a", "596a2c1a-6f55-44f6-b1dc-9d895d62edf3"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "Description", "Geometry": {"BoundingBox": {"Width": 0.07347099483013153, "Height": 0.011183403432369232, "Left": 0.3394065797328949, "Top": 0.4610744118690491}, "Polygon": [{"X": 0.3394065797328949, "Y": 0.46107470989227295}, {"X": 0.41287755966186523, "Y": 0.4610744118690491}, {"X": 0.41287755966186523, "Y": 0.472257524728775}, {"X": 0.3394066095352173, "Y": 0.4722578227519989}]}, "Id": "da786c0b-31c0-43c9-8258-fe7654f404cf", "Relationships": [{"Type": "CHILD", "Ids": ["7f9cc992-034e-4626-be6e-5b2b8ec098b2"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Qty", "Geometry": {"BoundingBox": {"Width": 0.02315523847937584, "Height": 0.01105691771954298, "Left": 0.6885911822319031, "Top": 0.461099237203598}, "Polygon": [{"X": 0.6885911822319031, "Y": 0.4610993266105652}, {"X": 0.7117463946342468, "Y": 0.461099237203598}, {"X": 0.7117463946342468, "Y": 0.4721560478210449}, {"X": 0.6885911822319031, "Y": 0.4721561670303345}]}, "Id": "f2917dfd-4d1c-4148-9d85-84be992db3d2", "Relationships": [{"Type": "CHILD", "Ids": ["1d0cc269-2226-4d6b-bb8d-0e89b852893f"]}]}, {"BlockType": "LINE", "Confidence": 99.89178466796875, "Text": "Rate", "Geometry": {"BoundingBox": {"Width": 0.029089871793985367, "Height": 0.009016372263431549, "Left": 0.7935863137245178, "Top": 0.46101444959640503}, "Polygon": [{"X": 0.7935863137245178, "Y": 0.4610145688056946}, {"X": 0.8226761817932129, "Y": 0.46101444959640503}, {"X": 0.8226761817932129, "Y": 0.4700307250022888}, {"X": 0.7935863137245178, "Y": 0.470030814409256}]}, "Id": "5f4787ba-63fb-4634-8bcc-a3a956a15c89", "Relationships": [{"Type": "CHILD", "Ids": ["123fc2ac-17b9-41fc-a963-166501839853"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.05287376791238785, "Height": 0.008844123221933842, "Left": 0.8717021942138672, "Top": 0.4612569808959961}, "Polygon": [{"X": 0.8717021942138672, "Y": 0.4612571895122528}, {"X": 0.9245759844779968, "Y": 0.4612569808959961}, {"X": 0.9245759844779968, "Y": 0.47010087966918945}, {"X": 0.8717021942138672, "Y": 0.47010108828544617}]}, "Id": "906285f7-7eaf-4d30-98c6-29273729ceed", "Relationships": [{"Type": "CHILD", "Ids": ["0c081a0b-de41-47de-a81f-19e71ae2a495"]}]}, {"BlockType": "LINE", "Confidence": 99.30424499511719, "Text": "1.", "Geometry": {"BoundingBox": {"Width": 0.009298951365053654, "Height": 0.007789513096213341, "Left": 0.044812750071287155, "Top": 0.4962839186191559}, "Polygon": [{"X": 0.044812750071287155, "Y": 0.49628394842147827}, {"X": 0.05411166697740555, "Y": 0.4962839186191559}, {"X": 0.054111700505018234, "Y": 0.5040733814239502}, {"X": 0.04481278359889984, "Y": 0.504073441028595}]}, "Id": "c54bd03a-1e6f-4bec-8b16-4e6f10a4d5a7", "Relationships": [{"Type": "CHILD", "Ids": ["5bf1f388-d02f-4225-8831-9385880a7a02"]}]}, {"BlockType": "LINE", "Confidence": 99.716796875, "Text": "LOAD", "Geometry": {"BoundingBox": {"Width": 0.038159340620040894, "Height": 0.008048713207244873, "Left": 0.07388897985219955, "Top": 0.4959985017776489}, "Polygon": [{"X": 0.07388897985219955, "Y": 0.49599865078926086}, {"X": 0.11204829066991806, "Y": 0.4959985017776489}, {"X": 0.11204832047224045, "Y": 0.5040470361709595}, {"X": 0.07388900965452194, "Y": 0.5040472149848938}]}, "Id": "121ebae1-1b71-48a7-bab9-cffa6b184ccf", "Relationships": [{"Type": "CHILD", "Ids": ["97c35dab-b81e-417d-9ed8-0d70ec84729b"]}]}, {"BlockType": "LINE", "Confidence": 96.43614196777344, "Text": "GREEN BAY, WI TO GARDNER, KS 8/5/25", "Geometry": {"BoundingBox": {"Width": 0.2556934952735901, "Height": 0.00956359226256609, "Left": 0.33793187141418457, "Top": 0.4959632456302643}, "Polygon": [{"X": 0.33793187141418457, "Y": 0.49596428871154785}, {"X": 0.5936253666877747, "Y": 0.4959632456302643}, {"X": 0.5936253666877747, "Y": 0.5055257678031921}, {"X": 0.33793190121650696, "Y": 0.5055268406867981}]}, "Id": "131a4d9b-6d35-45f8-9969-21afcebc8203", "Relationships": [{"Type": "CHILD", "Ids": ["52dd0cf5-8e21-4ac1-86c2-c02a6c29b509", "f158a5f7-64de-4f35-abea-deb26a9f3663", "b5db5fb8-187b-444c-949a-827b3ee32afa", "f6ccae15-c2d3-4655-87dc-021f01122838", "abe4b3a3-4c52-4a29-80ac-a158f262aa9d", "45390c91-c631-436f-9dc0-bc176a09873e", "28fe6856-da5a-4235-a232-6203afb54cc2"]}]}, {"BlockType": "LINE", "Confidence": 97.998046875, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.004342956468462944, "Height": 0.007855932228267193, "Left": 0.7064436078071594, "Top": 0.4962140619754791}, "Polygon": [{"X": 0.7064436078071594, "Y": 0.4962140619754791}, {"X": 0.7107865810394287, "Y": 0.4962140619754791}, {"X": 0.7107865810394287, "Y": 0.504069983959198}, {"X": 0.7064436078071594, "Y": 0.504069983959198}]}, "Id": "af6be203-1d06-4457-b468-8cc937b71014", "Relationships": [{"Type": "CHILD", "Ids": ["dbf89bb9-a313-4b62-988a-6b5288ffe3c3"]}]}, {"BlockType": "LINE", "Confidence": 99.912109375, "Text": "$1,400.00", "Geometry": {"BoundingBox": {"Width": 0.05844784900546074, "Height": 0.009543734602630138, "Left": 0.7650240063667297, "Top": 0.4959595799446106}, "Polygon": [{"X": 0.7650240063667297, "Y": 0.4959598183631897}, {"X": 0.8234718441963196, "Y": 0.4959595799446106}, {"X": 0.8234718441963196, "Y": 0.5055030584335327}, {"X": 0.7650240063667297, "Y": 0.5055032968521118}]}, "Id": "b68aa216-d859-440c-9e25-2f5ad9352e0d", "Relationships": [{"Type": "CHILD", "Ids": ["4deeb2fc-8f34-4cc5-98eb-8517ce760476"]}]}, {"BlockType": "LINE", "Confidence": 99.873046875, "Text": "$1,400.00", "Geometry": {"BoundingBox": {"Width": 0.05783401057124138, "Height": 0.009980030357837677, "Left": 0.8674253225326538, "Top": 0.49569299817085266}, "Polygon": [{"X": 0.8674253225326538, "Y": 0.49569323658943176}, {"X": 0.9252593517303467, "Y": 0.49569299817085266}, {"X": 0.9252593517303467, "Y": 0.5056727528572083}, {"X": 0.8674253225326538, "Y": 0.5056730508804321}]}, "Id": "38016811-f355-4be7-aef3-ad0094e052fc", "Relationships": [{"Type": "CHILD", "Ids": ["3262d6a6-feb9-4831-8cab-45a3abc91490"]}]}, {"BlockType": "LINE", "Confidence": 99.5703125, "Text": "#GBP11480264", "Geometry": {"BoundingBox": {"Width": 0.09404055029153824, "Height": 0.008286896161735058, "Left": 0.33764493465423584, "Top": 0.5113115310668945}, "Polygon": [{"X": 0.33764493465423584, "Y": 0.5113118886947632}, {"X": 0.4316854774951935, "Y": 0.5113115310668945}, {"X": 0.43168550729751587, "Y": 0.5195980072021484}, {"X": 0.3376449644565582, "Y": 0.5195984244346619}]}, "Id": "3a6c9f8c-2d65-460d-aad7-47146defb065", "Relationships": [{"Type": "CHILD", "Ids": ["f63e194b-0c73-4a49-aa5b-719997631915"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.03154497221112251, "Height": 0.008259673602879047, "Left": 0.6069855690002441, "Top": 0.5500890612602234}, "Polygon": [{"X": 0.6069855690002441, "Y": 0.5500892400741577}, {"X": 0.6385305523872375, "Y": 0.5500890612602234}, {"X": 0.6385305523872375, "Y": 0.5583485960960388}, {"X": 0.6069855690002441, "Y": 0.5583487749099731}]}, "Id": "2be40438-405e-4b60-b66d-c10c6d8a7f94", "Relationships": [{"Type": "CHILD", "Ids": ["e1dfb349-ae29-4f53-8761-038ec422dcf4"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "$1,400.00", "Geometry": {"BoundingBox": {"Width": 0.0884387195110321, "Height": 0.015128754079341888, "Left": 0.8385432362556458, "Top": 0.5470384359359741}, "Polygon": [{"X": 0.8385432362556458, "Y": 0.5470388531684875}, {"X": 0.9269819855690002, "Y": 0.5470384359359741}, {"X": 0.9269819259643555, "Y": 0.5621667504310608}, {"X": 0.8385432362556458, "Y": 0.5621671676635742}]}, "Id": "182f91df-d829-4e34-a0c6-1e3ff62ba32a", "Relationships": [{"Type": "CHILD", "Ids": ["0e8c02e6-b9a7-4ef6-9ec3-ee0d9e86ed8d"]}]}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "INVOICE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0891040563583374, "Height": 0.011822395958006382, "Left": 0.05322177708148956, "Top": 0.059693578630685806}, "Polygon": [{"X": 0.05322177708148956, "Y": 0.059693727642297745}, {"X": 0.14232578873634338, "Y": 0.059693578630685806}, {"X": 0.14232583343982697, "Y": 0.07151582092046738}, {"X": 0.05322182923555374, "Y": 0.07151597738265991}]}, "Id": "f9daab6b-c199-4201-9005-58c5375471bb"}, {"BlockType": "WORD", "Confidence": 98.16326141357422, "Text": "R", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00814748927950859, "Height": 0.007638720329850912, "Left": 0.053084589540958405, "Top": 0.07885689288377762}, "Polygon": [{"X": 0.053084589540958405, "Y": 0.07885690778493881}, {"X": 0.06123204901814461, "Y": 0.07885689288377762}, {"X": 0.061232078820466995, "Y": 0.08649559319019318}, {"X": 0.05308462306857109, "Y": 0.08649560809135437}]}, "Id": "84c418d7-5a17-4d20-8fb7-baad18248cf9"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "LOGISTICS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0653008371591568, "Height": 0.007765737362205982, "Left": 0.0649341493844986, "Top": 0.07863399386405945}, "Polygon": [{"X": 0.0649341493844986, "Y": 0.078634113073349}, {"X": 0.130234956741333, "Y": 0.07863399386405945}, {"X": 0.1302349865436554, "Y": 0.086399607360363}, {"X": 0.06493418663740158, "Y": 0.08639973402023315}]}, "Id": "81c5969d-e5e4-4d18-867e-36b5841f6057"}, {"BlockType": "WORD", "Confidence": 99.326171875, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023108722642064095, "Height": 0.007691068574786186, "Left": 0.133979931473732, "Top": 0.07864388823509216}, "Polygon": [{"X": 0.133979931473732, "Y": 0.07864392548799515}, {"X": 0.15708863735198975, "Y": 0.07864388823509216}, {"X": 0.15708865225315094, "Y": 0.08633491396903992}, {"X": 0.13397996127605438, "Y": 0.0863349586725235}]}, "Id": "761b9a68-1848-48ce-90c9-a04bf6191890"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "<EMAIL>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1157127097249031, "Height": 0.009510430507361889, "Left": 0.2847188711166382, "Top": 0.07771598547697067}, "Polygon": [{"X": 0.2847188711166382, "Y": 0.07771619409322739}, {"X": 0.4004315733909607, "Y": 0.07771598547697067}, {"X": 0.4004315733909607, "Y": 0.08722620457410812}, {"X": 0.28471890091896057, "Y": 0.08722641319036484}]}, "Id": "d85660ad-64e2-4a17-aae8-928c66f63c06"}, {"BlockType": "WORD", "Confidence": 98.9795913696289, "Text": "E7986", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03583095595240593, "Height": 0.007669696118682623, "Left": 0.05298956483602524, "Top": 0.09299364686012268}, "Polygon": [{"X": 0.05298956483602524, "Y": 0.09299371391534805}, {"X": 0.08882048726081848, "Y": 0.09299364686012268}, {"X": 0.08882051706314087, "Y": 0.10066327452659607}, {"X": 0.052989594638347626, "Y": 0.10066334158182144}]}, "Id": "eb1fc7b8-b279-46c2-9b73-e2ed203f1582"}, {"BlockType": "WORD", "Confidence": 99.20500183105469, "Text": "770th", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03229128196835518, "Height": 0.0076229083351790905, "Left": 0.09313153475522995, "Top": 0.09314112365245819}, "Polygon": [{"X": 0.09313153475522995, "Y": 0.09314118325710297}, {"X": 0.12542279064655304, "Y": 0.09314112365245819}, {"X": 0.12542282044887543, "Y": 0.1007639691233635}, {"X": 0.09313156455755234, "Y": 0.10076402872800827}]}, "Id": "a8e100b5-704e-44da-a5c0-6360847d24cf"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "Ave", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020361099392175674, "Height": 0.007455020677298307, "Left": 0.1293862909078598, "Top": 0.09318367391824722}, "Polygon": [{"X": 0.1293862909078598, "Y": 0.09318371117115021}, {"X": 0.14974737167358398, "Y": 0.09318367391824722}, {"X": 0.14974738657474518, "Y": 0.10063865035772324}, {"X": 0.1293863207101822, "Y": 0.10063869506120682}]}, "Id": "febbc16b-34fd-4b6d-8ca2-a23e89ca16e2"}, {"BlockType": "WORD", "Confidence": 98.58976745605469, "Text": "+1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012220846489071846, "Height": 0.007462244480848312, "Left": 0.2849085032939911, "Top": 0.09232070297002792}, "Polygon": [{"X": 0.2849085032939911, "Y": 0.09232072532176971}, {"X": 0.2971293330192566, "Y": 0.09232070297002792}, {"X": 0.297129362821579, "Y": 0.09978292137384415}, {"X": 0.2849085330963135, "Y": 0.09978295117616653}]}, "Id": "526609db-91a0-4ae1-933f-9305ac3a7759"}, {"BlockType": "WORD", "Confidence": 76.17765808105469, "Text": "(715) 962-3202", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09401626884937286, "Height": 0.009917768649756908, "Left": 0.2980175018310547, "Top": 0.09193120151758194}, "Polygon": [{"X": 0.2980175018310547, "Y": 0.09193138033151627}, {"X": 0.39203375577926636, "Y": 0.09193120151758194}, {"X": 0.39203375577926636, "Y": 0.1018487885594368}, {"X": 0.2980175316333771, "Y": 0.10184896737337112}]}, "Id": "3bdca395-c626-40fd-b4b4-914c9d7f4836"}, {"BlockType": "WORD", "Confidence": 99.0344009399414, "Text": "Colfax,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036992643028497696, "Height": 0.008847901597619057, "Left": 0.052837274968624115, "Top": 0.10716909915208817}, "Polygon": [{"X": 0.052837274968624115, "Y": 0.10716916620731354}, {"X": 0.08982988446950912, "Y": 0.10716909915208817}, {"X": 0.08982991427183151, "Y": 0.1160169243812561}, {"X": 0.0528373122215271, "Y": 0.11601699888706207}]}, "Id": "7b4671e0-217c-4c9e-833f-d1edf91eec1e"}, {"BlockType": "WORD", "Confidence": 99.51750183105469, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014085263945162296, "Height": 0.007439705543220043, "Left": 0.09405224770307541, "Top": 0.1073589026927948}, "Polygon": [{"X": 0.09405224770307541, "Y": 0.10735893249511719}, {"X": 0.10813748836517334, "Y": 0.1073589026927948}, {"X": 0.10813751816749573, "Y": 0.11479858309030533}, {"X": 0.0940522775053978, "Y": 0.11479861289262772}]}, "Id": "4fc193e6-24aa-4079-9fbd-3bd3706eb66b"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "54730", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0358673520386219, "Height": 0.0075468397699296474, "Left": 0.11284327507019043, "Top": 0.1073588877916336}, "Polygon": [{"X": 0.11284327507019043, "Y": 0.10735895484685898}, {"X": 0.14871059358119965, "Y": 0.1073588877916336}, {"X": 0.14871062338352203, "Y": 0.11490565538406372}, {"X": 0.11284330487251282, "Y": 0.11490572988986969}]}, "Id": "fe7e5a3b-77a1-4856-a712-b8f2bedab929"}, {"BlockType": "WORD", "Confidence": 99.8046875, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01826304756104946, "Height": 0.007759671192616224, "Left": 0.053165141493082047, "Top": 0.1886565238237381}, "Polygon": [{"X": 0.053165141493082047, "Y": 0.18865656852722168}, {"X": 0.07142815738916397, "Y": 0.1886565238237381}, {"X": 0.07142819464206696, "Y": 0.19641615450382233}, {"X": 0.05316517502069473, "Y": 0.1964161992073059}]}, "Id": "298607f0-2173-40d9-a210-778374767c74"}, {"BlockType": "WORD", "Confidence": 99.404296875, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012381205335259438, "Height": 0.007290968671441078, "Left": 0.07554836571216583, "Top": 0.18919311463832855}, "Polygon": [{"X": 0.07554836571216583, "Y": 0.18919314444065094}, {"X": 0.08792953938245773, "Y": 0.18919311463832855}, {"X": 0.08792956918478012, "Y": 0.1964840441942215}, {"X": 0.07554839551448822, "Y": 0.19648407399654388}]}, "Id": "fb7f2e48-5e34-4d4c-9ac2-32ca46b0ddad"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "PACKER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05720863863825798, "Height": 0.008943688124418259, "Left": 0.053285129368305206, "Top": 0.20444728434085846}, "Polygon": [{"X": 0.053285129368305206, "Y": 0.2044474184513092}, {"X": 0.1104937344789505, "Y": 0.20444728434085846}, {"X": 0.11049376428127289, "Y": 0.21339082717895508}, {"X": 0.05328516662120819, "Y": 0.21339096128940582}]}, "Id": "a68c6727-7c9f-4ddb-bbef-c895a4a1d439"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "FREIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06020734831690788, "Height": 0.008850093930959702, "Left": 0.11486583203077316, "Top": 0.20447874069213867}, "Polygon": [{"X": 0.11486583203077316, "Y": 0.2044788897037506}, {"X": 0.17507314682006836, "Y": 0.20447874069213867}, {"X": 0.17507317662239075, "Y": 0.21332868933677673}, {"X": 0.11486586928367615, "Y": 0.21332883834838867}]}, "Id": "95c30bfc-1a2e-41fa-a49e-1e3683f97b7b"}, {"BlockType": "WORD", "Confidence": 99.59721374511719, "Text": "P.O.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02570261061191559, "Height": 0.008889839984476566, "Left": 0.05327608808875084, "Top": 0.22146207094192505}, "Polygon": [{"X": 0.05327608808875084, "Y": 0.22146214544773102}, {"X": 0.07897866517305374, "Y": 0.22146207094192505}, {"X": 0.07897869497537613, "Y": 0.23035185039043427}, {"X": 0.053276125341653824, "Y": 0.23035190999507904}]}, "Id": "1022c1e6-cd2c-4007-ac10-8bfac4ad9219"}, {"BlockType": "WORD", "Confidence": 99.2783432006836, "Text": "BOX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029107647016644478, "Height": 0.008778521791100502, "Left": 0.08384693413972855, "Top": 0.22143954038619995}, "Polygon": [{"X": 0.08384693413972855, "Y": 0.22143961489200592}, {"X": 0.11295454949140549, "Y": 0.22143954038619995}, {"X": 0.11295457929372787, "Y": 0.23021797835826874}, {"X": 0.08384697139263153, "Y": 0.2302180528640747}]}, "Id": "127fea3d-817b-4915-83e9-61ec7fb3e151"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "28407", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04225549101829529, "Height": 0.008892240934073925, "Left": 0.11621351540088654, "Top": 0.2214491069316864}, "Polygon": [{"X": 0.11621351540088654, "Y": 0.22144921123981476}, {"X": 0.15846897661685944, "Y": 0.2214491069316864}, {"X": 0.15846900641918182, "Y": 0.23034124076366425}, {"X": 0.11621355265378952, "Y": 0.2303413450717926}]}, "Id": "e5cc4f8e-42f6-41af-9e60-d41abec13175"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "<EMAIL>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.145509272813797, "Height": 0.011515108868479729, "Left": 0.052981358021497726, "Top": 0.23838292062282562}, "Polygon": [{"X": 0.052981358021497726, "Y": 0.23838332295417786}, {"X": 0.19849058985710144, "Y": 0.23838292062282562}, {"X": 0.19849061965942383, "Y": 0.24989762902259827}, {"X": 0.052981406450271606, "Y": 0.2498980313539505}]}, "Id": "3b51e2ef-6ca3-4d73-b00d-48996a6325ad"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "GREEN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04767078161239624, "Height": 0.008938136510550976, "Left": 0.053000226616859436, "Top": 0.25571268796920776}, "Polygon": [{"X": 0.053000226616859436, "Y": 0.2557128369808197}, {"X": 0.10067097097635269, "Y": 0.25571268796920776}, {"X": 0.10067100822925568, "Y": 0.26465070247650146}, {"X": 0.05300026759505272, "Y": 0.2646508514881134}]}, "Id": "1701af4a-5b03-47f7-8b47-5f18c0d752a0"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "BAY,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029639389365911484, "Height": 0.01020457036793232, "Left": 0.10592824965715408, "Top": 0.255793035030365}, "Polygon": [{"X": 0.10592824965715408, "Y": 0.25579312443733215}, {"X": 0.13556760549545288, "Y": 0.255793035030365}, {"X": 0.13556763529777527, "Y": 0.2659975290298462}, {"X": 0.10592828691005707, "Y": 0.26599761843681335}]}, "Id": "9587cdfa-5c91-4bdb-872a-eaff68756167"}, {"BlockType": "WORD", "Confidence": 99.71520233154297, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016944466158747673, "Height": 0.008701840415596962, "Left": 0.1393710970878601, "Top": 0.25579988956451416}, "Polygon": [{"X": 0.1393710970878601, "Y": 0.25579991936683655}, {"X": 0.15631553530693054, "Y": 0.25579988956451416}, {"X": 0.15631556510925293, "Y": 0.2645016610622406}, {"X": 0.1393711268901825, "Y": 0.2645017206668854}]}, "Id": "c947d1d0-9798-465d-aff8-054dae75caaa"}, {"BlockType": "WORD", "Confidence": 99.6875, "Text": "54324", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04199374467134476, "Height": 0.008857745677232742, "Left": 0.1608496606349945, "Top": 0.25577038526535034}, "Polygon": [{"X": 0.1608496606349945, "Y": 0.2557705044746399}, {"X": 0.20284338295459747, "Y": 0.25577038526535034}, {"X": 0.20284341275691986, "Y": 0.26462802290916443}, {"X": 0.1608496904373169, "Y": 0.264628142118454}]}, "Id": "300bd4e9-7ac3-4bca-b1a7-8e5313c2fea2"}, {"BlockType": "WORD", "Confidence": 99.609375, "Text": "US", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019730500876903534, "Height": 0.008942446671426296, "Left": 0.20759183168411255, "Top": 0.2556881308555603}, "Polygon": [{"X": 0.20759183168411255, "Y": 0.2556881904602051}, {"X": 0.2273222953081131, "Y": 0.2556881308555603}, {"X": 0.22732232511043549, "Y": 0.264630526304245}, {"X": 0.20759186148643494, "Y": 0.26463058590888977}]}, "Id": "fde7f196-13d5-4414-ac3c-c440900282f2"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04909813031554222, "Height": 0.00899074599146843, "Left": 0.05334409326314926, "Top": 0.34474700689315796}, "Polygon": [{"X": 0.05334409326314926, "Y": 0.3447471559047699}, {"X": 0.1024421900510788, "Y": 0.34474700689315796}, {"X": 0.10244221985340118, "Y": 0.35373759269714355}, {"X": 0.053344130516052246, "Y": 0.3537377417087555}]}, "Id": "d8dd18e0-d9c8-4e44-970a-0eeddaec1270"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "details", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04583127796649933, "Height": 0.00889495573937893, "Left": 0.10656241327524185, "Top": 0.34492048621177673}, "Polygon": [{"X": 0.10656241327524185, "Y": 0.34492063522338867}, {"X": 0.1523936539888382, "Y": 0.34492048621177673}, {"X": 0.15239368379116058, "Y": 0.3538152873516083}, {"X": 0.10656245052814484, "Y": 0.3538154363632202}]}, "Id": "ef3fdabe-4b9f-4844-8269-055fcb64319f"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046096716076135635, "Height": 0.00898426678031683, "Left": 0.05350954458117485, "Top": 0.36367201805114746}, "Polygon": [{"X": 0.05350954458117485, "Y": 0.3636721670627594}, {"X": 0.0996062234044075, "Y": 0.36367201805114746}, {"X": 0.09960626065731049, "Y": 0.37265610694885254}, {"X": 0.053509581834077835, "Y": 0.37265628576278687}]}, "Id": "9a790547-3395-4033-a2ae-6930d669d172"}, {"BlockType": "WORD", "Confidence": 99.51988983154297, "Text": "no.:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023030970245599747, "Height": 0.006493995897471905, "Left": 0.10354536771774292, "Top": 0.36583399772644043}, "Polygon": [{"X": 0.10354536771774292, "Y": 0.3658340871334076}, {"X": 0.12657631933689117, "Y": 0.36583399772644043}, {"X": 0.12657633423805237, "Y": 0.37232792377471924}, {"X": 0.10354539752006531, "Y": 0.372327983379364}]}, "Id": "2dae76fb-2ce7-4478-b641-3e394291cf4a"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "9975", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033313874155282974, "Height": 0.00886857882142067, "Left": 0.1311589628458023, "Top": 0.3636644780635834}, "Polygon": [{"X": 0.1311589628458023, "Y": 0.36366456747055054}, {"X": 0.1644728034734726, "Y": 0.3636644780635834}, {"X": 0.16447283327579498, "Y": 0.3725329339504242}, {"X": 0.1311589926481247, "Y": 0.37253305315971375}]}, "Id": "04f3ce66-f6b3-4dd1-a098-319ee3281aae"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04614857956767082, "Height": 0.008769189938902855, "Left": 0.05349475517868996, "Top": 0.3807046115398407}, "Polygon": [{"X": 0.05349475517868996, "Y": 0.38070476055145264}, {"X": 0.09964330494403839, "Y": 0.3807046115398407}, {"X": 0.09964333474636078, "Y": 0.3894736170768738}, {"X": 0.05349479243159294, "Y": 0.3894737958908081}]}, "Id": "65c7fda2-11fb-4085-a348-ec0303a76149"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031162697821855545, "Height": 0.008716300129890442, "Left": 0.1032552421092987, "Top": 0.38064703345298767}, "Polygon": [{"X": 0.1032552421092987, "Y": 0.3806471526622772}, {"X": 0.13441790640354156, "Y": 0.38064703345298767}, {"X": 0.13441793620586395, "Y": 0.38936322927474976}, {"X": 0.10325527936220169, "Y": 0.3893633484840393}]}, "Id": "99d7f653-efc2-4d0c-9196-06726b7ac14c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "08/06/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07747814804315567, "Height": 0.008826972916722298, "Left": 0.13864798843860626, "Top": 0.3806251883506775}, "Polygon": [{"X": 0.13864798843860626, "Y": 0.380625456571579}, {"X": 0.21612611413002014, "Y": 0.3806251883506775}, {"X": 0.21612614393234253, "Y": 0.38945189118385315}, {"X": 0.13864803314208984, "Y": 0.38945215940475464}]}, "Id": "0c1b3f28-2d26-4721-9cef-a4ca773093ce"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Due", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025124885141849518, "Height": 0.008691493421792984, "Left": 0.05342615395784378, "Top": 0.39786845445632935}, "Polygon": [{"X": 0.05342615395784378, "Y": 0.3978685438632965}, {"X": 0.07855100184679031, "Y": 0.39786845445632935}, {"X": 0.0785510390996933, "Y": 0.40655985474586487}, {"X": 0.05342618748545647, "Y": 0.40655994415283203}]}, "Id": "a5fe5835-f26d-4e95-8676-0d0566397b81"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031170619651675224, "Height": 0.00886812899261713, "Left": 0.08239904046058655, "Top": 0.3979245126247406}, "Polygon": [{"X": 0.08239904046058655, "Y": 0.39792460203170776}, {"X": 0.11356962472200394, "Y": 0.3979245126247406}, {"X": 0.11356966197490692, "Y": 0.4067925214767456}, {"X": 0.08239907771348953, "Y": 0.40679264068603516}]}, "Id": "5180c52d-f040-4031-bf50-ba5236e7af54"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "09/05/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07741263508796692, "Height": 0.009018491953611374, "Left": 0.11783312261104584, "Top": 0.39786189794540405}, "Polygon": [{"X": 0.11783312261104584, "Y": 0.39786216616630554}, {"X": 0.19524572789669037, "Y": 0.39786189794540405}, {"X": 0.19524575769901276, "Y": 0.40688008069992065}, {"X": 0.11783315986394882, "Y": 0.40688037872314453}]}, "Id": "8a237f73-04f6-4e4b-9bc5-5728b5936702"}, {"BlockType": "WORD", "Confidence": 99.4921875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007260054815560579, "Height": 0.007916828617453575, "Left": 0.04401132091879845, "Top": 0.459294855594635}, "Polygon": [{"X": 0.04401132091879845, "Y": 0.4592948853969574}, {"X": 0.05127134174108505, "Y": 0.459294855594635}, {"X": 0.05127137526869774, "Y": 0.46721166372299194}, {"X": 0.04401135444641113, "Y": 0.46721169352531433}]}, "Id": "1f854d5e-a491-4dfe-bb28-fa76b3bd667f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052201759070158005, "Height": 0.008870415389537811, "Left": 0.07526688277721405, "Top": 0.46125468611717224}, "Polygon": [{"X": 0.07526688277721405, "Y": 0.46125489473342896}, {"X": 0.12746860086917877, "Y": 0.46125468611717224}, {"X": 0.12746864557266235, "Y": 0.47012490034103394}, {"X": 0.07526691257953644, "Y": 0.47012510895729065}]}, "Id": "77455441-fc6f-47d4-93aa-a991725a839e"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "or", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013812143355607986, "Height": 0.006841289810836315, "Left": 0.13110388815402985, "Top": 0.46319714188575745}, "Polygon": [{"X": 0.13110388815402985, "Y": 0.4631972014904022}, {"X": 0.14491601288318634, "Y": 0.46319714188575745}, {"X": 0.14491604268550873, "Y": 0.47003838419914246}, {"X": 0.13110391795635223, "Y": 0.47003841400146484}]}, "Id": "94a17a60-0737-490b-8c14-75255364cb9a"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "service", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04567411169409752, "Height": 0.008676879107952118, "Left": 0.1483800709247589, "Top": 0.4614051282405853}, "Polygon": [{"X": 0.1483800709247589, "Y": 0.46140530705451965}, {"X": 0.19405415654182434, "Y": 0.4614051282405853}, {"X": 0.19405418634414673, "Y": 0.47008180618286133}, {"X": 0.1483801007270813, "Y": 0.47008198499679565}]}, "Id": "596a2c1a-6f55-44f6-b1dc-9d895d62edf3"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Description", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07347099483013153, "Height": 0.011183403432369232, "Left": 0.3394065797328949, "Top": 0.4610744118690491}, "Polygon": [{"X": 0.3394065797328949, "Y": 0.46107470989227295}, {"X": 0.41287755966186523, "Y": 0.4610744118690491}, {"X": 0.41287755966186523, "Y": 0.472257524728775}, {"X": 0.3394066095352173, "Y": 0.4722578227519989}]}, "Id": "7f9cc992-034e-4626-be6e-5b2b8ec098b2"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Qty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02315523847937584, "Height": 0.01105691771954298, "Left": 0.6885911822319031, "Top": 0.461099237203598}, "Polygon": [{"X": 0.6885911822319031, "Y": 0.4610993266105652}, {"X": 0.7117463946342468, "Y": 0.461099237203598}, {"X": 0.7117463946342468, "Y": 0.4721560478210449}, {"X": 0.6885911822319031, "Y": 0.4721561670303345}]}, "Id": "1d0cc269-2226-4d6b-bb8d-0e89b852893f"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "Rate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029089871793985367, "Height": 0.009016372263431549, "Left": 0.7935863137245178, "Top": 0.46101444959640503}, "Polygon": [{"X": 0.7935863137245178, "Y": 0.4610145688056946}, {"X": 0.8226761817932129, "Y": 0.46101444959640503}, {"X": 0.8226761817932129, "Y": 0.4700307250022888}, {"X": 0.7935863137245178, "Y": 0.470030814409256}]}, "Id": "123fc2ac-17b9-41fc-a963-166501839853"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05287376791238785, "Height": 0.008844123221933842, "Left": 0.8717021942138672, "Top": 0.4612569808959961}, "Polygon": [{"X": 0.8717021942138672, "Y": 0.4612571895122528}, {"X": 0.9245759844779968, "Y": 0.4612569808959961}, {"X": 0.9245759844779968, "Y": 0.47010087966918945}, {"X": 0.8717021942138672, "Y": 0.47010108828544617}]}, "Id": "0c081a0b-de41-47de-a81f-19e71ae2a495"}, {"BlockType": "WORD", "Confidence": 99.30424499511719, "Text": "1.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009298951365053654, "Height": 0.007789513096213341, "Left": 0.044812750071287155, "Top": 0.4962839186191559}, "Polygon": [{"X": 0.044812750071287155, "Y": 0.49628394842147827}, {"X": 0.05411166697740555, "Y": 0.4962839186191559}, {"X": 0.054111700505018234, "Y": 0.5040733814239502}, {"X": 0.04481278359889984, "Y": 0.504073441028595}]}, "Id": "5bf1f388-d02f-4225-8831-9385880a7a02"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "LOAD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038159340620040894, "Height": 0.008048713207244873, "Left": 0.07388897985219955, "Top": 0.4959985017776489}, "Polygon": [{"X": 0.07388897985219955, "Y": 0.49599865078926086}, {"X": 0.11204829066991806, "Y": 0.4959985017776489}, {"X": 0.11204832047224045, "Y": 0.5040470361709595}, {"X": 0.07388900965452194, "Y": 0.5040472149848938}]}, "Id": "97c35dab-b81e-417d-9ed8-0d70ec84729b"}, {"BlockType": "WORD", "Confidence": 99.833984375, "Text": "GREEN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04570889472961426, "Height": 0.008115650154650211, "Left": 0.33793187141418457, "Top": 0.49613192677497864}, "Polygon": [{"X": 0.33793187141418457, "Y": 0.49613213539123535}, {"X": 0.38364076614379883, "Y": 0.49613192677497864}, {"X": 0.38364076614379883, "Y": 0.5042473673820496}, {"X": 0.33793190121650696, "Y": 0.5042476058006287}]}, "Id": "52dd0cf5-8e21-4ac1-86c2-c02a6c29b509"}, {"BlockType": "WORD", "Confidence": 99.76403045654297, "Text": "BAY,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02699136734008789, "Height": 0.00918734259903431, "Left": 0.3886222243309021, "Top": 0.4963233470916748}, "Polygon": [{"X": 0.3886222243309021, "Y": 0.49632346630096436}, {"X": 0.4156135618686676, "Y": 0.4963233470916748}, {"X": 0.41561359167099, "Y": 0.5055105686187744}, {"X": 0.3886222541332245, "Y": 0.505510687828064}]}, "Id": "f158a5f7-64de-4f35-abea-deb26a9f3663"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015365654602646828, "Height": 0.007835964672267437, "Left": 0.41976112127304077, "Top": 0.49625736474990845}, "Polygon": [{"X": 0.41976112127304077, "Y": 0.4962574243545532}, {"X": 0.43512678146362305, "Y": 0.49625736474990845}, {"X": 0.43512678146362305, "Y": 0.5040932893753052}, {"X": 0.41976115107536316, "Y": 0.50409334897995}]}, "Id": "b5db5fb8-187b-444c-949a-827b3ee32afa"}, {"BlockType": "WORD", "Confidence": 76.63484954833984, "Text": "TO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017326341941952705, "Height": 0.007976929657161236, "Left": 0.4391133487224579, "Top": 0.496274471282959}, "Polygon": [{"X": 0.4391133487224579, "Y": 0.49627453088760376}, {"X": 0.45643967390060425, "Y": 0.496274471282959}, {"X": 0.45643970370292664, "Y": 0.5042513012886047}, {"X": 0.4391133785247803, "Y": 0.5042513608932495}]}, "Id": "f6ccae15-c2d3-4655-87dc-021f01122838"}, {"BlockType": "WORD", "Confidence": 99.65182495117188, "Text": "GARDNER,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06891083717346191, "Height": 0.00909743644297123, "Left": 0.4604048430919647, "Top": 0.49642887711524963}, "Polygon": [{"X": 0.4604048430919647, "Y": 0.4964291453361511}, {"X": 0.5293156504631042, "Y": 0.49642887711524963}, {"X": 0.529315710067749, "Y": 0.5055260062217712}, {"X": 0.4604048728942871, "Y": 0.5055263042449951}]}, "Id": "abe4b3a3-4c52-4a29-80ac-a158f262aa9d"}, {"BlockType": "WORD", "Confidence": 99.44255828857422, "Text": "KS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016289982944726944, "Height": 0.008092234842479229, "Left": 0.5344629287719727, "Top": 0.4961875379085541}, "Polygon": [{"X": 0.5344629287719727, "Y": 0.49618759751319885}, {"X": 0.5507528781890869, "Y": 0.4961875379085541}, {"X": 0.5507529377937317, "Y": 0.5042796730995178}, {"X": 0.5344629287719727, "Y": 0.5042797327041626}]}, "Id": "45390c91-c631-436f-9dc0-bc176a09873e"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "8/5/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038717981427907944, "Height": 0.008493097499012947, "Left": 0.5549073815345764, "Top": 0.4959632456302643}, "Polygon": [{"X": 0.5549073815345764, "Y": 0.4959633946418762}, {"X": 0.5936253666877747, "Y": 0.4959632456302643}, {"X": 0.5936253666877747, "Y": 0.5044561624526978}, {"X": 0.5549074411392212, "Y": 0.5044563412666321}]}, "Id": "28fe6856-da5a-4235-a232-6203afb54cc2"}, {"BlockType": "WORD", "Confidence": 97.998046875, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004342956468462944, "Height": 0.007855932228267193, "Left": 0.7064436078071594, "Top": 0.4962140619754791}, "Polygon": [{"X": 0.7064436078071594, "Y": 0.4962140619754791}, {"X": 0.7107865810394287, "Y": 0.4962140619754791}, {"X": 0.7107865810394287, "Y": 0.504069983959198}, {"X": 0.7064436078071594, "Y": 0.504069983959198}]}, "Id": "dbf89bb9-a313-4b62-988a-6b5288ffe3c3"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "$1,400.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05844784900546074, "Height": 0.009543734602630138, "Left": 0.7650240063667297, "Top": 0.4959595799446106}, "Polygon": [{"X": 0.7650240063667297, "Y": 0.4959598183631897}, {"X": 0.8234718441963196, "Y": 0.4959595799446106}, {"X": 0.8234718441963196, "Y": 0.5055030584335327}, {"X": 0.7650240063667297, "Y": 0.5055032968521118}]}, "Id": "4deeb2fc-8f34-4cc5-98eb-8517ce760476"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "$1,400.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05783401057124138, "Height": 0.009980030357837677, "Left": 0.8674253225326538, "Top": 0.49569299817085266}, "Polygon": [{"X": 0.8674253225326538, "Y": 0.49569323658943176}, {"X": 0.9252593517303467, "Y": 0.49569299817085266}, {"X": 0.9252593517303467, "Y": 0.5056727528572083}, {"X": 0.8674253225326538, "Y": 0.5056730508804321}]}, "Id": "3262d6a6-feb9-4831-8cab-45a3abc91490"}, {"BlockType": "WORD", "Confidence": 99.5703125, "Text": "#GBP11480264", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09404055029153824, "Height": 0.008286896161735058, "Left": 0.33764493465423584, "Top": 0.5113115310668945}, "Polygon": [{"X": 0.33764493465423584, "Y": 0.5113118886947632}, {"X": 0.4316854774951935, "Y": 0.5113115310668945}, {"X": 0.43168550729751587, "Y": 0.5195980072021484}, {"X": 0.3376449644565582, "Y": 0.5195984244346619}]}, "Id": "f63e194b-0c73-4a49-aa5b-719997631915"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03154497221112251, "Height": 0.008259673602879047, "Left": 0.6069855690002441, "Top": 0.5500890612602234}, "Polygon": [{"X": 0.6069855690002441, "Y": 0.5500892400741577}, {"X": 0.6385305523872375, "Y": 0.5500890612602234}, {"X": 0.6385305523872375, "Y": 0.5583485960960388}, {"X": 0.6069855690002441, "Y": 0.5583487749099731}]}, "Id": "e1dfb349-ae29-4f53-8761-038ec422dcf4"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "$1,400.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0884387195110321, "Height": 0.015128754079341888, "Left": 0.8385432362556458, "Top": 0.5470384359359741}, "Polygon": [{"X": 0.8385432362556458, "Y": 0.5470388531684875}, {"X": 0.9269819855690002, "Y": 0.5470384359359741}, {"X": 0.9269819259643555, "Y": 0.5621667504310608}, {"X": 0.8385432362556458, "Y": 0.5621671676635742}]}, "Id": "0e8c02e6-b9a7-4ef6-9ec3-ee0d9e86ed8d"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "dff9fab7-72c0-4e57-ab14-c92279e46bd6", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "dff9fab7-72c0-4e57-ab14-c92279e46bd6", "content-type": "application/x-amz-json-1.1", "content-length": "47715", "date": "Thu, 18 Sep 2025 09:49:58 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "00427cdd-d0c4-4dc1-829a-784bfc782424", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 09:50:00 GMT", "content-type": "application/json", "content-length": "508", "connection": "keep-alive", "x-amzn-requestid": "00427cdd-d0c4-4dc1-829a-784bfc782424"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have one page. It's invoice with header \"INVOICE\". So doc_type invoice. No lumper. So classify invoice."}}}, {"toolUse": {"toolUseId": "tooluse_R-39FdR_RPaEBK5gRys3yQ", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "invoice"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2319, "outputTokens": 63, "totalTokens": 2382}, "metrics": {"latencyMs": 504}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/83b64f8d_10_invoice_8.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Initial pallets, finished pallets, carrier, Total amount, PO, Door number, Date-Time, service charge, \n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nINVOICE\nR LOGISTICS LLC\n<EMAIL>\nE7986 770th Ave\n+1 (715) 962-3202\nColfax, WI 54730\nBill to\nPACKER FREIGHT\nP.O. BOX 28407\n<EMAIL>\nGREEN BAY, WI 54324 US\nInvoice details\nInvoice no.: 9975\nInvoice date: 08/06/2025\nDue date: 09/05/2025\n#\nProduct or service\nDescription\nQty\nRate\nAmount\n1.\nLOAD\nGREEN BAY, WI TO GARDNER, KS 8/5/25\n1\n$1,400.00\n$1,400.00\n#GBP11480264\nTotal\n$1,400.00\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}