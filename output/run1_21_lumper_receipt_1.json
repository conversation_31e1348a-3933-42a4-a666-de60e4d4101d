{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 4.7897985577805e-10}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 1.89212823897833e-05, "Y": 1.0}]}, "Id": "f790a329-4bd1-4a78-8a18-878499c697cd", "Relationships": [{"Type": "CHILD", "Ids": ["e1ab2631-448c-4426-8b7e-9f68e967277b", "2ac99984-42bf-4d6d-83fe-a143a84ce3df", "5667e679-5785-48f9-bc42-0fc3d2faa299", "d0311fc1-a8b3-4fb1-8a42-1770c9c1dd4e", "3464401c-9292-4ce3-987a-876fa997267c", "327ce2b4-da16-40d6-97d1-1bd48ec73f52", "bcd5036d-4ca4-4e4d-8158-fc4bd0669ba2", "d86e3878-af60-43de-90f8-9cfdd0731f68", "31cc55aa-ff95-4ccd-9938-155788eb2c20", "8de51667-f09a-43a6-8b76-51b80739fd02", "a2247e03-2e21-4649-9141-f6c9ee51d009", "8e3e08c1-1dd4-4769-bdd9-41a010071e3f", "24cd0e21-f36b-4af3-815e-b2d446f49714", "69786859-f9d3-434e-a174-42f38786a2dc", "fa1e60f2-3c44-4cdd-82ec-b19809e898ef", "54ea76e3-7e71-4088-a73f-af5020094cc0", "c59de6f1-2f40-499e-a68b-25aa9350c10f", "5d53e172-8dd0-4751-b68f-6c6538ac1cde", "b7a2cf1d-7c90-4a6d-81c8-e193813a2656", "8d682a82-8d3a-4705-9ae1-5941374bc61f", "a05d1a0f-c08c-4766-a1f2-c14f8eb5e484", "9db29013-e6cd-405b-b21a-075655d321ca", "8a7564d8-e7b5-47f4-b517-8be8efe1d402", "e0baf739-5e3f-4318-aa6b-1e4e8183c8ab", "8c84b86b-e4c0-48dd-8ae7-0da1c5be6b82", "eafdfefd-2f52-4123-b052-db879cdd3195", "9289b27a-5f6f-4531-99eb-71b34fadbca8", "2bede795-945e-454a-8b2a-890a71f6454b", "b152183a-42f7-4937-837d-3160db378010", "3fb02c71-ec09-4ccc-ad8b-22deb49c0497", "178e0243-979c-47db-b0ee-4947e3f1cd6c", "d5d9a2b7-2cfd-47fa-8569-2b96026aab7e", "156c536a-d2d0-46e4-a9db-bc28082bdcc4", "db0952de-0c51-4b4c-8480-974145ac828c", "728c525b-65b0-4e66-a6e4-9ce3fd4b24ef", "7144ba56-1c5e-447b-bde5-b4f0e65db728", "53e473a0-6dea-485a-99e3-e4b5a40e4a95"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "10:35", "Geometry": {"BoundingBox": {"Width": 0.10924337804317474, "Height": 0.015186871401965618, "Left": 0.13406506180763245, "Top": 0.01596767269074917}, "Polygon": [{"X": 0.13406506180763245, "Y": 0.015968693420290947}, {"X": 0.24330759048461914, "Y": 0.01596767269074917}, {"X": 0.2433084398508072, "Y": 0.031153568997979164}, {"X": 0.13406598567962646, "Y": 0.031154543161392212}]}, "Id": "e1ab2631-448c-4426-8b7e-9f68e967277b", "Relationships": [{"Type": "CHILD", "Ids": ["2ed73fee-c6c4-4e2a-9d18-15983b325f8a"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "5G", "Geometry": {"BoundingBox": {"Width": 0.04514143988490105, "Height": 0.01322881504893303, "Left": 0.7789398431777954, "Top": 0.025501087307929993}, "Polygon": [{"X": 0.7789398431777954, "Y": 0.025501495227217674}, {"X": 0.8240809440612793, "Y": 0.025501087307929993}, {"X": 0.8240812420845032, "Y": 0.038729507476091385}, {"X": 0.7789402008056641, "Y": 0.03872990235686302}]}, "Id": "2ac99984-42bf-4d6d-83fe-a143a84ce3df", "Relationships": [{"Type": "CHILD", "Ids": ["df1bbed0-dbf2-4334-aed9-72c627287ae6"]}]}, {"BlockType": "LINE", "Confidence": 99.931640625, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.0663069561123848, "Height": 0.012058558873832226, "Left": 0.05706661567091942, "Top": 0.03926363214850426}, "Polygon": [{"X": 0.05706661567091942, "Y": 0.03926420956850052}, {"X": 0.12337282299995422, "Y": 0.03926363214850426}, {"X": 0.12337356805801392, "Y": 0.05132163688540459}, {"X": 0.057067397981882095, "Y": 0.05132219195365906}]}, "Id": "5667e679-5785-48f9-bc42-0fc3d2faa299", "Relationships": [{"Type": "CHILD", "Ids": ["0e8f3524-d12b-41f9-acfb-beae3e0217d2"]}]}, {"BlockType": "LINE", "Confidence": 99.67526245117188, "Text": "Raley's Arizona LLC dba Bashas'", "Geometry": {"BoundingBox": {"Width": 0.6145228147506714, "Height": 0.019364407286047935, "Left": 0.19444774091243744, "Top": 0.18935193121433258}, "Polygon": [{"X": 0.19444774091243744, "Y": 0.18935468792915344}, {"X": 0.8089700937271118, "Y": 0.18935193121433258}, {"X": 0.80897057056427, "Y": 0.20871390402317047}, {"X": 0.19444885849952698, "Y": 0.20871633291244507}]}, "Id": "d0311fc1-a8b3-4fb1-8a42-1770c9c1dd4e", "Relationships": [{"Type": "CHILD", "Ids": ["1383cff9-3382-4773-abdb-87fddfdfe3e4", "f45e042b-27f3-4360-bc16-68a27bc7284d", "485dbd66-3951-4b55-80a3-1c0dfb90784a", "0baa4518-27b3-4334-a09f-dd3a8670b2e0", "d61f5acf-76f9-4079-99e1-b79df31b4c7e"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "480-895-9350", "Geometry": {"BoundingBox": {"Width": 0.25868475437164307, "Height": 0.01486135832965374, "Left": 0.37065744400024414, "Top": 0.24221761524677277}, "Polygon": [{"X": 0.37065744400024414, "Y": 0.24221840500831604}, {"X": 0.6293416619300842, "Y": 0.24221761524677277}, {"X": 0.6293421983718872, "Y": 0.2570783197879791}, {"X": 0.37065815925598145, "Y": 0.25707897543907166}]}, "Id": "3464401c-9292-4ce3-987a-876fa997267c", "Relationships": [{"Type": "CHILD", "Ids": ["3105dec3-c282-4aac-9bf8-a61aaa2b4c5f"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "Warehouse:", "Geometry": {"BoundingBox": {"Width": 0.22485847771167755, "Height": 0.015509686432778835, "Left": 0.09146405011415482, "Top": 0.2938244342803955}, "Polygon": [{"X": 0.09146405011415482, "Y": 0.29382479190826416}, {"X": 0.3163217306137085, "Y": 0.2938244342803955}, {"X": 0.3163225054740906, "Y": 0.309333860874176}, {"X": 0.09146502614021301, "Y": 0.3093341290950775}]}, "Id": "327ce2b4-da16-40d6-97d1-1bd48ec73f52", "Relationships": [{"Type": "CHILD", "Ids": ["e337988d-d85c-4562-99b3-f888ff7acbbc"]}]}, {"BlockType": "LINE", "Confidence": 80.56959533691406, "Text": "Bashasâ€ᵀ Receiving", "Geometry": {"BoundingBox": {"Width": 0.4191046357154846, "Height": 0.019354073330760002, "Left": 0.4886784255504608, "Top": 0.29363709688186646}, "Polygon": [{"X": 0.4886784255504608, "Y": 0.293637752532959}, {"X": 0.9077826738357544, "Y": 0.29363709688186646}, {"X": 0.9077830910682678, "Y": 0.3129907548427582}, {"X": 0.48867926001548767, "Y": 0.3129911720752716}]}, "Id": "bcd5036d-4ca4-4e4d-8158-fc4bd0669ba2", "Relationships": [{"Type": "CHILD", "Ids": ["9fb306de-f9ab-4fdc-b9d6-c86c324695e9", "2c4e7286-4cbe-49a8-bdd7-1decf1ceba93"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "(Dry)", "Geometry": {"BoundingBox": {"Width": 0.08901557326316833, "Height": 0.01917518861591816, "Left": 0.8177277445793152, "Top": 0.3198806941509247}, "Polygon": [{"X": 0.8177277445793152, "Y": 0.31988075375556946}, {"X": 0.9067429304122925, "Y": 0.3198806941509247}, {"X": 0.9067433476448059, "Y": 0.3390558362007141}, {"X": 0.8177282214164734, "Y": 0.3390558660030365}]}, "Id": "d86e3878-af60-43de-90f8-9cfdd0731f68", "Relationships": [{"Type": "CHILD", "Ids": ["904490eb-442a-4b00-ba39-f27531890330"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Warehouse", "Geometry": {"BoundingBox": {"Width": 0.2122659981250763, "Height": 0.015251632779836655, "Left": 0.09184269607067108, "Top": 0.346177339553833}, "Polygon": [{"X": 0.09184269607067108, "Y": 0.346177339553833}, {"X": 0.3041079044342041, "Y": 0.346177339553833}, {"X": 0.30410870909690857, "Y": 0.36142897605895996}, {"X": 0.09184365719556808, "Y": 0.3614288866519928}]}, "Id": "31cc55aa-ff95-4ccd-9938-155788eb2c20", "Relationships": [{"Type": "CHILD", "Ids": ["e2decb70-aca5-4ed5-8a99-4e05f775fdf0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "************", "Geometry": {"BoundingBox": {"Width": 0.2599426209926605, "Height": 0.014974582940340042, "Left": 0.6486552357673645, "Top": 0.3464948832988739}, "Polygon": [{"X": 0.6486552357673645, "Y": 0.3464948832988739}, {"X": 0.9085975289344788, "Y": 0.3464948832988739}, {"X": 0.9085978865623474, "Y": 0.36146944761276245}, {"X": 0.6486557722091675, "Y": 0.3614693582057953}]}, "Id": "8de51667-f09a-43a6-8b76-51b80739fd02", "Relationships": [{"Type": "CHILD", "Ids": ["415c56d7-a385-4181-9f91-9e17b38d1714"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Phone:", "Geometry": {"BoundingBox": {"Width": 0.12894096970558167, "Height": 0.015490328893065453, "Left": 0.09400252252817154, "Top": 0.3724817633628845}, "Polygon": [{"X": 0.09400252252817154, "Y": 0.3724817633628845}, {"X": 0.22294262051582336, "Y": 0.3724818527698517}, {"X": 0.2229434847831726, "Y": 0.3879721164703369}, {"X": 0.09400349855422974, "Y": 0.387971967458725}]}, "Id": "a2247e03-2e21-4649-9141-f6c9ee51d009", "Relationships": [{"Type": "CHILD", "Ids": ["78324146-68fa-4fb4-b18b-c6ea5d2fb430"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "Tax ID:", "Geometry": {"BoundingBox": {"Width": 0.13392868638038635, "Height": 0.01541423611342907, "Left": 0.09121667593717575, "Top": 0.39849165081977844}, "Polygon": [{"X": 0.09121667593717575, "Y": 0.39849165081977844}, {"X": 0.22514450550079346, "Y": 0.39849182963371277}, {"X": 0.2251453697681427, "Y": 0.41390588879585266}, {"X": 0.09121765196323395, "Y": 0.41390565037727356}]}, "Id": "8e3e08c1-1dd4-4769-bdd9-41a010071e3f", "Relationships": [{"Type": "CHILD", "Ids": ["5e5f4c84-a7e1-42ad-8e83-be0711f7223b", "e3e42ddb-cdf9-4293-b1d6-446aee77dfad"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "47-3650657", "Geometry": {"BoundingBox": {"Width": 0.22218813002109528, "Height": 0.015060359612107277, "Left": 0.6867167353630066, "Top": 0.3990285098552704}, "Polygon": [{"X": 0.6867167353630066, "Y": 0.3990285098552704}, {"X": 0.9089045524597168, "Y": 0.39902880787849426}, {"X": 0.9089048504829407, "Y": 0.4140888750553131}, {"X": 0.6867172122001648, "Y": 0.4140884578227997}]}, "Id": "24cd0e21-f36b-4af3-815e-b2d446f49714", "Relationships": [{"Type": "CHILD", "Ids": ["dc69369d-7241-4319-a036-a8fb8211f8d1"]}]}, {"BlockType": "LINE", "Confidence": 99.72417449951172, "Text": "Receipt#:", "Geometry": {"BoundingBox": {"Width": 0.17303772270679474, "Height": 0.019188545644283295, "Left": 0.09420457482337952, "Top": 0.424584299325943}, "Polygon": [{"X": 0.09420457482337952, "Y": 0.424584299325943}, {"X": 0.2672412693500519, "Y": 0.42458465695381165}, {"X": 0.26724231243133545, "Y": 0.4437728226184845}, {"X": 0.09420578926801682, "Y": 0.4437723755836487}]}, "Id": "69786859-f9d3-434e-a174-42f38786a2dc", "Relationships": [{"Type": "CHILD", "Ids": ["1e5bed71-5923-49e9-ab01-b77fd91907b0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "6713", "Geometry": {"BoundingBox": {"Width": 0.09214519709348679, "Height": 0.0148881571367383, "Left": 0.8165384531021118, "Top": 0.42516234517097473}, "Polygon": [{"X": 0.8165384531021118, "Y": 0.42516234517097473}, {"X": 0.9086833000183105, "Y": 0.42516252398490906}, {"X": 0.9086835980415344, "Y": 0.44005051255226135}, {"X": 0.8165388107299805, "Y": 0.44005027413368225}]}, "Id": "fa1e60f2-3c44-4cdd-82ec-b19809e898ef", "Relationships": [{"Type": "CHILD", "Ids": ["08d3cbd9-18e7-4cb7-b2ed-696637a2f7c1"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Timestamp:", "Geometry": {"BoundingBox": {"Width": 0.2177906185388565, "Height": 0.0188320092856884, "Left": 0.09097958356142044, "Top": 0.45105764269828796}, "Polygon": [{"X": 0.09097958356142044, "Y": 0.45105764269828796}, {"X": 0.30876922607421875, "Y": 0.4510582685470581}, {"X": 0.30877020955085754, "Y": 0.46988964080810547}, {"X": 0.09098077565431595, "Y": 0.46988892555236816}]}, "Id": "54ea76e3-7e71-4088-a73f-af5020094cc0", "Relationships": [{"Type": "CHILD", "Ids": ["c5907f85-8443-46a1-abed-91877c810eb3"]}]}, {"BlockType": "LINE", "Confidence": 99.78515625, "Text": "08/04/25 11:29 PM", "Geometry": {"BoundingBox": {"Width": 0.3507072925567627, "Height": 0.01533791609108448, "Left": 0.55629962682724, "Top": 0.45097947120666504}, "Polygon": [{"X": 0.55629962682724, "Y": 0.45097947120666504}, {"X": 0.907006561756134, "Y": 0.4509804844856262}, {"X": 0.9070068597793579, "Y": 0.46631738543510437}, {"X": 0.5563002228736877, "Y": 0.46631625294685364}]}, "Id": "c59de6f1-2f40-499e-a68b-25aa9350c10f", "Relationships": [{"Type": "CHILD", "Ids": ["0c067856-09c9-4181-b742-890cc185d8ea", "bb9dd331-cbdc-437e-94dd-abc63b5ab155", "4a749cf6-8144-4ab2-ad1b-9d2642e03da7"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Payment:", "Geometry": {"BoundingBox": {"Width": 0.17320527136325836, "Height": 0.019410597160458565, "Left": 0.09399387240409851, "Top": 0.****************}, "Polygon": [{"X": 0.09399387240409851, "Y": 0.****************}, {"X": 0.2671980857849121, "Y": 0.*****************}, {"X": 0.2671991288661957, "Y": 0.****************}, {"X": 0.093995101749897, "Y": 0.****************}]}, "Id": "5d53e172-8dd0-4751-b68f-6c6538ac1cde", "Relationships": [{"Type": "CHILD", "Ids": ["5d7d9a7d-dea2-4c38-a2d9-ecbb9272d59b"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "visa", "Geometry": {"BoundingBox": {"Width": 0.07536591589450836, "Height": 0.015304108150303364, "Left": 0.8339299559593201, "Top": 0.*****************}, "Polygon": [{"X": 0.8339299559593201, "Y": 0.*****************}, {"X": 0.9092954993247986, "Y": 0.*****************}, {"X": 0.9092958569526672, "Y": 0.*****************}, {"X": 0.8339303135871887, "Y": 0.*****************}]}, "Id": "b7a2cf1d-7c90-4a6d-81c8-e193813a2656", "Relationships": [{"Type": "CHILD", "Ids": ["f4993ce4-644c-432d-8ee2-884568638dca"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Carrier:", "Geometry": {"BoundingBox": {"Width": 0.13743320107460022, "Height": 0.015566100366413593, "Left": 0.09242082387208939, "Top": 0.5028253793716431}, "Polygon": [{"X": 0.09242082387208939, "Y": 0.5028253793716431}, {"X": 0.22985315322875977, "Y": 0.5028259754180908}, {"X": 0.2298540323972702, "Y": 0.518391489982605}, {"X": 0.09242180734872818, "Y": 0.****************}]}, "Id": "8d682a82-8d3a-4705-9ae1-5941374bc61f", "Relationships": [{"Type": "CHILD", "Ids": ["a7797b4a-b7c6-4111-aa0a-6e64c7cd5289"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "BEELINE EXP", "Geometry": {"BoundingBox": {"Width": 0.26872310042381287, "Height": 0.015316733159124851, "Left": 0.6392035484313965, "Top": 0.5030676126480103}, "Polygon": [{"X": 0.6392035484313965, "Y": 0.5030676126480103}, {"X": 0.9079262614250183, "Y": 0.5030688047409058}, {"X": 0.907926619052887, "Y": 0.5183843970298767}, {"X": 0.6392040848731995, "Y": 0.5183830857276917}]}, "Id": "a05d1a0f-c08c-4766-a1f2-c14f8eb5e484", "Relationships": [{"Type": "CHILD", "Ids": ["e588f93b-a8ff-4ab8-b66c-18c40b848859", "aa3c9190-7bb1-492e-9010-64483fdc73f7"]}]}, {"BlockType": "LINE", "Confidence": 99.94589233398438, "Text": "Reference #:", "Geometry": {"BoundingBox": {"Width": 0.23519183695316315, "Height": 0.015426913276314735, "Left": 0.09423208981752396, "Top": 0.529067873954773}, "Polygon": [{"X": 0.09423208981752396, "Y": 0.529067873954773}, {"X": 0.32942312955856323, "Y": 0.5290690660476685}, {"X": 0.3294239044189453, "Y": 0.5444948077201843}, {"X": 0.09423305839300156, "Y": 0.544493556022644}]}, "Id": "9db29013-e6cd-405b-b21a-075655d321ca", "Relationships": [{"Type": "CHILD", "Ids": ["a278c9a3-01d5-4923-aa51-040be97e47db", "0e7404e7-22ba-41cf-a218-3c4c9a29f834"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "16948226", "Geometry": {"BoundingBox": {"Width": 0.18174347281455994, "Height": 0.014795958064496517, "Left": 0.7262914776802063, "Top": 0.5296412706375122}, "Polygon": [{"X": 0.7262914776802063, "Y": 0.5296412706375122}, {"X": 0.90803462266922, "Y": 0.5296421647071838}, {"X": 0.9080349206924438, "Y": 0.5444372296333313}, {"X": 0.7262918949127197, "Y": 0.5444362163543701}]}, "Id": "8a7564d8-e7b5-47f4-b517-8be8efe1d402", "Relationships": [{"Type": "CHILD", "Ids": ["5d92e7c2-88ba-40f7-bb7d-22bee4ac504e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Door:", "Geometry": {"BoundingBox": {"Width": 0.09904955327510834, "Height": 0.01510334387421608, "Left": 0.09391622245311737, "Top": 0.5554738640785217}, "Polygon": [{"X": 0.09391622245311737, "Y": 0.5554738640785217}, {"X": 0.19296489655971527, "Y": 0.5554744601249695}, {"X": 0.1929657757282257, "Y": 0.5705772042274475}, {"X": 0.09391717612743378, "Y": 0.5705766081809998}]}, "Id": "e0baf739-5e3f-4318-aa6b-1e4e8183c8ab", "Relationships": [{"Type": "CHILD", "Ids": ["b9cae6c1-8228-42fc-9884-14103c64c509"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "43", "Geometry": {"BoundingBox": {"Width": 0.04630774259567261, "Height": 0.014918831177055836, "Left": 0.8624879717826843, "Top": 0.5556795001029968}, "Polygon": [{"X": 0.8624879717826843, "Y": 0.5556795001029968}, {"X": 0.9087954163551331, "Y": 0.5556797981262207}, {"X": 0.9087957143783569, "Y": 0.5705983638763428}, {"X": 0.862488329410553, "Y": 0.5705980658531189}]}, "Id": "8c84b86b-e4c0-48dd-8ae7-0da1c5be6b82", "Relationships": [{"Type": "CHILD", "Ids": ["a830ab10-86c7-433e-baff-abc3917ec801"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Activity:", "Geometry": {"BoundingBox": {"Width": 0.1425144076347351, "Height": 0.01907169260084629, "Left": 0.09150274097919464, "Top": 0.5813577771186829}, "Polygon": [{"X": 0.09150274097919464, "Y": 0.5813577771186829}, {"X": 0.2340160757303238, "Y": 0.5813586711883545}, {"X": 0.23401714861392975, "Y": 0.6004294753074646}, {"X": 0.09150394052267075, "Y": 0.6004284620285034}]}, "Id": "eafdfefd-2f52-4123-b052-db879cdd3195", "Relationships": [{"Type": "CHILD", "Ids": ["8e0dd67e-aa9e-4628-aa90-8447391c31cb"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total Unloading", "Geometry": {"BoundingBox": {"Width": 0.2907721996307373, "Height": 0.019465584307909012, "Left": 0.09143466502428055, "Top": 0.6073167324066162}, "Polygon": [{"X": 0.09143466502428055, "Y": 0.6073167324066162}, {"X": 0.38220593333244324, "Y": 0.6073188781738281}, {"X": 0.38220685720443726, "Y": 0.6267823576927185}, {"X": 0.09143589437007904, "Y": 0.626780092716217}]}, "Id": "9289b27a-5f6f-4531-99eb-71b34fadbca8", "Relationships": [{"Type": "CHILD", "Ids": ["7f62679f-d5a8-4efb-a365-6dc4158e490a", "760fe108-c0f8-484b-b45c-023ab7b44344"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$100.00", "Geometry": {"BoundingBox": {"Width": 0.15070292353630066, "Height": 0.017968865111470222, "Left": 0.7573922276496887, "Top": 0.6067190170288086}, "Polygon": [{"X": 0.7573922276496887, "Y": 0.6067190170288086}, {"X": 0.9080947637557983, "Y": 0.6067200899124146}, {"X": 0.9080951809883118, "Y": 0.624687910079956}, {"X": 0.7573927640914917, "Y": 0.6246867179870605}]}, "Id": "2bede795-945e-454a-8b2a-890a71f6454b", "Relationships": [{"Type": "CHILD", "Ids": ["e537e7a2-779c-46a2-9dc8-30d089e581de"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Fee", "Geometry": {"BoundingBox": {"Width": 0.06984797865152359, "Height": 0.015369575470685959, "Left": 0.09341099858283997, "Top": 0.633856475353241}, "Polygon": [{"X": 0.09341099858283997, "Y": 0.633856475353241}, {"X": 0.16325807571411133, "Y": 0.633857011795044}, {"X": 0.16325898468494415, "Y": 0.6492260098457336}, {"X": 0.09341196715831757, "Y": 0.6492254734039307}]}, "Id": "b152183a-42f7-4937-837d-3160db378010", "Relationships": [{"Type": "CHILD", "Ids": ["4d7af812-2129-47da-b595-99de5ae6c55e"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Amount:", "Geometry": {"BoundingBox": {"Width": 0.1543438732624054, "Height": 0.015346098691225052, "Left": 0.09161767363548279, "Top": 0.686164379119873}, "Polygon": [{"X": 0.09161767363548279, "Y": 0.686164379119873}, {"X": 0.24596069753170013, "Y": 0.6861658096313477}, {"X": 0.24596154689788818, "Y": 0.701510488986969}, {"X": 0.09161864221096039, "Y": 0.7015089392662048}]}, "Id": "3fb02c71-ec09-4ccc-ad8b-22deb49c0497", "Relationships": [{"Type": "CHILD", "Ids": ["c9cf2aca-6f20-4f75-97d7-6ea21eeb1141"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$100.00", "Geometry": {"BoundingBox": {"Width": 0.1505882740020752, "Height": 0.01771673932671547, "Left": 0.7579399943351746, "Top": 0.6853280663490295}, "Polygon": [{"X": 0.7579399943351746, "Y": 0.6853280663490295}, {"X": 0.9085279107093811, "Y": 0.6853294372558594}, {"X": 0.9085282683372498, "Y": 0.7030447721481323}, {"X": 0.7579405307769775, "Y": 0.7030432820320129}]}, "Id": "178e0243-979c-47db-b0ee-4947e3f1cd6c", "Relationships": [{"Type": "CHILD", "Ids": ["c47b950e-eb43-419c-a11e-89bc0b560792"]}]}, {"BlockType": "LINE", "Confidence": 99.80069732666016, "Text": "Convenience", "Geometry": {"BoundingBox": {"Width": 0.24206304550170898, "Height": 0.016054881736636162, "Left": 0.092539481818676, "Top": 0.7116965055465698}, "Polygon": [{"X": 0.092539481818676, "Y": 0.7116965055465698}, {"X": 0.3346017301082611, "Y": 0.7116989493370056}, {"X": 0.3346025347709656, "Y": 0.7277513742446899}, {"X": 0.09254049509763718, "Y": 0.7277488112449646}]}, "Id": "d5d9a2b7-2cfd-47fa-8569-2b96026aab7e", "Relationships": [{"Type": "CHILD", "Ids": ["b1e70ec2-56f7-45fd-a516-c0141a29ad14"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$4.00", "Geometry": {"BoundingBox": {"Width": 0.1035904586315155, "Height": 0.018078600987792015, "Left": 0.8050713539123535, "Top": 0.7114302515983582}, "Polygon": [{"X": 0.8050713539123535, "Y": 0.7114302515983582}, {"X": 0.908661425113678, "Y": 0.7114312648773193}, {"X": 0.9086618423461914, "Y": 0.7295088171958923}, {"X": 0.8050718307495117, "Y": 0.7295077443122864}]}, "Id": "156c536a-d2d0-46e4-a9db-bc28082bdcc4", "Relationships": [{"Type": "CHILD", "Ids": ["781ece83-75bc-4ca0-a124-1b104520ea43"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Fee:", "Geometry": {"BoundingBox": {"Width": 0.08006590604782104, "Height": 0.015452561900019646, "Left": 0.09419222176074982, "Top": 0.738362729549408}, "Polygon": [{"X": 0.09419222176074982, "Y": 0.738362729549408}, {"X": 0.17425721883773804, "Y": 0.7383636236190796}, {"X": 0.17425812780857086, "Y": 0.7538152933120728}, {"X": 0.09419319033622742, "Y": 0.7538143992424011}]}, "Id": "db0952de-0c51-4b4c-8480-974145ac828c", "Relationships": [{"Type": "CHILD", "Ids": ["81a90fd0-3313-4842-aff5-4ad0e97ea9ed"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Total:", "Geometry": {"BoundingBox": {"Width": 0.1038372665643692, "Height": 0.015384048223495483, "Left": 0.09131172299385071, "Top": 0.7642757892608643}, "Polygon": [{"X": 0.09131172299385071, "Y": 0.7642757892608643}, {"X": 0.19514809548854828, "Y": 0.7642769813537598}, {"X": 0.1951489895582199, "Y": 0.7796598076820374}, {"X": 0.0913126990199089, "Y": 0.7796585559844971}]}, "Id": "728c525b-65b0-4e66-a6e4-9ce3fd4b24ef", "Relationships": [{"Type": "CHILD", "Ids": ["6eb68062-f060-4662-967c-44e567cad952"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$104.00", "Geometry": {"BoundingBox": {"Width": 0.15062476694583893, "Height": 0.018154043704271317, "Left": 0.7579430937767029, "Top": 0.7636222243309021}, "Polygon": [{"X": 0.7579430937767029, "Y": 0.7636222243309021}, {"X": 0.908567488193512, "Y": 0.7636239528656006}, {"X": 0.9085678458213806, "Y": 0.7817762494087219}, {"X": 0.7579436302185059, "Y": 0.7817744016647339}]}, "Id": "7144ba56-1c5e-447b-bde5-b4f0e65db728", "Relationships": [{"Type": "CHILD", "Ids": ["3cd9d379-3a49-4737-b84e-d45ecae573af"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "app.relaypayments.com", "Geometry": {"BoundingBox": {"Width": 0.41901397705078125, "Height": 0.017863880842924118, "Left": 0.29149606823921204, "Top": 0.8817157745361328}, "Polygon": [{"X": 0.29149606823921204, "Y": 0.8817157745361328}, {"X": 0.7105094790458679, "Y": 0.8817220330238342}, {"X": 0.7105100154876709, "Y": 0.899579644203186}, {"X": 0.29149699211120605, "Y": 0.8995732069015503}]}, "Id": "53e473a0-6dea-485a-99e3-e4b5a40e4a95", "Relationships": [{"Type": "CHILD", "Ids": ["9767fbc6-464f-49db-a054-05b4ac95944d"]}]}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "10:35", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10924337804317474, "Height": 0.015186871401965618, "Left": 0.13406506180763245, "Top": 0.01596767269074917}, "Polygon": [{"X": 0.13406506180763245, "Y": 0.015968693420290947}, {"X": 0.24330759048461914, "Y": 0.01596767269074917}, {"X": 0.2433084398508072, "Y": 0.031153568997979164}, {"X": 0.13406598567962646, "Y": 0.031154543161392212}]}, "Id": "2ed73fee-c6c4-4e2a-9d18-15983b325f8a"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "5G", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04514143988490105, "Height": 0.01322881504893303, "Left": 0.7789398431777954, "Top": 0.025501087307929993}, "Polygon": [{"X": 0.7789398431777954, "Y": 0.025501495227217674}, {"X": 0.8240809440612793, "Y": 0.025501087307929993}, {"X": 0.8240812420845032, "Y": 0.038729507476091385}, {"X": 0.7789402008056641, "Y": 0.03872990235686302}]}, "Id": "df1bbed0-dbf2-4334-aed9-72c627287ae6"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0663069561123848, "Height": 0.012058558873832226, "Left": 0.05706661567091942, "Top": 0.03926363214850426}, "Polygon": [{"X": 0.05706661567091942, "Y": 0.03926420956850052}, {"X": 0.12337282299995422, "Y": 0.03926363214850426}, {"X": 0.12337356805801392, "Y": 0.05132163688540459}, {"X": 0.057067397981882095, "Y": 0.05132219195365906}]}, "Id": "0e8f3524-d12b-41f9-acfb-beae3e0217d2"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "<PERSON><PERSON>'s", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1325816810131073, "Height": 0.01934232749044895, "Left": 0.19444774091243744, "Top": 0.18937399983406067}, "Polygon": [{"X": 0.19444774091243744, "Y": 0.18937459588050842}, {"X": 0.32702842354774475, "Y": 0.18937399983406067}, {"X": 0.32702940702438354, "Y": 0.20871581137180328}, {"X": 0.19444885849952698, "Y": 0.20871633291244507}]}, "Id": "1383cff9-3382-4773-abdb-87fddfdfe3e4"}, {"BlockType": "WORD", "Confidence": 99.0832290649414, "Text": "Arizona", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14235380291938782, "Height": 0.01530013419687748, "Left": 0.3389122486114502, "Top": 0.189543217420578}, "Polygon": [{"X": 0.3389122486114502, "Y": 0.18954385817050934}, {"X": 0.4812653958797455, "Y": 0.189543217420578}, {"X": 0.481266051530838, "Y": 0.20484277606010437}, {"X": 0.3389130234718323, "Y": 0.20484335720539093}]}, "Id": "f45e042b-27f3-4360-bc16-68a27bc7284d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0735207051038742, "Height": 0.015159925445914268, "Left": 0.49380186200141907, "Top": 0.18952436745166779}, "Polygon": [{"X": 0.49380186200141907, "Y": 0.18952469527721405}, {"X": 0.5673219561576843, "Y": 0.18952436745166779}, {"X": 0.5673225522041321, "Y": 0.20468398928642273}, {"X": 0.4938024878501892, "Y": 0.2046842873096466}]}, "Id": "485dbd66-3951-4b55-80a3-1c0dfb90784a"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "dba", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06973902881145477, "Height": 0.015402846038341522, "Left": 0.5803850889205933, "Top": 0.1893526315689087}, "Polygon": [{"X": 0.5803850889205933, "Y": 0.18935294449329376}, {"X": 0.6501235961914062, "Y": 0.1893526315689087}, {"X": 0.6501241326332092, "Y": 0.20475520193576813}, {"X": 0.580385684967041, "Y": 0.2047554850578308}]}, "Id": "0baa4518-27b3-4334-a09f-dd3a8670b2e0"}, {"BlockType": "WORD", "Confidence": 99.59163665771484, "Text": "<PERSON><PERSON><PERSON>'", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1449967622756958, "Height": 0.01538028847426176, "Left": 0.6639736890792847, "Top": 0.18942786753177643}, "Polygon": [{"X": 0.6639736890792847, "Y": 0.18942852318286896}, {"X": 0.8089700937271118, "Y": 0.18942786753177643}, {"X": 0.8089704513549805, "Y": 0.2048075795173645}, {"X": 0.6639742255210876, "Y": 0.20480816066265106}]}, "Id": "d61f5acf-76f9-4079-99e1-b79df31b4c7e"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "480-895-9350", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.25868475437164307, "Height": 0.01486135832965374, "Left": 0.37065744400024414, "Top": 0.24221761524677277}, "Polygon": [{"X": 0.37065744400024414, "Y": 0.24221840500831604}, {"X": 0.6293416619300842, "Y": 0.24221761524677277}, {"X": 0.6293421983718872, "Y": 0.2570783197879791}, {"X": 0.37065815925598145, "Y": 0.25707897543907166}]}, "Id": "3105dec3-c282-4aac-9bf8-a61aaa2b4c5f"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Warehouse:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22485847771167755, "Height": 0.015509686432778835, "Left": 0.09146405011415482, "Top": 0.2938244342803955}, "Polygon": [{"X": 0.09146405011415482, "Y": 0.29382479190826416}, {"X": 0.3163217306137085, "Y": 0.2938244342803955}, {"X": 0.3163225054740906, "Y": 0.309333860874176}, {"X": 0.09146502614021301, "Y": 0.3093341290950775}]}, "Id": "e337988d-d85c-4562-99b3-f888ff7acbbc"}, {"BlockType": "WORD", "Confidence": 61.14895248413086, "Text": "Bashasâ€ᵀ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2209392637014389, "Height": 0.015555416233837605, "Left": 0.4886784255504608, "Top": 0.29363739490509033}, "Polygon": [{"X": 0.4886784255504608, "Y": 0.293637752532959}, {"X": 0.7096171975135803, "Y": 0.29363739490509033}, {"X": 0.7096177339553833, "Y": 0.30919256806373596}, {"X": 0.48867911100387573, "Y": 0.30919280648231506}]}, "Id": "9fb306de-f9ab-4fdc-b9d6-c86c324695e9"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Receiving", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17877531051635742, "Height": 0.01906268298625946, "Left": 0.7290077805519104, "Top": 0.29392823576927185}, "Polygon": [{"X": 0.7290077805519104, "Y": 0.29392850399017334}, {"X": 0.9077826738357544, "Y": 0.29392823576927185}, {"X": 0.9077830910682678, "Y": 0.3129907548427582}, {"X": 0.7290083169937134, "Y": 0.3129909336566925}]}, "Id": "2c4e7286-4cbe-49a8-bdd7-1decf1ceba93"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "(Dry)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08901557326316833, "Height": 0.01917518861591816, "Left": 0.8177277445793152, "Top": 0.3198806941509247}, "Polygon": [{"X": 0.8177277445793152, "Y": 0.31988075375556946}, {"X": 0.9067429304122925, "Y": 0.3198806941509247}, {"X": 0.9067433476448059, "Y": 0.3390558362007141}, {"X": 0.8177282214164734, "Y": 0.3390558660030365}]}, "Id": "904490eb-442a-4b00-ba39-f27531890330"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Warehouse", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2122659981250763, "Height": 0.015251632779836655, "Left": 0.09184269607067108, "Top": 0.346177339553833}, "Polygon": [{"X": 0.09184269607067108, "Y": 0.346177339553833}, {"X": 0.3041079044342041, "Y": 0.346177339553833}, {"X": 0.30410870909690857, "Y": 0.36142897605895996}, {"X": 0.09184365719556808, "Y": 0.3614288866519928}]}, "Id": "e2decb70-aca5-4ed5-8a99-4e05f775fdf0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2599426209926605, "Height": 0.014974582940340042, "Left": 0.6486552357673645, "Top": 0.3464948832988739}, "Polygon": [{"X": 0.6486552357673645, "Y": 0.3464948832988739}, {"X": 0.9085975289344788, "Y": 0.3464948832988739}, {"X": 0.9085978865623474, "Y": 0.36146944761276245}, {"X": 0.6486557722091675, "Y": 0.3614693582057953}]}, "Id": "415c56d7-a385-4181-9f91-9e17b38d1714"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Phone:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12894096970558167, "Height": 0.015490328893065453, "Left": 0.09400252252817154, "Top": 0.3724817633628845}, "Polygon": [{"X": 0.09400252252817154, "Y": 0.3724817633628845}, {"X": 0.22294262051582336, "Y": 0.3724818527698517}, {"X": 0.2229434847831726, "Y": 0.3879721164703369}, {"X": 0.09400349855422974, "Y": 0.387971967458725}]}, "Id": "78324146-68fa-4fb4-b18b-c6ea5d2fb430"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07007855176925659, "Height": 0.015223827213048935, "Left": 0.09121669083833694, "Top": 0.3986819386482239}, "Polygon": [{"X": 0.09121669083833694, "Y": 0.3986819386482239}, {"X": 0.1612943410873413, "Y": 0.39868202805519104}, {"X": 0.16129525005817413, "Y": 0.4139057695865631}, {"X": 0.09121765196323395, "Y": 0.41390565037727356}]}, "Id": "5e5f4c84-a7e1-42ad-8e83-be0711f7223b"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04900370165705681, "Height": 0.015348074026405811, "Left": 0.1761416643857956, "Top": 0.398491770029068}, "Polygon": [{"X": 0.1761416643857956, "Y": 0.398491770029068}, {"X": 0.22514450550079346, "Y": 0.39849182963371277}, {"X": 0.2251453697681427, "Y": 0.41383984684944153}, {"X": 0.17614255845546722, "Y": 0.41383975744247437}]}, "Id": "e3e42ddb-cdf9-4293-b1d6-446aee77dfad"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "47-3650657", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22218813002109528, "Height": 0.015060359612107277, "Left": 0.6867167353630066, "Top": 0.3990285098552704}, "Polygon": [{"X": 0.6867167353630066, "Y": 0.3990285098552704}, {"X": 0.9089045524597168, "Y": 0.39902880787849426}, {"X": 0.9089048504829407, "Y": 0.4140888750553131}, {"X": 0.6867172122001648, "Y": 0.4140884578227997}]}, "Id": "dc69369d-7241-4319-a036-a8fb8211f8d1"}, {"BlockType": "WORD", "Confidence": 99.72417449951172, "Text": "Receipt#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17303772270679474, "Height": 0.019188545644283295, "Left": 0.09420457482337952, "Top": 0.424584299325943}, "Polygon": [{"X": 0.09420457482337952, "Y": 0.424584299325943}, {"X": 0.2672412693500519, "Y": 0.42458465695381165}, {"X": 0.26724231243133545, "Y": 0.4437728226184845}, {"X": 0.09420578926801682, "Y": 0.4437723755836487}]}, "Id": "1e5bed71-5923-49e9-ab01-b77fd91907b0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "6713", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09214519709348679, "Height": 0.0148881571367383, "Left": 0.8165384531021118, "Top": 0.42516234517097473}, "Polygon": [{"X": 0.8165384531021118, "Y": 0.42516234517097473}, {"X": 0.9086833000183105, "Y": 0.42516252398490906}, {"X": 0.9086835980415344, "Y": 0.44005051255226135}, {"X": 0.8165388107299805, "Y": 0.44005027413368225}]}, "Id": "08d3cbd9-18e7-4cb7-b2ed-696637a2f7c1"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Timestamp:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2177906185388565, "Height": 0.0188320092856884, "Left": 0.09097958356142044, "Top": 0.45105764269828796}, "Polygon": [{"X": 0.09097958356142044, "Y": 0.45105764269828796}, {"X": 0.30876922607421875, "Y": 0.4510582685470581}, {"X": 0.30877020955085754, "Y": 0.46988964080810547}, {"X": 0.09098077565431595, "Y": 0.46988892555236816}]}, "Id": "c5907f85-8443-46a1-abed-91877c810eb3"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "08/04/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16152101755142212, "Height": 0.015165695920586586, "Left": 0.55629962682724, "Top": 0.4511510729789734}, "Polygon": [{"X": 0.55629962682724, "Y": 0.4511510729789734}, {"X": 0.7178201675415039, "Y": 0.4511515498161316}, {"X": 0.7178206443786621, "Y": 0.46631675958633423}, {"X": 0.5563002228736877, "Y": 0.46631625294685364}]}, "Id": "0c067856-09c9-4181-b742-890cc185d8ea"}, {"BlockType": "WORD", "Confidence": 99.53125, "Text": "11:29", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1011512354016304, "Height": 0.015094905160367489, "Left": 0.7327229380607605, "Top": 0.45120537281036377}, "Polygon": [{"X": 0.7327229380607605, "Y": 0.45120537281036377}, {"X": 0.8338738083839417, "Y": 0.45120567083358765}, {"X": 0.8338741660118103, "Y": 0.46630027890205383}, {"X": 0.7327234148979187, "Y": 0.46629995107650757}]}, "Id": "bb9dd331-cbdc-437e-94dd-abc63b5ab155"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05847734212875366, "Height": 0.015038399025797844, "Left": 0.8485295176506042, "Top": 0.4509803056716919}, "Polygon": [{"X": 0.8485295176506042, "Y": 0.4509803056716919}, {"X": 0.907006561756134, "Y": 0.4509804844856262}, {"X": 0.9070068597793579, "Y": 0.4660187065601349}, {"X": 0.8485299348831177, "Y": 0.46601852774620056}]}, "Id": "4a749cf6-8144-4ab2-ad1b-9d2642e03da7"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Payment:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17320527136325836, "Height": 0.019410597160458565, "Left": 0.09399387240409851, "Top": 0.****************}, "Polygon": [{"X": 0.09399387240409851, "Y": 0.****************}, {"X": 0.2671980857849121, "Y": 0.*****************}, {"X": 0.2671991288661957, "Y": 0.****************}, {"X": 0.093995101749897, "Y": 0.****************}]}, "Id": "5d7d9a7d-dea2-4c38-a2d9-ecbb9272d59b"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "visa", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07536591589450836, "Height": 0.015304108150303364, "Left": 0.8339299559593201, "Top": 0.*****************}, "Polygon": [{"X": 0.8339299559593201, "Y": 0.*****************}, {"X": 0.9092954993247986, "Y": 0.*****************}, {"X": 0.9092958569526672, "Y": 0.*****************}, {"X": 0.8339303135871887, "Y": 0.*****************}]}, "Id": "f4993ce4-644c-432d-8ee2-884568638dca"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Carrier:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13743320107460022, "Height": 0.015566100366413593, "Left": 0.09242082387208939, "Top": 0.5028253793716431}, "Polygon": [{"X": 0.09242082387208939, "Y": 0.5028253793716431}, {"X": 0.22985315322875977, "Y": 0.5028259754180908}, {"X": 0.2298540323972702, "Y": 0.518391489982605}, {"X": 0.09242180734872818, "Y": 0.****************}]}, "Id": "a7797b4a-b7c6-4111-aa0a-6e64c7cd5289"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "BEELINE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17396219074726105, "Height": 0.015316284261643887, "Left": 0.6392035484313965, "Top": 0.5030676126480103}, "Polygon": [{"X": 0.6392035484313965, "Y": 0.5030676126480103}, {"X": 0.8131653070449829, "Y": 0.5030683875083923}, {"X": 0.8131657242774963, "Y": 0.5183839201927185}, {"X": 0.6392040848731995, "Y": 0.5183830857276917}]}, "Id": "e588f93b-a8ff-4ab8-b66c-18c40b848859"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "EXP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0800146833062172, "Height": 0.014991027303040028, "Left": 0.827911913394928, "Top": 0.5031505823135376}, "Polygon": [{"X": 0.827911913394928, "Y": 0.5031505823135376}, {"X": 0.9079262614250183, "Y": 0.5031509399414062}, {"X": 0.907926619052887, "Y": 0.5181416273117065}, {"X": 0.8279123306274414, "Y": 0.5181412100791931}]}, "Id": "aa3c9190-7bb1-492e-9010-64483fdc73f7"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Reference", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.18964016437530518, "Height": 0.015353016555309296, "Left": 0.09423208981752396, "Top": 0.5291106700897217}, "Polygon": [{"X": 0.09423208981752396, "Y": 0.5291106700897217}, {"X": 0.28387144207954407, "Y": 0.5291116237640381}, {"X": 0.28387224674224854, "Y": 0.5444636940956116}, {"X": 0.09423305839300156, "Y": 0.5444626212120056}]}, "Id": "a278c9a3-01d5-4923-aa51-040be97e47db"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03433087840676308, "Height": 0.015425900928676128, "Left": 0.29509302973747253, "Top": 0.5290688872337341}, "Polygon": [{"X": 0.29509302973747253, "Y": 0.5290688872337341}, {"X": 0.32942312955856323, "Y": 0.5290690660476685}, {"X": 0.3294239044189453, "Y": 0.5444948077201843}, {"X": 0.2950938642024994, "Y": 0.54449462890625}]}, "Id": "0e7404e7-22ba-41cf-a218-3c4c9a29f834"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "16948226", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.18174347281455994, "Height": 0.014795958064496517, "Left": 0.7262914776802063, "Top": 0.5296412706375122}, "Polygon": [{"X": 0.7262914776802063, "Y": 0.5296412706375122}, {"X": 0.90803462266922, "Y": 0.5296421647071838}, {"X": 0.9080349206924438, "Y": 0.5444372296333313}, {"X": 0.7262918949127197, "Y": 0.5444362163543701}]}, "Id": "5d92e7c2-88ba-40f7-bb7d-22bee4ac504e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Door:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09904955327510834, "Height": 0.01510334387421608, "Left": 0.09391622245311737, "Top": 0.5554738640785217}, "Polygon": [{"X": 0.09391622245311737, "Y": 0.5554738640785217}, {"X": 0.19296489655971527, "Y": 0.5554744601249695}, {"X": 0.1929657757282257, "Y": 0.5705772042274475}, {"X": 0.09391717612743378, "Y": 0.5705766081809998}]}, "Id": "b9cae6c1-8228-42fc-9884-14103c64c509"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "43", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04630774259567261, "Height": 0.014918831177055836, "Left": 0.8624879717826843, "Top": 0.5556795001029968}, "Polygon": [{"X": 0.8624879717826843, "Y": 0.5556795001029968}, {"X": 0.9087954163551331, "Y": 0.5556797981262207}, {"X": 0.9087957143783569, "Y": 0.5705983638763428}, {"X": 0.862488329410553, "Y": 0.5705980658531189}]}, "Id": "a830ab10-86c7-433e-baff-abc3917ec801"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Activity:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1425144076347351, "Height": 0.01907169260084629, "Left": 0.09150274097919464, "Top": 0.5813577771186829}, "Polygon": [{"X": 0.09150274097919464, "Y": 0.5813577771186829}, {"X": 0.2340160757303238, "Y": 0.5813586711883545}, {"X": 0.23401714861392975, "Y": 0.6004294753074646}, {"X": 0.09150394052267075, "Y": 0.6004284620285034}]}, "Id": "8e0dd67e-aa9e-4628-aa90-8447391c31cb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09201815724372864, "Height": 0.015299486927688122, "Left": 0.09143467992544174, "Top": 0.6075280904769897}, "Polygon": [{"X": 0.09143467992544174, "Y": 0.6075280904769897}, {"X": 0.18345195055007935, "Y": 0.6075287461280823}, {"X": 0.18345284461975098, "Y": 0.6228275895118713}, {"X": 0.09143564850091934, "Y": 0.622826874256134}]}, "Id": "7f62679f-d5a8-4efb-a365-6dc4158e490a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Unloading", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.18364091217517853, "Height": 0.019464809447526932, "Left": 0.19856594502925873, "Top": 0.6073175072669983}, "Polygon": [{"X": 0.19856594502925873, "Y": 0.6073175072669983}, {"X": 0.38220593333244324, "Y": 0.6073188781738281}, {"X": 0.38220685720443726, "Y": 0.6267823576927185}, {"X": 0.19856707751750946, "Y": 0.6267809271812439}]}, "Id": "760fe108-c0f8-484b-b45c-023ab7b44344"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$100.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15070292353630066, "Height": 0.017968865111470222, "Left": 0.7573922276496887, "Top": 0.6067190170288086}, "Polygon": [{"X": 0.7573922276496887, "Y": 0.6067190170288086}, {"X": 0.9080947637557983, "Y": 0.6067200899124146}, {"X": 0.9080951809883118, "Y": 0.624687910079956}, {"X": 0.7573927640914917, "Y": 0.6246867179870605}]}, "Id": "e537e7a2-779c-46a2-9dc8-30d089e581de"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Fee", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06984797865152359, "Height": 0.015369575470685959, "Left": 0.09341099858283997, "Top": 0.633856475353241}, "Polygon": [{"X": 0.09341099858283997, "Y": 0.633856475353241}, {"X": 0.16325807571411133, "Y": 0.633857011795044}, {"X": 0.16325898468494415, "Y": 0.6492260098457336}, {"X": 0.09341196715831757, "Y": 0.6492254734039307}]}, "Id": "4d7af812-2129-47da-b595-99de5ae6c55e"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Amount:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1543438732624054, "Height": 0.015346098691225052, "Left": 0.09161767363548279, "Top": 0.686164379119873}, "Polygon": [{"X": 0.09161767363548279, "Y": 0.686164379119873}, {"X": 0.24596069753170013, "Y": 0.6861658096313477}, {"X": 0.24596154689788818, "Y": 0.701510488986969}, {"X": 0.09161864221096039, "Y": 0.7015089392662048}]}, "Id": "c9cf2aca-6f20-4f75-97d7-6ea21eeb1141"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$100.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1505882740020752, "Height": 0.01771673932671547, "Left": 0.7579399943351746, "Top": 0.6853280663490295}, "Polygon": [{"X": 0.7579399943351746, "Y": 0.6853280663490295}, {"X": 0.9085279107093811, "Y": 0.6853294372558594}, {"X": 0.9085282683372498, "Y": 0.7030447721481323}, {"X": 0.7579405307769775, "Y": 0.7030432820320129}]}, "Id": "c47b950e-eb43-419c-a11e-89bc0b560792"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Convenience", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24206304550170898, "Height": 0.016054881736636162, "Left": 0.092539481818676, "Top": 0.7116965055465698}, "Polygon": [{"X": 0.092539481818676, "Y": 0.7116965055465698}, {"X": 0.3346017301082611, "Y": 0.7116989493370056}, {"X": 0.3346025347709656, "Y": 0.7277513742446899}, {"X": 0.09254049509763718, "Y": 0.7277488112449646}]}, "Id": "b1e70ec2-56f7-45fd-a516-c0141a29ad14"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$4.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1035904586315155, "Height": 0.018078600987792015, "Left": 0.8050713539123535, "Top": 0.7114302515983582}, "Polygon": [{"X": 0.8050713539123535, "Y": 0.7114302515983582}, {"X": 0.908661425113678, "Y": 0.7114312648773193}, {"X": 0.9086618423461914, "Y": 0.7295088171958923}, {"X": 0.8050718307495117, "Y": 0.7295077443122864}]}, "Id": "781ece83-75bc-4ca0-a124-1b104520ea43"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Fee:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08006590604782104, "Height": 0.015452561900019646, "Left": 0.09419222176074982, "Top": 0.738362729549408}, "Polygon": [{"X": 0.09419222176074982, "Y": 0.738362729549408}, {"X": 0.17425721883773804, "Y": 0.7383636236190796}, {"X": 0.17425812780857086, "Y": 0.7538152933120728}, {"X": 0.09419319033622742, "Y": 0.7538143992424011}]}, "Id": "81a90fd0-3313-4842-aff5-4ad0e97ea9ed"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Total:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1038372665643692, "Height": 0.015384048223495483, "Left": 0.09131172299385071, "Top": 0.7642757892608643}, "Polygon": [{"X": 0.09131172299385071, "Y": 0.7642757892608643}, {"X": 0.19514809548854828, "Y": 0.7642769813537598}, {"X": 0.1951489895582199, "Y": 0.7796598076820374}, {"X": 0.0913126990199089, "Y": 0.7796585559844971}]}, "Id": "6eb68062-f060-4662-967c-44e567cad952"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$104.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15062476694583893, "Height": 0.018154043704271317, "Left": 0.7579430937767029, "Top": 0.7636222243309021}, "Polygon": [{"X": 0.7579430937767029, "Y": 0.7636222243309021}, {"X": 0.908567488193512, "Y": 0.7636239528656006}, {"X": 0.9085678458213806, "Y": 0.7817762494087219}, {"X": 0.7579436302185059, "Y": 0.7817744016647339}]}, "Id": "3cd9d379-3a49-4737-b84e-d45ecae573af"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "app.relaypayments.com", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.41901397705078125, "Height": 0.017863880842924118, "Left": 0.29149606823921204, "Top": 0.8817157745361328}, "Polygon": [{"X": 0.29149606823921204, "Y": 0.8817157745361328}, {"X": 0.7105094790458679, "Y": 0.8817220330238342}, {"X": 0.7105100154876709, "Y": 0.899579644203186}, {"X": 0.29149699211120605, "Y": 0.8995732069015503}]}, "Id": "9767fbc6-464f-49db-a054-05b4ac95944d"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "b1902df8-73c5-4d66-b93e-de1dbb2024ab", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "b1902df8-73c5-4d66-b93e-de1dbb2024ab", "content-type": "application/x-amz-json-1.1", "content-length": "44744", "date": "Thu, 18 Sep 2025 10:33:54 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "0c4bf6d3-8d2e-43b9-acb5-7385f68ac946", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 10:33:58 GMT", "content-type": "application/json", "content-length": "1911", "connection": "keep-alive", "x-amzn-requestid": "0c4bf6d3-8d2e-43b9-acb5-7385f68ac946"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "The user provided a single page content. We need to classify the document type.\n\nLet's examine content: It includes \"Carrier: BEELINE EXP\" \"Reference #\", \"Door\", \"Total Unloading Fee\", \"Convenience Fee\", \"Total\". There's also a payment info: \"Payment: visa\", \"Carrier: BEELINE EXP\". It appears to be a receipt for unloading services, maybe a lumper receipt. The description: \"Total Unloading Fee\" and \"Convenience Fee\" suggests it's a receipt for unloading services at a warehouse. The presence of \"Carrier\" indicates it's a carrier. The content also shows \"Receipt#\" and \"Timestamp\". This matches lumper receipt: keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. Here we have \"Door: 43\", \"Total Unloading Fee\", \"Convenience Fee\", \"Total\". There's no mention of pallets though. But it might be a lumper receipt.\n\nThe doc type \"lumper_receipt\" vs \"invoice\". Distinction: lumper receipt has lumper related description or lumper related keywords. This page says \"Total Unloading Fee\". This appears to be a lumper receipt. So classify as lumper_receipt.\n\nAlso check if it's a continuation of previous page. There's no \"continued\" or \"page 2 of 3\". It's a single page. So output exactly one object for page 1.\n\nThus we need to call function classify_logistics_doc_type with documents array having one entry: page_no 1, doc_type \"lumper_receipt\".\n\nWe output via tool call.\n\nLet's do that."}}}, {"toolUse": {"toolUseId": "tooluse_8vcJZOB8RmWAj25ffK6Nrw", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2294, "outputTokens": 379, "totalTokens": 2673}, "metrics": {"latencyMs": 1605}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/b0e748fc_21_lumper_receipt_1.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n10:35\n5G\nRelay\nRaley's Arizona LLC dba Bashas'\n480-895-9350\nWarehouse:\nBashasâ€ᵀ Receiving\n(Dry)\nWarehouse\n************\nPhone:\nTax ID:\n47-3650657\nReceipt#:\n6713\nTimestamp:\n08/04/25 11:29 PM\nPayment:\nvisa\nCarrier:\nBEELINE EXP\nReference #:\n16948226\nDoor:\n43\nActivity:\nTotal Unloading\n$100.00\nFee\nAmount:\n$100.00\nConvenience\n$4.00\nFee:\nTotal:\n$104.00\napp.relaypayments.com\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}