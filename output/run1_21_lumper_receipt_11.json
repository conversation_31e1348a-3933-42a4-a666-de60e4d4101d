{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9999728202819824, "Height": 0.9999380111694336, "Left": 2.718538962653838e-05, "Top": 6.199473864398897e-05}, "Polygon": [{"X": 2.718538962653838e-05, "Y": 7.449216354871169e-05}, {"X": 1.0, "Y": 6.199473864398897e-05}, {"X": 1.0, "Y": 1.0}, {"X": 0.0001409734832122922, "Y": 1.0}]}, "Id": "867d4b02-f822-4d70-9887-d2254bbee699", "Relationships": [{"Type": "CHILD", "Ids": ["0cb07563-eb6e-440c-9e80-d4a30b5f1867", "81b89f00-be17-4fef-92ac-c0616f845f39", "077d9b9e-06ac-42b7-ac9c-7e3e666c5f6b", "daaeb8b1-9a62-40eb-a823-f5bcfb2e8e5d", "1c4db57c-06b6-4af9-b67b-d5b37963d827", "4db6ff6b-7d99-4afc-aa1b-0945ccdad164", "aeafba33-f5b6-42b7-99c3-4e97436a9c1d", "3d21314e-4b6d-4d0d-b146-6134de209134", "25fb6da2-23a9-44c0-a07f-a34aa0d94f3c", "33c88161-5f69-4787-b06c-c4238a3b9d7d", "68aaea45-de5a-4e3d-b861-ad377d1efdaf", "213d964a-dad2-4654-8c9c-d2a337da67e9", "5bc080b1-d42f-4568-9eef-e1ccfae6e7ef", "3cdb4b81-8cb6-4388-a296-50dc5ee05151", "fc62d208-d9eb-4016-b415-aaee36aba66d", "ca95192f-2265-4a0a-9395-3d5cf193001d", "ef9a1fd2-46aa-4127-b618-7ec8b835a6f6", "e2488f8a-2383-43ac-9bdc-c7dc1e35d1de", "a30adfe3-89a1-4434-8b7e-07569afce9bd", "489de818-a791-4202-8ac2-8e5af6f47f0a", "73d40ae7-5adc-4130-900a-23a87b302a08", "19851ca3-ca79-40fb-82b9-3cb8f4c94985", "f676f5ce-05c7-479c-afb9-2546ef1015a2", "fbe70a7d-dea6-458d-b366-9fb03cb3c7c0", "a63fde78-3585-4606-a17f-ea32eb489ad3", "def8197d-40d3-409b-a115-1e9c5615eb3d", "04861f17-68ac-4442-9e34-b7190a58b2c9", "9f02fae9-919a-40cb-8e4f-4e2d408a1b60", "0181d952-b715-4251-920e-477b3678ff60", "186df208-d574-46d0-8ed5-f092b2a94210", "bdd57dee-7326-4c9f-ad5a-97421a1de2e7", "9f62e1eb-6f6c-42c9-a9c0-674e1fbfbc3b", "8deebf00-0252-4234-85db-d4fcf1155649", "a396aae6-d2d6-491b-9a0d-6ee43ef0e2f1", "4df246a7-c16c-4f5f-9c18-89501837ac20", "0f71f5a5-4eee-4908-983f-5fed4260f46e", "a7f71c71-aa8e-44d7-b4a4-327dd0b75fb5", "c77859be-bc6f-4fce-900c-ed18c1b6e0e3", "09ee8ba5-d966-4b6b-9643-a8ae47a21e9e", "43c058bb-6427-411d-98ad-7b6495155e41", "7eeecf3b-c20f-4e95-944b-0061f5233221", "7973cab1-61fe-41ef-88a3-0b751074a95f", "c8eefe29-910c-4f7e-ba9a-4882fb3f2edc", "914302d5-bcf2-487b-bea2-5786791f6a9a", "34944b5a-fed2-4e41-a14f-119329c2ee29", "7ae5c6c0-ce97-414a-b9d2-6bef44c99648", "2e15ca4a-7c57-4077-8fee-5b4d4efecdaa", "fb917ddf-0661-4a06-9aef-e9503fb153c9", "c0828b70-a4b7-46a6-8866-f0105db03ce9"]}]}, {"BlockType": "LINE", "Confidence": 98.26909637451172, "Text": "MERIT", "Geometry": {"BoundingBox": {"Width": 0.24773530662059784, "Height": 0.030968578532338142, "Left": 0.18507200479507446, "Top": 0.1073952466249466}, "Polygon": [{"X": 0.18507200479507446, "Y": 0.10739777982234955}, {"X": 0.43280428647994995, "Y": 0.1073952466249466}, {"X": 0.4328072965145111, "Y": 0.13836143910884857}, {"X": 0.1850752979516983, "Y": 0.1383638232946396}]}, "Id": "0cb07563-eb6e-440c-9e80-d4a30b5f1867", "Relationships": [{"Type": "CHILD", "Ids": ["8d4fa586-9ec4-41c9-aa63-485e33efc818"]}]}, {"BlockType": "LINE", "Confidence": 57.2064323425293, "Text": "LOGISTIC", "Geometry": {"BoundingBox": {"Width": 0.3510882556438446, "Height": 0.03660334646701813, "Left": 0.5848082900047302, "Top": 0.0968146026134491}, "Polygon": [{"X": 0.5848082900047302, "Y": 0.09681828320026398}, {"X": 0.9358936548233032, "Y": 0.0968146026134491}, {"X": 0.9358965158462524, "Y": 0.13341455161571503}, {"X": 0.5848116278648376, "Y": 0.13341794908046722}]}, "Id": "81b89f00-be17-4fef-92ac-c0616f845f39", "Relationships": [{"Type": "CHILD", "Ids": ["58ac29e9-0d4d-4800-849d-e80dff173d1c"]}]}, {"BlockType": "LINE", "Confidence": 97.59825134277344, "Text": "(949) 481-0685", "Geometry": {"BoundingBox": {"Width": 0.3080807328224182, "Height": 0.022024720907211304, "Left": 0.34846240282058716, "Top": 0.17896682024002075}, "Polygon": [{"X": 0.34846240282058716, "Y": 0.17896951735019684}, {"X": 0.6565411686897278, "Y": 0.17896682024002075}, {"X": 0.6565431356430054, "Y": 0.2009889930486679}, {"X": 0.34846460819244385, "Y": 0.20099154114723206}]}, "Id": "077d9b9e-06ac-42b7-ac9c-7e3e666c5f6b", "Relationships": [{"Type": "CHILD", "Ids": ["08e537df-2f63-4ea9-96df-93382170234c", "21e369cd-0d7a-4a82-ac60-68d2cc1a7cb2"]}]}, {"BlockType": "LINE", "Confidence": 99.15582275390625, "Text": "<PERSON><PERSON> (ALDFRE), <PERSON>, <PERSON>", "Geometry": {"BoundingBox": {"Width": 0.6641154289245605, "Height": 0.023905981332063675, "Left": 0.17080968618392944, "Top": 0.21789641678333282}, "Polygon": [{"X": 0.17080968618392944, "Y": 0.21790169179439545}, {"X": 0.8349231481552124, "Y": 0.21789641678333282}, {"X": 0.83492511510849, "Y": 0.24179746210575104}, {"X": 0.17081224918365479, "Y": 0.2418024092912674}]}, "Id": "daaeb8b1-9a62-40eb-a823-f5bcfb2e8e5d", "Relationships": [{"Type": "CHILD", "Ids": ["a5909eca-215b-4fda-8009-cf8d679d3250", "9b5e529b-2de7-441a-b83c-7d5a8cb1d10a", "07ebc9ad-a432-4570-b4ee-34e2eee1c8ee", "b91672b7-9028-43a9-a316-632bddfde86f"]}]}, {"BlockType": "LINE", "Confidence": 97.20553588867188, "Text": "Tax Id", "Geometry": {"BoundingBox": {"Width": 0.10942088067531586, "Height": 0.012472054921090603, "Left": 0.14416749775409698, "Top": 0.27878475189208984}, "Polygon": [{"X": 0.14416749775409698, "Y": 0.27878549695014954}, {"X": 0.2535870671272278, "Y": 0.27878475189208984}, {"X": 0.25358837842941284, "Y": 0.2912561297416687}, {"X": 0.14416883885860443, "Y": 0.2912568151950836}]}, "Id": "1c4db57c-06b6-4af9-b67b-d5b37963d827", "Relationships": [{"Type": "CHILD", "Ids": ["72f782e6-f0b2-4590-9492-418297dd2909", "eee63e26-ca25-425c-9298-bcaa19ee027d"]}]}, {"BlockType": "LINE", "Confidence": 99.89098358154297, "Text": "*********", "Geometry": {"BoundingBox": {"Width": 0.19964005053043365, "Height": 0.01619478315114975, "Left": 0.6647797226905823, "Top": 0.2744753062725067}, "Polygon": [{"X": 0.6647797226905823, "Y": 0.27447664737701416}, {"X": 0.8644184470176697, "Y": 0.2744753062725067}, {"X": 0.8644197583198547, "Y": 0.2906688153743744}, {"X": 0.6647811532020569, "Y": 0.29067009687423706}]}, "Id": "4db6ff6b-7d99-4afc-aa1b-0945ccdad164", "Relationships": [{"Type": "CHILD", "Ids": ["d47bfc63-b24c-4791-8255-4e7adb2612a4"]}]}, {"BlockType": "LINE", "Confidence": 92.99844360351562, "Text": "WO #", "Geometry": {"BoundingBox": {"Width": 0.0932382270693779, "Height": 0.012204968370497227, "Left": 0.4134569764137268, "Top": 0.2944173514842987}, "Polygon": [{"X": 0.4134569764137268, "Y": 0.29441794753074646}, {"X": 0.5066940784454346, "Y": 0.2944173514842987}, {"X": 0.5066952109336853, "Y": 0.3066217303276062}, {"X": 0.4134581685066223, "Y": 0.30662232637405396}]}, "Id": "aeafba33-f5b6-42b7-99c3-4e97436a9c1d", "Relationships": [{"Type": "CHILD", "Ids": ["099242a8-20a9-485a-bc3c-0a2cf90b0803", "770b088f-1794-4622-ac93-1041b5fac8ca"]}]}, {"BlockType": "LINE", "Confidence": 79.87165832519531, "Text": "ALDFRE9734307252025203958", "Geometry": {"BoundingBox": {"Width": 0.5582346320152283, "Height": 0.016875185072422028, "Left": 0.2240321785211563, "Top": 0.310842365026474}, "Polygon": [{"X": 0.2240321785211563, "Y": 0.3108457028865814}, {"X": 0.7822654247283936, "Y": 0.310842365026474}, {"X": 0.7822668552398682, "Y": 0.32771438360214233}, {"X": 0.22403395175933838, "Y": 0.32771754264831543}]}, "Id": "3d21314e-4b6d-4d0d-b146-6134de209134", "Relationships": [{"Type": "CHILD", "Ids": ["ed26ceed-0b82-4114-b775-e7fc2c255806"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.08533041924238205, "Height": 0.012756140902638435, "Left": 0.14407946169376373, "Top": 0.3356296718120575}, "Polygon": [{"X": 0.14407946169376373, "Y": 0.3356301188468933}, {"X": 0.22940854728221893, "Y": 0.3356296718120575}, {"X": 0.22940988838672638, "Y": 0.34838536381721497}, {"X": 0.14408084750175476, "Y": 0.3483858108520508}]}, "Id": "25fb6da2-23a9-44c0-a07f-a34aa0d94f3c", "Relationships": [{"Type": "CHILD", "Ids": ["db44ad0d-2691-41a5-bc89-dbf47211dd46"]}]}, {"BlockType": "LINE", "Confidence": 99.326171875, "Text": "7/25/2025 8:39:00 PM", "Geometry": {"BoundingBox": {"Width": 0.3877936601638794, "Height": 0.017211854457855225, "Left": 0.47627541422843933, "Top": 0.3310616910457611}, "Polygon": [{"X": 0.47627541422843933, "Y": 0.3310638666152954}, {"X": 0.8640676736831665, "Y": 0.3310616910457611}, {"X": 0.8640691041946411, "Y": 0.34827151894569397}, {"X": 0.47627705335617065, "Y": 0.34827354550361633}]}, "Id": "33c88161-5f69-4787-b06c-c4238a3b9d7d", "Relationships": [{"Type": "CHILD", "Ids": ["ed27e078-a138-4b02-bc8f-b400b3b02acf", "c55a68c4-5548-48ea-a5a7-d8d230012aac", "458c11a9-ab72-45e2-87cf-a68b6054e8a4"]}]}, {"BlockType": "LINE", "Confidence": 99.2578125, "Text": "PO #", "Geometry": {"BoundingBox": {"Width": 0.08805301785469055, "Height": 0.011958755552768707, "Left": 0.14226120710372925, "Top": 0.35761022567749023}, "Polygon": [{"X": 0.14226120710372925, "Y": 0.35761067271232605}, {"X": 0.23031297326087952, "Y": 0.35761022567749023}, {"X": 0.2303142249584198, "Y": 0.3695685565471649}, {"X": 0.1422625035047531, "Y": 0.36956897377967834}]}, "Id": "68aaea45-de5a-4e3d-b861-ad377d1efdaf", "Relationships": [{"Type": "CHILD", "Ids": ["8ddc276c-785c-486d-b107-3ae5150f4a1b", "a63ab3aa-82bc-4263-8641-ccb4ebce7811"]}]}, {"BlockType": "LINE", "Confidence": 98.6756591796875, "Text": "7507418921", "Geometry": {"BoundingBox": {"Width": 0.21574129164218903, "Height": 0.016964301466941833, "Left": 0.6466652750968933, "Top": 0.3529658615589142}, "Polygon": [{"X": 0.6466652750968933, "Y": 0.35296696424484253}, {"X": 0.8624052405357361, "Y": 0.3529658615589142}, {"X": 0.8624066114425659, "Y": 0.36992913484573364}, {"X": 0.6466668248176575, "Y": 0.3699301779270172}]}, "Id": "213d964a-dad2-4654-8c9c-d2a337da67e9", "Relationships": [{"Type": "CHILD", "Ids": ["578c84aa-b7a3-4a6e-a4a5-8a483e7f6810"]}]}, {"BlockType": "LINE", "Confidence": 99.189453125, "Text": "Truck #", "Geometry": {"BoundingBox": {"Width": 0.1336185485124588, "Height": 0.012677724473178387, "Left": 0.14428651332855225, "Top": 0.3771931827068329}, "Polygon": [{"X": 0.14428651332855225, "Y": 0.377193808555603}, {"X": 0.2779037654399872, "Y": 0.3771931827068329}, {"X": 0.27790507674217224, "Y": 0.3898703157901764}, {"X": 0.14428788423538208, "Y": 0.38987091183662415}]}, "Id": "5bc080b1-d42f-4568-9eef-e1ccfae6e7ef", "Relationships": [{"Type": "CHILD", "Ids": ["080c5e87-c885-46e2-8fe9-de966a7f0e36", "e16f3d4b-eade-464e-afc8-dbe2d1510870"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "101", "Geometry": {"BoundingBox": {"Width": 0.06180214136838913, "Height": 0.015584289096295834, "Left": 0.8016756772994995, "Top": 0.37275105714797974}, "Polygon": [{"X": 0.8016756772994995, "Y": 0.3727513551712036}, {"X": 0.863476574420929, "Y": 0.37275105714797974}, {"X": 0.8634778261184692, "Y": 0.38833507895469666}, {"X": 0.8016769289970398, "Y": 0.38833534717559814}]}, "Id": "3cdb4b81-8cb6-4388-a296-50dc5ee05151", "Relationships": [{"Type": "CHILD", "Ids": ["d8c9337b-eec6-403c-98b7-c16cbe7d6b39"]}]}, {"BlockType": "LINE", "Confidence": 98.70027160644531, "Text": "Trailer #", "Geometry": {"BoundingBox": {"Width": 0.14712195098400116, "Height": 0.012239117175340652, "Left": 0.14484895765781403, "Top": 0.39901021122932434}, "Polygon": [{"X": 0.14484895765781403, "Y": 0.3990108072757721}, {"X": 0.2919696569442749, "Y": 0.39901021122932434}, {"X": 0.2919709086418152, "Y": 0.41124874353408813}, {"X": 0.14485028386116028, "Y": 0.4112493097782135}]}, "Id": "fc62d208-d9eb-4016-b415-aaee36aba66d", "Relationships": [{"Type": "CHILD", "Ids": ["2137d100-242d-4a09-aa0f-23285fcd1cf2", "de571e50-37dc-449c-8244-c40abd5265a8"]}]}, {"BlockType": "LINE", "Confidence": 88.96424865722656, "Text": "()--288", "Geometry": {"BoundingBox": {"Width": 0.12617294490337372, "Height": 0.019286949187517166, "Left": 0.7425933480262756, "Top": 0.39464476704597473}, "Polygon": [{"X": 0.7425933480262756, "Y": 0.3946453034877777}, {"X": 0.8687646985054016, "Y": 0.39464476704597473}, {"X": 0.8687662482261658, "Y": 0.4139312207698822}, {"X": 0.7425949573516846, "Y": 0.4139316976070404}]}, "Id": "ca95192f-2265-4a0a-9395-3d5cf193001d", "Relationships": [{"Type": "CHILD", "Ids": ["2eb0de5e-6b5b-427c-9b8f-aacc1725df25"]}]}, {"BlockType": "LINE", "Confidence": 98.94003295898438, "Text": "Door #", "Geometry": {"BoundingBox": {"Width": 0.11571364104747772, "Height": 0.011846007779240608, "Left": 0.14406432211399078, "Top": 0.4203703999519348}, "Polygon": [{"X": 0.14406432211399078, "Y": 0.42037081718444824}, {"X": 0.2597767412662506, "Y": 0.4203703999519348}, {"X": 0.2597779631614685, "Y": 0.43221598863601685}, {"X": 0.14406560361385345, "Y": 0.4322164058685303}]}, "Id": "ef9a1fd2-46aa-4127-b618-7ec8b835a6f6", "Relationships": [{"Type": "CHILD", "Ids": ["04faddab-4ed5-467b-823d-a42d2f82452f", "ff65beeb-bff5-4bca-ba14-d8b85128cc36"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "34", "Geometry": {"BoundingBox": {"Width": 0.04900645464658737, "Height": 0.01625366322696209, "Left": 0.8215357661247253, "Top": 0.4149540066719055}, "Polygon": [{"X": 0.8215357661247253, "Y": 0.41495418548583984}, {"X": 0.8705409169197083, "Y": 0.4149540066719055}, {"X": 0.8705422282218933, "Y": 0.4312075078487396}, {"X": 0.8215371370315552, "Y": 0.43120765686035156}]}, "Id": "e2488f8a-2383-43ac-9bdc-c7dc1e35d1de", "Relationships": [{"Type": "CHILD", "Ids": ["eb57d5ef-8e92-4e99-abeb-f23bdd09bc70"]}]}, {"BlockType": "LINE", "Confidence": 99.48222351074219, "Text": "Client's Dept", "Geometry": {"BoundingBox": {"Width": 0.22377616167068481, "Height": 0.013752000406384468, "Left": 0.1440788060426712, "Top": 0.44123655557632446}, "Polygon": [{"X": 0.1440788060426712, "Y": 0.44123730063438416}, {"X": 0.367853581905365, "Y": 0.44123655557632446}, {"X": 0.3678549528121948, "Y": 0.45498788356781006}, {"X": 0.1440802961587906, "Y": 0.454988569021225}]}, "Id": "a30adfe3-89a1-4434-8b7e-07569afce9bd", "Relationships": [{"Type": "CHILD", "Ids": ["e5ce6dc7-2d0a-4a07-87d1-c770de0b2a99", "4177ddef-e6c5-4a9b-b7f4-3665de4a4133"]}]}, {"BlockType": "LINE", "Confidence": 83.53784942626953, "Text": "Unipading (GRP)", "Geometry": {"BoundingBox": {"Width": 0.3153095841407776, "Height": 0.018871990963816643, "Left": 0.5517191290855408, "Top": 0.43752774596214294}, "Polygon": [{"X": 0.5517191290855408, "Y": 0.4375287890434265}, {"X": 0.867027223110199, "Y": 0.43752774596214294}, {"X": 0.8670287132263184, "Y": 0.45639878511428833}, {"X": 0.551720917224884, "Y": 0.45639973878860474}]}, "Id": "489de818-a791-4202-8ac2-8e5af6f47f0a", "Relationships": [{"Type": "CHILD", "Ids": ["84544ee1-f2a8-48f4-95df-92ebd866b7e7", "5edfbd48-1611-4116-80f0-59e71e9017c1"]}]}, {"BlockType": "LINE", "Confidence": 99.24745178222656, "Text": "Product", "Geometry": {"BoundingBox": {"Width": 0.14044232666492462, "Height": 0.011927680112421513, "Left": 0.14443199336528778, "Top": 0.4618944227695465}, "Polygon": [{"X": 0.14443199336528778, "Y": 0.46189481019973755}, {"X": 0.2848730981349945, "Y": 0.4618944227695465}, {"X": 0.2848743200302124, "Y": 0.4738217294216156}, {"X": 0.14443328976631165, "Y": 0.47382208704948425}]}, "Id": "73d40ae7-5adc-4130-900a-23a87b302a08", "Relationships": [{"Type": "CHILD", "Ids": ["468269ae-f691-49a6-b891-d7dbe0c6cfd3"]}]}, {"BlockType": "LINE", "Confidence": 99.15876770019531, "Text": "GM", "Geometry": {"BoundingBox": {"Width": 0.06667549163103104, "Height": 0.0152316028252244, "Left": 0.8020117282867432, "Top": 0.4576770067214966}, "Polygon": [{"X": 0.8020117282867432, "Y": 0.4576772153377533}, {"X": 0.8686860203742981, "Y": 0.4576770067214966}, {"X": 0.8686872124671936, "Y": 0.4729084372520447}, {"X": 0.8020129799842834, "Y": 0.472908616065979}]}, "Id": "19851ca3-ca79-40fb-82b9-3cb8f4c94985", "Relationships": [{"Type": "CHILD", "Ids": ["f1179481-f195-413b-b937-818002e68642"]}]}, {"BlockType": "LINE", "Confidence": 98.73505401611328, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.12628546357154846, "Height": 0.011748413555324078, "Left": 0.1465824842453003, "Top": 0.4827662408351898}, "Polygon": [{"X": 0.1465824842453003, "Y": 0.4827665388584137}, {"X": 0.27286672592163086, "Y": 0.4827662408351898}, {"X": 0.27286794781684875, "Y": 0.4945143759250641}, {"X": 0.14658375084400177, "Y": 0.4945146441459656}]}, "Id": "f676f5ce-05c7-479c-afb9-2546ef1015a2", "Relationships": [{"Type": "CHILD", "Ids": ["640dc98a-b366-4769-a516-aac94736be0f"]}]}, {"BlockType": "LINE", "Confidence": 98.52598571777344, "Text": "PREMIUM WATERS INC", "Geometry": {"BoundingBox": {"Width": 0.42432907223701477, "Height": 0.016455309465527534, "Left": 0.44603678584098816, "Top": 0.47775039076805115}, "Polygon": [{"X": 0.44603678584098816, "Y": 0.4777514338493347}, {"X": 0.8703645467758179, "Y": 0.47775039076805115}, {"X": 0.8703658580780029, "Y": 0.4942047595977783}, {"X": 0.4460383951663971, "Y": 0.49420568346977234}]}, "Id": "fbe70a7d-dea6-458d-b366-9fb03cb3c7c0", "Relationships": [{"Type": "CHILD", "Ids": ["dfae1816-8a3a-443d-82fc-1f6b99202b43", "1a2d96b9-192a-4f36-9bc4-8ec5df4c070c", "df13fcbc-7fcb-4a7f-9042-c8b436c90a83"]}]}, {"BlockType": "LINE", "Confidence": 99.3126220703125, "Text": "Carrier", "Geometry": {"BoundingBox": {"Width": 0.12479837983846664, "Height": 0.012299999594688416, "Left": 0.1439296156167984, "Top": 0.5031832456588745}, "Polygon": [{"X": 0.1439296156167984, "Y": 0.5031834840774536}, {"X": 0.26872673630714417, "Y": 0.5031832456588745}, {"X": 0.26872798800468445, "Y": 0.515483021736145}, {"X": 0.14393095672130585, "Y": 0.5154832005500793}]}, "Id": "a63fde78-3585-4606-a17f-ea32eb489ad3", "Relationships": [{"Type": "CHILD", "Ids": ["9d877b23-ea72-4823-8878-95c7ecc775de"]}]}, {"BlockType": "LINE", "Confidence": 97.76048278808594, "Text": "3A TRANSPORT", "Geometry": {"BoundingBox": {"Width": 0.30032235383987427, "Height": 0.01761532574892044, "Left": 0.5707411766052246, "Top": 0.4987722635269165}, "Polygon": [{"X": 0.5707411766052246, "Y": 0.49877285957336426}, {"X": 0.8710620999336243, "Y": 0.4987722635269165}, {"X": 0.8710635304450989, "Y": 0.5163870453834534}, {"X": 0.5707427859306335, "Y": 0.5163875818252563}]}, "Id": "def8197d-40d3-409b-a115-1e9c5615eb3d", "Relationships": [{"Type": "CHILD", "Ids": ["5c83c131-0abd-4687-a9b0-32d289d51486", "e6248954-e49d-4de4-9c55-d9555e043b09"]}]}, {"BlockType": "LINE", "Confidence": 99.73313903808594, "Text": "Bill <PERSON>", "Geometry": {"BoundingBox": {"Width": 0.11350125819444656, "Height": 0.012435048818588257, "Left": 0.14364956319332123, "Top": 0.5256401300430298}, "Polygon": [{"X": 0.14364956319332123, "Y": 0.5256402492523193}, {"X": 0.25714951753616333, "Y": 0.5256401300430298}, {"X": 0.2571508288383484, "Y": 0.5380750298500061}, {"X": 0.14365090429782867, "Y": 0.5380751490592957}]}, "Id": "04861f17-68ac-4442-9e34-b7190a58b2c9", "Relationships": [{"Type": "CHILD", "Ids": ["1dbcacf5-71b1-4bf9-b2da-f7a3202a71c9", "83b55957-3a7b-451c-9cd2-6142794e9037"]}]}, {"BlockType": "LINE", "Confidence": 99.5301513671875, "Text": "3A TRANSPORT", "Geometry": {"BoundingBox": {"Width": 0.3009212911128998, "Height": 0.01580245979130268, "Left": 0.5708645582199097, "Top": 0.5210983157157898}, "Polygon": [{"X": 0.5708645582199097, "Y": 0.521098792552948}, {"X": 0.8717845678329468, "Y": 0.5210983157157898}, {"X": 0.8717858195304871, "Y": 0.5369004011154175}, {"X": 0.5708659887313843, "Y": 0.5369007587432861}]}, "Id": "9f02fae9-919a-40cb-8e4f-4e2d408a1b60", "Relationships": [{"Type": "CHILD", "Ids": ["5262bf6e-ebc0-41bf-87e7-a87e984c5c85", "a5980559-b971-48ee-b1d2-90aa785eac8e"]}]}, {"BlockType": "LINE", "Confidence": 95.54956817626953, "Text": "QTY < Case >", "Geometry": {"BoundingBox": {"Width": 0.24496714770793915, "Height": 0.014735712669789791, "Left": 0.14367571473121643, "Top": 0.5462460517883301}, "Polygon": [{"X": 0.14367571473121643, "Y": 0.5462462902069092}, {"X": 0.3886414170265198, "Y": 0.5462460517883301}, {"X": 0.3886428773403168, "Y": 0.5609815716743469}, {"X": 0.14367730915546417, "Y": 0.5609817504882812}]}, "Id": "0181d952-b715-4251-920e-477b3678ff60", "Relationships": [{"Type": "CHILD", "Ids": ["2a771b86-8083-4160-bdf4-22cffc9e9b90", "54641369-19a0-4dd7-9f7e-c400a3fb0ed8", "b37d6884-2fc4-4c70-902f-b92a432fee89", "7d4fc56b-cea5-43c0-af9e-710a72a04e85"]}]}, {"BlockType": "LINE", "Confidence": 99.833984375, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.02006562240421772, "Height": 0.017020318657159805, "Left": 0.8509932160377502, "Top": 0.5419660210609436}, "Polygon": [{"X": 0.8509932160377502, "Y": 0.5419660806655884}, {"X": 0.8710575103759766, "Y": 0.5419660210609436}, {"X": 0.8710588812828064, "Y": 0.5589863657951355}, {"X": 0.8509945869445801, "Y": 0.5589863657951355}]}, "Id": "186df208-d574-46d0-8ed5-f092b2a94210", "Relationships": [{"Type": "CHILD", "Ids": ["3efd7556-a521-42b7-b7d5-60a52f24c27b"]}]}, {"BlockType": "LINE", "Confidence": 99.77458953857422, "Text": "Activity", "Geometry": {"BoundingBox": {"Width": 0.13567721843719482, "Height": 0.014926869422197342, "Left": 0.14278826117515564, "Top": 0.5681006908416748}, "Polygon": [{"X": 0.14278826117515564, "Y": 0.5681008100509644}, {"X": 0.2784639298915863, "Y": 0.5681006908416748}, {"X": 0.27846547961235046, "Y": 0.5830275416374207}, {"X": 0.14278988540172577, "Y": 0.5830276012420654}]}, "Id": "bdd57dee-7326-4c9f-ad5a-97421a1de2e7", "Relationships": [{"Type": "CHILD", "Ids": ["0086486e-2ba6-4178-811a-280895fc9f08"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$91.00", "Geometry": {"BoundingBox": {"Width": 0.12356474250555038, "Height": 0.01906082034111023, "Left": 0.7518740296363831, "Top": 0.5626896023750305}, "Polygon": [{"X": 0.7518740296363831, "Y": 0.5626896619796753}, {"X": 0.8754372596740723, "Y": 0.5626896023750305}, {"X": 0.8754388093948364, "Y": 0.5817503333091736}, {"X": 0.751875638961792, "Y": 0.5817503929138184}]}, "Id": "9f62e1eb-6f6c-42c9-a9c0-674e1fbfbc3b", "Relationships": [{"Type": "CHILD", "Ids": ["9f0a08d6-d4af-4cbb-b29b-9be5106b3410"]}]}, {"BlockType": "LINE", "Confidence": 99.7944564819336, "Text": "Work Order Charge", "Geometry": {"BoundingBox": {"Width": 0.34035080671310425, "Height": 0.015810536220669746, "Left": 0.1442163586616516, "Top": 0.587481677532196}, "Polygon": [{"X": 0.1442163586616516, "Y": 0.5874817371368408}, {"X": 0.4845656752586365, "Y": 0.587481677532196}, {"X": 0.48456716537475586, "Y": 0.6032922267913818}, {"X": 0.1442180722951889, "Y": 0.6032921671867371}]}, "Id": "8deebf00-0252-4234-85db-d4fcf1155649", "Relationships": [{"Type": "CHILD", "Ids": ["0d8aefe8-5775-4936-86d8-6772c358e30c", "cbb610b1-cb50-499d-be39-1b3088ab689e", "8932ec34-84c2-4ea9-8268-2d88d127ed75"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$91.00", "Geometry": {"BoundingBox": {"Width": 0.12454274296760559, "Height": 0.01950083300471306, "Left": 0.7530006170272827, "Top": 0.5844067335128784}, "Polygon": [{"X": 0.7530006170272827, "Y": 0.5844067335128784}, {"X": 0.8775417804718018, "Y": 0.5844067335128784}, {"X": 0.8775433301925659, "Y": 0.6039075255393982}, {"X": 0.7530022263526917, "Y": 0.6039075255393982}]}, "Id": "a396aae6-d2d6-491b-9a0d-6ee43ef0e2f1", "Relationships": [{"Type": "CHILD", "Ids": ["51a72387-3fc6-46ee-ae1b-74ea59fb7345"]}]}, {"BlockType": "LINE", "Confidence": 99.50703430175781, "Text": "Service Charge", "Geometry": {"BoundingBox": {"Width": 0.2778431475162506, "Height": 0.014770335517823696, "Left": 0.14305239915847778, "Top": 0.6108126640319824}, "Polygon": [{"X": 0.14305239915847778, "Y": 0.6108126640319824}, {"X": 0.4208940863609314, "Y": 0.6108127236366272}, {"X": 0.4208955466747284, "Y": 0.6255829930305481}, {"X": 0.14305399358272552, "Y": 0.6255828738212585}]}, "Id": "4df246a7-c16c-4f5f-9c18-89501837ac20", "Relationships": [{"Type": "CHILD", "Ids": ["d3249859-ba05-4d3c-a072-04701ac70dc6", "45b1153e-32cc-4b80-86e5-9658aec27be5"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "$8.00", "Geometry": {"BoundingBox": {"Width": 0.10247338563203812, "Height": 0.017269406467676163, "Left": 0.7763200998306274, "Top": 0.6070967316627502}, "Polygon": [{"X": 0.7763200998306274, "Y": 0.6070967316627502}, {"X": 0.8787921071052551, "Y": 0.607096791267395}, {"X": 0.878793478012085, "Y": 0.6243661642074585}, {"X": 0.776321530342102, "Y": 0.6243661046028137}]}, "Id": "0f71f5a5-4eee-4908-983f-5fed4260f46e", "Relationships": [{"Type": "CHILD", "Ids": ["97e64f87-16d9-41dc-a213-df65a45fc205"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.0894695296883583, "Height": 0.014490507543087006, "Left": 0.1441158652305603, "Top": 0.6322702169418335}, "Polygon": [{"X": 0.1441158652305603, "Y": 0.6322702169418335}, {"X": 0.23358388245105743, "Y": 0.6322702765464783}, {"X": 0.2335854023694992, "Y": 0.6467607021331787}, {"X": 0.14411742985248566, "Y": 0.6467606425285339}]}, "Id": "a7f71c71-aa8e-44d7-b4a4-327dd0b75fb5", "Relationships": [{"Type": "CHILD", "Ids": ["777f4c2b-b9a1-4552-813e-ba6b198e38ec"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "$99.00", "Geometry": {"BoundingBox": {"Width": 0.12457958608865738, "Height": 0.019586818292737007, "Left": 0.7548099756240845, "Top": 0.6282671093940735}, "Polygon": [{"X": 0.7548099756240845, "Y": 0.6282671093940735}, {"X": 0.8793879747390747, "Y": 0.6282671689987183}, {"X": 0.8793895244598389, "Y": 0.6478539109230042}, {"X": 0.7548116445541382, "Y": 0.6478537917137146}]}, "Id": "c77859be-bc6f-4fce-900c-ed18c1b6e0e3", "Relationships": [{"Type": "CHILD", "Ids": ["ea78c193-3ca2-4a45-b4a5-1eb7dbd8151e"]}]}, {"BlockType": "LINE", "Confidence": 99.87145233154297, "Text": "Payment", "Geometry": {"BoundingBox": {"Width": 0.16054531931877136, "Height": 0.015378644689917564, "Left": 0.14344261586666107, "Top": 0.6547008156776428}, "Polygon": [{"X": 0.14344261586666107, "Y": 0.6547008156776428}, {"X": 0.3039863705635071, "Y": 0.6547009944915771}, {"X": 0.30398792028427124, "Y": 0.6700794696807861}, {"X": 0.14344428479671478, "Y": 0.670079231262207}]}, "Id": "09ee8ba5-d966-4b6b-9643-a8ae47a21e9e", "Relationships": [{"Type": "CHILD", "Ids": ["a0c09f12-5eff-492d-8c82-4ff50ca15636"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.10958477109670639, "Height": 0.018479187041521072, "Left": 0.7728049159049988, "Top": 0.6508646011352539}, "Polygon": [{"X": 0.7728049159049988, "Y": 0.6508646011352539}, {"X": 0.8823882341384888, "Y": 0.6508647799491882}, {"X": 0.8823896646499634, "Y": 0.6693438291549683}, {"X": 0.7728064656257629, "Y": 0.6693436503410339}]}, "Id": "43c058bb-6427-411d-98ad-7b6495155e41", "Relationships": [{"Type": "CHILD", "Ids": ["0abdd421-bddd-4db6-bf0a-5edc015ce7fe"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "Authorization", "Geometry": {"BoundingBox": {"Width": 0.23951470851898193, "Height": 0.013458259403705597, "Left": 0.13951319456100464, "Top": 0.6752681136131287}, "Polygon": [{"X": 0.13951319456100464, "Y": 0.6752681136131287}, {"X": 0.3790265619754791, "Y": 0.6752685308456421}, {"X": 0.3790279030799866, "Y": 0.6887263655662537}, {"X": 0.13951465487480164, "Y": 0.6887259483337402}]}, "Id": "7eeecf3b-c20f-4e95-944b-0061f5233221", "Relationships": [{"Type": "CHILD", "Ids": ["dfaccade-6812-48d7-8e86-65c695b5a0b9"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "12007196", "Geometry": {"BoundingBox": {"Width": 0.177385151386261, "Height": 0.015971913933753967, "Left": 0.7045323252677917, "Top": 0.6715840101242065}, "Polygon": [{"X": 0.7045323252677917, "Y": 0.6715840101242065}, {"X": 0.8819162249565125, "Y": 0.6715843081474304}, {"X": 0.8819174766540527, "Y": 0.6875559687614441}, {"X": 0.7045337557792664, "Y": 0.6875556111335754}]}, "Id": "7973cab1-61fe-41ef-88a3-0b751074a95f", "Relationships": [{"Type": "CHILD", "Ids": ["24144519-f6dd-4265-9169-0ef52ebebda8"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Driver", "Geometry": {"BoundingBox": {"Width": 0.11279182881116867, "Height": 0.01331867091357708, "Left": 0.1428855061531067, "Top": 0.6973837018013}, "Polygon": [{"X": 0.1428855061531067, "Y": 0.6973837018013}, {"X": 0.25567594170570374, "Y": 0.6973839402198792}, {"X": 0.25567731261253357, "Y": 0.7107023596763611}, {"X": 0.1428869366645813, "Y": 0.710702121257782}]}, "Id": "c8eefe29-910c-4f7e-ba9a-4882fb3f2edc", "Relationships": [{"Type": "CHILD", "Ids": ["948e474d-4711-417b-bbdd-da2e99b2764a"]}]}, {"BlockType": "LINE", "Confidence": 99.45731353759766, "Text": "RRR", "Geometry": {"BoundingBox": {"Width": 0.08374407887458801, "Height": 0.016965564340353012, "Left": 0.803195059299469, "Top": 0.6927899718284607}, "Polygon": [{"X": 0.803195059299469, "Y": 0.6927899718284607}, {"X": 0.8869377970695496, "Y": 0.692790150642395}, {"X": 0.8869391083717346, "Y": 0.709755539894104}, {"X": 0.8031964302062988, "Y": 0.7097553610801697}]}, "Id": "914302d5-bcf2-487b-bea2-5786791f6a9a", "Relationships": [{"Type": "CHILD", "Ids": ["4df1e3d1-9700-4384-b980-c2f5d1909d30"]}]}, {"BlockType": "LINE", "Confidence": 99.68192291259766, "Text": "Driver's Signature", "Geometry": {"BoundingBox": {"Width": 0.32594433426856995, "Height": 0.01589202508330345, "Left": 0.3199101388454437, "Top": 0.7359161972999573}, "Polygon": [{"X": 0.3199101388454437, "Y": 0.7359161972999573}, {"X": 0.6458530426025391, "Y": 0.7359171509742737}, {"X": 0.6458544731140137, "Y": 0.751808226108551}, {"X": 0.31991174817085266, "Y": 0.7518072128295898}]}, "Id": "34944b5a-fed2-4e41-a14f-119329c2ee29", "Relationships": [{"Type": "CHILD", "Ids": ["7666206c-ecb0-4aaf-9758-f58b699add2c", "3458041b-97a0-4dfc-a743-a9ca6fd28a4e"]}]}, {"BlockType": "LINE", "Confidence": 99.56194305419922, "Text": "Supervisor's Signature", "Geometry": {"BoundingBox": {"Width": 0.41156283020973206, "Height": 0.016970403492450714, "Left": 0.28128546476364136, "Top": 0.8308382034301758}, "Polygon": [{"X": 0.28128546476364136, "Y": 0.8308382034301758}, {"X": 0.6928467750549316, "Y": 0.8308401703834534}, {"X": 0.692848265171051, "Y": 0.8478085994720459}, {"X": 0.28128719329833984, "Y": 0.847806453704834}]}, "Id": "7ae5c6c0-ce97-414a-b9d2-6bef44c99648", "Relationships": [{"Type": "CHILD", "Ids": ["5c121dea-9b58-4c84-be28-e9c0e4294898", "2f9dc12c-5b02-4331-80b7-ffa7e0b1fdfe"]}]}, {"BlockType": "LINE", "Confidence": 70.44145202636719, "Text": "29", "Geometry": {"BoundingBox": {"Width": 0.25587156414985657, "Height": 0.03180857002735138, "Left": 0.3889331519603729, "Top": 0.8613988757133484}, "Polygon": [{"X": 0.3889331519603729, "Y": 0.8613988757133484}, {"X": 0.644801914691925, "Y": 0.861400306224823}, {"X": 0.6448047161102295, "Y": 0.8932074308395386}, {"X": 0.38893628120422363, "Y": 0.8932058811187744}]}, "Id": "2e15ca4a-7c57-4077-8fee-5b4d4efecdaa", "Relationships": [{"Type": "CHILD", "Ids": ["66c5a19d-bba4-4c13-8099-15f4f16d7377"]}]}, {"BlockType": "LINE", "Confidence": 96.42597961425781, "Text": "07/26/2025 00:35", "Geometry": {"BoundingBox": {"Width": 0.29983964562416077, "Height": 0.011939950287342072, "Left": 0.36763235926628113, "Top": 0.9582545161247253}, "Polygon": [{"X": 0.36763235926628113, "Y": 0.9582545161247253}, {"X": 0.6674709320068359, "Y": 0.9582567811012268}, {"X": 0.6674720048904419, "Y": 0.9701944589614868}, {"X": 0.36763355135917664, "Y": 0.9701921343803406}]}, "Id": "fb917ddf-0661-4a06-9aef-e9503fb153c9", "Relationships": [{"Type": "CHILD", "Ids": ["575d0636-9820-4efc-98e4-9411f5a8d56f", "946d5e98-d5cf-4480-9de9-6a40f9bb9f46"]}]}, {"BlockType": "LINE", "Confidence": 30.004981994628906, "Text": "www.merittogistics.com", "Geometry": {"BoundingBox": {"Width": 0.4804471433162689, "Height": 0.012499780394136906, "Left": 0.27515482902526855, "Top": 0.9721404910087585}, "Polygon": [{"X": 0.27515482902526855, "Y": 0.9721404910087585}, {"X": 0.7556009292602539, "Y": 0.9721442461013794}, {"X": 0.7556019425392151, "Y": 0.9846402406692505}, {"X": 0.2751561105251312, "Y": 0.9846363663673401}]}, "Id": "c0828b70-a4b7-46a6-8866-f0105db03ce9", "Relationships": [{"Type": "CHILD", "Ids": ["cd893ffc-230d-415a-9e86-cb33e2dcc76f"]}]}, {"BlockType": "WORD", "Confidence": 98.26909637451172, "Text": "MERIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24773530662059784, "Height": 0.030968578532338142, "Left": 0.18507200479507446, "Top": 0.1073952466249466}, "Polygon": [{"X": 0.18507200479507446, "Y": 0.10739777982234955}, {"X": 0.43280428647994995, "Y": 0.1073952466249466}, {"X": 0.4328072965145111, "Y": 0.13836143910884857}, {"X": 0.1850752979516983, "Y": 0.1383638232946396}]}, "Id": "8d4fa586-9ec4-41c9-aa63-485e33efc818"}, {"BlockType": "WORD", "Confidence": 57.2064323425293, "Text": "LOGISTIC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.3510882556438446, "Height": 0.03660334646701813, "Left": 0.5848082900047302, "Top": 0.0968146026134491}, "Polygon": [{"X": 0.5848082900047302, "Y": 0.09681828320026398}, {"X": 0.9358936548233032, "Y": 0.0968146026134491}, {"X": 0.9358965158462524, "Y": 0.13341455161571503}, {"X": 0.5848116278648376, "Y": 0.13341794908046722}]}, "Id": "58ac29e9-0d4d-4800-849d-e80dff173d1c"}, {"BlockType": "WORD", "Confidence": 98.13397216796875, "Text": "(949)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10251326858997345, "Height": 0.021099045872688293, "Left": 0.3484624922275543, "Top": 0.17989249527454376}, "Polygon": [{"X": 0.3484624922275543, "Y": 0.1798933893442154}, {"X": 0.4509737491607666, "Y": 0.17989249527454376}, {"X": 0.45097577571868896, "Y": 0.200990691781044}, {"X": 0.34846460819244385, "Y": 0.20099154114723206}]}, "Id": "08e537df-2f63-4ea9-96df-93382170234c"}, {"BlockType": "WORD", "Confidence": 97.06253814697266, "Text": "481-0685", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19154953956604004, "Height": 0.017770186066627502, "Left": 0.4649932086467743, "Top": 0.17896682024002075}, "Polygon": [{"X": 0.4649932086467743, "Y": 0.17896850407123566}, {"X": 0.6565411686897278, "Y": 0.17896682024002075}, {"X": 0.6565427780151367, "Y": 0.19673539698123932}, {"X": 0.4649949073791504, "Y": 0.19673700630664825}]}, "Id": "21e369cd-0d7a-4a82-ac60-68d2cc1a7cb2"}, {"BlockType": "WORD", "Confidence": 99.37281036376953, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08758188039064407, "Height": 0.016989395022392273, "Left": 0.1708100140094757, "Top": 0.22101864218711853}, "Polygon": [{"X": 0.1708100140094757, "Y": 0.22101934254169464}, {"X": 0.2583901286125183, "Y": 0.22101864218711853}, {"X": 0.2583919167518616, "Y": 0.23800738155841827}, {"X": 0.17081184685230255, "Y": 0.2380080372095108}]}, "Id": "a5909eca-215b-4fda-8009-cf8d679d3250"}, {"BlockType": "WORD", "Confidence": 98.76435089111328, "Text": "(ALDFRE),", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22142870724201202, "Height": 0.020506413653492928, "Left": 0.27561527490615845, "Top": 0.22129520773887634}, "Polygon": [{"X": 0.27561527490615845, "Y": 0.22129695117473602}, {"X": 0.49704205989837646, "Y": 0.22129520773887634}, {"X": 0.49704399704933167, "Y": 0.2417999804019928}, {"X": 0.275617390871048, "Y": 0.24180161952972412}]}, "Id": "9b5e529b-2de7-441a-b83c-7d5a8cb1d10a"}, {"BlockType": "WORD", "Confidence": 98.48612976074219, "Text": "<PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2255355566740036, "Height": 0.018261950463056564, "Left": 0.5158470273017883, "Top": 0.21942326426506042}, "Polygon": [{"X": 0.5158470273017883, "Y": 0.2194250524044037}, {"X": 0.741381049156189, "Y": 0.21942326426506042}, {"X": 0.7413825988769531, "Y": 0.2376835197210312}, {"X": 0.5158487558364868, "Y": 0.2376852184534073}]}, "Id": "07ebc9ad-a432-4570-b4ee-34e2eee1c8ee"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07407316565513611, "Height": 0.01718895696103573, "Left": 0.7608513832092285, "Top": 0.21789641678333282}, "Polygon": [{"X": 0.7608513832092285, "Y": 0.21789701282978058}, {"X": 0.8349231481552124, "Y": 0.21789641678333282}, {"X": 0.834924578666687, "Y": 0.23508481681346893}, {"X": 0.7608528137207031, "Y": 0.2350853830575943}]}, "Id": "b91672b7-9028-43a9-a316-632bddfde86f"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06189828738570213, "Height": 0.012198771350085735, "Left": 0.14416752755641937, "Top": 0.279058039188385}, "Polygon": [{"X": 0.14416752755641937, "Y": 0.27905845642089844}, {"X": 0.20606452226638794, "Y": 0.279058039188385}, {"X": 0.2060658037662506, "Y": 0.2912564277648926}, {"X": 0.14416883885860443, "Y": 0.2912568151950836}]}, "Id": "72f782e6-f0b2-4590-9492-418297dd2909"}, {"BlockType": "WORD", "Confidence": 94.63568115234375, "Text": "Id", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036270540207624435, "Height": 0.012380054220557213, "Left": 0.21731781959533691, "Top": 0.27878475189208984}, "Polygon": [{"X": 0.21731781959533691, "Y": 0.27878499031066895}, {"X": 0.2535870671272278, "Y": 0.27878475189208984}, {"X": 0.25358834862709045, "Y": 0.2911645770072937}, {"X": 0.21731913089752197, "Y": 0.2911648154258728}]}, "Id": "eee63e26-ca25-425c-9298-bcaa19ee027d"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "*********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19964005053043365, "Height": 0.01619478315114975, "Left": 0.6647797226905823, "Top": 0.2744753062725067}, "Polygon": [{"X": 0.6647797226905823, "Y": 0.27447664737701416}, {"X": 0.8644184470176697, "Y": 0.2744753062725067}, {"X": 0.8644197583198547, "Y": 0.2906688153743744}, {"X": 0.6647811532020569, "Y": 0.29067009687423706}]}, "Id": "d47bfc63-b24c-4791-8255-4e7adb2612a4"}, {"BlockType": "WORD", "Confidence": 87.84259796142578, "Text": "WO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05964205786585808, "Height": 0.01220475509762764, "Left": 0.4134569764137268, "Top": 0.2944175601005554}, "Polygon": [{"X": 0.4134569764137268, "Y": 0.29441794753074646}, {"X": 0.47309786081314087, "Y": 0.2944175601005554}, {"X": 0.473099023103714, "Y": 0.3066219389438629}, {"X": 0.4134581685066223, "Y": 0.30662232637405396}]}, "Id": "099242a8-20a9-485a-bc3c-0a2cf90b0803"}, {"BlockType": "WORD", "Confidence": 98.154296875, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02495601400732994, "Height": 0.011620372533798218, "Left": 0.4817391633987427, "Top": 0.2947494387626648}, "Polygon": [{"X": 0.4817391633987427, "Y": 0.29474958777427673}, {"X": 0.5066940784454346, "Y": 0.2947494387626648}, {"X": 0.5066951513290405, "Y": 0.3063696622848511}, {"X": 0.481740266084671, "Y": 0.306369811296463}]}, "Id": "770b088f-1794-4622-ac93-1041b5fac8ca"}, {"BlockType": "WORD", "Confidence": 79.87165832519531, "Text": "ALDFRE9734307252025203958", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.5582346320152283, "Height": 0.016875185072422028, "Left": 0.2240321785211563, "Top": 0.310842365026474}, "Polygon": [{"X": 0.2240321785211563, "Y": 0.3108457028865814}, {"X": 0.7822654247283936, "Y": 0.310842365026474}, {"X": 0.7822668552398682, "Y": 0.32771438360214233}, {"X": 0.22403395175933838, "Y": 0.32771754264831543}]}, "Id": "ed26ceed-0b82-4114-b775-e7fc2c255806"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08533041924238205, "Height": 0.012756140902638435, "Left": 0.14407946169376373, "Top": 0.3356296718120575}, "Polygon": [{"X": 0.14407946169376373, "Y": 0.3356301188468933}, {"X": 0.22940854728221893, "Y": 0.3356296718120575}, {"X": 0.22940988838672638, "Y": 0.34838536381721497}, {"X": 0.14408084750175476, "Y": 0.3483858108520508}]}, "Id": "db44ad0d-2691-41a5-bc89-dbf47211dd46"}, {"BlockType": "WORD", "Confidence": 99.052734375, "Text": "7/25/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17674770951271057, "Height": 0.014293763786554337, "Left": 0.4762756824493408, "Top": 0.3339797854423523}, "Polygon": [{"X": 0.4762756824493408, "Y": 0.3339807689189911}, {"X": 0.6530221104621887, "Y": 0.3339797854423523}, {"X": 0.653023362159729, "Y": 0.3482726216316223}, {"X": 0.47627705335617065, "Y": 0.34827354550361633}]}, "Id": "ed27e078-a138-4b02-bc8f-b400b3b02acf"}, {"BlockType": "WORD", "Confidence": 99.228515625, "Text": "8:39:00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13315479457378387, "Height": 0.015541712753474712, "Left": 0.6603617668151855, "Top": 0.33210861682891846}, "Polygon": [{"X": 0.6603617668151855, "Y": 0.33210936188697815}, {"X": 0.7935152649879456, "Y": 0.33210861682891846}, {"X": 0.7935165762901306, "Y": 0.34764963388442993}, {"X": 0.6603631377220154, "Y": 0.34765031933784485}]}, "Id": "c55a68c4-5548-48ea-a5a7-d8d230012aac"}, {"BlockType": "WORD", "Confidence": 99.697265625, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06124924123287201, "Height": 0.015116817317903042, "Left": 0.8028196692466736, "Top": 0.3310616910457611}, "Polygon": [{"X": 0.8028196692466736, "Y": 0.33106204867362976}, {"X": 0.8640676736831665, "Y": 0.3310616910457611}, {"X": 0.8640689253807068, "Y": 0.34617817401885986}, {"X": 0.8028209209442139, "Y": 0.34617850184440613}]}, "Id": "458c11a9-ab72-45e2-87cf-a68b6054e8a4"}, {"BlockType": "WORD", "Confidence": 99.1796875, "Text": "PO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05549069494009018, "Height": 0.01195859257131815, "Left": 0.14226120710372925, "Top": 0.3576103746891022}, "Polygon": [{"X": 0.14226120710372925, "Y": 0.35761067271232605}, {"X": 0.19775062799453735, "Y": 0.3576103746891022}, {"X": 0.19775189459323883, "Y": 0.36956870555877686}, {"X": 0.1422625035047531, "Y": 0.36956897377967834}]}, "Id": "8ddc276c-785c-486d-b107-3ae5150f4a1b"}, {"BlockType": "WORD", "Confidence": 99.3359375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022576121613383293, "Height": 0.01141516026109457, "Left": 0.20773808658123016, "Top": 0.3579596281051636}, "Polygon": [{"X": 0.20773808658123016, "Y": 0.35795971751213074}, {"X": 0.2303130030632019, "Y": 0.3579596281051636}, {"X": 0.2303142100572586, "Y": 0.36937466263771057}, {"X": 0.20773929357528687, "Y": 0.3693747818470001}]}, "Id": "a63ab3aa-82bc-4263-8641-ccb4ebce7811"}, {"BlockType": "WORD", "Confidence": 98.6756591796875, "Text": "7507418921", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.21574129164218903, "Height": 0.016964301466941833, "Left": 0.6466652750968933, "Top": 0.3529658615589142}, "Polygon": [{"X": 0.6466652750968933, "Y": 0.35296696424484253}, {"X": 0.8624052405357361, "Y": 0.3529658615589142}, {"X": 0.8624066114425659, "Y": 0.36992913484573364}, {"X": 0.6466668248176575, "Y": 0.3699301779270172}]}, "Id": "578c84aa-b7a3-4a6e-a4a5-8a483e7f6810"}, {"BlockType": "WORD", "Confidence": 99.013671875, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09974201023578644, "Height": 0.012015053071081638, "Left": 0.14428658783435822, "Top": 0.3778558671474457}, "Polygon": [{"X": 0.14428658783435822, "Y": 0.3778563141822815}, {"X": 0.24402734637260437, "Y": 0.3778558671474457}, {"X": 0.24402859807014465, "Y": 0.38987046480178833}, {"X": 0.14428788423538208, "Y": 0.38987091183662415}]}, "Id": "080c5e87-c885-46e2-8fe9-de966a7f0e36"}, {"BlockType": "WORD", "Confidence": 99.365234375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025024201720952988, "Height": 0.01248437911272049, "Left": 0.25288084149360657, "Top": 0.3771931827068329}, "Polygon": [{"X": 0.25288084149360657, "Y": 0.37719330191612244}, {"X": 0.2779037654399872, "Y": 0.3771931827068329}, {"X": 0.27790504693984985, "Y": 0.3896774649620056}, {"X": 0.2528821527957916, "Y": 0.3896775543689728}]}, "Id": "e16f3d4b-eade-464e-afc8-dbe2d1510870"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "101", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06180214136838913, "Height": 0.015584289096295834, "Left": 0.8016756772994995, "Top": 0.37275105714797974}, "Polygon": [{"X": 0.8016756772994995, "Y": 0.3727513551712036}, {"X": 0.863476574420929, "Y": 0.37275105714797974}, {"X": 0.8634778261184692, "Y": 0.38833507895469666}, {"X": 0.8016769289970398, "Y": 0.38833534717559814}]}, "Id": "d8c9337b-eec6-403c-98b7-c16cbe7d6b39"}, {"BlockType": "WORD", "Confidence": 98.05484771728516, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11645343154668808, "Height": 0.01223898958414793, "Left": 0.14484895765781403, "Top": 0.3990103304386139}, "Polygon": [{"X": 0.14484895765781403, "Y": 0.3990108072757721}, {"X": 0.2613011300563812, "Y": 0.3990103304386139}, {"X": 0.2613024115562439, "Y": 0.4112488627433777}, {"X": 0.14485028386116028, "Y": 0.4112493097782135}]}, "Id": "2137d100-242d-4a09-aa0f-23285fcd1cf2"}, {"BlockType": "WORD", "Confidence": 99.345703125, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02246459759771824, "Height": 0.011130369268357754, "Left": 0.2695062458515167, "Top": 0.3993787169456482}, "Polygon": [{"X": 0.2695062458515167, "Y": 0.39937883615493774}, {"X": 0.2919696867465973, "Y": 0.3993787169456482}, {"X": 0.2919708490371704, "Y": 0.41050902009010315}, {"X": 0.26950737833976746, "Y": 0.4105091094970703}]}, "Id": "de571e50-37dc-449c-8244-c40abd5265a8"}, {"BlockType": "WORD", "Confidence": 88.96424865722656, "Text": "()--288", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12617294490337372, "Height": 0.019286949187517166, "Left": 0.7425933480262756, "Top": 0.39464476704597473}, "Polygon": [{"X": 0.7425933480262756, "Y": 0.3946453034877777}, {"X": 0.8687646985054016, "Y": 0.39464476704597473}, {"X": 0.8687662482261658, "Y": 0.4139312207698822}, {"X": 0.7425949573516846, "Y": 0.4139316976070404}]}, "Id": "2eb0de5e-6b5b-427c-9b8f-aacc1725df25"}, {"BlockType": "WORD", "Confidence": 99.05193328857422, "Text": "Door", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08543319255113602, "Height": 0.01184589508920908, "Left": 0.14406432211399078, "Top": 0.420370489358902}, "Polygon": [{"X": 0.14406432211399078, "Y": 0.42037081718444824}, {"X": 0.2294962853193283, "Y": 0.420370489358902}, {"X": 0.2294975221157074, "Y": 0.4322161078453064}, {"X": 0.14406560361385345, "Y": 0.4322164058685303}]}, "Id": "04faddab-4ed5-467b-823d-a42d2f82452f"}, {"BlockType": "WORD", "Confidence": 98.828125, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02272077277302742, "Height": 0.011581983417272568, "Left": 0.237057164311409, "Top": 0.4203850328922272}, "Polygon": [{"X": 0.237057164311409, "Y": 0.42038512229919434}, {"X": 0.2597767412662506, "Y": 0.4203850328922272}, {"X": 0.2597779333591461, "Y": 0.43196696043014526}, {"X": 0.2370583862066269, "Y": 0.43196702003479004}]}, "Id": "ff65beeb-bff5-4bca-ba14-d8b85128cc36"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "34", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04900645464658737, "Height": 0.01625366322696209, "Left": 0.8215357661247253, "Top": 0.4149540066719055}, "Polygon": [{"X": 0.8215357661247253, "Y": 0.41495418548583984}, {"X": 0.8705409169197083, "Y": 0.4149540066719055}, {"X": 0.8705422282218933, "Y": 0.4312075078487396}, {"X": 0.8215371370315552, "Y": 0.43120765686035156}]}, "Id": "eb57d5ef-8e92-4e99-abeb-f23bdd09bc70"}, {"BlockType": "WORD", "Confidence": 99.41266632080078, "Text": "Client's", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12869594991207123, "Height": 0.012451906688511372, "Left": 0.14407885074615479, "Top": 0.4415885806083679}, "Polygon": [{"X": 0.14407885074615479, "Y": 0.44158899784088135}, {"X": 0.27277350425720215, "Y": 0.4415885806083679}, {"X": 0.2727747857570648, "Y": 0.4540401101112366}, {"X": 0.14408019185066223, "Y": 0.4540404975414276}]}, "Id": "e5ce6dc7-2d0a-4a07-87d1-c770de0b2a99"}, {"BlockType": "WORD", "Confidence": 99.5517807006836, "Text": "Dept", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08303327113389969, "Height": 0.013751579448580742, "Left": 0.28482168912887573, "Top": 0.44123655557632446}, "Polygon": [{"X": 0.28482168912887573, "Y": 0.44123682379722595}, {"X": 0.367853581905365, "Y": 0.44123655557632446}, {"X": 0.3678549528121948, "Y": 0.45498788356781006}, {"X": 0.28482311964035034, "Y": 0.45498812198638916}]}, "Id": "4177ddef-e6c5-4a9b-b7f4-3665de4a4133"}, {"BlockType": "WORD", "Confidence": 68.30974578857422, "Text": "Unipading", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1949533075094223, "Height": 0.01763341948390007, "Left": 0.5517192482948303, "Top": 0.4387663006782532}, "Polygon": [{"X": 0.5517192482948303, "Y": 0.4387669563293457}, {"X": 0.7466710805892944, "Y": 0.4387663006782532}, {"X": 0.7466725707054138, "Y": 0.456399142742157}, {"X": 0.551720917224884, "Y": 0.45639973878860474}]}, "Id": "84544ee1-f2a8-48f4-95df-92ebd866b7e7"}, {"BlockType": "WORD", "Confidence": 98.76594543457031, "Text": "(GRP)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1080993041396141, "Height": 0.017862476408481598, "Left": 0.7589293718338013, "Top": 0.43752774596214294}, "Polygon": [{"X": 0.7589293718338013, "Y": 0.4375281035900116}, {"X": 0.867027223110199, "Y": 0.43752774596214294}, {"X": 0.8670286536216736, "Y": 0.4553898870944977}, {"X": 0.7589308619499207, "Y": 0.45539021492004395}]}, "Id": "5edfbd48-1611-4116-80f0-59e71e9017c1"}, {"BlockType": "WORD", "Confidence": 99.24745178222656, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14044232666492462, "Height": 0.011927680112421513, "Left": 0.14443199336528778, "Top": 0.4618944227695465}, "Polygon": [{"X": 0.14443199336528778, "Y": 0.46189481019973755}, {"X": 0.2848730981349945, "Y": 0.4618944227695465}, {"X": 0.2848743200302124, "Y": 0.4738217294216156}, {"X": 0.14443328976631165, "Y": 0.47382208704948425}]}, "Id": "468269ae-f691-49a6-b891-d7dbe0c6cfd3"}, {"BlockType": "WORD", "Confidence": 99.15876770019531, "Text": "GM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06667549163103104, "Height": 0.0152316028252244, "Left": 0.8020117282867432, "Top": 0.4576770067214966}, "Polygon": [{"X": 0.8020117282867432, "Y": 0.4576772153377533}, {"X": 0.8686860203742981, "Y": 0.4576770067214966}, {"X": 0.8686872124671936, "Y": 0.4729084372520447}, {"X": 0.8020129799842834, "Y": 0.472908616065979}]}, "Id": "f1179481-f195-413b-b937-818002e68642"}, {"BlockType": "WORD", "Confidence": 98.73505401611328, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12628546357154846, "Height": 0.011748413555324078, "Left": 0.1465824842453003, "Top": 0.4827662408351898}, "Polygon": [{"X": 0.1465824842453003, "Y": 0.4827665388584137}, {"X": 0.27286672592163086, "Y": 0.4827662408351898}, {"X": 0.27286794781684875, "Y": 0.4945143759250641}, {"X": 0.14658375084400177, "Y": 0.4945146441459656}]}, "Id": "640dc98a-b366-4769-a516-aac94736be0f"}, {"BlockType": "WORD", "Confidence": 97.0948257446289, "Text": "PREMIUM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17048752307891846, "Height": 0.01445438340306282, "Left": 0.4460369944572449, "Top": 0.479751318693161}, "Polygon": [{"X": 0.4460369944572449, "Y": 0.47975173592567444}, {"X": 0.6165232062339783, "Y": 0.479751318693161}, {"X": 0.6165245175361633, "Y": 0.4942053258419037}, {"X": 0.4460383951663971, "Y": 0.49420568346977234}]}, "Id": "dfae1816-8a3a-443d-82fc-1f6b99202b43"}, {"BlockType": "WORD", "Confidence": 98.984375, "Text": "WATERS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1675719916820526, "Height": 0.014728314243257046, "Left": 0.6264291405677795, "Top": 0.47906169295310974}, "Polygon": [{"X": 0.6264291405677795, "Y": 0.47906211018562317}, {"X": 0.7939999103546143, "Y": 0.47906169295310974}, {"X": 0.7940011024475098, "Y": 0.4937896430492401}, {"X": 0.6264304518699646, "Y": 0.49379000067710876}]}, "Id": "1a2d96b9-192a-4f36-9bc4-8ec5df4c070c"}, {"BlockType": "WORD", "Confidence": 99.49876403808594, "Text": "INC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06712885200977325, "Height": 0.01506961788982153, "Left": 0.8032369017601013, "Top": 0.47775039076805115}, "Polygon": [{"X": 0.8032369017601013, "Y": 0.4777505397796631}, {"X": 0.8703645467758179, "Y": 0.47775039076805115}, {"X": 0.8703657388687134, "Y": 0.4928198456764221}, {"X": 0.8032381534576416, "Y": 0.49281999468803406}]}, "Id": "df13fcbc-7fcb-4a7f-9042-c8b436c90a83"}, {"BlockType": "WORD", "Confidence": 99.3126220703125, "Text": "Carrier", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12479837983846664, "Height": 0.012299999594688416, "Left": 0.1439296156167984, "Top": 0.5031832456588745}, "Polygon": [{"X": 0.1439296156167984, "Y": 0.5031834840774536}, {"X": 0.26872673630714417, "Y": 0.5031832456588745}, {"X": 0.26872798800468445, "Y": 0.515483021736145}, {"X": 0.14393095672130585, "Y": 0.5154832005500793}]}, "Id": "9d877b23-ea72-4823-8878-95c7ecc775de"}, {"BlockType": "WORD", "Confidence": 95.7276382446289, "Text": "3A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05320126563310623, "Height": 0.01563923805952072, "Left": 0.5707413554191589, "Top": 0.500748336315155}, "Polygon": [{"X": 0.5707413554191589, "Y": 0.5007484555244446}, {"X": 0.62394118309021, "Y": 0.500748336315155}, {"X": 0.6239426136016846, "Y": 0.5163874626159668}, {"X": 0.5707427859306335, "Y": 0.5163875818252563}]}, "Id": "5c83c131-0abd-4687-a9b0-32d289d51486"}, {"BlockType": "WORD", "Confidence": 99.79332733154297, "Text": "TRANSPORT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24108321964740753, "Height": 0.01743924617767334, "Left": 0.6299802660942078, "Top": 0.4987722635269165}, "Polygon": [{"X": 0.6299802660942078, "Y": 0.4987727403640747}, {"X": 0.8710620999336243, "Y": 0.4987722635269165}, {"X": 0.8710635304450989, "Y": 0.5162110924720764}, {"X": 0.6299818158149719, "Y": 0.5162115097045898}]}, "Id": "e6248954-e49d-4de4-9c55-d9555e043b09"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05958958715200424, "Height": 0.012090464122593403, "Left": 0.14364959299564362, "Top": 0.5259847044944763}, "Polygon": [{"X": 0.14364959299564362, "Y": 0.5259847640991211}, {"X": 0.20323790609836578, "Y": 0.5259847044944763}, {"X": 0.20323918759822845, "Y": 0.5380750894546509}, {"X": 0.14365090429782867, "Y": 0.5380751490592957}]}, "Id": "1dbcacf5-71b1-4bf9-b2da-f7a3202a71c9"}, {"BlockType": "WORD", "Confidence": 99.59402465820312, "Text": "To", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043086618185043335, "Height": 0.011830746196210384, "Left": 0.21406413614749908, "Top": 0.5256401300430298}, "Polygon": [{"X": 0.21406413614749908, "Y": 0.5256401896476746}, {"X": 0.25714951753616333, "Y": 0.5256401300430298}, {"X": 0.2571507394313812, "Y": 0.537470817565918}, {"X": 0.21406538784503937, "Y": 0.5374708771705627}]}, "Id": "83b55957-3a7b-451c-9cd2-6142794e9037"}, {"BlockType": "WORD", "Confidence": 99.25642395019531, "Text": "3A", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05361201986670494, "Height": 0.014675240032374859, "Left": 0.5708646774291992, "Top": 0.522225558757782}, "Polygon": [{"X": 0.5708646774291992, "Y": 0.5222256183624268}, {"X": 0.624475359916687, "Y": 0.522225558757782}, {"X": 0.6244766712188721, "Y": 0.5369006991386414}, {"X": 0.5708659887313843, "Y": 0.5369007587432861}]}, "Id": "5262bf6e-ebc0-41bf-87e7-a87e984c5c85"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "TRANSPORT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2408258467912674, "Height": 0.015583988279104233, "Left": 0.6309599876403809, "Top": 0.5210983157157898}, "Polygon": [{"X": 0.6309599876403809, "Y": 0.5210987329483032}, {"X": 0.8717845678329468, "Y": 0.5210983157157898}, {"X": 0.8717858195304871, "Y": 0.5366820096969604}, {"X": 0.6309613585472107, "Y": 0.5366823077201843}]}, "Id": "a5980559-b971-48ee-b1d2-90aa785eac8e"}, {"BlockType": "WORD", "Confidence": 99.52069091796875, "Text": "QTY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07920538634061813, "Height": 0.014591755345463753, "Left": 0.14367572963237762, "Top": 0.5463899970054626}, "Polygon": [{"X": 0.14367572963237762, "Y": 0.5463900566101074}, {"X": 0.2228795886039734, "Y": 0.5463899970054626}, {"X": 0.22288112342357635, "Y": 0.5609816908836365}, {"X": 0.14367730915546417, "Y": 0.5609817504882812}]}, "Id": "2a771b86-8083-4160-bdf4-22cffc9e9b90"}, {"BlockType": "WORD", "Confidence": 92.7648696899414, "Text": "<", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02094186097383499, "Height": 0.009188602678477764, "Left": 0.23336319625377655, "Top": 0.5478973984718323}, "Polygon": [{"X": 0.23336319625377655, "Y": 0.5478973984718323}, {"X": 0.25430411100387573, "Y": 0.5478973984718323}, {"X": 0.25430506467819214, "Y": 0.5570859313011169}, {"X": 0.23336416482925415, "Y": 0.5570859909057617}]}, "Id": "54641369-19a0-4dd7-9f7e-c400a3fb0ed8"}, {"BlockType": "WORD", "Confidence": 94.99302673339844, "Text": "Case", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08773268759250641, "Height": 0.012426801957190037, "Left": 0.26646989583969116, "Top": 0.5462460517883301}, "Polygon": [{"X": 0.26646989583969116, "Y": 0.5462461709976196}, {"X": 0.3542013466358185, "Y": 0.5462460517883301}, {"X": 0.35420259833335876, "Y": 0.5586727857589722}, {"X": 0.2664712071418762, "Y": 0.5586728453636169}]}, "Id": "b37d6884-2fc4-4c70-902f-b92a432fee89"}, {"BlockType": "WORD", "Confidence": 94.919677734375, "Text": ">", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022970275953412056, "Height": 0.008907956071197987, "Left": 0.3656720817089081, "Top": 0.5470844507217407}, "Polygon": [{"X": 0.3656720817089081, "Y": 0.5470844507217407}, {"X": 0.38864147663116455, "Y": 0.5470844507217407}, {"X": 0.3886423707008362, "Y": 0.5559923648834229}, {"X": 0.3656729757785797, "Y": 0.5559923648834229}]}, "Id": "7d4fc56b-cea5-43c0-af9e-710a72a04e85"}, {"BlockType": "WORD", "Confidence": 99.833984375, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02006562240421772, "Height": 0.017020318657159805, "Left": 0.8509932160377502, "Top": 0.5419660210609436}, "Polygon": [{"X": 0.8509932160377502, "Y": 0.5419660806655884}, {"X": 0.8710575103759766, "Y": 0.5419660210609436}, {"X": 0.8710588812828064, "Y": 0.5589863657951355}, {"X": 0.8509945869445801, "Y": 0.5589863657951355}]}, "Id": "3efd7556-a521-42b7-b7d5-60a52f24c27b"}, {"BlockType": "WORD", "Confidence": 99.77458953857422, "Text": "Activity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13567721843719482, "Height": 0.014926869422197342, "Left": 0.14278826117515564, "Top": 0.5681006908416748}, "Polygon": [{"X": 0.14278826117515564, "Y": 0.5681008100509644}, {"X": 0.2784639298915863, "Y": 0.5681006908416748}, {"X": 0.27846547961235046, "Y": 0.5830275416374207}, {"X": 0.14278988540172577, "Y": 0.5830276012420654}]}, "Id": "0086486e-2ba6-4178-811a-280895fc9f08"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$91.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12356474250555038, "Height": 0.01906082034111023, "Left": 0.7518740296363831, "Top": 0.5626896023750305}, "Polygon": [{"X": 0.7518740296363831, "Y": 0.5626896619796753}, {"X": 0.8754372596740723, "Y": 0.5626896023750305}, {"X": 0.8754388093948364, "Y": 0.5817503333091736}, {"X": 0.751875638961792, "Y": 0.5817503929138184}]}, "Id": "9f0a08d6-d4af-4cbb-b29b-9be5106b3410"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Work", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09174610674381256, "Height": 0.013595961965620518, "Left": 0.14421646296977997, "Top": 0.5884254574775696}, "Polygon": [{"X": 0.14421646296977997, "Y": 0.5884254574775696}, {"X": 0.23596113920211792, "Y": 0.5884254574775696}, {"X": 0.23596256971359253, "Y": 0.6020213961601257}, {"X": 0.14421793818473816, "Y": 0.6020213961601257}]}, "Id": "0d8aefe8-5775-4936-86d8-6772c358e30c"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10387392342090607, "Height": 0.013472368009388447, "Left": 0.24729061126708984, "Top": 0.588137686252594}, "Polygon": [{"X": 0.24729061126708984, "Y": 0.5881377458572388}, {"X": 0.35116317868232727, "Y": 0.588137686252594}, {"X": 0.3511645197868347, "Y": 0.6016100645065308}, {"X": 0.24729201197624207, "Y": 0.6016100645065308}]}, "Id": "cbb610b1-cb50-499d-be39-1b3088ab689e"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "Charge", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12417999655008316, "Height": 0.015810536220669746, "Left": 0.3603871762752533, "Top": 0.587481677532196}, "Polygon": [{"X": 0.3603871762752533, "Y": 0.5874817371368408}, {"X": 0.4845656752586365, "Y": 0.587481677532196}, {"X": 0.48456716537475586, "Y": 0.6032922267913818}, {"X": 0.36038875579833984, "Y": 0.6032922267913818}]}, "Id": "8932ec34-84c2-4ea9-8268-2d88d127ed75"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$91.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12454274296760559, "Height": 0.01950083300471306, "Left": 0.7530006170272827, "Top": 0.5844067335128784}, "Polygon": [{"X": 0.7530006170272827, "Y": 0.5844067335128784}, {"X": 0.8775417804718018, "Y": 0.5844067335128784}, {"X": 0.8775433301925659, "Y": 0.6039075255393982}, {"X": 0.7530022263526917, "Y": 0.6039075255393982}]}, "Id": "51a72387-3fc6-46ee-ae1b-74ea59fb7345"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Service", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1390913426876068, "Height": 0.012761499732732773, "Left": 0.14305247366428375, "Top": 0.6114959120750427}, "Polygon": [{"X": 0.14305247366428375, "Y": 0.6114959120750427}, {"X": 0.2821424901485443, "Y": 0.6114959716796875}, {"X": 0.28214380145072937, "Y": 0.6242574453353882}, {"X": 0.14305385947227478, "Y": 0.6242573261260986}]}, "Id": "d3249859-ba05-4d3c-a072-04701ac70dc6"}, {"BlockType": "WORD", "Confidence": 99.17350769042969, "Text": "Charge", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1269756257534027, "Height": 0.014770295470952988, "Left": 0.2939199209213257, "Top": 0.6108127236366272}, "Polygon": [{"X": 0.2939199209213257, "Y": 0.6108127236366272}, {"X": 0.4208940863609314, "Y": 0.6108127236366272}, {"X": 0.4208955466747284, "Y": 0.6255829930305481}, {"X": 0.29392141103744507, "Y": 0.6255829334259033}]}, "Id": "45b1153e-32cc-4b80-86e5-9658aec27be5"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "$8.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10247338563203812, "Height": 0.017269406467676163, "Left": 0.7763200998306274, "Top": 0.6070967316627502}, "Polygon": [{"X": 0.7763200998306274, "Y": 0.6070967316627502}, {"X": 0.8787921071052551, "Y": 0.607096791267395}, {"X": 0.878793478012085, "Y": 0.6243661642074585}, {"X": 0.776321530342102, "Y": 0.6243661046028137}]}, "Id": "97e64f87-16d9-41dc-a213-df65a45fc205"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0894695296883583, "Height": 0.014490507543087006, "Left": 0.1441158652305603, "Top": 0.6322702169418335}, "Polygon": [{"X": 0.1441158652305603, "Y": 0.6322702169418335}, {"X": 0.23358388245105743, "Y": 0.6322702765464783}, {"X": 0.2335854023694992, "Y": 0.6467607021331787}, {"X": 0.14411742985248566, "Y": 0.6467606425285339}]}, "Id": "777f4c2b-b9a1-4552-813e-ba6b198e38ec"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "$99.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12457958608865738, "Height": 0.019586818292737007, "Left": 0.7548099756240845, "Top": 0.6282671093940735}, "Polygon": [{"X": 0.7548099756240845, "Y": 0.6282671093940735}, {"X": 0.8793879747390747, "Y": 0.6282671689987183}, {"X": 0.8793895244598389, "Y": 0.6478539109230042}, {"X": 0.7548116445541382, "Y": 0.6478537917137146}]}, "Id": "ea78c193-3ca2-4a45-b4a5-1eb7dbd8151e"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "Payment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16054531931877136, "Height": 0.015378644689917564, "Left": 0.14344261586666107, "Top": 0.6547008156776428}, "Polygon": [{"X": 0.14344261586666107, "Y": 0.6547008156776428}, {"X": 0.3039863705635071, "Y": 0.6547009944915771}, {"X": 0.30398792028427124, "Y": 0.6700794696807861}, {"X": 0.14344428479671478, "Y": 0.670079231262207}]}, "Id": "a0c09f12-5eff-492d-8c82-4ff50ca15636"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10958477109670639, "Height": 0.018479187041521072, "Left": 0.7728049159049988, "Top": 0.6508646011352539}, "Polygon": [{"X": 0.7728049159049988, "Y": 0.6508646011352539}, {"X": 0.8823882341384888, "Y": 0.6508647799491882}, {"X": 0.8823896646499634, "Y": 0.6693438291549683}, {"X": 0.7728064656257629, "Y": 0.6693436503410339}]}, "Id": "0abdd421-bddd-4db6-bf0a-5edc015ce7fe"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Authorization", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.23951470851898193, "Height": 0.013458259403705597, "Left": 0.13951319456100464, "Top": 0.6752681136131287}, "Polygon": [{"X": 0.13951319456100464, "Y": 0.6752681136131287}, {"X": 0.3790265619754791, "Y": 0.6752685308456421}, {"X": 0.3790279030799866, "Y": 0.6887263655662537}, {"X": 0.13951465487480164, "Y": 0.6887259483337402}]}, "Id": "dfaccade-6812-48d7-8e86-65c695b5a0b9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "12007196", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.177385151386261, "Height": 0.015971913933753967, "Left": 0.7045323252677917, "Top": 0.6715840101242065}, "Polygon": [{"X": 0.7045323252677917, "Y": 0.6715840101242065}, {"X": 0.8819162249565125, "Y": 0.6715843081474304}, {"X": 0.8819174766540527, "Y": 0.6875559687614441}, {"X": 0.7045337557792664, "Y": 0.6875556111335754}]}, "Id": "24144519-f6dd-4265-9169-0ef52ebebda8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Driver", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11279182881116867, "Height": 0.01331867091357708, "Left": 0.1428855061531067, "Top": 0.6973837018013}, "Polygon": [{"X": 0.1428855061531067, "Y": 0.6973837018013}, {"X": 0.25567594170570374, "Y": 0.6973839402198792}, {"X": 0.25567731261253357, "Y": 0.7107023596763611}, {"X": 0.1428869366645813, "Y": 0.710702121257782}]}, "Id": "948e474d-4711-417b-bbdd-da2e99b2764a"}, {"BlockType": "WORD", "Confidence": 99.45731353759766, "Text": "RRR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08374407887458801, "Height": 0.016965564340353012, "Left": 0.803195059299469, "Top": 0.6927899718284607}, "Polygon": [{"X": 0.803195059299469, "Y": 0.6927899718284607}, {"X": 0.8869377970695496, "Y": 0.692790150642395}, {"X": 0.8869391083717346, "Y": 0.709755539894104}, {"X": 0.8031964302062988, "Y": 0.7097553610801697}]}, "Id": "4df1e3d1-9700-4384-b980-c2f5d1909d30"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "Driver's", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14111576974391937, "Height": 0.013166619464755058, "Left": 0.3199102282524109, "Top": 0.736911416053772}, "Polygon": [{"X": 0.3199102282524109, "Y": 0.736911416053772}, {"X": 0.4610247313976288, "Y": 0.7369118332862854}, {"X": 0.46102598309516907, "Y": 0.750078022480011}, {"X": 0.31991156935691833, "Y": 0.7500776052474976}]}, "Id": "7666206c-ecb0-4aaf-9758-f58b699add2c"}, {"BlockType": "WORD", "Confidence": 99.5224838256836, "Text": "Signature", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17171697318553925, "Height": 0.015891581773757935, "Left": 0.47413748502731323, "Top": 0.7359166741371155}, "Polygon": [{"X": 0.47413748502731323, "Y": 0.7359166741371155}, {"X": 0.6458530426025391, "Y": 0.7359171509742737}, {"X": 0.6458544731140137, "Y": 0.751808226108551}, {"X": 0.474139004945755, "Y": 0.751807689666748}]}, "Id": "3458041b-97a0-4dfc-a743-a9ca6fd28a4e"}, {"BlockType": "WORD", "Confidence": 99.6021957397461, "Text": "Supervisor's", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22632354497909546, "Height": 0.016284354031085968, "Left": 0.28128552436828613, "Top": 0.8315232992172241}, "Polygon": [{"X": 0.28128552436828613, "Y": 0.8315232992172241}, {"X": 0.5076075196266174, "Y": 0.8315243721008301}, {"X": 0.5076090693473816, "Y": 0.8478076457977295}, {"X": 0.28128719329833984, "Y": 0.847806453704834}]}, "Id": "5c121dea-9b58-4c84-be28-e9c0e4294898"}, {"BlockType": "WORD", "Confidence": 99.52168273925781, "Text": "Signature", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17173366248607635, "Height": 0.015085752122104168, "Left": 0.5211144685745239, "Top": 0.8308393359184265}, "Polygon": [{"X": 0.5211144685745239, "Y": 0.8308393359184265}, {"X": 0.6928467750549316, "Y": 0.8308401703834534}, {"X": 0.6928480863571167, "Y": 0.8459250926971436}, {"X": 0.5211158394813538, "Y": 0.8459241986274719}]}, "Id": "2f9dc12c-5b02-4331-80b7-ffa7e0b1fdfe"}, {"BlockType": "WORD", "Confidence": 70.44145202636719, "Text": "29", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.25587156414985657, "Height": 0.03180857002735138, "Left": 0.3889331519603729, "Top": 0.8613988757133484}, "Polygon": [{"X": 0.3889331519603729, "Y": 0.8613988757133484}, {"X": 0.644801914691925, "Y": 0.861400306224823}, {"X": 0.6448047161102295, "Y": 0.8932074308395386}, {"X": 0.38893628120422363, "Y": 0.8932058811187744}]}, "Id": "66c5a19d-bba4-4c13-8099-15f4f16d7377"}, {"BlockType": "WORD", "Confidence": 98.142333984375, "Text": "07/26/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1928347945213318, "Height": 0.011245655827224255, "Left": 0.3676324188709259, "Top": 0.9589479565620422}, "Polygon": [{"X": 0.3676324188709259, "Y": 0.9589479565620422}, {"X": 0.5604661703109741, "Y": 0.9589494466781616}, {"X": 0.5604672431945801, "Y": 0.97019362449646}, {"X": 0.36763355135917664, "Y": 0.9701921343803406}]}, "Id": "575d0636-9820-4efc-98e4-9411f5a8d56f"}, {"BlockType": "WORD", "Confidence": 94.70962524414062, "Text": "00:35", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09949705004692078, "Height": 0.011365711688995361, "Left": 0.5679749250411987, "Top": 0.9582560062408447}, "Polygon": [{"X": 0.5679749250411987, "Y": 0.9582560062408447}, {"X": 0.6674709320068359, "Y": 0.9582567811012268}, {"X": 0.6674719452857971, "Y": 0.9696217179298401}, {"X": 0.5679759383201599, "Y": 0.969620943069458}]}, "Id": "946d5e98-d5cf-4480-9de9-6a40f9bb9f46"}, {"BlockType": "WORD", "Confidence": 30.004981994628906, "Text": "www.merittogistics.com", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.4804471433162689, "Height": 0.012499780394136906, "Left": 0.27515482902526855, "Top": 0.9721404910087585}, "Polygon": [{"X": 0.27515482902526855, "Y": 0.9721404910087585}, {"X": 0.7556009292602539, "Y": 0.9721442461013794}, {"X": 0.7556019425392151, "Y": 0.9846402406692505}, {"X": 0.2751561105251312, "Y": 0.9846363663673401}]}, "Id": "cd893ffc-230d-415a-9e86-cb33e2dcc76f"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "aa24527a-6407-4c50-95e3-b7bcce6a0133", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "aa24527a-6407-4c50-95e3-b7bcce6a0133", "content-type": "application/x-amz-json-1.1", "content-length": "66435", "date": "Thu, 18 Sep 2025 10:33:55 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "8d9b82a9-b008-4ae6-8e51-1c25f314cc6d", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 10:33:59 GMT", "content-type": "application/json", "content-length": "2222", "connection": "keep-alive", "x-amzn-requestid": "8d9b82a9-b008-4ae6-8e51-1c25f314cc6d"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. Only page1 provided. We need to determine doc_type. Let's analyze text. It has \"MERIT LOGISTIC\" header. Contains PO #, Work Order Charge, Service Charge, total. It includes \"Driver's Signature\" and \"Supervisor's Signature\". Looks like a driver log or a service receipt? Could be a \"lumper receipt\"? There's a \"Work Order Charge\" and \"Service Charge\". The document name might be \"Work Order\". It might be a \"lumper receipt\" (receipt for loading/unloading labor). The doc_type definitions: lumper_receipt includes PO, Door number, Date-Time, initial pallets, finished pallets, carrier, service charge, total amount. This page includes PO #, Door #, Date/Time (7/25/2025 8:39:00 PM). There is Service Charge $8.00. It doesn't include pallet numbers. But it includes Work Order Charge. It might still be a lumper receipt. The definition for invoice: Bill issued by a carrier for goods being transported; includes invoice header. This page does not show \"Invoice\" heading. It looks more like a \"Work Order\" or \"Driver Log\".\n\nBut there is no \"log\" or \"driver log\" heading. It has \"Driver's Signature\" and \"Supervisor's Signature\". That could be part of a \"driver log\" but not necessarily. Let's search for \"Log\". The doc type \"log\" includes driver log, hours of service, etc. This page includes \"Work Order Charge\" and \"Service Charge\" which might be charges for the driver. That could be a \"lumper receipt\".\n\nLumper receipts often have PO, door number, date/time, initial pallets, finished pallets, service charge. Yes.\n\nHence choose doc_type: lumper_receipt.\n\nWe need to produce tool call classify_logistics_doc_type with documents array: page_no 1, doc_type \"lumper_receipt\".\n\nWe must output exactly one object. Let's produce that."}}}, {"toolUse": {"toolUseId": "tooluse_UhPoZiUVRkOrG-hQdUpUqQ", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2354, "outputTokens": 454, "totalTokens": 2808}, "metrics": {"latencyMs": 1897}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/b9afb946_21_lumper_receipt_11.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nMERIT\nLOGISTIC\n(949) 481-0685\n<PERSON><PERSON> (ALDFRE), <PERSON>, MD\nTax Id\n*********\nWO #\nALDFRE9734307252025203958\nTime\n7/25/2025 8:39:00 PM\nPO #\n7507418921\nTruck #\n101\nTrailer #\n()--288\nDoor #\n34\nClient's Dept\nUnipading (GRP)\nProduct\nGM\nVendor\nPREMIUM WATERS INC\nCarrier\n3A TRANSPORT\nBill To\n3A TRANSPORT\nQTY < Case >\n1\nActivity\n$91.00\nWork Order Charge\n$91.00\nService Charge\n$8.00\nTotal\n$99.00\nPayment\nRelay\nAuthorization\n12007196\nDriver\nRRR\nDriver's Signature\nSupervisor's Signature\n29\n07/26/2025 00:35\nwww.merittogistics.com\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}