{"classification_result": {"documents": [{"page_no": 1, "doc_type": "other"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "d2f21de7-3a30-4dce-b3fc-535eae2b312a", "Relationships": [{"Type": "CHILD", "Ids": ["10851812-8ddc-4e7e-b43a-1f30b26775e3", "39765b55-9ab0-4de9-a1d8-8657421a0477", "27140b95-67fb-4082-843c-675861a0f306", "7bc25957-c625-471a-8812-ad81235168df", "fe1f5025-1110-42ae-8b7e-1c6aeecf844d", "83745190-eda9-46f9-a67e-730cf9c9572b", "b4213282-2494-4870-ab39-d4f2ae64bc1a", "cede3d81-a9f4-440a-85ff-35340a913e1c", "f7ec17ef-5447-4962-ac62-f7a8aef08610", "52e78392-d06b-44e6-9a9a-95522edbee77", "cccf202c-da98-46dc-9022-9cc6999ae4df", "1d5cc20d-b440-4bd3-acf5-91ee38507c8d", "459f49b1-549e-4102-97da-476fa16279fe", "9d83052d-3645-4af8-bc99-fcb247d11a04", "65fe1de4-91bc-4339-bcd4-6e800611733a", "639c915f-8ddc-481a-8932-4698283d7a2e", "27180d21-f84d-42c0-a929-f8533e0462e1", "88ecbc70-164c-4190-b723-b56ff2749d8d", "a1eba152-aaa8-49cc-b9ee-486e70fbb783", "8f07d0e9-2b1b-4c14-9c0e-c2fe34a86ae0", "560f1354-1249-4ecb-ae05-880e1ada233b", "2333d794-23d4-4ee0-a6bc-6ce7640c7114", "7653e92b-cf8c-4d14-8e22-9f7a0a682834", "58bc5f35-a1c7-46b8-a030-f2485b7560e4", "cf80fe1d-7d0e-43b5-aa95-14b0bca18154", "1781e611-1533-4f49-8105-bc7222de2264", "4f0c7dc4-ad7d-4a97-8738-d89ce19bafdf", "229b684a-7404-4c5a-ac5c-e92f04e8ca4a", "cce31f67-d2f4-44f7-8cbf-8bfeb61d72d7", "143b9d36-2e69-44f2-865d-6d2b2d6466b8", "1778e820-c787-4ad2-b006-e03691a50c57", "7dd45cb7-996b-4298-8788-8ed725936aec", "e1a9f7bb-fd41-4cd8-ab44-77c1cfb4a9d4", "f3c8f436-31f1-43fa-a103-75ce647a5f71", "6b583293-1ac6-43ec-bc57-346e3979531c", "9f58b9df-ef24-4931-b609-afb1499d5600", "50fbb4ba-e995-4f74-9f4c-82e101e34e52", "cbdf2962-0e61-4fde-88a9-55079ee929f8"]}]}, {"BlockType": "LINE", "Confidence": 99.80677795410156, "Text": "FHI, LLC", "Geometry": {"BoundingBox": {"Width": 0.11965914815664291, "Height": 0.013422587886452675, "Left": 0.440313458442688, "Top": 0.043452490121126175}, "Polygon": [{"X": 0.4403136670589447, "Y": 0.043452490121126175}, {"X": 0.5599725842475891, "Y": 0.04345250874757767}, {"X": 0.55997234582901, "Y": 0.0568750761449337}, {"X": 0.440313458442688, "Y": 0.056875042617321014}]}, "Id": "10851812-8ddc-4e7e-b43a-1f30b26775e3", "Relationships": [{"Type": "CHILD", "Ids": ["d5410ea3-44e6-42a0-9168-4d6f1efb594c", "a1d17b3d-31fc-4471-bf01-9c50a11538de"]}]}, {"BlockType": "LINE", "Confidence": 99.96419525146484, "Text": "Fed. ID #56-2174318", "Geometry": {"BoundingBox": {"Width": 0.27822768688201904, "Height": 0.011341417208313942, "Left": 0.36109742522239685, "Top": 0.06315767019987106}, "Polygon": [{"X": 0.36109763383865356, "Y": 0.06315767019987106}, {"X": 0.6393251419067383, "Y": 0.06315776705741882}, {"X": 0.6393249034881592, "Y": 0.07449908554553986}, {"X": 0.36109742522239685, "Y": 0.07449895888566971}]}, "Id": "39765b55-9ab0-4de9-a1d8-8657421a0477", "Relationships": [{"Type": "CHILD", "Ids": ["aca53fd7-0dc2-4399-bc07-97c0bfc3564a", "3330d904-f858-442f-bceb-8149973c55ee", "7cd23631-c066-4471-9fe2-e81d554642b4"]}]}, {"BlockType": "LINE", "Confidence": 99.90101623535156, "Text": "P.O. Box 546", "Geometry": {"BoundingBox": {"Width": 0.17865242063999176, "Height": 0.01143514271825552, "Left": 0.4111614227294922, "Top": 0.08281977474689484}, "Polygon": [{"X": 0.4111616015434265, "Y": 0.08281977474689484}, {"X": 0.5898138284683228, "Y": 0.0828198716044426}, {"X": 0.5898136496543884, "Y": 0.09425491839647293}, {"X": 0.4111614227294922, "Y": 0.09425479918718338}]}, "Id": "27140b95-67fb-4082-843c-675861a0f306", "Relationships": [{"Type": "CHILD", "Ids": ["14cf1045-3240-45ea-a5e8-3be3bdd2bb32", "d8079a18-c964-4b6c-8806-90b14ccc14f3", "0fe3864a-0e58-44fa-8a19-b89328eab77f"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Fuquay Varina, NC 27526", "Geometry": {"BoundingBox": {"Width": 0.3581905961036682, "Height": 0.0142056317999959, "Left": 0.32114604115486145, "Top": 0.1027875542640686}, "Polygon": [{"X": 0.32114627957344055, "Y": 0.1027875542640686}, {"X": 0.6793366074562073, "Y": 0.10278782993555069}, {"X": 0.679336428642273, "Y": 0.11699318885803223}, {"X": 0.32114604115486145, "Y": 0.11699286848306656}]}, "Id": "7bc25957-c625-471a-8812-ad81235168df", "Relationships": [{"Type": "CHILD", "Ids": ["6813dd7b-a150-4739-a52e-7556ab746e1c", "3e6fe4e4-493b-414a-99fc-7ca0f1b1595e", "3206b054-7a77-4a82-bed1-1eb7089fca14", "72b41310-3133-464a-911c-8c91c145f3c2"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "919-552-3157", "Geometry": {"BoundingBox": {"Width": 0.183644637465477, "Height": 0.011433270759880543, "Left": 0.4082373082637787, "Top": 0.12250351905822754}, "Polygon": [{"X": 0.408237487077713, "Y": 0.12250351905822754}, {"X": 0.5918819308280945, "Y": 0.12250369787216187}, {"X": 0.5918817520141602, "Y": 0.1339367926120758}, {"X": 0.4082373082637787, "Y": 0.13393659889698029}]}, "Id": "fe1f5025-1110-42ae-8b7e-1c6aeecf844d", "Relationships": [{"Type": "CHILD", "Ids": ["0382cd24-4ee3-4b01-9633-7a6621a5a5fb"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.06025361269712448, "Height": 0.01126458216458559, "Left": 0.06799536943435669, "Top": 0.16588950157165527}, "Polygon": [{"X": 0.06799556314945221, "Y": 0.16588950157165527}, {"X": 0.12824898958206177, "Y": 0.16588957607746124}, {"X": 0.12824879586696625, "Y": 0.177154079079628}, {"X": 0.06799536943435669, "Y": 0.17715398967266083}]}, "Id": "83745190-eda9-46f9-a67e-730cf9c9572b", "Relationships": [{"Type": "CHILD", "Ids": ["2c444d03-7d3a-436e-864c-ed3a745a08ec"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "08/11/25", "Geometry": {"BoundingBox": {"Width": 0.11387137323617935, "Height": 0.011411534622311592, "Left": 0.6250819563865662, "Top": 0.1658749282360077}, "Polygon": [{"X": 0.6250821352005005, "Y": 0.1658749282360077}, {"X": 0.7389532923698425, "Y": 0.16587509214878082}, {"X": 0.7389531135559082, "Y": 0.17728646099567413}, {"X": 0.6250819563865662, "Y": 0.1772862821817398}]}, "Id": "b4213282-2494-4870-ab39-d4f2ae64bc1a", "Relationships": [{"Type": "CHILD", "Ids": ["5d20efa6-9597-4754-8cd5-a6704c01817b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "RECEIPT", "Geometry": {"BoundingBox": {"Width": 0.12763401865959167, "Height": 0.01150046568363905, "Left": 0.43700599670410156, "Top": 0.20938195288181305}, "Polygon": [{"X": 0.4370061755180359, "Y": 0.20938195288181305}, {"X": 0.5646399855613708, "Y": 0.20938219130039215}, {"X": 0.5646398067474365, "Y": 0.22088241577148438}, {"X": 0.43700599670410156, "Y": 0.22088216245174408}]}, "Id": "cede3d81-a9f4-440a-85ff-35340a913e1c", "Relationships": [{"Type": "CHILD", "Ids": ["94475ee0-60d9-4434-9311-fe4ee78292cb"]}]}, {"BlockType": "LINE", "Confidence": 99.6484375, "Text": "Receipt #:", "Geometry": {"BoundingBox": {"Width": 0.1295841634273529, "Height": 0.014019904658198357, "Left": 0.06801876425743103, "Top": 0.2332058995962143}, "Polygon": [{"X": 0.06801900267601013, "Y": 0.2332058995962143}, {"X": 0.19760292768478394, "Y": 0.23320616781711578}, {"X": 0.19760268926620483, "Y": 0.2472258061170578}, {"X": 0.06801876425743103, "Y": 0.24722550809383392}]}, "Id": "f7ec17ef-5447-4962-ac62-f7a8aef08610", "Relationships": [{"Type": "CHILD", "Ids": ["6bc7b6a7-4107-4c11-bee4-3807bbbf9ae7", "072a8f3a-c204-4143-b524-3e6e499e6019"]}]}, {"BlockType": "LINE", "Confidence": 97.40911865234375, "Text": "RAI250811065739", "Geometry": {"BoundingBox": {"Width": 0.24437358975410461, "Height": 0.011593890376389027, "Left": 0.62575364112854, "Top": 0.23321114480495453}, "Polygon": [{"X": 0.6257538199424744, "Y": 0.23321114480495453}, {"X": 0.8701272010803223, "Y": 0.23321165144443512}, {"X": 0.8701270222663879, "Y": 0.24480503797531128}, {"X": 0.62575364112854, "Y": 0.2448045015335083}]}, "Id": "52e78392-d06b-44e6-9a9a-95522edbee77", "Relationships": [{"Type": "CHILD", "Ids": ["49f07eb0-53e6-45fc-bb94-79807921f345"]}]}, {"BlockType": "LINE", "Confidence": 99.89098358154297, "Text": "FACILITY:", "Geometry": {"BoundingBox": {"Width": 0.13432680070400238, "Height": 0.011322923935949802, "Left": 0.06801436841487885, "Top": 0.2610757052898407}, "Polygon": [{"X": 0.06801456212997437, "Y": 0.2610757052898407}, {"X": 0.20234116911888123, "Y": 0.2610760033130646}, {"X": 0.2023409754037857, "Y": 0.27239862084388733}, {"X": 0.06801436841487885, "Y": 0.27239829301834106}]}, "Id": "cccf202c-da98-46dc-9022-9cc6999ae4df", "Relationships": [{"Type": "CHILD", "Ids": ["84f0e412-1f71-48fb-9d85-5426d107bab5"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Ocala", "Geometry": {"BoundingBox": {"Width": 0.07582534104585648, "Height": 0.011631847359240055, "Left": 0.6253350973129272, "Top": 0.2609020471572876}, "Polygon": [{"X": 0.6253352761268616, "Y": 0.2609020471572876}, {"X": 0.7011604309082031, "Y": 0.2609022259712219}, {"X": 0.7011602520942688, "Y": 0.2725338935852051}, {"X": 0.6253350973129272, "Y": 0.27253368496894836}]}, "Id": "1d5cc20d-b440-4bd3-acf5-91ee38507c8d", "Relationships": [{"Type": "CHILD", "Ids": ["ad8ddbbe-e510-4cdf-b82f-435deb343cf5"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Department:", "Geometry": {"BoundingBox": {"Width": 0.1587502658367157, "Height": 0.013907834887504578, "Left": 0.06806491315364838, "Top": 0.28887009620666504}, "Polygon": [{"X": 0.06806515157222748, "Y": 0.28887009620666504}, {"X": 0.22681517899036407, "Y": 0.28887054324150085}, {"X": 0.22681494057178497, "Y": 0.3027779459953308}, {"X": 0.06806491315364838, "Y": 0.302777498960495}]}, "Id": "459f49b1-549e-4102-97da-476fa16279fe", "Relationships": [{"Type": "CHILD", "Ids": ["ea6f7ca1-e4eb-4d5a-9b05-e51e0ea81298"]}]}, {"BlockType": "LINE", "Confidence": 99.76164245605469, "Text": "GRO-Ocala", "Geometry": {"BoundingBox": {"Width": 0.15273724496364594, "Height": 0.011507879942655563, "Left": 0.6255061030387878, "Top": 0.2886818051338196}, "Polygon": [{"X": 0.6255062818527222, "Y": 0.2886818051338196}, {"X": 0.778243362903595, "Y": 0.288682222366333}, {"X": 0.7782431840896606, "Y": 0.30018970370292664}, {"X": 0.6255061030387878, "Y": 0.3001892864704132}]}, "Id": "9d83052d-3645-4af8-bc99-fcb247d11a04", "Relationships": [{"Type": "CHILD", "Ids": ["fe45eaf2-cec8-4dfb-adc7-803ef9069003"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Door:", "Geometry": {"BoundingBox": {"Width": 0.06922127306461334, "Height": 0.011067783460021019, "Left": 0.06801866739988327, "Top": 0.31671464443206787}, "Polygon": [{"X": 0.0680188536643982, "Y": 0.31671464443206787}, {"X": 0.1372399479150772, "Y": 0.3167148530483246}, {"X": 0.1372397541999817, "Y": 0.32778242230415344}, {"X": 0.06801866739988327, "Y": 0.32778221368789673}]}, "Id": "65fe1de4-91bc-4339-bcd4-6e800611733a", "Relationships": [{"Type": "CHILD", "Ids": ["8272e784-86b3-407c-a28f-d8155e6f98ec"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "16", "Geometry": {"BoundingBox": {"Width": 0.030719656497240067, "Height": 0.011005452834069729, "Left": 0.6267974376678467, "Top": 0.3169155418872833}, "Polygon": [{"X": 0.626797616481781, "Y": 0.3169155418872833}, {"X": 0.6575170755386353, "Y": 0.3169156312942505}, {"X": 0.6575168967247009, "Y": 0.3279210031032562}, {"X": 0.6267974376678467, "Y": 0.32792091369628906}]}, "Id": "639c915f-8ddc-481a-8932-4698283d7a2e", "Relationships": [{"Type": "CHILD", "Ids": ["0f5c8449-7a78-4f86-9408-4ef885a56eba"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Shift:", "Geometry": {"BoundingBox": {"Width": 0.06394462287425995, "Height": 0.011428557336330414, "Left": 0.06792221963405609, "Top": 0.3442031443119049}, "Polygon": [{"X": 0.06792241334915161, "Y": 0.3442031443119049}, {"X": 0.13186684250831604, "Y": 0.3442033529281616}, {"X": 0.13186664879322052, "Y": 0.3556317090988159}, {"X": 0.06792221963405609, "Y": 0.3556315004825592}]}, "Id": "27180d21-f84d-42c0-a929-f8533e0462e1", "Relationships": [{"Type": "CHILD", "Ids": ["f33ebc39-435e-4ed2-a798-96e862614bea"]}]}, {"BlockType": "LINE", "Confidence": 99.15995788574219, "Text": "1st Shift", "Geometry": {"BoundingBox": {"Width": 0.10468015819787979, "Height": 0.01163840014487505, "Left": 0.6268889904022217, "Top": 0.34409958124160767}, "Polygon": [{"X": 0.6268892288208008, "Y": 0.34409958124160767}, {"X": 0.7315691709518433, "Y": 0.34409990906715393}, {"X": 0.7315689921379089, "Y": 0.35573798418045044}, {"X": 0.6268889904022217, "Y": 0.3557376265525818}]}, "Id": "88ecbc70-164c-4190-b723-b56ff2749d8d", "Relationships": [{"Type": "CHILD", "Ids": ["491bfb32-1954-4c9a-8410-3080f0a20fe9", "df955e5b-81cc-44c2-86b6-ed833c0b8740"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "LOAD INFO:", "Geometry": {"BoundingBox": {"Width": 0.1645379513502121, "Height": 0.011415382847189903, "Left": 0.06751324981451035, "Top": 0.3917284905910492}, "Polygon": [{"X": 0.06751345098018646, "Y": 0.3917284905910492}, {"X": 0.23205120861530304, "Y": 0.39172911643981934}, {"X": 0.23205101490020752, "Y": 0.40314388275146484}, {"X": 0.06751324981451035, "Y": 0.4031432569026947}]}, "Id": "a1eba152-aaa8-49cc-b9ee-486e70fbb783", "Relationships": [{"Type": "CHILD", "Ids": ["a1ee823d-2a36-40c0-9850-28569062b313", "d5a1ffbe-9ae7-4ce7-803b-9dac6dccedc3"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Carrier:", "Geometry": {"BoundingBox": {"Width": 0.09611997753381729, "Height": 0.011409152299165726, "Left": 0.06708154082298279, "Top": 0.4195166230201721}, "Polygon": [{"X": 0.06708173453807831, "Y": 0.4195166230201721}, {"X": 0.16320151090621948, "Y": 0.41951701045036316}, {"X": 0.16320131719112396, "Y": 0.43092575669288635}, {"X": 0.06708154082298279, "Y": 0.4309253692626953}]}, "Id": "8f07d0e9-2b1b-4c14-9c0e-c2fe34a86ae0", "Relationships": [{"Type": "CHILD", "Ids": ["33296e4a-66be-453a-9f71-5798954f8893"]}]}, {"BlockType": "LINE", "Confidence": 99.931640625, "Text": "59 north", "Geometry": {"BoundingBox": {"Width": 0.10595554113388062, "Height": 0.01127858180552721, "Left": 0.6253074407577515, "Top": 0.4197206497192383}, "Polygon": [{"X": 0.6253076791763306, "Y": 0.4197206497192383}, {"X": 0.7312629818916321, "Y": 0.4197210669517517}, {"X": 0.7312628030776978, "Y": 0.430999219417572}, {"X": 0.6253074407577515, "Y": 0.4309988021850586}]}, "Id": "560f1354-1249-4ecb-ae05-880e1ada233b", "Relationships": [{"Type": "CHILD", "Ids": ["3c9d026b-b97a-4f08-9caa-3b8bce97e846", "856f2a10-8667-45b2-9f93-5617f86396fb"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "Vendor:", "Geometry": {"BoundingBox": {"Width": 0.10209459811449051, "Height": 0.01136750914156437, "Left": 0.06617559492588043, "Top": 0.4472859501838684}, "Polygon": [{"X": 0.06617578864097595, "Y": 0.4472859501838684}, {"X": 0.16827018558979034, "Y": 0.44728636741638184}, {"X": 0.16826999187469482, "Y": 0.45865345001220703}, {"X": 0.06617559492588043, "Y": 0.4586530029773712}]}, "Id": "2333d794-23d4-4ee0-a6bc-6ce7640c7114", "Relationships": [{"Type": "CHILD", "Ids": ["a0f89578-96e8-4878-bad1-c40f6ca006d5"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Amercare Royal", "Geometry": {"BoundingBox": {"Width": 0.21078410744667053, "Height": 0.014070700854063034, "Left": 0.6242707371711731, "Top": 0.4472483694553375}, "Polygon": [{"X": 0.6242709755897522, "Y": 0.4472483694553375}, {"X": 0.8350548148155212, "Y": 0.44724926352500916}, {"X": 0.8350545763969421, "Y": 0.46131905913352966}, {"X": 0.6242707371711731, "Y": 0.46131813526153564}]}, "Id": "7653e92b-cf8c-4d14-8e22-9f7a0a682834", "Relationships": [{"Type": "CHILD", "Ids": ["7bb08841-efc3-4f67-8573-0b3303dcd0f2", "01f17057-7cbc-442f-b89c-3f4cc3918a7c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Truck", "Geometry": {"BoundingBox": {"Width": 0.0743144154548645, "Height": 0.011338412761688232, "Left": 0.06626966595649719, "Top": 0.475087434053421}, "Polygon": [{"X": 0.06626986712217331, "Y": 0.475087434053421}, {"X": 0.1405840814113617, "Y": 0.4750877618789673}, {"X": 0.14058388769626617, "Y": 0.48642584681510925}, {"X": 0.06626966595649719, "Y": 0.4864254891872406}]}, "Id": "58bc5f35-a1c7-46b8-a030-f2485b7560e4", "Relationships": [{"Type": "CHILD", "Ids": ["3bf0567a-3d0c-40f5-b513-862bbd2ce6bd"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "01", "Geometry": {"BoundingBox": {"Width": 0.028749730437994003, "Height": 0.011069179512560368, "Left": 0.6250150799751282, "Top": 0.47542041540145874}, "Polygon": [{"X": 0.6250152587890625, "Y": 0.47542041540145874}, {"X": 0.6537647843360901, "Y": 0.4754205644130707}, {"X": 0.6537646055221558, "Y": 0.48648959398269653}, {"X": 0.6250150799751282, "Y": 0.486489474773407}]}, "Id": "cf80fe1d-7d0e-43b5-aa95-14b0bca18154", "Relationships": [{"Type": "CHILD", "Ids": ["4886c739-f6c1-44aa-aabe-b2d4d0c39989"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Trailer", "Geometry": {"BoundingBox": {"Width": 0.08388730138540268, "Height": 0.011210412718355656, "Left": 0.06640441715717316, "Top": 0.5029498338699341}, "Polygon": [{"X": 0.06640461087226868, "Y": 0.5029498338699341}, {"X": 0.15029171109199524, "Y": 0.5029502511024475}, {"X": 0.15029151737689972, "Y": 0.5141602754592896}, {"X": 0.06640441715717316, "Y": 0.5141598582267761}]}, "Id": "1781e611-1533-4f49-8105-bc7222de2264", "Relationships": [{"Type": "CHILD", "Ids": ["3ca7db84-b889-439a-a929-bf8ce4800b62"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "5250234", "Geometry": {"BoundingBox": {"Width": 0.11469906568527222, "Height": 0.011033291928470135, "Left": 0.6249483823776245, "Top": 0.5031672120094299}, "Polygon": [{"X": 0.6249485611915588, "Y": 0.5031672120094299}, {"X": 0.7396474480628967, "Y": 0.5031677484512329}, {"X": 0.7396472096443176, "Y": 0.5142005085945129}, {"X": 0.6249483823776245, "Y": 0.5141999125480652}]}, "Id": "4f0c7dc4-ad7d-4a97-8738-d89ce19bafdf", "Relationships": [{"Type": "CHILD", "Ids": ["8f570177-b5c7-4241-bc19-9a9d4c43c17a"]}]}, {"BlockType": "LINE", "Confidence": 99.97518920898438, "Text": "Amount Due", "Geometry": {"BoundingBox": {"Width": 0.16375760734081268, "Height": 0.011330433189868927, "Left": 0.06595931947231293, "Top": 0.5503145456314087}, "Polygon": [{"X": 0.06595951318740845, "Y": 0.5503145456314087}, {"X": 0.2297169268131256, "Y": 0.5503154397010803}, {"X": 0.2297167330980301, "Y": 0.561644971370697}, {"X": 0.06595931947231293, "Y": 0.5616440773010254}]}, "Id": "229b684a-7404-4c5a-ac5c-e92f04e8ca4a", "Relationships": [{"Type": "CHILD", "Ids": ["d007079a-fff5-4a8d-8470-967a62fb77cb", "5af866cc-7b7c-46dc-bb8e-09897d81a1cf"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$180.00", "Geometry": {"BoundingBox": {"Width": 0.10625392198562622, "Height": 0.01354591641575098, "Left": 0.6249967813491821, "Top": 0.549399197101593}, "Polygon": [{"X": 0.6249970197677612, "Y": 0.549399197101593}, {"X": 0.7312507033348083, "Y": 0.549399733543396}, {"X": 0.7312504649162292, "Y": 0.5629451274871826}, {"X": 0.6249967813491821, "Y": 0.5629445314407349}]}, "Id": "cce31f67-d2f4-44f7-8cbf-8bfeb61d72d7", "Relationships": [{"Type": "CHILD", "Ids": ["3ab2960e-64e5-46b2-9982-85f5112055fd"]}]}, {"BlockType": "LINE", "Confidence": 99.91539764404297, "Text": "Convenience Fee", "Geometry": {"BoundingBox": {"Width": 0.2295553833246231, "Height": 0.011339168064296246, "Left": 0.06719447672367096, "Top": 0.5780941247940063}, "Polygon": [{"X": 0.06719467043876648, "Y": 0.5780941247940063}, {"X": 0.29674986004829407, "Y": 0.5780953764915466}, {"X": 0.29674965143203735, "Y": 0.5894333124160767}, {"X": 0.06719447672367096, "Y": 0.5894320011138916}]}, "Id": "143b9d36-2e69-44f2-865d-6d2b2d6466b8", "Relationships": [{"Type": "CHILD", "Ids": ["f5585b34-b597-4797-816b-dac3deda85a7", "cea6237b-7321-4167-99c2-288838703d10"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "$9.75", "Geometry": {"BoundingBox": {"Width": 0.07284560054540634, "Height": 0.013382857665419579, "Left": 0.6250905990600586, "Top": 0.5773332118988037}, "Polygon": [{"X": 0.6250907778739929, "Y": 0.5773332118988037}, {"X": 0.6979361772537231, "Y": 0.5773336291313171}, {"X": 0.697935938835144, "Y": 0.5907160639762878}, {"X": 0.6250905990600586, "Y": 0.5907156467437744}]}, "Id": "1778e820-c787-4ad2-b006-e03691a50c57", "Relationships": [{"Type": "CHILD", "Ids": ["c1956a96-c480-4b13-a992-78aabfe30c6e"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Electronic Payment", "Geometry": {"BoundingBox": {"Width": 0.2524924874305725, "Height": 0.013875389471650124, "Left": 0.06825123727321625, "Top": 0.6059488654136658}, "Polygon": [{"X": 0.06825147569179535, "Y": 0.6059488654136658}, {"X": 0.32074373960494995, "Y": 0.6059503555297852}, {"X": 0.32074350118637085, "Y": 0.6198242902755737}, {"X": 0.06825123727321625, "Y": 0.6198227405548096}]}, "Id": "7dd45cb7-996b-4298-8788-8ed725936aec", "Relationships": [{"Type": "CHILD", "Ids": ["9492ca66-4d92-4a62-9b1c-a218f9c42f06", "c2864d7c-6289-411a-97c8-b0e41f205cb7"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "$189.75", "Geometry": {"BoundingBox": {"Width": 0.10612017661333084, "Height": 0.013586929999291897, "Left": 0.6248946189880371, "Top": 0.6048755645751953}, "Polygon": [{"X": 0.6248947978019714, "Y": 0.6048755645751953}, {"X": 0.7310147881507874, "Y": 0.6048761606216431}, {"X": 0.7310145497322083, "Y": 0.6184625029563904}, {"X": 0.6248946189880371, "Y": 0.6184618473052979}]}, "Id": "e1a9f7bb-fd41-4cd8-ab44-77c1cfb4a9d4", "Relationships": [{"Type": "CHILD", "Ids": ["61ec763f-4799-4b27-acb5-4f0e5129b829"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "BALANCE DUE", "Geometry": {"BoundingBox": {"Width": 0.2055393010377884, "Height": 0.011493567377328873, "Left": 0.06729398667812347, "Top": 0.6530996561050415}, "Polygon": [{"X": 0.06729418784379959, "Y": 0.6530996561050415}, {"X": 0.27283328771591187, "Y": 0.6531009078025818}, {"X": 0.27283310890197754, "Y": 0.6645932197570801}, {"X": 0.06729398667812347, "Y": 0.6645918488502502}]}, "Id": "f3c8f436-31f1-43fa-a103-75ce647a5f71", "Relationships": [{"Type": "CHILD", "Ids": ["7f721427-44d1-4324-be85-ba3c287333af", "a717742a-2b76-4cfd-8381-b1cdb2af0c6d"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$0.00", "Geometry": {"BoundingBox": {"Width": 0.0735323503613472, "Height": 0.013299154117703438, "Left": 0.6249604821205139, "Top": 0.6525976061820984}, "Polygon": [{"X": 0.624960720539093, "Y": 0.6525976061820984}, {"X": 0.6984928250312805, "Y": 0.6525980234146118}, {"X": 0.6984926462173462, "Y": 0.6658967137336731}, {"X": 0.6249604821205139, "Y": 0.6658962368965149}]}, "Id": "6b583293-1ac6-43ec-bc57-346e3979531c", "Relationships": [{"Type": "CHILD", "Ids": ["39ef4896-a6e8-4388-b852-ae3021f0b6b1"]}]}, {"BlockType": "LINE", "Confidence": 99.9867172241211, "Text": "DESCRIPTION OF CHARGES", "Geometry": {"BoundingBox": {"Width": 0.4018067717552185, "Height": 0.012055947445333004, "Left": 0.06753572821617126, "Top": 0.7001860737800598}, "Polygon": [{"X": 0.06753593683242798, "Y": 0.7001860737800598}, {"X": 0.46934249997138977, "Y": 0.7001888155937195}, {"X": 0.46934229135513306, "Y": 0.712242066860199}, {"X": 0.06753572821617126, "Y": 0.7122392654418945}]}, "Id": "9f58b9df-ef24-4931-b609-afb1499d5600", "Relationships": [{"Type": "CHILD", "Ids": ["87f6fc5c-3c9a-402f-a93d-31fd6984811e", "2932d254-9cb0-4a88-a5eb-6911378cfd3d", "182d34e8-d322-49eb-84fb-770bb43f6b21"]}]}, {"BlockType": "LINE", "Confidence": 99.11322021484375, "Text": "PL 21-25 <PERSON>lts", "Geometry": {"BoundingBox": {"Width": 0.22329773008823395, "Height": 0.011831068433821201, "Left": 0.06800378113985062, "Top": 0.7281111478805542}, "Polygon": [{"X": 0.06800398230552673, "Y": 0.7281111478805542}, {"X": 0.29130151867866516, "Y": 0.7281127572059631}, {"X": 0.29130131006240845, "Y": 0.7399422526359558}, {"X": 0.06800378113985062, "Y": 0.7399406433105469}]}, "Id": "50fbb4ba-e995-4f74-9f4c-82e101e34e52", "Relationships": [{"Type": "CHILD", "Ids": ["24692107-d287-477d-8af8-ba4dc210f4b2", "3909dfa8-ae7f-47a8-964f-679da7a00f53", "c55fc80d-ca75-4bc0-9c78-d5ad79311470", "8a180fb5-f9c0-4faa-8ff6-c5615d338cdc"]}]}, {"BlockType": "LINE", "Confidence": 92.44499206542969, "Text": "4511576095(Amercare Royal)", "Geometry": {"BoundingBox": {"Width": 0.39425113797187805, "Height": 0.01432619895786047, "Left": 0.0651758685708046, "Top": 0.7761422991752625}, "Polygon": [{"X": 0.0651761144399643, "Y": 0.7761422991752625}, {"X": 0.45942699909210205, "Y": 0.7761452794075012}, {"X": 0.45942676067352295, "Y": 0.7904685139656067}, {"X": 0.0651758685708046, "Y": 0.7904654145240784}]}, "Id": "cbdf2962-0e61-4fde-88a9-55079ee929f8", "Relationships": [{"Type": "CHILD", "Ids": ["cc727c4e-cfae-41c7-a9c8-12c59ad2eb7e", "2579dae9-b8d2-4f51-a931-cba43963ef18"]}]}, {"BlockType": "WORD", "Confidence": 99.68192291259766, "Text": "FHI,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05395852029323578, "Height": 0.013422568328678608, "Left": 0.440313458442688, "Top": 0.043452490121126175}, "Polygon": [{"X": 0.4403136670589447, "Y": 0.043452490121126175}, {"X": 0.4942719638347626, "Y": 0.04345249757170677}, {"X": 0.49427172541618347, "Y": 0.05687505751848221}, {"X": 0.440313458442688, "Y": 0.056875042617321014}]}, "Id": "d5410ea3-44e6-42a0-9168-4d6f1efb594c"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05547850951552391, "Height": 0.011234242469072342, "Left": 0.5044940710067749, "Top": 0.04356548935174942}, "Polygon": [{"X": 0.5044942498207092, "Y": 0.04356548935174942}, {"X": 0.5599725842475891, "Y": 0.043565500527620316}, {"X": 0.5599724054336548, "Y": 0.05479973182082176}, {"X": 0.5044940710067749, "Y": 0.05479971691966057}]}, "Id": "a1d17b3d-31fc-4471-bf01-9c50a11538de"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Fed.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05874408408999443, "Height": 0.011293645016849041, "Left": 0.36109742522239685, "Top": 0.06315767019987106}, "Polygon": [{"X": 0.36109763383865356, "Y": 0.06315767019987106}, {"X": 0.4198414981365204, "Y": 0.06315769255161285}, {"X": 0.41984131932258606, "Y": 0.07445131242275238}, {"X": 0.36109742522239685, "Y": 0.07445129007101059}]}, "Id": "aca53fd7-0dc2-4399-bc07-97c0bfc3564a"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "ID", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028080228716135025, "Height": 0.010754511691629887, "Left": 0.4295829236507416, "Top": 0.06343037635087967}, "Polygon": [{"X": 0.4295831024646759, "Y": 0.06343037635087967}, {"X": 0.4576631486415863, "Y": 0.06343038380146027}, {"X": 0.457662969827652, "Y": 0.07418488711118698}, {"X": 0.4295829236507416, "Y": 0.07418487221002579}]}, "Id": "3330d904-f858-442f-bceb-8149973c55ee"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "#56-2174318", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1735733151435852, "Height": 0.01110165473073721, "Left": 0.4657517969608307, "Top": 0.06339742988348007}, "Polygon": [{"X": 0.465751975774765, "Y": 0.06339742988348007}, {"X": 0.6393251419067383, "Y": 0.06339749693870544}, {"X": 0.6393249034881592, "Y": 0.07449908554553986}, {"X": 0.4657517969608307, "Y": 0.07449900358915329}]}, "Id": "7cd23631-c066-4471-9fe2-e81d554642b4"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "P.O.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05687521770596504, "Height": 0.011414367705583572, "Left": 0.4111614227294922, "Top": 0.0828404650092125}, "Polygon": [{"X": 0.4111616015434265, "Y": 0.0828404650092125}, {"X": 0.46803662180900574, "Y": 0.08284049481153488}, {"X": 0.4680364429950714, "Y": 0.09425483644008636}, {"X": 0.4111614227294922, "Y": 0.09425479918718338}]}, "Id": "14cf1045-3240-45ea-a5e8-3be3bdd2bb32"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Box", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0545131154358387, "Height": 0.011321538127958775, "Left": 0.4781702756881714, "Top": 0.08281981199979782}, "Polygon": [{"X": 0.4781704843044281, "Y": 0.08281981199979782}, {"X": 0.5326834321022034, "Y": 0.08281984180212021}, {"X": 0.5326831936836243, "Y": 0.09414134919643402}, {"X": 0.4781702756881714, "Y": 0.09414131194353104}]}, "Id": "d8079a18-c964-4b6c-8806-90b14ccc14f3"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "546", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04879835993051529, "Height": 0.01134564820677042, "Left": 0.5410154461860657, "Top": 0.08289141952991486}, "Polygon": [{"X": 0.5410156846046448, "Y": 0.08289141952991486}, {"X": 0.5898138284683228, "Y": 0.08289144933223724}, {"X": 0.5898136496543884, "Y": 0.0942370668053627}, {"X": 0.5410154461860657, "Y": 0.09423703700304031}]}, "Id": "0fe3864a-0e58-44fa-8a19-b89328eab77f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Fuquay", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1038079485297203, "Height": 0.014136921614408493, "Left": 0.32114604115486145, "Top": 0.10285604000091553}, "Polygon": [{"X": 0.32114627957344055, "Y": 0.10285604000091553}, {"X": 0.42495399713516235, "Y": 0.1028561145067215}, {"X": 0.42495375871658325, "Y": 0.11699295789003372}, {"X": 0.32114604115486145, "Y": 0.11699286848306656}]}, "Id": "6813dd7b-a150-4739-a52e-7556ab746e1c"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Varina,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09751097112894058, "Height": 0.013353771530091763, "Left": 0.4326494038105011, "Top": 0.10278764367103577}, "Polygon": [{"X": 0.4326496124267578, "Y": 0.10278764367103577}, {"X": 0.5301603674888611, "Y": 0.10278771817684174}, {"X": 0.530160129070282, "Y": 0.1161414161324501}, {"X": 0.4326494038105011, "Y": 0.11614132672548294}]}, "Id": "3e6fe4e4-493b-414a-99fc-7ca0f1b1595e"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "NC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0412915013730526, "Height": 0.011302242986857891, "Left": 0.5398619174957275, "Top": 0.1028178408741951}, "Polygon": [{"X": 0.5398620963096619, "Y": 0.1028178408741951}, {"X": 0.581153392791748, "Y": 0.10281787067651749}, {"X": 0.5811532139778137, "Y": 0.11412008106708527}, {"X": 0.5398619174957275, "Y": 0.11412004381418228}]}, "Id": "3206b054-7a77-4a82-bed1-1eb7089fca14"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "27526", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08114208281040192, "Height": 0.01119225099682808, "Left": 0.5981945395469666, "Top": 0.10300985723733902}, "Polygon": [{"X": 0.5981947183609009, "Y": 0.10300985723733902}, {"X": 0.6793366074562073, "Y": 0.10300992429256439}, {"X": 0.679336428642273, "Y": 0.1142021119594574}, {"X": 0.5981945395469666, "Y": 0.11420203745365143}]}, "Id": "72b41310-3133-464a-911c-8c91c145f3c2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "919-552-3157", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.183644637465477, "Height": 0.011433270759880543, "Left": 0.4082373082637787, "Top": 0.12250351905822754}, "Polygon": [{"X": 0.408237487077713, "Y": 0.12250351905822754}, {"X": 0.5918819308280945, "Y": 0.12250369787216187}, {"X": 0.5918817520141602, "Y": 0.1339367926120758}, {"X": 0.4082373082637787, "Y": 0.13393659889698029}]}, "Id": "0382cd24-4ee3-4b01-9633-7a6621a5a5fb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06025361269712448, "Height": 0.01126458216458559, "Left": 0.06799536943435669, "Top": 0.16588950157165527}, "Polygon": [{"X": 0.06799556314945221, "Y": 0.16588950157165527}, {"X": 0.12824898958206177, "Y": 0.16588957607746124}, {"X": 0.12824879586696625, "Y": 0.177154079079628}, {"X": 0.06799536943435669, "Y": 0.17715398967266083}]}, "Id": "2c444d03-7d3a-436e-864c-ed3a745a08ec"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/11/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11387137323617935, "Height": 0.011411534622311592, "Left": 0.6250819563865662, "Top": 0.1658749282360077}, "Polygon": [{"X": 0.6250821352005005, "Y": 0.1658749282360077}, {"X": 0.7389532923698425, "Y": 0.16587509214878082}, {"X": 0.7389531135559082, "Y": 0.17728646099567413}, {"X": 0.6250819563865662, "Y": 0.1772862821817398}]}, "Id": "5d20efa6-9597-4754-8cd5-a6704c01817b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "RECEIPT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12763401865959167, "Height": 0.01150046568363905, "Left": 0.43700599670410156, "Top": 0.20938195288181305}, "Polygon": [{"X": 0.4370061755180359, "Y": 0.20938195288181305}, {"X": 0.5646399855613708, "Y": 0.20938219130039215}, {"X": 0.5646398067474365, "Y": 0.22088241577148438}, {"X": 0.43700599670410156, "Y": 0.22088216245174408}]}, "Id": "94475ee0-60d9-4434-9311-fe4ee78292cb"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09905491024255753, "Height": 0.01401983667165041, "Left": 0.06801876425743103, "Top": 0.2332058995962143}, "Polygon": [{"X": 0.06801900267601013, "Y": 0.2332058995962143}, {"X": 0.16707366704940796, "Y": 0.233206108212471}, {"X": 0.16707342863082886, "Y": 0.24722573161125183}, {"X": 0.06801876425743103, "Y": 0.24722550809383392}]}, "Id": "6bc7b6a7-4107-4c11-bee4-3807bbbf9ae7"}, {"BlockType": "WORD", "Confidence": 99.345703125, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023377617821097374, "Height": 0.010638154111802578, "Left": 0.17422530055046082, "Top": 0.23374329507350922}, "Polygon": [{"X": 0.17422547936439514, "Y": 0.23374329507350922}, {"X": 0.19760291278362274, "Y": 0.2337433397769928}, {"X": 0.19760273396968842, "Y": 0.24438144266605377}, {"X": 0.17422530055046082, "Y": 0.2443813979625702}]}, "Id": "072a8f3a-c204-4143-b524-3e6e499e6019"}, {"BlockType": "WORD", "Confidence": 97.40911865234375, "Text": "RAI250811065739", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24437358975410461, "Height": 0.011593890376389027, "Left": 0.62575364112854, "Top": 0.23321114480495453}, "Polygon": [{"X": 0.6257538199424744, "Y": 0.23321114480495453}, {"X": 0.8701272010803223, "Y": 0.23321165144443512}, {"X": 0.8701270222663879, "Y": 0.24480503797531128}, {"X": 0.62575364112854, "Y": 0.2448045015335083}]}, "Id": "49f07eb0-53e6-45fc-bb94-79807921f345"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "FACILITY:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13432680070400238, "Height": 0.011322923935949802, "Left": 0.06801436841487885, "Top": 0.2610757052898407}, "Polygon": [{"X": 0.06801456212997437, "Y": 0.2610757052898407}, {"X": 0.20234116911888123, "Y": 0.2610760033130646}, {"X": 0.2023409754037857, "Y": 0.27239862084388733}, {"X": 0.06801436841487885, "Y": 0.27239829301834106}]}, "Id": "84f0e412-1f71-48fb-9d85-5426d107bab5"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Ocala", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07582534104585648, "Height": 0.011631847359240055, "Left": 0.6253350973129272, "Top": 0.2609020471572876}, "Polygon": [{"X": 0.6253352761268616, "Y": 0.2609020471572876}, {"X": 0.7011604309082031, "Y": 0.2609022259712219}, {"X": 0.7011602520942688, "Y": 0.2725338935852051}, {"X": 0.6253350973129272, "Y": 0.27253368496894836}]}, "Id": "ad8ddbbe-e510-4cdf-b82f-435deb343cf5"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Department:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1587502658367157, "Height": 0.013907834887504578, "Left": 0.06806491315364838, "Top": 0.28887009620666504}, "Polygon": [{"X": 0.06806515157222748, "Y": 0.28887009620666504}, {"X": 0.22681517899036407, "Y": 0.28887054324150085}, {"X": 0.22681494057178497, "Y": 0.3027779459953308}, {"X": 0.06806491315364838, "Y": 0.302777498960495}]}, "Id": "ea6f7ca1-e4eb-4d5a-9b05-e51e0ea81298"}, {"BlockType": "WORD", "Confidence": 99.76164245605469, "Text": "GRO-Ocala", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15273724496364594, "Height": 0.011507879942655563, "Left": 0.6255061030387878, "Top": 0.2886818051338196}, "Polygon": [{"X": 0.6255062818527222, "Y": 0.2886818051338196}, {"X": 0.778243362903595, "Y": 0.288682222366333}, {"X": 0.7782431840896606, "Y": 0.30018970370292664}, {"X": 0.6255061030387878, "Y": 0.3001892864704132}]}, "Id": "fe45eaf2-cec8-4dfb-adc7-803ef9069003"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Door:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06922127306461334, "Height": 0.011067783460021019, "Left": 0.06801866739988327, "Top": 0.31671464443206787}, "Polygon": [{"X": 0.0680188536643982, "Y": 0.31671464443206787}, {"X": 0.1372399479150772, "Y": 0.3167148530483246}, {"X": 0.1372397541999817, "Y": 0.32778242230415344}, {"X": 0.06801866739988327, "Y": 0.32778221368789673}]}, "Id": "8272e784-86b3-407c-a28f-d8155e6f98ec"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "16", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030719656497240067, "Height": 0.011005452834069729, "Left": 0.6267974376678467, "Top": 0.3169155418872833}, "Polygon": [{"X": 0.626797616481781, "Y": 0.3169155418872833}, {"X": 0.6575170755386353, "Y": 0.3169156312942505}, {"X": 0.6575168967247009, "Y": 0.3279210031032562}, {"X": 0.6267974376678467, "Y": 0.32792091369628906}]}, "Id": "0f5c8449-7a78-4f86-9408-4ef885a56eba"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Shift:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06394462287425995, "Height": 0.011428557336330414, "Left": 0.06792221963405609, "Top": 0.3442031443119049}, "Polygon": [{"X": 0.06792241334915161, "Y": 0.3442031443119049}, {"X": 0.13186684250831604, "Y": 0.3442033529281616}, {"X": 0.13186664879322052, "Y": 0.3556317090988159}, {"X": 0.06792221963405609, "Y": 0.3556315004825592}]}, "Id": "f33ebc39-435e-4ed2-a798-96e862614bea"}, {"BlockType": "WORD", "Confidence": 98.34921264648438, "Text": "1st", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03811126574873924, "Height": 0.011082427576184273, "Left": 0.6268889904022217, "Top": 0.3446553349494934}, "Polygon": [{"X": 0.6268892288208008, "Y": 0.3446553349494934}, {"X": 0.6650002598762512, "Y": 0.34465545415878296}, {"X": 0.6650000810623169, "Y": 0.3557377755641937}, {"X": 0.6268889904022217, "Y": 0.3557376265525818}]}, "Id": "491bfb32-1954-4c9a-8410-3080f0a20fe9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Shift", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05837273597717285, "Height": 0.011485032737255096, "Left": 0.6731964349746704, "Top": 0.3440997302532196}, "Polygon": [{"X": 0.6731966137886047, "Y": 0.3440997302532196}, {"X": 0.7315691709518433, "Y": 0.34409990906715393}, {"X": 0.7315689921379089, "Y": 0.3555847704410553}, {"X": 0.6731964349746704, "Y": 0.3555845618247986}]}, "Id": "df955e5b-81cc-44c2-86b6-ed833c0b8740"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "LOAD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07872325927019119, "Height": 0.011307629756629467, "Left": 0.06751324981451035, "Top": 0.3917284905910492}, "Polygon": [{"X": 0.06751345098018646, "Y": 0.3917284905910492}, {"X": 0.14623650908470154, "Y": 0.39172878861427307}, {"X": 0.14623631536960602, "Y": 0.4030361473560333}, {"X": 0.06751324981451035, "Y": 0.40303584933280945}]}, "Id": "a1ee823d-2a36-40c0-9850-28569062b313"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "INFO:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07570252567529678, "Height": 0.011300141923129559, "Left": 0.15634867548942566, "Top": 0.3918437361717224}, "Polygon": [{"X": 0.15634886920452118, "Y": 0.3918437361717224}, {"X": 0.23205120861530304, "Y": 0.3918440341949463}, {"X": 0.23205101490020752, "Y": 0.40314388275146484}, {"X": 0.15634867548942566, "Y": 0.40314358472824097}]}, "Id": "d5a1ffbe-9ae7-4ce7-803b-9dac6dccedc3"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Carrier:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09611997753381729, "Height": 0.011409152299165726, "Left": 0.06708154082298279, "Top": 0.4195166230201721}, "Polygon": [{"X": 0.06708173453807831, "Y": 0.4195166230201721}, {"X": 0.16320151090621948, "Y": 0.41951701045036316}, {"X": 0.16320131719112396, "Y": 0.43092575669288635}, {"X": 0.06708154082298279, "Y": 0.4309253692626953}]}, "Id": "33296e4a-66be-453a-9f71-5798954f8893"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "59", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03218729421496391, "Height": 0.011197906918823719, "Left": 0.6253074407577515, "Top": 0.4198010265827179}, "Polygon": [{"X": 0.6253076195716858, "Y": 0.4198010265827179}, {"X": 0.6574947834014893, "Y": 0.41980114579200745}, {"X": 0.6574945449829102, "Y": 0.43099892139434814}, {"X": 0.6253074407577515, "Y": 0.4309988021850586}]}, "Id": "3c9d026b-b97a-4f08-9caa-3b8bce97e846"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "north", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0647185817360878, "Height": 0.011159615591168404, "Left": 0.6665444374084473, "Top": 0.4197208285331726}, "Polygon": [{"X": 0.6665446162223816, "Y": 0.4197208285331726}, {"X": 0.7312629818916321, "Y": 0.4197210669517517}, {"X": 0.7312628030776978, "Y": 0.43088042736053467}, {"X": 0.6665444374084473, "Y": 0.4308801591396332}]}, "Id": "856f2a10-8667-45b2-9f93-5617f86396fb"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Vendor:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10209459811449051, "Height": 0.01136750914156437, "Left": 0.06617559492588043, "Top": 0.4472859501838684}, "Polygon": [{"X": 0.06617578864097595, "Y": 0.4472859501838684}, {"X": 0.16827018558979034, "Y": 0.44728636741638184}, {"X": 0.16826999187469482, "Y": 0.45865345001220703}, {"X": 0.06617559492588043, "Y": 0.4586530029773712}]}, "Id": "a0f89578-96e8-4878-bad1-c40f6ca006d5"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Amercare", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1281566172838211, "Height": 0.01140429638326168, "Left": 0.6242707371711731, "Top": 0.44737857580184937}, "Polygon": [{"X": 0.6242709755897522, "Y": 0.44737857580184937}, {"X": 0.7524273991584778, "Y": 0.44737911224365234}, {"X": 0.7524272203445435, "Y": 0.4587828814983368}, {"X": 0.6242707371711731, "Y": 0.4587823152542114}]}, "Id": "7bb08841-efc3-4f67-8573-0b3303dcd0f2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Royal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07290571928024292, "Height": 0.014070115983486176, "Left": 0.7621490955352783, "Top": 0.4472489356994629}, "Polygon": [{"X": 0.7621493339538574, "Y": 0.4472489356994629}, {"X": 0.8350548148155212, "Y": 0.44724926352500916}, {"X": 0.8350545763969421, "Y": 0.46131905913352966}, {"X": 0.7621490955352783, "Y": 0.4613187313079834}]}, "Id": "01f17057-7cbc-442f-b89c-3f4cc3918a7c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0743144154548645, "Height": 0.011338412761688232, "Left": 0.06626966595649719, "Top": 0.475087434053421}, "Polygon": [{"X": 0.06626986712217331, "Y": 0.475087434053421}, {"X": 0.1405840814113617, "Y": 0.4750877618789673}, {"X": 0.14058388769626617, "Y": 0.48642584681510925}, {"X": 0.06626966595649719, "Y": 0.4864254891872406}]}, "Id": "3bf0567a-3d0c-40f5-b513-862bbd2ce6bd"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "01", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028749730437994003, "Height": 0.011069179512560368, "Left": 0.6250150799751282, "Top": 0.47542041540145874}, "Polygon": [{"X": 0.6250152587890625, "Y": 0.47542041540145874}, {"X": 0.6537647843360901, "Y": 0.4754205644130707}, {"X": 0.6537646055221558, "Y": 0.48648959398269653}, {"X": 0.6250150799751282, "Y": 0.486489474773407}]}, "Id": "4886c739-f6c1-44aa-aabe-b2d4d0c39989"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08388730138540268, "Height": 0.011210412718355656, "Left": 0.06640441715717316, "Top": 0.5029498338699341}, "Polygon": [{"X": 0.06640461087226868, "Y": 0.5029498338699341}, {"X": 0.15029171109199524, "Y": 0.5029502511024475}, {"X": 0.15029151737689972, "Y": 0.5141602754592896}, {"X": 0.06640441715717316, "Y": 0.5141598582267761}]}, "Id": "3ca7db84-b889-439a-a929-bf8ce4800b62"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "5250234", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11469906568527222, "Height": 0.011033291928470135, "Left": 0.6249483823776245, "Top": 0.5031672120094299}, "Polygon": [{"X": 0.6249485611915588, "Y": 0.5031672120094299}, {"X": 0.7396474480628967, "Y": 0.5031677484512329}, {"X": 0.7396472096443176, "Y": 0.5142005085945129}, {"X": 0.6249483823776245, "Y": 0.5141999125480652}]}, "Id": "8f570177-b5c7-4241-bc19-9a9d4c43c17a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10282771289348602, "Height": 0.011186067946255207, "Left": 0.06595931947231293, "Top": 0.5504586100578308}, "Polygon": [{"X": 0.06595951318740845, "Y": 0.5504586100578308}, {"X": 0.16878703236579895, "Y": 0.5504591464996338}, {"X": 0.16878683865070343, "Y": 0.5616446733474731}, {"X": 0.06595931947231293, "Y": 0.5616440773010254}]}, "Id": "d007079a-fff5-4a8d-8470-967a62fb77cb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Due", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052473392337560654, "Height": 0.011303659528493881, "Left": 0.17724353075027466, "Top": 0.5503151416778564}, "Polygon": [{"X": 0.17724372446537018, "Y": 0.5503151416778564}, {"X": 0.2297169268131256, "Y": 0.5503154397010803}, {"X": 0.2297167330980301, "Y": 0.5616188049316406}, {"X": 0.17724353075027466, "Y": 0.5616185069084167}]}, "Id": "5af866cc-7b7c-46dc-bb8e-09897d81a1cf"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$180.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10625392198562622, "Height": 0.01354591641575098, "Left": 0.6249967813491821, "Top": 0.549399197101593}, "Polygon": [{"X": 0.6249970197677612, "Y": 0.549399197101593}, {"X": 0.7312507033348083, "Y": 0.549399733543396}, {"X": 0.7312504649162292, "Y": 0.5629451274871826}, {"X": 0.6249967813491821, "Y": 0.5629445314407349}]}, "Id": "3ab2960e-64e5-46b2-9982-85f5112055fd"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Convenience", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17042532563209534, "Height": 0.01125436369329691, "Left": 0.06719447672367096, "Top": 0.5781688094139099}, "Polygon": [{"X": 0.06719467043876648, "Y": 0.5781688094139099}, {"X": 0.2376197874546051, "Y": 0.5781697630882263}, {"X": 0.23761960864067078, "Y": 0.5894231200218201}, {"X": 0.06719447672367096, "Y": 0.5894221663475037}]}, "Id": "f5585b34-b597-4797-816b-dac3deda85a7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Fee", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04895894601941109, "Height": 0.01133816223591566, "Left": 0.24779091775417328, "Top": 0.5780951380729675}, "Polygon": [{"X": 0.2477911114692688, "Y": 0.5780951380729675}, {"X": 0.29674986004829407, "Y": 0.5780953764915466}, {"X": 0.29674965143203735, "Y": 0.5894333124160767}, {"X": 0.24779091775417328, "Y": 0.5894330143928528}]}, "Id": "cea6237b-7321-4167-99c2-288838703d10"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "$9.75", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07284560054540634, "Height": 0.013382857665419579, "Left": 0.6250905990600586, "Top": 0.5773332118988037}, "Polygon": [{"X": 0.6250907778739929, "Y": 0.5773332118988037}, {"X": 0.6979361772537231, "Y": 0.5773336291313171}, {"X": 0.697935938835144, "Y": 0.5907160639762878}, {"X": 0.6250905990600586, "Y": 0.5907156467437744}]}, "Id": "c1956a96-c480-4b13-a992-78aabfe30c6e"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Electronic", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12785638868808746, "Height": 0.01126656774431467, "Left": 0.06825127452611923, "Top": 0.6059856414794922}, "Polygon": [{"X": 0.06825147569179535, "Y": 0.6059856414794922}, {"X": 0.1961076557636261, "Y": 0.6059863567352295}, {"X": 0.19610747694969177, "Y": 0.6172521710395813}, {"X": 0.06825127452611923, "Y": 0.617251455783844}]}, "Id": "9492ca66-4d92-4a62-9b1c-a218f9c42f06"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Payment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11594195663928986, "Height": 0.013874590396881104, "Left": 0.2048017829656601, "Top": 0.6059496998786926}, "Polygon": [{"X": 0.204802006483078, "Y": 0.6059496998786926}, {"X": 0.32074373960494995, "Y": 0.6059503555297852}, {"X": 0.32074350118637085, "Y": 0.6198242902755737}, {"X": 0.2048017829656601, "Y": 0.6198235750198364}]}, "Id": "c2864d7c-6289-411a-97c8-b0e41f205cb7"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "$189.75", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10612017661333084, "Height": 0.013586929999291897, "Left": 0.6248946189880371, "Top": 0.6048755645751953}, "Polygon": [{"X": 0.6248947978019714, "Y": 0.6048755645751953}, {"X": 0.7310147881507874, "Y": 0.6048761606216431}, {"X": 0.7310145497322083, "Y": 0.6184625029563904}, {"X": 0.6248946189880371, "Y": 0.6184618473052979}]}, "Id": "61ec763f-4799-4b27-acb5-4f0e5129b829"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "BALANCE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13549405336380005, "Height": 0.011493115685880184, "Left": 0.06729398667812347, "Top": 0.6530996561050415}, "Polygon": [{"X": 0.06729418784379959, "Y": 0.6530996561050415}, {"X": 0.20278804004192352, "Y": 0.6531004905700684}, {"X": 0.202787846326828, "Y": 0.6645927429199219}, {"X": 0.06729398667812347, "Y": 0.6645918488502502}]}, "Id": "7f721427-44d1-4324-be85-ba3c287333af"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DUE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059577010571956635, "Height": 0.011466583237051964, "Left": 0.21325628459453583, "Top": 0.6531088948249817}, "Polygon": [{"X": 0.21325647830963135, "Y": 0.6531088948249817}, {"X": 0.27283328771591187, "Y": 0.6531093120574951}, {"X": 0.27283310890197754, "Y": 0.6645755171775818}, {"X": 0.21325628459453583, "Y": 0.6645750999450684}]}, "Id": "a717742a-2b76-4cfd-8381-b1cdb2af0c6d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$0.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0735323503613472, "Height": 0.013299154117703438, "Left": 0.6249604821205139, "Top": 0.6525976061820984}, "Polygon": [{"X": 0.624960720539093, "Y": 0.6525976061820984}, {"X": 0.6984928250312805, "Y": 0.6525980234146118}, {"X": 0.6984926462173462, "Y": 0.6658967137336731}, {"X": 0.6249604821205139, "Y": 0.6658962368965149}]}, "Id": "39ef4896-a6e8-4388-b852-ae3021f0b6b1"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "DESCRIPTION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1993838995695114, "Height": 0.012054544873535633, "Left": 0.06753572821617126, "Top": 0.7001860737800598}, "Polygon": [{"X": 0.06753593683242798, "Y": 0.7001860737800598}, {"X": 0.2669196128845215, "Y": 0.7001874446868896}, {"X": 0.26691940426826477, "Y": 0.7122406363487244}, {"X": 0.06753572821617126, "Y": 0.7122392654418945}]}, "Id": "87f6fc5c-3c9a-402f-a93d-31fd6984811e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "OF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040287502110004425, "Height": 0.011627068743109703, "Left": 0.27631476521492004, "Top": 0.7002607583999634}, "Polygon": [{"X": 0.27631497383117676, "Y": 0.7002607583999634}, {"X": 0.31660225987434387, "Y": 0.7002609968185425}, {"X": 0.31660208106040955, "Y": 0.7118878364562988}, {"X": 0.27631476521492004, "Y": 0.711887538433075}]}, "Id": "2932d254-9cb0-4a88-a5eb-6911378cfd3d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CHARGES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14449745416641235, "Height": 0.0117886271327734, "Left": 0.3248450458049774, "Top": 0.7003851532936096}, "Polygon": [{"X": 0.32484525442123413, "Y": 0.7003851532936096}, {"X": 0.46934249997138977, "Y": 0.7003861665725708}, {"X": 0.46934232115745544, "Y": 0.7121738195419312}, {"X": 0.3248450458049774, "Y": 0.71217280626297}]}, "Id": "182d34e8-d322-49eb-84fb-770bb43f6b21"}, {"BlockType": "WORD", "Confidence": 99.599609375, "Text": "PL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03499974310398102, "Height": 0.010978816077113152, "Left": 0.06800378859043121, "Top": 0.7284688949584961}, "Polygon": [{"X": 0.06800398230552673, "Y": 0.7284688949584961}, {"X": 0.10300353169441223, "Y": 0.7284691333770752}, {"X": 0.10300334542989731, "Y": 0.7394477128982544}, {"X": 0.06800378859043121, "Y": 0.7394474744796753}]}, "Id": "24692107-d287-477d-8af8-ba4dc210f4b2"}, {"BlockType": "WORD", "Confidence": 99.52069091796875, "Text": "21-25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07484465837478638, "Height": 0.011266269721090794, "Left": 0.11100166290998459, "Top": 0.728356122970581}, "Polygon": [{"X": 0.11100185662508011, "Y": 0.728356122970581}, {"X": 0.18584631383419037, "Y": 0.728356659412384}, {"X": 0.18584612011909485, "Y": 0.7396223545074463}, {"X": 0.11100166290998459, "Y": 0.7396218180656433}]}, "Id": "3909dfa8-ae7f-47a8-964f-679da7a00f53"}, {"BlockType": "WORD", "Confidence": 99.81285858154297, "Text": "Fin", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038659870624542236, "Height": 0.011147629469633102, "Left": 0.19569280743598938, "Top": 0.7284335494041443}, "Polygon": [{"X": 0.1956930011510849, "Y": 0.7284335494041443}, {"X": 0.23435267806053162, "Y": 0.7284338474273682}, {"X": 0.2343524843454361, "Y": 0.7395811676979065}, {"X": 0.19569280743598938, "Y": 0.7395809292793274}]}, "Id": "c55fc80d-ca75-4bc0-9c78-d5ad79311470"}, {"BlockType": "WORD", "Confidence": 97.51972961425781, "Text": "Plts", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04666166380047798, "Height": 0.011829815804958344, "Left": 0.24463984370231628, "Top": 0.7281123995780945}, "Polygon": [{"X": 0.244640052318573, "Y": 0.7281123995780945}, {"X": 0.29130151867866516, "Y": 0.7281127572059631}, {"X": 0.29130131006240845, "Y": 0.7399422526359558}, {"X": 0.24463984370231628, "Y": 0.7399418950080872}]}, "Id": "8a180fb5-f9c0-4faa-8ff6-c5615d338cdc"}, {"BlockType": "WORD", "Confidence": 84.9192886352539, "Text": "4511576095(Amercare", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.30175310373306274, "Height": 0.014096897095441818, "Left": 0.0651758685708046, "Top": 0.7761422991752625}, "Polygon": [{"X": 0.0651761144399643, "Y": 0.7761422991752625}, {"X": 0.36692899465560913, "Y": 0.7761445641517639}, {"X": 0.36692875623703003, "Y": 0.790239155292511}, {"X": 0.0651758685708046, "Y": 0.7902368307113647}]}, "Id": "cc727c4e-cfae-41c7-a9c8-12c59ad2eb7e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Royal)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08259562402963638, "Height": 0.014263940043747425, "Left": 0.37683138251304626, "Top": 0.776204526424408}, "Polygon": [{"X": 0.37683162093162537, "Y": 0.776204526424408}, {"X": 0.45942699909210205, "Y": 0.7762051820755005}, {"X": 0.45942676067352295, "Y": 0.7904685139656067}, {"X": 0.37683138251304626, "Y": 0.7904678583145142}]}, "Id": "2579dae9-b8d2-4f51-a931-cba43963ef18"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "c74debd0-d36e-4f49-9134-e8cc34da562c", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "c74debd0-d36e-4f49-9134-e8cc34da562c", "content-type": "application/x-amz-json-1.1", "content-length": "52113", "date": "Thu, 18 Sep 2025 10:33:58 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "30cffd6c-8780-4f08-8ec2-eb045d3fad5a", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 10:34:01 GMT", "content-type": "application/json", "content-length": "1976", "connection": "keep-alive", "x-amzn-requestid": "30cffd6c-8780-4f08-8ec2-eb045d3fad5a"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. This is page1. This looks like a receipt for a load (maybe a fuel receipt? Or a receipt for load charges). Contains \"RECEIPT\". It lists \"Amount Due\", \"Convenience Fee\", \"Electronic Payment\". This could be a \"receipt\" for a load, maybe a lumper receipt? But lumper receipt is for labor. This seems to be a receipt for a load, maybe a \"receipt\" from a carrier or a facility. But the doc type list includes \"receipt\"? There is no \"receipt\" type. But we have \"fuel_receipt\", \"clear_to_pay\", \"scale_ticket\", \"lodgement\"? None.\n\nIt might be \"combined_carrier_documents\"? No, only one header.\n\nCould be \"receipt\" for a load but not a standard doc type. Could be \"other\". But we can see \"Receipt\" header, but no typical doc types. It might be a \"receipt\" for load. The \"doc_type\" options: invoice, lumper_receipt, bol, pod, rate_confirmation, cust_rate_confirmation, clear_to_pay, scale_ticket, log, fuel_receipt, combined_carrier_documents, pack_list, po, comm_invoice, customs_doc, nmfc_cert, other, coa, tender_from_cust, so_confirmation, po_confirmation, ingate, outgate.\n\nThis page is a \"receipt\" of a load - maybe it's a \"receipt\" for a fuel? No fuel details. It's a receipt for a load at a facility. Could be a \"receipt\" for a load at a facility. Might be a \"receipt\" for a load, but none of the defined types. So classify as \"other\".\n\nBut we need to check if it is a continuation of previous page; it's page1, so no previous. No continuation. So we assign \"other\".\n\nReturn via tool call."}}}, {"toolUse": {"toolUseId": "tooluse_yZ5S0AfwRXuaFWVQgFkKog", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "other"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2320, "outputTokens": 425, "totalTokens": 2745}, "metrics": {"latencyMs": 1813}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/83f8a698_21_lumper_receipt_5.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nFHI, LLC\nFed. ID #56-2174318\nP.O. Box 546\nFuquay Varina, NC 27526\n919-552-3157\nDate\n08/11/25\nRECEIPT\nReceipt #:\nRAI250811065739\nFACILITY:\nOcala\nDepartment:\nGRO-Ocala\nDoor:\n16\nShift:\n1st Shift\nLOAD INFO:\nCarrier:\n59 north\nVendor:\nAmercare Royal\nTruck\n01\nTrailer\n5250234\nAmount Due\n$180.00\nConvenience Fee\n$9.75\nElectronic Payment\n$189.75\nBALANCE DUE\n$0.00\nDESCRIPTION OF CHARGES\nPL 21-25 Fin Plts\n4511576095(Amercare Royal)\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}