{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 2.7578863637245377e-08}, {"X": 1.0, "Y": 1.0}, {"X": 1.0609331013711198e-07, "Y": 1.0}]}, "Id": "5291955b-5928-4584-8755-2895cea6c3a8", "Relationships": [{"Type": "CHILD", "Ids": ["b91b7432-c193-40f9-b400-0f34016106d8", "67211c8a-6f09-40f5-b4c5-8e7be657fe60", "703528b5-163f-488c-8bdf-3d1f20aa4df7", "a43da29f-e07c-4cab-b935-b981c35c7a91", "133c2836-989e-4fcd-afd6-9516c2b4a2d6", "dc155ff6-74fc-4048-b0e8-8337cb5c5e0e", "50398a47-2d9d-4b49-b439-c8cdd7be9af0", "adf0c2be-8a0f-4147-ae38-09ff7517e67f", "548ef18b-9a4f-41e2-b7cd-e2801f371e4e", "100d3819-2dca-497a-bf6f-94ccbb521052", "36675d07-eed1-49b9-a91b-2d907ad10683", "e058e417-a11d-42f0-a6ae-762d035a8068", "a5ea9a0f-c1c7-46e8-a321-7a94b2981f01", "5409bcfc-05ac-4d0b-a088-07c8ad9fcf27", "f97d0cbf-3b54-4f76-9b6c-aaf13e88db5c", "f540423f-9e95-4fb5-a7cf-79583a4f789a", "8a3cac0c-cb96-4e53-9d33-27775377c784", "ea677f8d-c9d6-4695-8c53-8fbac10482c7", "f4e71c16-35e4-4f33-8e62-fa6e26487760", "51094f61-5b2d-44a4-8cdc-f6ec66e1a714", "b399dd79-d772-458f-b78c-cf7dd15f4899", "0976c402-f278-4b75-add7-baaec24fa01c", "81072f8f-fa28-45e4-93f1-80476c9062c5", "54172b84-15d3-41ae-b8ae-05da1ad82b35", "afbd259c-7a6c-441f-98b4-f3d730f217de", "ceae701f-6a36-423a-a5fd-f0858ef38dcf", "345e3b30-f5d4-4f92-96f9-9333c234e951", "30e9e1c7-c538-4db9-b65d-45719afe258b", "99d7b3ef-30fb-4808-8143-5bcdc467f899", "da78bb1c-f0b5-4db9-bfd0-2a01d495bb1d", "c6296fb1-396f-454d-b10b-9721aa6e6cb2", "4a689e67-293b-45d9-a174-e68341983825", "76e6c3f4-c7a9-4d67-9c64-ea4b0b3253d7"]}]}, {"BlockType": "LINE", "Confidence": 99.78475952148438, "Text": "Raley's Arizona LLC dba Bashas'", "Geometry": {"BoundingBox": {"Width": 0.6811171174049377, "Height": 0.024372845888137817, "Left": 0.16040676832199097, "Top": 0.05086463689804077}, "Polygon": [{"X": 0.16040676832199097, "Y": 0.05086463689804077}, {"X": 0.8415238857269287, "Y": 0.05086928978562355}, {"X": 0.8415237069129944, "Y": 0.07523748278617859}, {"X": 0.16040682792663574, "Y": 0.07523289322853088}]}, "Id": "b91b7432-c193-40f9-b400-0f34016106d8", "Relationships": [{"Type": "CHILD", "Ids": ["a6662a26-7446-42ff-ad52-6c85cef8354c", "c6f40eb6-d9eb-4c52-99d7-826cebbc6207", "7a5ade4f-9fcd-4ac7-89da-77b14f0dc1c6", "d3769fb9-2e22-4475-8ea5-1b1058c9efbf", "aec54c81-91ba-469d-b296-41e1e0118fc5"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "480-895-9350", "Geometry": {"BoundingBox": {"Width": 0.28750377893447876, "Height": 0.019217224791646004, "Left": 0.3560301959514618, "Top": 0.11872474104166031}, "Polygon": [{"X": 0.3560302257537842, "Y": 0.11872474104166031}, {"X": 0.6435340046882629, "Y": 0.11872663348913193}, {"X": 0.6435338854789734, "Y": 0.13794197142124176}, {"X": 0.3560301959514618, "Y": 0.13794009387493134}]}, "Id": "67211c8a-6f09-40f5-b4c5-8e7be657fe60", "Relationships": [{"Type": "CHILD", "Ids": ["f2100ba1-80cf-4313-88d0-87cbaf1f306e"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "Warehouse:", "Geometry": {"BoundingBox": {"Width": 0.24789227545261383, "Height": 0.01979471929371357, "Left": 0.046417102217674255, "Top": 0.185542494058609}, "Polygon": [{"X": 0.046417102217674255, "Y": 0.185542494058609}, {"X": 0.2943093776702881, "Y": 0.18554407358169556}, {"X": 0.2943093776702881, "Y": 0.20533721148967743}, {"X": 0.046417176723480225, "Y": 0.20533566176891327}]}, "Id": "703528b5-163f-488c-8bdf-3d1f20aa4df7", "Relationships": [{"Type": "CHILD", "Ids": ["ee22da20-53fc-40d2-84e5-85c5c8cc8bfc"]}]}, {"BlockType": "LINE", "Confidence": 78.34661865234375, "Text": "Bashasâ€ᵀ Receiving", "Geometry": {"BoundingBox": {"Width": 0.46954745054244995, "Height": 0.02484111115336418, "Left": 0.4880591630935669, "Top": 0.18524140119552612}, "Polygon": [{"X": 0.48805925250053406, "Y": 0.18524140119552612}, {"X": 0.9576066136360168, "Y": 0.1852443814277649}, {"X": 0.9576063752174377, "Y": 0.2100825160741806}, {"X": 0.4880591630935669, "Y": 0.21007958054542542}]}, "Id": "a43da29f-e07c-4cab-b935-b981c35c7a91", "Relationships": [{"Type": "CHILD", "Ids": ["a860649a-662d-4f38-af04-22782e9da453", "0af701a4-b8e3-4e96-81c4-f17cd9fba594"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "(Dry)", "Geometry": {"BoundingBox": {"Width": 0.09900682419538498, "Height": 0.0250411219894886, "Left": 0.8526988625526428, "Top": 0.219021737575531}, "Polygon": [{"X": 0.8526991009712219, "Y": 0.219021737575531}, {"X": 0.9517056941986084, "Y": 0.21902234852313995}, {"X": 0.9517054557800293, "Y": 0.2440628558397293}, {"X": 0.8526988625526428, "Y": 0.24406225979328156}]}, "Id": "133c2836-989e-4fcd-afd6-9516c2b4a2d6", "Relationships": [{"Type": "CHILD", "Ids": ["84238572-001a-4b55-baee-d2a272591885"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Warehouse", "Geometry": {"BoundingBox": {"Width": 0.23628418147563934, "Height": 0.019928626716136932, "Left": 0.04684007167816162, "Top": 0.2533677816390991}, "Polygon": [{"X": 0.04684007167816162, "Y": 0.2533677816390991}, {"X": 0.28312426805496216, "Y": 0.25336921215057373}, {"X": 0.28312426805496216, "Y": 0.27329641580581665}, {"X": 0.04684014990925789, "Y": 0.27329498529434204}]}, "Id": "dc155ff6-74fc-4048-b0e8-8337cb5c5e0e", "Relationships": [{"Type": "CHILD", "Ids": ["a4749fc8-8547-433c-bc40-18ad3ca36ece"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "480-940-9880", "Geometry": {"BoundingBox": {"Width": 0.2869906723499298, "Height": 0.019175546243786812, "Left": 0.6649048924446106, "Top": 0.25401216745376587}, "Polygon": [{"X": 0.6649050116539001, "Y": 0.25401216745376587}, {"X": 0.951895534992218, "Y": 0.25401392579078674}, {"X": 0.9518953561782837, "Y": 0.2731877267360687}, {"X": 0.6649048924446106, "Y": 0.27318599820137024}]}, "Id": "50398a47-2d9d-4b49-b439-c8cdd7be9af0", "Relationships": [{"Type": "CHILD", "Ids": ["7e143ed2-b6af-4046-af0a-ecfbeadbdb3e"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Phone:", "Geometry": {"BoundingBox": {"Width": 0.14214450120925903, "Height": 0.019697017967700958, "Left": 0.04927556589245796, "Top": 0.2866823077201843}, "Polygon": [{"X": 0.04927556589245796, "Y": 0.2866823077201843}, {"X": 0.1914200484752655, "Y": 0.2866831421852112}, {"X": 0.1914200782775879, "Y": 0.3063793182373047}, {"X": 0.04927564039826393, "Y": 0.30637848377227783}]}, "Id": "adf0c2be-8a0f-4147-ae38-09ff7517e67f", "Relationships": [{"Type": "CHILD", "Ids": ["63126d70-9a89-41e7-a096-5b1add03c0d7"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Tax ID:", "Geometry": {"BoundingBox": {"Width": 0.14691276848316193, "Height": 0.019395558163523674, "Left": 0.04607708379626274, "Top": 0.32094234228134155}, "Polygon": [{"X": 0.04607708379626274, "Y": 0.32094234228134155}, {"X": 0.19298982620239258, "Y": 0.3209432065486908}, {"X": 0.19298985600471497, "Y": 0.3403379023075104}, {"X": 0.04607715830206871, "Y": 0.3403370678424835}]}, "Id": "548ef18b-9a4f-41e2-b7cd-e2801f371e4e", "Relationships": [{"Type": "CHILD", "Ids": ["09e75998-b464-4b7e-83df-1dc904335c52", "81cfea15-1382-4ed5-9408-bc17c9bb4862"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "47-3650657", "Geometry": {"BoundingBox": {"Width": 0.2458619326353073, "Height": 0.019138071686029434, "Left": 0.7069084644317627, "Top": 0.32146066427230835}, "Polygon": [{"X": 0.7069085836410522, "Y": 0.32146066427230835}, {"X": 0.9527704119682312, "Y": 0.32146209478378296}, {"X": 0.9527701735496521, "Y": 0.3405987322330475}, {"X": 0.7069084644317627, "Y": 0.3405973017215729}]}, "Id": "100d3819-2dca-497a-bf6f-94ccbb521052", "Relationships": [{"Type": "CHILD", "Ids": ["f8d01cf1-ba48-4013-9dd5-f2c0ff6285fe"]}]}, {"BlockType": "LINE", "Confidence": 99.85112762451172, "Text": "Receipt#:", "Geometry": {"BoundingBox": {"Width": 0.1900765299797058, "Height": 0.024493467062711716, "Left": 0.049800191074609756, "Top": 0.3546672463417053}, "Polygon": [{"X": 0.049800191074609756, "Y": 0.3546672463417053}, {"X": 0.23987670242786407, "Y": 0.35466834902763367}, {"X": 0.23987671732902527, "Y": 0.37916073203086853}, {"X": 0.04980028420686722, "Y": 0.3791596591472626}]}, "Id": "36675d07-eed1-49b9-a91b-2d907ad10683", "Relationships": [{"Type": "CHILD", "Ids": ["05805f96-a14a-4a63-9310-9f69c15aa7a7"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "8675", "Geometry": {"BoundingBox": {"Width": 0.1004616990685463, "Height": 0.019122084602713585, "Left": 0.8520612716674805, "Top": 0.35540488362312317}, "Polygon": [{"X": 0.8520614504814148, "Y": 0.35540488362312317}, {"X": 0.9525229930877686, "Y": 0.3554054796695709}, {"X": 0.9525227546691895, "Y": 0.3745269775390625}, {"X": 0.8520612716674805, "Y": 0.37452641129493713}]}, "Id": "e058e417-a11d-42f0-a6ae-762d035a8068", "Relationships": [{"Type": "CHILD", "Ids": ["abb7aba9-cb44-44e5-b162-3f4e3f8a7cb8"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "Timestamp:", "Geometry": {"BoundingBox": {"Width": 0.24080057442188263, "Height": 0.02449478954076767, "Left": 0.045479558408260345, "Top": 0.3881748914718628}, "Polygon": [{"X": 0.045479558408260345, "Y": 0.3881748914718628}, {"X": 0.2862801253795624, "Y": 0.38817623257637024}, {"X": 0.2862801253795624, "Y": 0.41266965866088867}, {"X": 0.04547965154051781, "Y": 0.4126683473587036}]}, "Id": "a5ea9a0f-c1c7-46e8-a321-7a94b2981f01", "Relationships": [{"Type": "CHILD", "Ids": ["cb41d54a-4ad7-492d-a67a-1217a981c7ac"]}]}, {"BlockType": "LINE", "Confidence": 99.85298156738281, "Text": "09/08/25 2:57 AM", "Geometry": {"BoundingBox": {"Width": 0.3632984161376953, "Height": 0.020319776609539986, "Left": 0.5888515710830688, "Top": 0.38772067427635193}, "Polygon": [{"X": 0.5888516306877136, "Y": 0.38772067427635193}, {"X": 0.9521499872207642, "Y": 0.3877227008342743}, {"X": 0.9521497488021851, "Y": 0.40804046392440796}, {"X": 0.5888515710830688, "Y": 0.4080384373664856}]}, "Id": "5409bcfc-05ac-4d0b-a088-07c8ad9fcf27", "Relationships": [{"Type": "CHILD", "Ids": ["b44d65fb-6c3e-4a11-9562-043a9e5daa2e", "cbb73b9a-28e1-47c1-86be-43877984330f", "967cf8d6-e6a7-4b9d-a8e9-c46e44bafd11"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Payment:", "Geometry": {"BoundingBox": {"Width": 0.1905631124973297, "Height": 0.024538319557905197, "Left": 0.049464259296655655, "Top": 0.4222087264060974}, "Polygon": [{"X": 0.049464259296655655, "Y": 0.4222087264060974}, {"X": 0.24002735316753387, "Y": 0.422209769487381}, {"X": 0.24002736806869507, "Y": 0.4467470347881317}, {"X": 0.049464356154203415, "Y": 0.44674602150917053}]}, "Id": "f97d0cbf-3b54-4f76-9b6c-aaf13e88db5c", "Relationships": [{"Type": "CHILD", "Ids": ["e7c0d3a6-185c-44dd-a926-80dddd3a32ca"]}]}, {"BlockType": "LINE", "Confidence": 98.88392639160156, "Text": "efs", "Geometry": {"BoundingBox": {"Width": 0.06099490448832512, "Height": 0.019741658121347427, "Left": 0.8923768997192383, "Top": 0.42207473516464233}, "Polygon": [{"X": 0.8923770785331726, "Y": 0.42207473516464233}, {"X": 0.9533718228340149, "Y": 0.4220750629901886}, {"X": 0.9533715844154358, "Y": 0.44181638956069946}, {"X": 0.8923768997192383, "Y": 0.4418160617351532}]}, "Id": "f540423f-9e95-4fb5-a7cf-79583a4f789a", "Relationships": [{"Type": "CHILD", "Ids": ["e2fa3920-121c-42fe-93ad-51e7b952ee3f"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Carrier:", "Geometry": {"BoundingBox": {"Width": 0.15113526582717896, "Height": 0.01985451951622963, "Left": 0.04784320294857025, "Top": 0.455807626247406}, "Polygon": [{"X": 0.04784320294857025, "Y": 0.455807626247406}, {"X": 0.198978453874588, "Y": 0.4558084309101105}, {"X": 0.1989784687757492, "Y": 0.47566214203834534}, {"X": 0.04784328117966652, "Y": 0.47566136717796326}]}, "Id": "8a3cac0c-cb96-4e53-9d33-27775377c784", "Relationships": [{"Type": "CHILD", "Ids": ["0e7f1600-98ae-4181-8af5-9b1fce81325d"]}]}, {"BlockType": "LINE", "Confidence": 99.86576843261719, "Text": "AAA FREIGHT", "Geometry": {"BoundingBox": {"Width": 0.3088478744029999, "Height": 0.019430970773100853, "Left": 0.6454589366912842, "Top": 0.45598337054252625}, "Polygon": [{"X": 0.6454590559005737, "Y": 0.45598337054252625}, {"X": 0.9543068408966064, "Y": 0.45598503947257996}, {"X": 0.9543066024780273, "Y": 0.47541433572769165}, {"X": 0.6454589366912842, "Y": 0.4754127264022827}]}, "Id": "ea677f8d-c9d6-4695-8c53-8fbac10482c7", "Relationships": [{"Type": "CHILD", "Ids": ["6cb2d4ea-e05e-4c87-9c44-94b7416c6b18", "896a83db-95a6-4ec5-a620-c172e21b8e26"]}]}, {"BlockType": "LINE", "Confidence": 99.91539764404297, "Text": "Reference #:", "Geometry": {"BoundingBox": {"Width": 0.2592652440071106, "Height": 0.01980244368314743, "Left": 0.05006992071866989, "Top": 0.48919999599456787}, "Polygon": [{"X": 0.05006992071866989, "Y": 0.48919999599456787}, {"X": 0.3093351721763611, "Y": 0.4892013370990753}, {"X": 0.3093351721763611, "Y": 0.5090024471282959}, {"X": 0.05006999522447586, "Y": 0.5090010762214661}]}, "Id": "f4e71c16-35e4-4f33-8e62-fa6e26487760", "Relationships": [{"Type": "CHILD", "Ids": ["7e62c9a1-497f-4bae-b1d8-48c8dba24fc3", "280d5377-ab17-4eaf-8876-00635d97a5e6"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "16976805", "Geometry": {"BoundingBox": {"Width": 0.20210322737693787, "Height": 0.019309956580400467, "Left": 0.7511398196220398, "Top": 0.48983150720596313}, "Polygon": [{"X": 0.7511399984359741, "Y": 0.48983150720596313}, {"X": 0.9532430768013, "Y": 0.4898325502872467}, {"X": 0.953242838382721, "Y": 0.5091414451599121}, {"X": 0.7511398196220398, "Y": 0.5091404318809509}]}, "Id": "51094f61-5b2d-44a4-8cdc-f6ec66e1a714", "Relationships": [{"Type": "CHILD", "Ids": ["191c728f-16ee-4399-a0b5-f58609b66683"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Door:", "Geometry": {"BoundingBox": {"Width": 0.10840518027544022, "Height": 0.019407758489251137, "Left": 0.04958171769976616, "Top": 0.5235329866409302}, "Polygon": [{"X": 0.04958171769976616, "Y": 0.5235329866409302}, {"X": 0.1579868644475937, "Y": 0.5235335826873779}, {"X": 0.15798689424991608, "Y": 0.5429407954216003}, {"X": 0.04958179220557213, "Y": 0.5429401993751526}]}, "Id": "b399dd79-d772-458f-b78c-cf7dd15f4899", "Relationships": [{"Type": "CHILD", "Ids": ["25450f38-72d2-406c-bfa2-b43664fa8907"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "37", "Geometry": {"BoundingBox": {"Width": 0.05021434649825096, "Height": 0.019337017089128494, "Left": 0.9030658602714539, "Top": 0.5238494873046875}, "Polygon": [{"X": 0.9030660390853882, "Y": 0.5238494873046875}, {"X": 0.9532801508903503, "Y": 0.5238497257232666}, {"X": 0.953279972076416, "Y": 0.5431864857673645}, {"X": 0.9030658602714539, "Y": 0.5431862473487854}]}, "Id": "0976c402-f278-4b75-add7-baaec24fa01c", "Relationships": [{"Type": "CHILD", "Ids": ["ce2932a5-91c0-4626-a9e8-40bcce6076a1"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Activity:", "Geometry": {"BoundingBox": {"Width": 0.15650920569896698, "Height": 0.024600371718406677, "Left": 0.0468510203063488, "Top": 0.5572737455368042}, "Polygon": [{"X": 0.0468510203063488, "Y": 0.5572737455368042}, {"X": 0.2033601999282837, "Y": 0.5572745203971863}, {"X": 0.20336022973060608, "Y": 0.5818741321563721}, {"X": 0.04685111716389656, "Y": 0.58187335729599}]}, "Id": "81072f8f-fa28-45e4-93f1-80476c9062c5", "Relationships": [{"Type": "CHILD", "Ids": ["e47539e6-a887-4230-8580-feb20a5ebb9a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total Unloading", "Geometry": {"BoundingBox": {"Width": 0.3230661451816559, "Height": 0.024448875337839127, "Left": 0.04684820771217346, "Top": 0.5907901525497437}, "Polygon": [{"X": 0.04684820771217346, "Y": 0.5907901525497437}, {"X": 0.36991435289382935, "Y": 0.5907917022705078}, {"X": 0.36991432309150696, "Y": 0.6152390241622925}, {"X": 0.04684830084443092, "Y": 0.6152374744415283}]}, "Id": "54172b84-15d3-41ae-b8ae-05da1ad82b35", "Relationships": [{"Type": "CHILD", "Ids": ["62b2b77b-04bc-4927-bf47-1d53e3ac0d76", "614542db-af88-4dd2-89a5-53fec04e92e2"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$100.00", "Geometry": {"BoundingBox": {"Width": 0.16582074761390686, "Height": 0.023462656885385513, "Left": 0.7872930765151978, "Top": 0.5891531705856323}, "Polygon": [{"X": 0.7872932553291321, "Y": 0.5891531705856323}, {"X": 0.953113853931427, "Y": 0.5891540050506592}, {"X": 0.9531136155128479, "Y": 0.6126158833503723}, {"X": 0.7872930765151978, "Y": 0.6126150488853455}]}, "Id": "afbd259c-7a6c-441f-98b4-f3d730f217de", "Relationships": [{"Type": "CHILD", "Ids": ["3518d26f-62c8-46d3-bd88-69c9318d1aec"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Fee", "Geometry": {"BoundingBox": {"Width": 0.07624492794275284, "Height": 0.019595488905906677, "Left": 0.04958958551287651, "Top": 0.6245967745780945}, "Polygon": [{"X": 0.04958958551287651, "Y": 0.6245967745780945}, {"X": 0.12583446502685547, "Y": 0.6245971322059631}, {"X": 0.12583450973033905, "Y": 0.6441922187805176}, {"X": 0.04958966001868248, "Y": 0.6441918611526489}]}, "Id": "ceae701f-6a36-423a-a5fd-f0858ef38dcf", "Relationships": [{"Type": "CHILD", "Ids": ["cfe242cd-2a80-4efa-8e47-bda7ac5f5e09"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Amount:", "Geometry": {"BoundingBox": {"Width": 0.16983892023563385, "Height": 0.018983161076903343, "Left": 0.0468769297003746, "Top": 0.6923843622207642}, "Polygon": [{"X": 0.0468769297003746, "Y": 0.6923843622207642}, {"X": 0.21671582758426666, "Y": 0.6923851370811462}, {"X": 0.21671584248542786, "Y": 0.7113675475120544}, {"X": 0.04687700420618057, "Y": 0.7113667726516724}]}, "Id": "345e3b30-f5d4-4f92-96f9-9333c234e951", "Relationships": [{"Type": "CHILD", "Ids": ["d534781e-729f-442a-a534-e5195ed71d1f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$100.00", "Geometry": {"BoundingBox": {"Width": 0.16634094715118408, "Height": 0.02337288111448288, "Left": 0.7871142029762268, "Top": 0.6907019019126892}, "Polygon": [{"X": 0.7871143817901611, "Y": 0.6907019019126892}, {"X": 0.9534551501274109, "Y": 0.6907026767730713}, {"X": 0.9534549117088318, "Y": 0.7140747904777527}, {"X": 0.7871142029762268, "Y": 0.7140740752220154}]}, "Id": "30e9e1c7-c538-4db9-b65d-45719afe258b", "Relationships": [{"Type": "CHILD", "Ids": ["58bc2acd-8ea1-433a-b0ca-e85e28a27e74"]}]}, {"BlockType": "LINE", "Confidence": 99.80069732666016, "Text": "Convenience", "Geometry": {"BoundingBox": {"Width": 0.26756125688552856, "Height": 0.019818374887108803, "Left": 0.04831015318632126, "Top": 0.7258783578872681}, "Polygon": [{"X": 0.04831015318632126, "Y": 0.7258783578872681}, {"X": 0.3158714175224304, "Y": 0.7258795499801636}, {"X": 0.31587138772010803, "Y": 0.7456967830657959}, {"X": 0.04831022769212723, "Y": 0.7456955909729004}]}, "Id": "99d7b3ef-30fb-4808-8143-5bcdc467f899", "Relationships": [{"Type": "CHILD", "Ids": ["cbae235a-d493-4adf-950f-df40e1fc7d2e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$4.00", "Geometry": {"BoundingBox": {"Width": 0.11390580236911774, "Height": 0.023045193403959274, "Left": 0.8387858867645264, "Top": 0.7248877286911011}, "Polygon": [{"X": 0.8387860655784607, "Y": 0.7248877286911011}, {"X": 0.9526916742324829, "Y": 0.7248882055282593}, {"X": 0.9526914358139038, "Y": 0.7479329109191895}, {"X": 0.8387858867645264, "Y": 0.7479324340820312}]}, "Id": "da78bb1c-f0b5-4db9-bfd0-2a01d495bb1d", "Relationships": [{"Type": "CHILD", "Ids": ["0b2f8e77-d901-4a1b-b26e-18a07f42f4ef"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Fee:", "Geometry": {"BoundingBox": {"Width": 0.086802177131176, "Height": 0.019618339836597443, "Left": 0.04970268905162811, "Top": 0.7599744200706482}, "Polygon": [{"X": 0.04970268905162811, "Y": 0.7599744200706482}, {"X": 0.13650481402873993, "Y": 0.7599747776985168}, {"X": 0.1365048587322235, "Y": 0.779592752456665}, {"X": 0.04970276355743408, "Y": 0.7795923948287964}]}, "Id": "c6296fb1-396f-454d-b10b-9721aa6e6cb2", "Relationships": [{"Type": "CHILD", "Ids": ["ed2b9b88-3225-4f9f-a090-06107f6e711f"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Total:", "Geometry": {"BoundingBox": {"Width": 0.11439317464828491, "Height": 0.01941213198006153, "Left": 0.046483006328344345, "Top": 0.7931845784187317}, "Polygon": [{"X": 0.046483006328344345, "Y": 0.7931845784187317}, {"X": 0.16087613999843597, "Y": 0.7931850552558899}, {"X": 0.16087618470191956, "Y": 0.8125967383384705}, {"X": 0.046483080834150314, "Y": 0.8125962615013123}]}, "Id": "4a689e67-293b-45d9-a174-e68341983825", "Relationships": [{"Type": "CHILD", "Ids": ["6e574b60-0282-4e78-8793-4d269dc994d3"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$104.00", "Geometry": {"BoundingBox": {"Width": 0.16670654714107513, "Height": 0.02260495163500309, "Left": 0.7869665622711182, "Top": 0.7924583554267883}, "Polygon": [{"X": 0.7869667410850525, "Y": 0.7924583554267883}, {"X": 0.9536731243133545, "Y": 0.7924590110778809}, {"X": 0.9536728858947754, "Y": 0.8150632977485657}, {"X": 0.7869665622711182, "Y": 0.8150625824928284}]}, "Id": "76e6c3f4-c7a9-4d67-9c64-ea4b0b3253d7", "Relationships": [{"Type": "CHILD", "Ids": ["5b4b588e-e614-46a1-bfbc-c3eaccdf3423"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>'s", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14752157032489777, "Height": 0.024369249120354652, "Left": 0.16040676832199097, "Top": 0.05086463689804077}, "Polygon": [{"X": 0.16040676832199097, "Y": 0.05086463689804077}, {"X": 0.3079283535480499, "Y": 0.050865642726421356}, {"X": 0.3079283535480499, "Y": 0.07523388415575027}, {"X": 0.16040682792663574, "Y": 0.07523289322853088}]}, "Id": "a6662a26-7446-42ff-ad52-6c85cef8354c"}, {"BlockType": "WORD", "Confidence": 99.60140228271484, "Text": "Arizona", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15658710896968842, "Height": 0.019506826996803284, "Left": 0.321397602558136, "Top": 0.05088391900062561}, "Polygon": [{"X": 0.321397602558136, "Y": 0.05088391900062561}, {"X": 0.4779846966266632, "Y": 0.05088498815894127}, {"X": 0.47798463702201843, "Y": 0.0703907459974289}, {"X": 0.321397602558136, "Y": 0.07038968801498413}]}, "Id": "c6f40eb6-d9eb-4c52-99d7-826cebbc6207"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08091825246810913, "Height": 0.01937464065849781, "Left": 0.4937080144882202, "Top": 0.05087439343333244}, "Polygon": [{"X": 0.493708074092865, "Y": 0.05087439343333244}, {"X": 0.5746262669563293, "Y": 0.050874944776296616}, {"X": 0.5746262073516846, "Y": 0.0702490359544754}, {"X": 0.4937080144882202, "Y": 0.07024848461151123}]}, "Id": "7a5ade4f-9fcd-4ac7-89da-77b14f0dc1c6"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "dba", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0760532021522522, "Height": 0.01934567093849182, "Left": 0.5896593332290649, "Top": 0.05099813640117645}, "Polygon": [{"X": 0.5896593928337097, "Y": 0.05099813640117645}, {"X": 0.6657125353813171, "Y": 0.05099865421652794}, {"X": 0.6657124161720276, "Y": 0.07034380733966827}, {"X": 0.5896593332290649, "Y": 0.07034329324960709}]}, "Id": "d3769fb9-2e22-4475-8ea5-1b1058c9efbf"}, {"BlockType": "WORD", "Confidence": 99.52168273925781, "Text": "<PERSON><PERSON><PERSON>'", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15992191433906555, "Height": 0.019380949437618256, "Left": 0.6816020011901855, "Top": 0.05096696317195892}, "Polygon": [{"X": 0.6816021203994751, "Y": 0.05096696317195892}, {"X": 0.8415238857269287, "Y": 0.05096805468201637}, {"X": 0.8415237069129944, "Y": 0.07034791260957718}, {"X": 0.6816020011901855, "Y": 0.07034683227539062}]}, "Id": "aec54c81-91ba-469d-b296-41e1e0118fc5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "480-895-9350", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.28750377893447876, "Height": 0.019217224791646004, "Left": 0.3560301959514618, "Top": 0.11872474104166031}, "Polygon": [{"X": 0.3560302257537842, "Y": 0.11872474104166031}, {"X": 0.6435340046882629, "Y": 0.11872663348913193}, {"X": 0.6435338854789734, "Y": 0.13794197142124176}, {"X": 0.3560301959514618, "Y": 0.13794009387493134}]}, "Id": "f2100ba1-80cf-4313-88d0-87cbaf1f306e"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Warehouse:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24789227545261383, "Height": 0.01979471929371357, "Left": 0.046417102217674255, "Top": 0.185542494058609}, "Polygon": [{"X": 0.046417102217674255, "Y": 0.185542494058609}, {"X": 0.2943093776702881, "Y": 0.18554407358169556}, {"X": 0.2943093776702881, "Y": 0.20533721148967743}, {"X": 0.046417176723480225, "Y": 0.20533566176891327}]}, "Id": "ee22da20-53fc-40d2-84e5-85c5c8cc8bfc"}, {"BlockType": "WORD", "Confidence": 56.6932373046875, "Text": "Bashasâ€ᵀ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2510383129119873, "Height": 0.019641971215605736, "Left": 0.4880591928958893, "Top": 0.18524140119552612}, "Polygon": [{"X": 0.48805925250053406, "Y": 0.18524140119552612}, {"X": 0.7390974760055542, "Y": 0.18524299561977386}, {"X": 0.7390973567962646, "Y": 0.2048833817243576}, {"X": 0.4880591928958893, "Y": 0.20488180220127106}]}, "Id": "a860649a-662d-4f38-af04-22782e9da453"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Receiving", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19875894486904144, "Height": 0.0246796365827322, "Left": 0.7588476538658142, "Top": 0.18540288507938385}, "Polygon": [{"X": 0.7588478326797485, "Y": 0.18540288507938385}, {"X": 0.9576066136360168, "Y": 0.18540413677692413}, {"X": 0.9576063752174377, "Y": 0.2100825160741806}, {"X": 0.7588476538658142, "Y": 0.2100812792778015}]}, "Id": "0af701a4-b8e3-4e96-81c4-f17cd9fba594"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "(Dry)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09900682419538498, "Height": 0.0250411219894886, "Left": 0.8526988625526428, "Top": 0.219021737575531}, "Polygon": [{"X": 0.8526991009712219, "Y": 0.219021737575531}, {"X": 0.9517056941986084, "Y": 0.21902234852313995}, {"X": 0.9517054557800293, "Y": 0.2440628558397293}, {"X": 0.8526988625526428, "Y": 0.24406225979328156}]}, "Id": "84238572-001a-4b55-baee-d2a272591885"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Warehouse", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.23628418147563934, "Height": 0.019928626716136932, "Left": 0.04684007167816162, "Top": 0.2533677816390991}, "Polygon": [{"X": 0.04684007167816162, "Y": 0.2533677816390991}, {"X": 0.28312426805496216, "Y": 0.25336921215057373}, {"X": 0.28312426805496216, "Y": 0.27329641580581665}, {"X": 0.04684014990925789, "Y": 0.27329498529434204}]}, "Id": "a4749fc8-8547-433c-bc40-18ad3ca36ece"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "480-940-9880", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2869906723499298, "Height": 0.019175546243786812, "Left": 0.6649048924446106, "Top": 0.25401216745376587}, "Polygon": [{"X": 0.6649050116539001, "Y": 0.25401216745376587}, {"X": 0.951895534992218, "Y": 0.25401392579078674}, {"X": 0.9518953561782837, "Y": 0.2731877267360687}, {"X": 0.6649048924446106, "Y": 0.27318599820137024}]}, "Id": "7e143ed2-b6af-4046-af0a-ecfbeadbdb3e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Phone:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14214450120925903, "Height": 0.019697017967700958, "Left": 0.04927556589245796, "Top": 0.2866823077201843}, "Polygon": [{"X": 0.04927556589245796, "Y": 0.2866823077201843}, {"X": 0.1914200484752655, "Y": 0.2866831421852112}, {"X": 0.1914200782775879, "Y": 0.3063793182373047}, {"X": 0.04927564039826393, "Y": 0.30637848377227783}]}, "Id": "63126d70-9a89-41e7-a096-5b1add03c0d7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07843861728906631, "Height": 0.019395163282752037, "Left": 0.04607708379626274, "Top": 0.32094234228134155}, "Polygon": [{"X": 0.04607708379626274, "Y": 0.32094234228134155}, {"X": 0.12451564520597458, "Y": 0.32094281911849976}, {"X": 0.12451569736003876, "Y": 0.34033751487731934}, {"X": 0.04607715830206871, "Y": 0.3403370678424835}]}, "Id": "09e75998-b464-4b7e-83df-1dc904335c52"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052933868020772934, "Height": 0.01930973306298256, "Left": 0.14005598425865173, "Top": 0.320950984954834}, "Polygon": [{"X": 0.14005598425865173, "Y": 0.320950984954834}, {"X": 0.19298982620239258, "Y": 0.32095128297805786}, {"X": 0.19298985600471497, "Y": 0.34026071429252625}, {"X": 0.14005602896213531, "Y": 0.34026041626930237}]}, "Id": "81cfea15-1382-4ed5-9408-bc17c9bb4862"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "47-3650657", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2458619326353073, "Height": 0.019138071686029434, "Left": 0.7069084644317627, "Top": 0.32146066427230835}, "Polygon": [{"X": 0.7069085836410522, "Y": 0.32146066427230835}, {"X": 0.9527704119682312, "Y": 0.32146209478378296}, {"X": 0.9527701735496521, "Y": 0.3405987322330475}, {"X": 0.7069084644317627, "Y": 0.3405973017215729}]}, "Id": "f8d01cf1-ba48-4013-9dd5-f2c0ff6285fe"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Receipt#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1900765299797058, "Height": 0.024493467062711716, "Left": 0.049800191074609756, "Top": 0.3546672463417053}, "Polygon": [{"X": 0.049800191074609756, "Y": 0.3546672463417053}, {"X": 0.23987670242786407, "Y": 0.35466834902763367}, {"X": 0.23987671732902527, "Y": 0.37916073203086853}, {"X": 0.04980028420686722, "Y": 0.3791596591472626}]}, "Id": "05805f96-a14a-4a63-9310-9f69c15aa7a7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "8675", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1004616990685463, "Height": 0.019122084602713585, "Left": 0.8520612716674805, "Top": 0.35540488362312317}, "Polygon": [{"X": 0.8520614504814148, "Y": 0.35540488362312317}, {"X": 0.9525229930877686, "Y": 0.3554054796695709}, {"X": 0.9525227546691895, "Y": 0.3745269775390625}, {"X": 0.8520612716674805, "Y": 0.37452641129493713}]}, "Id": "abb7aba9-cb44-44e5-b162-3f4e3f8a7cb8"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Timestamp:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24080057442188263, "Height": 0.02449478954076767, "Left": 0.045479558408260345, "Top": 0.3881748914718628}, "Polygon": [{"X": 0.045479558408260345, "Y": 0.3881748914718628}, {"X": 0.2862801253795624, "Y": 0.38817623257637024}, {"X": 0.2862801253795624, "Y": 0.41266965866088867}, {"X": 0.04547965154051781, "Y": 0.4126683473587036}]}, "Id": "cb41d54a-4ad7-492d-a67a-1217a981c7ac"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "09/08/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17867998778820038, "Height": 0.020318757742643356, "Left": 0.5888515710830688, "Top": 0.38772067427635193}, "Polygon": [{"X": 0.5888516306877136, "Y": 0.38772067427635193}, {"X": 0.7675315141677856, "Y": 0.3877216875553131}, {"X": 0.7675313949584961, "Y": 0.4080394208431244}, {"X": 0.5888515710830688, "Y": 0.4080384373664856}]}, "Id": "b44d65fb-6c3e-4a11-9562-043a9e5daa2e"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "2:57", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08958885818719864, "Height": 0.019419021904468536, "Left": 0.781663179397583, "Top": 0.3883362114429474}, "Polygon": [{"X": 0.7816633582115173, "Y": 0.3883362114429474}, {"X": 0.8712520599365234, "Y": 0.388336718082428}, {"X": 0.8712518811225891, "Y": 0.4077552556991577}, {"X": 0.781663179397583, "Y": 0.4077547490596771}]}, "Id": "cbb73b9a-28e1-47c1-86be-43877984330f"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "AM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06761488318443298, "Height": 0.01950409635901451, "Left": 0.8845350742340088, "Top": 0.3880592882633209}, "Polygon": [{"X": 0.8845352530479431, "Y": 0.3880592882633209}, {"X": 0.9521499872207642, "Y": 0.3880596458911896}, {"X": 0.9521497488021851, "Y": 0.40756338834762573}, {"X": 0.8845350742340088, "Y": 0.4075630009174347}]}, "Id": "967cf8d6-e6a7-4b9d-a8e9-c46e44bafd11"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Payment:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1905631124973297, "Height": 0.024538319557905197, "Left": 0.049464259296655655, "Top": 0.4222087264060974}, "Polygon": [{"X": 0.049464259296655655, "Y": 0.4222087264060974}, {"X": 0.24002735316753387, "Y": 0.422209769487381}, {"X": 0.24002736806869507, "Y": 0.4467470347881317}, {"X": 0.049464356154203415, "Y": 0.44674602150917053}]}, "Id": "e7c0d3a6-185c-44dd-a926-80dddd3a32ca"}, {"BlockType": "WORD", "Confidence": 98.88392639160156, "Text": "efs", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06099490448832512, "Height": 0.019741658121347427, "Left": 0.8923768997192383, "Top": 0.42207473516464233}, "Polygon": [{"X": 0.8923770785331726, "Y": 0.42207473516464233}, {"X": 0.9533718228340149, "Y": 0.4220750629901886}, {"X": 0.9533715844154358, "Y": 0.44181638956069946}, {"X": 0.8923768997192383, "Y": 0.4418160617351532}]}, "Id": "e2fa3920-121c-42fe-93ad-51e7b952ee3f"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Carrier:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15113526582717896, "Height": 0.01985451951622963, "Left": 0.04784320294857025, "Top": 0.455807626247406}, "Polygon": [{"X": 0.04784320294857025, "Y": 0.455807626247406}, {"X": 0.198978453874588, "Y": 0.4558084309101105}, {"X": 0.1989784687757492, "Y": 0.47566214203834534}, {"X": 0.04784328117966652, "Y": 0.47566136717796326}]}, "Id": "0e7f1600-98ae-4181-8af5-9b1fce81325d"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "AAA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09249217808246613, "Height": 0.01937028579413891, "Left": 0.6454589366912842, "Top": 0.45598337054252625}, "Polygon": [{"X": 0.6454590559005737, "Y": 0.45598337054252625}, {"X": 0.7379510998725891, "Y": 0.45598387718200684}, {"X": 0.7379509806632996, "Y": 0.4753536581993103}, {"X": 0.6454589366912842, "Y": 0.4753531813621521}]}, "Id": "6cb2d4ea-e05e-4c87-9c44-94b7416c6b18"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FREIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.20122955739498138, "Height": 0.019367940723896027, "Left": 0.7530772686004639, "Top": 0.4560464024543762}, "Polygon": [{"X": 0.7530773878097534, "Y": 0.4560464024543762}, {"X": 0.9543068408966064, "Y": 0.4560474753379822}, {"X": 0.9543066024780273, "Y": 0.47541433572769165}, {"X": 0.7530772686004639, "Y": 0.4754132926464081}]}, "Id": "896a83db-95a6-4ec5-a620-c172e21b8e26"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Reference", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.20974142849445343, "Height": 0.019802188500761986, "Left": 0.05006992071866989, "Top": 0.48919999599456787}, "Polygon": [{"X": 0.05006992071866989, "Y": 0.48919999599456787}, {"X": 0.2598113417625427, "Y": 0.48920106887817383}, {"X": 0.2598113417625427, "Y": 0.509002149105072}, {"X": 0.05006999522447586, "Y": 0.5090010762214661}]}, "Id": "7e62c9a1-497f-4bae-b1d8-48c8dba24fc3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03635659068822861, "Height": 0.018898995593190193, "Left": 0.2729785740375519, "Top": 0.4900316596031189}, "Polygon": [{"X": 0.2729785740375519, "Y": 0.4900316596031189}, {"X": 0.3093351721763611, "Y": 0.4900318384170532}, {"X": 0.3093351721763611, "Y": 0.5089306831359863}, {"X": 0.27297860383987427, "Y": 0.5089304447174072}]}, "Id": "280d5377-ab17-4eaf-8876-00635d97a5e6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "16976805", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.20210322737693787, "Height": 0.019309956580400467, "Left": 0.7511398196220398, "Top": 0.48983150720596313}, "Polygon": [{"X": 0.7511399984359741, "Y": 0.48983150720596313}, {"X": 0.9532430768013, "Y": 0.4898325502872467}, {"X": 0.953242838382721, "Y": 0.5091414451599121}, {"X": 0.7511398196220398, "Y": 0.5091404318809509}]}, "Id": "191c728f-16ee-4399-a0b5-f58609b66683"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Door:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10840518027544022, "Height": 0.019407758489251137, "Left": 0.04958171769976616, "Top": 0.5235329866409302}, "Polygon": [{"X": 0.04958171769976616, "Y": 0.5235329866409302}, {"X": 0.1579868644475937, "Y": 0.5235335826873779}, {"X": 0.15798689424991608, "Y": 0.5429407954216003}, {"X": 0.04958179220557213, "Y": 0.5429401993751526}]}, "Id": "25450f38-72d2-406c-bfa2-b43664fa8907"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "37", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05021434649825096, "Height": 0.019337017089128494, "Left": 0.9030658602714539, "Top": 0.5238494873046875}, "Polygon": [{"X": 0.9030660390853882, "Y": 0.5238494873046875}, {"X": 0.9532801508903503, "Y": 0.5238497257232666}, {"X": 0.953279972076416, "Y": 0.5431864857673645}, {"X": 0.9030658602714539, "Y": 0.5431862473487854}]}, "Id": "ce2932a5-91c0-4626-a9e8-40bcce6076a1"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Activity:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15650920569896698, "Height": 0.024600371718406677, "Left": 0.0468510203063488, "Top": 0.5572737455368042}, "Polygon": [{"X": 0.0468510203063488, "Y": 0.5572737455368042}, {"X": 0.2033601999282837, "Y": 0.5572745203971863}, {"X": 0.20336022973060608, "Y": 0.5818741321563721}, {"X": 0.04685111716389656, "Y": 0.58187335729599}]}, "Id": "e47539e6-a887-4230-8580-feb20a5ebb9a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10121646523475647, "Height": 0.01939411647617817, "Left": 0.04684820771217346, "Top": 0.5908312797546387}, "Polygon": [{"X": 0.04684820771217346, "Y": 0.5908312797546387}, {"X": 0.14806462824344635, "Y": 0.5908317565917969}, {"X": 0.14806467294692993, "Y": 0.6102253794670105}, {"X": 0.04684828221797943, "Y": 0.6102249026298523}]}, "Id": "62b2b77b-04bc-4927-bf47-1d53e3ac0d76"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Unloading", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.20421236753463745, "Height": 0.024448299780488014, "Left": 0.1657019853591919, "Top": 0.5907906889915466}, "Polygon": [{"X": 0.1657019853591919, "Y": 0.5907906889915466}, {"X": 0.36991435289382935, "Y": 0.5907917022705078}, {"X": 0.36991432309150696, "Y": 0.6152390241622925}, {"X": 0.16570204496383667, "Y": 0.6152380108833313}]}, "Id": "614542db-af88-4dd2-89a5-53fec04e92e2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$100.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16582074761390686, "Height": 0.023462656885385513, "Left": 0.7872930765151978, "Top": 0.5891531705856323}, "Polygon": [{"X": 0.7872932553291321, "Y": 0.5891531705856323}, {"X": 0.953113853931427, "Y": 0.5891540050506592}, {"X": 0.9531136155128479, "Y": 0.6126158833503723}, {"X": 0.7872930765151978, "Y": 0.6126150488853455}]}, "Id": "3518d26f-62c8-46d3-bd88-69c9318d1aec"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Fee", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07624492794275284, "Height": 0.019595488905906677, "Left": 0.04958958551287651, "Top": 0.6245967745780945}, "Polygon": [{"X": 0.04958958551287651, "Y": 0.6245967745780945}, {"X": 0.12583446502685547, "Y": 0.6245971322059631}, {"X": 0.12583450973033905, "Y": 0.6441922187805176}, {"X": 0.04958966001868248, "Y": 0.6441918611526489}]}, "Id": "cfe242cd-2a80-4efa-8e47-bda7ac5f5e09"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Amount:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16983892023563385, "Height": 0.018983161076903343, "Left": 0.0468769297003746, "Top": 0.6923843622207642}, "Polygon": [{"X": 0.0468769297003746, "Y": 0.6923843622207642}, {"X": 0.21671582758426666, "Y": 0.6923851370811462}, {"X": 0.21671584248542786, "Y": 0.7113675475120544}, {"X": 0.04687700420618057, "Y": 0.7113667726516724}]}, "Id": "d534781e-729f-442a-a534-e5195ed71d1f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$100.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16634094715118408, "Height": 0.02337288111448288, "Left": 0.7871142029762268, "Top": 0.6907019019126892}, "Polygon": [{"X": 0.7871143817901611, "Y": 0.6907019019126892}, {"X": 0.9534551501274109, "Y": 0.6907026767730713}, {"X": 0.9534549117088318, "Y": 0.7140747904777527}, {"X": 0.7871142029762268, "Y": 0.7140740752220154}]}, "Id": "58bc2acd-8ea1-433a-b0ca-e85e28a27e74"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Convenience", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.26756125688552856, "Height": 0.019818374887108803, "Left": 0.04831015318632126, "Top": 0.7258783578872681}, "Polygon": [{"X": 0.04831015318632126, "Y": 0.7258783578872681}, {"X": 0.3158714175224304, "Y": 0.7258795499801636}, {"X": 0.31587138772010803, "Y": 0.7456967830657959}, {"X": 0.04831022769212723, "Y": 0.7456955909729004}]}, "Id": "cbae235a-d493-4adf-950f-df40e1fc7d2e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$4.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11390580236911774, "Height": 0.023045193403959274, "Left": 0.8387858867645264, "Top": 0.7248877286911011}, "Polygon": [{"X": 0.8387860655784607, "Y": 0.7248877286911011}, {"X": 0.9526916742324829, "Y": 0.7248882055282593}, {"X": 0.9526914358139038, "Y": 0.7479329109191895}, {"X": 0.8387858867645264, "Y": 0.7479324340820312}]}, "Id": "0b2f8e77-d901-4a1b-b26e-18a07f42f4ef"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Fee:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.086802177131176, "Height": 0.019618339836597443, "Left": 0.04970268905162811, "Top": 0.7599744200706482}, "Polygon": [{"X": 0.04970268905162811, "Y": 0.7599744200706482}, {"X": 0.13650481402873993, "Y": 0.7599747776985168}, {"X": 0.1365048587322235, "Y": 0.779592752456665}, {"X": 0.04970276355743408, "Y": 0.7795923948287964}]}, "Id": "ed2b9b88-3225-4f9f-a090-06107f6e711f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Total:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11439317464828491, "Height": 0.01941213198006153, "Left": 0.046483006328344345, "Top": 0.7931845784187317}, "Polygon": [{"X": 0.046483006328344345, "Y": 0.7931845784187317}, {"X": 0.16087613999843597, "Y": 0.7931850552558899}, {"X": 0.16087618470191956, "Y": 0.8125967383384705}, {"X": 0.046483080834150314, "Y": 0.8125962615013123}]}, "Id": "6e574b60-0282-4e78-8793-4d269dc994d3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$104.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16670654714107513, "Height": 0.02260495163500309, "Left": 0.7869665622711182, "Top": 0.7924583554267883}, "Polygon": [{"X": 0.7869667410850525, "Y": 0.7924583554267883}, {"X": 0.9536731243133545, "Y": 0.7924590110778809}, {"X": 0.9536728858947754, "Y": 0.8150632977485657}, {"X": 0.7869665622711182, "Y": 0.8150625824928284}]}, "Id": "5b4b588e-e614-46a1-bfbc-c3eaccdf3423"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "e9f77309-72ed-44eb-a0c9-3af908ac50f3", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "e9f77309-72ed-44eb-a0c9-3af908ac50f3", "content-type": "application/x-amz-json-1.1", "content-length": "40565", "date": "Thu, 18 Sep 2025 10:34:00 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "dacb8c3e-2f62-4862-8a7d-d924fd9c0916", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 10:34:02 GMT", "content-type": "application/json", "content-length": "1380", "connection": "keep-alive", "x-amzn-requestid": "dacb8c3e-2f62-4862-8a7d-d924fd9c0916"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to analyze the page. It looks like a receipt for unloading, with title \"Receipt\" maybe. It contains \"Receipt# 8675\", \"Payment: efs\", \"Carrier: AAA FREIGHT\", \"Total Unloading $100.00 Fee ...\". This appears to be a Lumper Receipt? According to definitions: lumper_receipt is invoice or receipt for services provided (loading/unloading labor). Keywords: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount. This page indeed includes \"Door: 37\", \"Total Unloading\", \"Fee\", \"Convenience Fee\". It includes \"Carrier: AAA FREIGHT\". So it's likely a Lumper Receipt. Not sure if \"Receipt#\" qualifies. But lumper receipt is a subset of invoice. But invoice has different keywords: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From. This page does not mention \"Invoice\". So lumper receipt is appropriate. So doc_type: lumper_receipt. Page 1. No continuation. So output: list with one entry."}}}, {"toolUse": {"toolUseId": "tooluse_Y194aY4fTZGgkYUdJRxxiQ", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2278, "outputTokens": 269, "totalTokens": 2547}, "metrics": {"latencyMs": 1195}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/afb69ac1_21_lumper_receipt_8.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n<PERSON>ley's Arizona LLC dba Bashas'\n480-895-9350\nWarehouse:\nBashasâ€ᵀ Receiving\n(Dry)\nWarehouse\n480-940-9880\nPhone:\nTax ID:\n47-3650657\nReceipt#:\n8675\nTimestamp:\n09/08/25 2:57 AM\nPayment:\nefs\nCarrier:\nAAA FREIGHT\nReference #:\n16976805\nDoor:\n37\nActivity:\nTotal Unloading\n$100.00\nFee\nAmount:\n$100.00\nConvenience\n$4.00\nFee:\nTotal:\n$104.00\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}