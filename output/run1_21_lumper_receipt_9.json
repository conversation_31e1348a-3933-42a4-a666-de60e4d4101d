{"classification_result": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 3.8435799069702625e-05, "Y": 0.0}, {"X": 1.0, "Y": 5.202933905934515e-08}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "6d4d8b9a-1dd0-442f-98ae-3c6697638ace", "Relationships": [{"Type": "CHILD", "Ids": ["452a319e-e31a-427f-8763-193c8770877c", "780e57b6-7387-4e9a-96f2-94a3c79b1cee", "963c23f4-e415-4d1b-a04c-970193950eac", "c5c31881-2829-4a8a-b953-c2e779f2b63d", "3f4cddcf-4b84-4257-868b-34888bfe2d7f", "e5f3969a-8fef-48d6-9740-cbe91f4db5de", "803d28de-7cd5-4975-b5fa-529a53408a4b", "a1ca6950-33ce-4f4b-a088-4b05888e069d", "35f416bb-4143-4d0e-b49f-e498951c0a61", "d4b843d5-5ee3-466f-b819-caa1c6f6aea3", "09d77da5-74ae-4626-a557-0a8671129618", "56a625bf-87dd-4cc9-bcf8-998d428dabd3", "6f3484c4-6de4-4e29-99ce-b65617952ce4", "613b06de-82a7-4dbe-8a99-037b513a248e", "27f3ec88-5d52-4aad-9098-004af67b5227", "fc8dacc9-9cba-4e03-b58a-0969a745ce29", "9ff8e939-fbcb-47e6-b0a9-ac887888d3ff", "b0eb703f-aabf-4165-92af-80b9d5a56060", "e7c33a05-a83f-4e57-81f7-9ac340e0ab5b", "7b05040c-e5be-49c6-8830-7bbcdfc9e15d", "f893f862-6692-49ec-9464-d1a5a994a1e3", "0fa5210b-e04d-4352-a077-5be15ff8a375", "f5d56444-089b-48ad-bdd3-5af895a18b9b", "1502f895-adbd-4303-bcfb-9dcc32dcced4", "2dc649bd-5956-4990-845a-f0fabc200b8a", "f06702ad-da82-4018-b8ba-b94b216b8aa2", "15a16f30-447f-46f2-a1cd-fea4f5daac7f", "03144b6b-9797-463d-81e3-05c5d91fa82e", "425d58ec-b348-4f3a-b825-9ecc97265943", "88a0c332-4b34-451d-b444-34057b534030", "d8924c31-1130-4105-8dd1-f09703d9b53d", "61d7fdf5-7562-4f1d-bbba-c11e1ac225a7", "ac601375-d56c-4d4c-8e54-b7fb37588d1f", "80266cc5-ddd9-43ab-b051-7fa4a8356ca3", "54c63b9d-15b1-4eda-8d90-e614209c7e44", "ee97b0cb-79db-42e7-a98e-82e09746ca53", "cd38934d-d2bf-4578-88fb-45b6ef48b614", "dd624146-2e41-49b8-bf0e-977bdfb8e98d", "6ab8296c-c0bb-465c-bb09-faaec2236643", "907af989-af5b-4615-b44f-8aabf6bd06d2", "75d203b2-c995-463f-b0a5-7a897a3728d4", "d659df31-8cde-4e3b-b1a4-aabc84ea48a7", "18ea8ff4-330b-439e-b16e-2dbbe3d30c06", "b506b943-7295-4e63-80ef-04bafffc2b61", "341603b5-53c4-46be-a25c-95e14f61dca5", "385673de-076c-4aec-81a2-42ae9c9bf69b", "aea55959-f72a-4184-ac9b-18b6c19134e9", "b3415434-e6bc-4b48-a18e-571085362efe", "ab0a09ca-7faf-4214-956b-411d3c61b8be", "fb54d900-1629-44fa-90b8-c1c0bcb12ef1", "ead4aa42-2ce0-413c-b303-9422fa70feb3", "d691aed7-2c91-4103-81d7-29507736b532", "e829288f-fb9e-4066-b1ce-b5a352f1b8c7"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Capstone Logistics", "Geometry": {"BoundingBox": {"Width": 0.33570319414138794, "Height": 0.03212166950106621, "Left": 0.27555519342422485, "Top": 0.06101561337709427}, "Polygon": [{"X": 0.27555781602859497, "Y": 0.06101561337709427}, {"X": 0.6112583875656128, "Y": 0.06103413179516792}, {"X": 0.611255943775177, "Y": 0.09313728660345078}, {"X": 0.27555519342422485, "Y": 0.09312023222446442}]}, "Id": "452a319e-e31a-427f-8763-193c8770877c", "Relationships": [{"Type": "CHILD", "Ids": ["2299e846-ca6b-4655-b5a1-bfe78494566c", "ad50dcc1-8bdc-48ad-a33a-57374efb4c61"]}]}, {"BlockType": "LINE", "Confidence": 99.87779998779297, "Text": "30 TECHNOLOGY PKWY SOUTH SUITE 200", "Geometry": {"BoundingBox": {"Width": 0.3456668555736542, "Height": 0.010544383898377419, "Left": 0.2704474627971649, "Top": 0.113766610622406}, "Polygon": [{"X": 0.27044832706451416, "Y": 0.113766610622406}, {"X": 0.6161143183708191, "Y": 0.11378319561481476}, {"X": 0.616113543510437, "Y": 0.12431099265813828}, {"X": 0.2704474627971649, "Y": 0.12429490685462952}]}, "Id": "780e57b6-7387-4e9a-96f2-94a3c79b1cee", "Relationships": [{"Type": "CHILD", "Ids": ["3f123c90-b03e-4046-a067-7790712da5f1", "ce9c4d9c-0c7d-4f5c-87ec-de2ab67f093d", "2f1f7121-471d-4212-aba6-b5ce33f46721", "655caf33-85e8-4901-8f83-511ef392abda", "ba57520f-54ac-41f3-9351-38a593a4b6d2", "4fd09fd3-7d7d-4e65-a1f6-ae0de2c5e3c8"]}]}, {"BlockType": "LINE", "Confidence": 99.87125396728516, "Text": "PEACHTREE CORNERS, GA 30092", "Geometry": {"BoundingBox": {"Width": 0.26968681812286377, "Height": 0.012281778268516064, "Left": 0.3081396520137787, "Top": 0.12947185337543488}, "Polygon": [{"X": 0.3081406354904175, "Y": 0.12947185337543488}, {"X": 0.5778264403343201, "Y": 0.12948420643806458}, {"X": 0.5778255462646484, "Y": 0.14175362884998322}, {"X": 0.3081396520137787, "Y": 0.14174172282218933}]}, "Id": "963c23f4-e415-4d1b-a04c-970193950eac", "Relationships": [{"Type": "CHILD", "Ids": ["863f2dd6-4a69-4e45-a44b-110e2925e846", "3a8dc869-fcc7-4265-a719-bf26825d5e26", "8c2b652c-002d-48e3-b470-74f866ad189a", "702654c7-7587-472d-ac53-58cc5601a8c2"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "770-414-1929", "Geometry": {"BoundingBox": {"Width": 0.09957974404096603, "Height": 0.01030945684760809, "Left": 0.39371198415756226, "Top": 0.1449764519929886}, "Polygon": [{"X": 0.3937127888202667, "Y": 0.1449764519929886}, {"X": 0.4932917058467865, "Y": 0.1449808031320572}, {"X": 0.49329090118408203, "Y": 0.15528590977191925}, {"X": 0.39371198415756226, "Y": 0.15528170764446259}]}, "Id": "c5c31881-2829-4a8a-b953-c2e779f2b63d", "Relationships": [{"Type": "CHILD", "Ids": ["d6df198b-16a9-4462-9e7c-76dbbdd530e3"]}]}, {"BlockType": "LINE", "Confidence": 99.5703125, "Text": "FED ID# 45-3087555", "Geometry": {"BoundingBox": {"Width": 0.15359216928482056, "Height": 0.010261784307658672, "Left": 0.3663591742515564, "Top": 0.16072729229927063}, "Polygon": [{"X": 0.36636000871658325, "Y": 0.16072729229927063}, {"X": 0.519951343536377, "Y": 0.1607336699962616}, {"X": 0.5199505686759949, "Y": 0.17098906636238098}, {"X": 0.3663591742515564, "Y": 0.17098289728164673}]}, "Id": "3f4cddcf-4b84-4257-868b-34888bfe2d7f", "Relationships": [{"Type": "CHILD", "Ids": ["7c687a99-319c-4627-8571-23de381b0489", "91bb06d1-dc47-4827-af45-a7b048e424cc", "4ffe5f44-b5d3-4216-b261-7c824d9cfe65"]}]}, {"BlockType": "LINE", "Confidence": 99.81201171875, "Text": "00:25:50 September 05, 2025", "Geometry": {"BoundingBox": {"Width": 0.31005093455314636, "Height": 0.019829675555229187, "Left": 0.2881050705909729, "Top": 0.19426213204860687}, "Polygon": [{"X": 0.2881067097187042, "Y": 0.19426213204860687}, {"X": 0.5981559753417969, "Y": 0.19427360594272614}, {"X": 0.5981544852256775, "Y": 0.21409180760383606}, {"X": 0.2881050705909729, "Y": 0.21408118307590485}]}, "Id": "e5f3969a-8fef-48d6-9740-cbe91f4db5de", "Relationships": [{"Type": "CHILD", "Ids": ["a4d1388a-cae0-430a-9ba1-26bfd736d425", "0b4866d3-bbf0-4bb6-b778-069d15a3588f", "4431ac33-0c90-4385-bfed-45b5782920cb", "39d1bcc5-a611-4ead-830e-9add0fe7a2c0"]}]}, {"BlockType": "LINE", "Confidence": 99.0380859375, "Text": "Receipt #:", "Geometry": {"BoundingBox": {"Width": 0.07173623889684677, "Height": 0.013200333341956139, "Left": 0.2186131626367569, "Top": 0.23391585052013397}, "Polygon": [{"X": 0.21861425042152405, "Y": 0.23391585052013397}, {"X": 0.29034939408302307, "Y": 0.23391811549663544}, {"X": 0.2903483211994171, "Y": 0.24711619317531586}, {"X": 0.2186131626367569, "Y": 0.24711404740810394}]}, "Id": "803d28de-7cd5-4975-b5fa-529a53408a4b", "Relationships": [{"Type": "CHILD", "Ids": ["9ab9c463-d410-4c48-a9a6-77b399bd5851", "b96abedb-bd63-4311-bb67-4c2171201fe6"]}]}, {"BlockType": "LINE", "Confidence": 94.48680877685547, "Text": "elee7cal-bac0-", "Geometry": {"BoundingBox": {"Width": 0.10993117839097977, "Height": 0.010394526645541191, "Left": 0.4390999376773834, "Top": 0.23406590521335602}, "Polygon": [{"X": 0.4391007721424103, "Y": 0.23406590521335602}, {"X": 0.549031138420105, "Y": 0.23406937718391418}, {"X": 0.5490303039550781, "Y": 0.24446043372154236}, {"X": 0.4390999376773834, "Y": 0.24445711076259613}]}, "Id": "a1ca6950-33ce-4f4b-a088-4b05888e069d", "Relationships": [{"Type": "CHILD", "Ids": ["99bf1258-d4df-4435-9531-79343cf20484"]}]}, {"BlockType": "LINE", "Confidence": 98.49589538574219, "Text": "4a81-9eb3-b164a689dc0e", "Geometry": {"BoundingBox": {"Width": 0.18350861966609955, "Height": 0.010509009473025799, "Left": 0.43838125467300415, "Top": 0.24979616701602936}, "Polygon": [{"X": 0.438382089138031, "Y": 0.24979616701602936}, {"X": 0.6218898892402649, "Y": 0.24980157613754272}, {"X": 0.621889054775238, "Y": 0.2603051960468292}, {"X": 0.43838125467300415, "Y": 0.26030004024505615}]}, "Id": "35f416bb-4143-4d0e-b49f-e498951c0a61", "Relationships": [{"Type": "CHILD", "Ids": ["a5ac7080-9aa4-4f8c-be28-698e1dafa4c0"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Location:", "Geometry": {"BoundingBox": {"Width": 0.06755780428647995, "Height": 0.010395477525889874, "Left": 0.21830002963542938, "Top": 0.2751118540763855}, "Polygon": [{"X": 0.21830089390277863, "Y": 0.2751118540763855}, {"X": 0.28585782647132874, "Y": 0.27511361241340637}, {"X": 0.2858569920063019, "Y": 0.28550732135772705}, {"X": 0.21830002963542938, "Y": 0.2855056822299957}]}, "Id": "d4b843d5-5ee3-466f-b819-caa1c6f6aea3", "Relationships": [{"Type": "CHILD", "Ids": ["cd596790-a43a-46fc-b5ee-af0c808400c4"]}]}, {"BlockType": "LINE", "Confidence": 99.47265625, "Text": "KROGER HUTCHINSON KS", "Geometry": {"BoundingBox": {"Width": 0.21732546389102936, "Height": 0.01050538755953312, "Left": 0.4394212067127228, "Top": 0.275088369846344}, "Polygon": [{"X": 0.43942204117774963, "Y": 0.275088369846344}, {"X": 0.6567466855049133, "Y": 0.27509400248527527}, {"X": 0.6567458510398865, "Y": 0.28559374809265137}, {"X": 0.4394212067127228, "Y": 0.28558841347694397}]}, "Id": "09d77da5-74ae-4626-a557-0a8671129618", "Relationships": [{"Type": "CHILD", "Ids": ["54b52a44-3b6e-401c-8eba-56c77bbee708", "a71de139-eccd-478a-a194-199063b7b092", "c225b475-b078-4e7e-a0b9-38e135ce16de"]}]}, {"BlockType": "LINE", "Confidence": 99.9658203125, "Text": "Work Date:", "Geometry": {"BoundingBox": {"Width": 0.08217151463031769, "Height": 0.010497386567294598, "Left": 0.2187345325946808, "Top": 0.31574884057044983}, "Polygon": [{"X": 0.21873539686203003, "Y": 0.31574884057044983}, {"X": 0.3009060323238373, "Y": 0.3157505393028259}, {"X": 0.3009051978588104, "Y": 0.3262462317943573}, {"X": 0.2187345325946808, "Y": 0.32624468207359314}]}, "Id": "56a625bf-87dd-4cc9-bcf8-998d428dabd3", "Relationships": [{"Type": "CHILD", "Ids": ["bc134b43-8cf7-4cc7-9163-edb927af447c", "19a6c252-a579-452e-811c-9a2ebf7cca9a"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "2025-09-04", "Geometry": {"BoundingBox": {"Width": 0.0818161889910698, "Height": 0.010093054734170437, "Left": 0.4391773045063019, "Top": 0.31585755944252014}, "Polygon": [{"X": 0.43917810916900635, "Y": 0.31585755944252014}, {"X": 0.5209935307502747, "Y": 0.31585922837257385}, {"X": 0.5209926962852478, "Y": 0.32595062255859375}, {"X": 0.4391773045063019, "Y": 0.3259490430355072}]}, "Id": "6f3484c4-6de4-4e29-99ce-b65617952ce4", "Relationships": [{"Type": "CHILD", "Ids": ["82b197b2-f0b6-420d-b40a-59c6831ca668"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Bill Code:", "Geometry": {"BoundingBox": {"Width": 0.07249073684215546, "Height": 0.010598352178931236, "Left": 0.21868808567523956, "Top": 0.33585333824157715}, "Polygon": [{"X": 0.21868896484375, "Y": 0.33585333824157715}, {"X": 0.291178822517395, "Y": 0.3358546197414398}, {"X": 0.2911779582500458, "Y": 0.34645169973373413}, {"X": 0.21868808567523956, "Y": 0.3464505076408386}]}, "Id": "613b06de-82a7-4dbe-8a99-037b513a248e", "Relationships": [{"Type": "CHILD", "Ids": ["da3b13be-ff90-49ef-9b52-f7d96a1d07c3", "b0c81103-6164-4b22-8327-3c137ac8822b"]}]}, {"BlockType": "LINE", "Confidence": 99.72098541259766, "Text": "RCOD40245", "Geometry": {"BoundingBox": {"Width": 0.09196632355451584, "Height": 0.0102922972291708, "Left": 0.43922024965286255, "Top": 0.33610281348228455}, "Polygon": [{"X": 0.439221054315567, "Y": 0.33610281348228455}, {"X": 0.531186580657959, "Y": 0.3361044228076935}, {"X": 0.5311857461929321, "Y": 0.3463951051235199}, {"X": 0.43922024965286255, "Y": 0.3463936150074005}]}, "Id": "27f3ec88-5d52-4aad-9098-004af67b5227", "Relationships": [{"Type": "CHILD", "Ids": ["d38f6b06-f0a3-42da-9f70-20aef462d7da"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Carrier:", "Geometry": {"BoundingBox": {"Width": 0.05436030030250549, "Height": 0.010646925307810307, "Left": 0.21878451108932495, "Top": 0.35626542568206787}, "Polygon": [{"X": 0.2187853902578354, "Y": 0.35626542568206787}, {"X": 0.27314481139183044, "Y": 0.35626623034477234}, {"X": 0.2731439471244812, "Y": 0.3669123351573944}, {"X": 0.21878451108932495, "Y": 0.3669116199016571}]}, "Id": "fc8dacc9-9cba-4e03-b58a-0969a745ce29", "Relationships": [{"Type": "CHILD", "Ids": ["03b15eca-9fe8-42ea-bada-2c5940968cf2"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "ADVANCE", "Geometry": {"BoundingBox": {"Width": 0.08602768927812576, "Height": 0.01040840893983841, "Left": 0.43892502784729004, "Top": 0.3564685881137848}, "Polygon": [{"X": 0.4389258623123169, "Y": 0.3564685881137848}, {"X": 0.5249527096748352, "Y": 0.35646986961364746}, {"X": 0.5249518752098083, "Y": 0.366877019405365}, {"X": 0.43892502784729004, "Y": 0.36687585711479187}]}, "Id": "9ff8e939-fbcb-47e6-b0a9-ac887888d3ff", "Relationships": [{"Type": "CHILD", "Ids": ["4c040239-96b9-4bc2-a165-856dbe6000ae"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Dock:", "Geometry": {"BoundingBox": {"Width": 0.042338691651821136, "Height": 0.010369798168540001, "Left": 0.21870021522045135, "Top": 0.37713855504989624}, "Polygon": [{"X": 0.2187010645866394, "Y": 0.37713855504989624}, {"X": 0.2610388994216919, "Y": 0.37713906168937683}, {"X": 0.26103803515434265, "Y": 0.387508362531662}, {"X": 0.21870021522045135, "Y": 0.3875078856945038}]}, "Id": "b0eb703f-aabf-4165-92af-80b9d5a56060", "Relationships": [{"Type": "CHILD", "Ids": ["55304813-ed9a-41f6-b3f9-327bfaa9ac90"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "FROZEN", "Geometry": {"BoundingBox": {"Width": 0.06858398765325546, "Height": 0.010339030995965004, "Left": 0.438956081867218, "Top": 0.3772141635417938}, "Polygon": [{"X": 0.4389568865299225, "Y": 0.3772141635417938}, {"X": 0.5075400471687317, "Y": 0.3772149682044983}, {"X": 0.5075392723083496, "Y": 0.3875531852245331}, {"X": 0.438956081867218, "Y": 0.3875524699687958}]}, "Id": "e7c33a05-a83f-4e57-81f7-9ac340e0ab5b", "Relationships": [{"Type": "CHILD", "Ids": ["af0a21e4-ae01-4a87-a945-e475ac7b3cfd"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Door:", "Geometry": {"BoundingBox": {"Width": 0.04090114310383797, "Height": 0.010565846227109432, "Left": 0.21875661611557007, "Top": 0.3975154459476471}, "Polygon": [{"X": 0.2187574952840805, "Y": 0.3975154459476471}, {"X": 0.25965777039527893, "Y": 0.39751583337783813}, {"X": 0.2596569061279297, "Y": 0.4080812931060791}, {"X": 0.21875661611557007, "Y": 0.40808096528053284}]}, "Id": "7b05040c-e5be-49c6-8830-7bbcdfc9e15d", "Relationships": [{"Type": "CHILD", "Ids": ["f3b8f640-db84-4690-94c8-c6dc38b69797"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "96", "Geometry": {"BoundingBox": {"Width": 0.01749410852789879, "Height": 0.010599376633763313, "Left": 0.43892908096313477, "Top": 0.397325724363327}, "Polygon": [{"X": 0.438929945230484, "Y": 0.397325724363327}, {"X": 0.45642319321632385, "Y": 0.39732587337493896}, {"X": 0.456422358751297, "Y": 0.4079250991344452}, {"X": 0.43892908096313477, "Y": 0.40792495012283325}]}, "Id": "f893f862-6692-49ec-9464-d1a5a994a1e3", "Relationships": [{"Type": "CHILD", "Ids": ["3a262477-83ed-4364-979f-8755e0715ccf"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Purchase Orders", "Geometry": {"BoundingBox": {"Width": 0.11651577800512314, "Height": 0.010508069768548012, "Left": 0.2184695452451706, "Top": 0.42592674493789673}, "Polygon": [{"X": 0.21847042441368103, "Y": 0.42592674493789673}, {"X": 0.33498531579971313, "Y": 0.42592737078666687}, {"X": 0.3349844813346863, "Y": 0.436434805393219}, {"X": 0.2184695452451706, "Y": 0.4364343583583832}]}, "Id": "0fa5210b-e04d-4352-a077-5be15ff8a375", "Relationships": [{"Type": "CHILD", "Ids": ["bb11a6d1-1071-472a-a500-bd36473cfbe6", "dabb2df3-8c46-4b1a-8906-f73a5a571fdb"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.053068265318870544, "Height": 0.010385178960859776, "Left": 0.4394688904285431, "Top": 0.42603445053100586}, "Polygon": [{"X": 0.43946969509124756, "Y": 0.42603445053100586}, {"X": 0.49253714084625244, "Y": 0.42603474855422974}, {"X": 0.492536336183548, "Y": 0.43641963601112366}, {"X": 0.4394688904285431, "Y": 0.43641942739486694}]}, "Id": "f5d56444-089b-48ad-bdd3-5af895a18b9b", "Relationships": [{"Type": "CHILD", "Ids": ["080b6b00-57ca-4970-b680-723270849a3f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "00303622196", "Geometry": {"BoundingBox": {"Width": 0.0970558226108551, "Height": 0.010610029101371765, "Left": 0.21857324242591858, "Top": 0.44622406363487244}, "Polygon": [{"X": 0.21857412159442902, "Y": 0.44622406363487244}, {"X": 0.3156290650367737, "Y": 0.4462243318557739}, {"X": 0.31562820076942444, "Y": 0.4568341076374054}, {"X": 0.21857324242591858, "Y": 0.45683398842811584}]}, "Id": "1502f895-adbd-4303-bcfb-9dcc32dcced4", "Relationships": [{"Type": "CHILD", "Ids": ["e5529f48-4578-4439-ae5c-f75ad9aeb076"]}]}, {"BlockType": "LINE", "Confidence": 99.93814849853516, "Text": "VAN ORIENTAL FOOD", "Geometry": {"BoundingBox": {"Width": 0.1825159192085266, "Height": 0.010345789603888988, "Left": 0.43942663073539734, "Top": 0.44635283946990967}, "Polygon": [{"X": 0.4394274353981018, "Y": 0.44635283946990967}, {"X": 0.6219425201416016, "Y": 0.44635331630706787}, {"X": 0.6219417452812195, "Y": 0.45669862627983093}, {"X": 0.43942663073539734, "Y": 0.4566984176635742}]}, "Id": "2dc649bd-5956-4990-845a-f0fabc200b8a", "Relationships": [{"Type": "CHILD", "Ids": ["3f79f87e-60e4-4b9c-8cb5-5b5b849db5b8", "44903bd0-5911-4e8c-b1f9-41d3e8abb89a", "48b88868-8138-4ee4-a3b0-dceb5e55c74f"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "INC.", "Geometry": {"BoundingBox": {"Width": 0.03359198570251465, "Height": 0.010330392979085445, "Left": 0.43944695591926575, "Top": 0.4621643126010895}, "Polygon": [{"X": 0.4394477605819702, "Y": 0.4621643126010895}, {"X": 0.4730389416217804, "Y": 0.4621643126010895}, {"X": 0.47303810715675354, "Y": 0.4724946618080139}, {"X": 0.43944695591926575, "Y": 0.4724946916103363}]}, "Id": "f06702ad-da82-4018-b8ba-b94b216b8aa2", "Relationships": [{"Type": "CHILD", "Ids": ["ec022059-0308-4ef2-9d43-1f19be889af0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total Initial Pallets:", "Geometry": {"BoundingBox": {"Width": 0.139712393283844, "Height": 0.010807034559547901, "Left": 0.21847528219223022, "Top": 0.49508580565452576}, "Polygon": [{"X": 0.21847617626190186, "Y": 0.4950863718986511}, {"X": 0.3581876754760742, "Y": 0.49508580565452576}, {"X": 0.358186811208725, "Y": 0.5058920979499817}, {"X": 0.21847528219223022, "Y": 0.5058928728103638}]}, "Id": "15a16f30-447f-46f2-a1cd-fea4f5daac7f", "Relationships": [{"Type": "CHILD", "Ids": ["45c0b15d-ea3d-4112-a3b6-f955ff1f63af", "6154c519-c2d6-41c7-a8ea-3053583628d3", "042a7909-c752-4a51-ade7-389bf50f7aaa"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "4.00", "Geometry": {"BoundingBox": {"Width": 0.03148221597075462, "Height": 0.010406574234366417, "Left": 0.4385485053062439, "Top": 0.49536436796188354}, "Polygon": [{"X": 0.43854930996894836, "Y": 0.4953644871711731}, {"X": 0.4700307250022888, "Y": 0.49536436796188354}, {"X": 0.47002989053726196, "Y": 0.505770742893219}, {"X": 0.4385485053062439, "Y": 0.5057709217071533}]}, "Id": "03144b6b-9797-463d-81e3-05c5d91fa82e", "Relationships": [{"Type": "CHILD", "Ids": ["f7674a85-8c22-4f0b-86c8-ffa305fbe941"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total Finished Pallets:", "Geometry": {"BoundingBox": {"Width": 0.15817855298519135, "Height": 0.010402879677712917, "Left": 0.21851858496665955, "Top": 0.5155848264694214}, "Polygon": [{"X": 0.2185194492340088, "Y": 0.5155858993530273}, {"X": 0.3766971230506897, "Y": 0.5155848264694214}, {"X": 0.37669628858566284, "Y": 0.5259864330291748}, {"X": 0.21851858496665955, "Y": 0.5259876847267151}]}, "Id": "425d58ec-b348-4f3a-b825-9ecc97265943", "Relationships": [{"Type": "CHILD", "Ids": ["45611401-dc71-4017-a068-a898d5016dbd", "297b8bca-6ca1-4059-be6e-c150be5cb156", "825f2fc8-a9f1-47df-8af2-85ffa5e8160f"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "8", "Geometry": {"BoundingBox": {"Width": 0.008067325688898563, "Height": 0.010253123007714748, "Left": 0.4397270381450653, "Top": 0.5156369209289551}, "Polygon": [{"X": 0.4397278428077698, "Y": 0.5156369805335999}, {"X": 0.4477943480014801, "Y": 0.5156369209289551}, {"X": 0.44779354333877563, "Y": 0.5258899331092834}, {"X": 0.4397270381450653, "Y": 0.5258899927139282}]}, "Id": "88a0c332-4b34-451d-b444-34057b534030", "Relationships": [{"Type": "CHILD", "Ids": ["a833f32d-5cb7-40e9-85b2-4b01f10b3da8"]}]}, {"BlockType": "LINE", "Confidence": 99.96366119384766, "Text": "Total Case Count:", "Geometry": {"BoundingBox": {"Width": 0.1279216706752777, "Height": 0.010550111532211304, "Left": 0.21855255961418152, "Top": 0.5358566045761108}, "Polygon": [{"X": 0.21855343878269196, "Y": 0.5358578562736511}, {"X": 0.34647423028945923, "Y": 0.5358566045761108}, {"X": 0.34647336602211, "Y": 0.5464053153991699}, {"X": 0.21855255961418152, "Y": 0.5464066863059998}]}, "Id": "d8924c31-1130-4105-8dd1-f09703d9b53d", "Relationships": [{"Type": "CHILD", "Ids": ["42d6db7c-e0fb-41bd-9989-b8fa90f6c6ea", "871d13f6-32f9-4689-a78a-b06f7db692ba", "d75d288b-4c44-4c87-a62b-bbf7ec660077"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "420", "Geometry": {"BoundingBox": {"Width": 0.02613457478582859, "Height": 0.010369951836764812, "Left": 0.4390515685081482, "Top": 0.5359799265861511}, "Polygon": [{"X": 0.43905240297317505, "Y": 0.5359801650047302}, {"X": 0.46518614888191223, "Y": 0.5359799265861511}, {"X": 0.46518534421920776, "Y": 0.5463495850563049}, {"X": 0.4390515685081482, "Y": 0.5463498830795288}]}, "Id": "61d7fdf5-7562-4f1d-bbba-c11e1ac225a7", "Relationships": [{"Type": "CHILD", "Ids": ["47d2100a-b462-4306-9d92-96d1ee1e9e14"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Trailer Number:", "Geometry": {"BoundingBox": {"Width": 0.1143752709031105, "Height": 0.010565007105469704, "Left": 0.21863503754138947, "Top": 0.556559145450592}, "Polygon": [{"X": 0.2186359167098999, "Y": 0.5565605759620667}, {"X": 0.33301031589508057, "Y": 0.556559145450592}, {"X": 0.3330094516277313, "Y": 0.5671225786209106}, {"X": 0.21863503754138947, "Y": 0.5671241283416748}]}, "Id": "ac601375-d56c-4d4c-8e54-b7fb37588d1f", "Relationships": [{"Type": "CHILD", "Ids": ["c87a4ed3-7b6d-4ca1-94bc-0d762c89b50b", "697e0fc1-7de4-4650-92d2-293a65e0f368"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "06", "Geometry": {"BoundingBox": {"Width": 0.017446059733629227, "Height": 0.010391131974756718, "Left": 0.43928879499435425, "Top": 0.5565201044082642}, "Polygon": [{"X": 0.4392896294593811, "Y": 0.5565203428268433}, {"X": 0.45673486590385437, "Y": 0.5565201044082642}, {"X": 0.4567340314388275, "Y": 0.566910982131958}, {"X": 0.43928879499435425, "Y": 0.5669112205505371}]}, "Id": "80266cc5-ddd9-43ab-b051-7fa4a8356ca3", "Relationships": [{"Type": "CHILD", "Ids": ["fb985a78-72c3-4598-b28c-8d65eb07f5d1"]}]}, {"BlockType": "LINE", "Confidence": 99.95525360107422, "Text": "Tractor Number:", "Geometry": {"BoundingBox": {"Width": 0.11840415745973587, "Height": 0.010639237239956856, "Left": 0.2188110202550888, "Top": 0.5768864154815674}, "Polygon": [{"X": 0.21881189942359924, "Y": 0.5768882036209106}, {"X": 0.3372151851654053, "Y": 0.5768864154815674}, {"X": 0.33721432089805603, "Y": 0.5875236392021179}, {"X": 0.2188110202550888, "Y": 0.5875256061553955}]}, "Id": "54c63b9d-15b1-4eda-8d90-e614209c7e44", "Relationships": [{"Type": "CHILD", "Ids": ["d51643d8-b39d-449c-af92-198a5a587cae", "396de86c-cd71-44ab-8495-b00252aa62c7"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "BOL:", "Geometry": {"BoundingBox": {"Width": 0.039586108177900314, "Height": 0.010258309543132782, "Left": 0.2186710238456726, "Top": 0.5973156094551086}, "Polygon": [{"X": 0.21867187321186066, "Y": 0.597316324710846}, {"X": 0.258257120847702, "Y": 0.5973156094551086}, {"X": 0.25825628638267517, "Y": 0.6075731515884399}, {"X": 0.2186710238456726, "Y": 0.607573926448822}]}, "Id": "ee97b0cb-79db-42e7-a98e-82e09746ca53", "Relationships": [{"Type": "CHILD", "Ids": ["edc8fc4e-1427-417c-b02d-bb8f7be300d5"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Comments:", "Geometry": {"BoundingBox": {"Width": 0.08017195761203766, "Height": 0.010574756190180779, "Left": 0.21885061264038086, "Top": 0.617591917514801}, "Polygon": [{"X": 0.2188514769077301, "Y": 0.6175935864448547}, {"X": 0.2990225553512573, "Y": 0.617591917514801}, {"X": 0.2990216910839081, "Y": 0.6281648874282837}, {"X": 0.21885061264038086, "Y": 0.628166675567627}]}, "Id": "cd38934d-d2bf-4578-88fb-45b6ef48b614", "Relationships": [{"Type": "CHILD", "Ids": ["203a6eed-d066-48fa-bf57-6f2777b45a78"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Canned Comments:", "Geometry": {"BoundingBox": {"Width": 0.1388722062110901, "Height": 0.010343560948967934, "Left": 0.2188299298286438, "Top": 0.6381862759590149}, "Polygon": [{"X": 0.21883077919483185, "Y": 0.6381895542144775}, {"X": 0.3577021360397339, "Y": 0.6381862759590149}, {"X": 0.35770130157470703, "Y": 0.6485263705253601}, {"X": 0.2188299298286438, "Y": 0.6485298871994019}]}, "Id": "dd624146-2e41-49b8-bf0e-977bdfb8e98d", "Relationships": [{"Type": "CHILD", "Ids": ["aa85abf4-61c6-426f-aea3-6937bf9d8ffc", "d7a91600-d7f7-497a-8cb3-a5b1d341581f"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Unloaders:", "Geometry": {"BoundingBox": {"Width": 0.0765959620475769, "Height": 0.01072867400944233, "Left": 0.21852576732635498, "Top": 0.658429741859436}, "Polygon": [{"X": 0.21852664649486542, "Y": 0.6584317088127136}, {"X": 0.2951217293739319, "Y": 0.658429741859436}, {"X": 0.29512083530426025, "Y": 0.6691562533378601}, {"X": 0.21852576732635498, "Y": 0.669158399105072}]}, "Id": "6ab8296c-c0bb-465c-bb09-faaec2236643", "Relationships": [{"Type": "CHILD", "Ids": ["ae4769f7-7aaf-4786-96ee-104b141724cc"]}]}, {"BlockType": "LINE", "Confidence": 99.892578125, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.008404986932873726, "Height": 0.009890802204608917, "Left": 0.4391712546348572, "Top": 0.6587877869606018}, "Polygon": [{"X": 0.43917202949523926, "Y": 0.6587880253791809}, {"X": 0.44757622480392456, "Y": 0.6587877869606018}, {"X": 0.4475754499435425, "Y": 0.668678343296051}, {"X": 0.4391712546348572, "Y": 0.6686785817146301}]}, "Id": "907af989-af5b-4615-b44f-8aabf6bd06d2", "Relationships": [{"Type": "CHILD", "Ids": ["3345be7e-1b23-45a1-81a8-d6352842aff0"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Base Charge:", "Geometry": {"BoundingBox": {"Width": 0.0939728319644928, "Height": 0.013027125969529152, "Left": 0.2185661792755127, "Top": 0.6871598362922668}, "Polygon": [{"X": 0.21856726706027985, "Y": 0.6871626973152161}, {"X": 0.3125390112400055, "Y": 0.6871598362922668}, {"X": 0.3125379681587219, "Y": 0.7001839280128479}, {"X": 0.2185661792755127, "Y": 0.7001869678497314}]}, "Id": "75d203b2-c995-463f-b0a5-7a897a3728d4", "Relationships": [{"Type": "CHILD", "Ids": ["cfc24bef-0d6b-448c-b40b-910466b3487d", "83af66d9-9d74-452e-a526-0255cbb8876d"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "86.00", "Geometry": {"BoundingBox": {"Width": 0.03917253389954567, "Height": 0.010388154536485672, "Left": 0.4397062063217163, "Top": 0.6871433854103088}, "Polygon": [{"X": 0.43970704078674316, "Y": 0.6871445775032043}, {"X": 0.4788787364959717, "Y": 0.6871433854103088}, {"X": 0.4788779318332672, "Y": 0.6975303292274475}, {"X": 0.4397062063217163, "Y": 0.6975315809249878}]}, "Id": "d659df31-8cde-4e3b-b1a4-aabc84ea48a7", "Relationships": [{"Type": "CHILD", "Ids": ["a3379d9c-28a4-42a2-b3f8-34e0fed5ea27"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Convenience Fee:", "Geometry": {"BoundingBox": {"Width": 0.12597399950027466, "Height": 0.01064546313136816, "Left": 0.21889370679855347, "Top": 0.7071905732154846}, "Polygon": [{"X": 0.2188945859670639, "Y": 0.7071947455406189}, {"X": 0.3448677062988281, "Y": 0.7071905732154846}, {"X": 0.3448668420314789, "Y": 0.7178316712379456}, {"X": 0.21889370679855347, "Y": 0.7178360223770142}]}, "Id": "18ea8ff4-330b-439e-b16e-2dbbe3d30c06", "Relationships": [{"Type": "CHILD", "Ids": ["4168c777-d371-4d1b-a2c8-574705c39248", "fa35a8b9-a24a-490c-b9b4-271a7cf6db29"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "10.00", "Geometry": {"BoundingBox": {"Width": 0.03766478970646858, "Height": 0.00986252911388874, "Left": 0.4409220814704895, "Top": 0.7077041268348694}, "Polygon": [{"X": 0.44092288613319397, "Y": 0.7077053785324097}, {"X": 0.478586882352829, "Y": 0.7077041268348694}, {"X": 0.4785861074924469, "Y": 0.7175653576850891}, {"X": 0.4409220814704895, "Y": 0.7175666093826294}]}, "Id": "b506b943-7295-4e63-80ef-04bafffc2b61", "Relationships": [{"Type": "CHILD", "Ids": ["8151e186-15c9-4012-a4a0-6fb2ac21e117"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Total Cost:", "Geometry": {"BoundingBox": {"Width": 0.07841265201568604, "Height": 0.010512390173971653, "Left": 0.2187347561120987, "Top": 0.7277215123176575}, "Polygon": [{"X": 0.21873563528060913, "Y": 0.7277243137359619}, {"X": 0.2971474230289459, "Y": 0.7277215123176575}, {"X": 0.2971465587615967, "Y": 0.7382310032844543}, {"X": 0.2187347561120987, "Y": 0.7382339239120483}]}, "Id": "341603b5-53c4-46be-a25c-95e14f61dca5", "Relationships": [{"Type": "CHILD", "Ids": ["aa968cf6-e9cc-41e6-8417-40ea5160b2e7", "07b8f091-d709-4962-b30f-5e7368794861"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "96.00", "Geometry": {"BoundingBox": {"Width": 0.039326343685388565, "Height": 0.010553417727351189, "Left": 0.4393634498119354, "Top": 0.7277570962905884}, "Polygon": [{"X": 0.4393642842769623, "Y": 0.727758526802063}, {"X": 0.4786897897720337, "Y": 0.7277570962905884}, {"X": 0.47868895530700684, "Y": 0.7383090853691101}, {"X": 0.4393634498119354, "Y": 0.7383105158805847}]}, "Id": "385673de-076c-4aec-81a2-42ae9c9bf69b", "Relationships": [{"Type": "CHILD", "Ids": ["2b6f508f-91eb-4551-be93-0350499190e2"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Payments:", "Geometry": {"BoundingBox": {"Width": 0.07276341319084167, "Height": 0.012906787917017937, "Left": 0.21860915422439575, "Top": 0.7566024661064148}, "Polygon": [{"X": 0.21861021220684052, "Y": 0.756605327129364}, {"X": 0.2913725674152374, "Y": 0.7566024661064148}, {"X": 0.29137149453163147, "Y": 0.7695062160491943}, {"X": 0.21860915422439575, "Y": 0.7695092558860779}]}, "Id": "aea55959-f72a-4184-ac9b-18b6c19134e9", "Relationships": [{"Type": "CHILD", "Ids": ["df6bc41d-9f16-41d6-809e-3983abbde9e2"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.05814646556973457, "Height": 0.010385558009147644, "Left": 0.43913525342941284, "Top": 0.7565652132034302}, "Polygon": [{"X": 0.4391360580921173, "Y": 0.7565675377845764}, {"X": 0.4972817003726959, "Y": 0.7565652132034302}, {"X": 0.49728089570999146, "Y": 0.766948401927948}, {"X": 0.43913525342941284, "Y": 0.766950786113739}]}, "Id": "b3415434-e6bc-4b48-a18e-571085362efe", "Relationships": [{"Type": "CHILD", "Ids": ["e505f893-d46c-4a57-81df-f97df4aa7065"]}]}, {"BlockType": "LINE", "Confidence": 99.32398223876953, "Text": "CapstonePay-", "Geometry": {"BoundingBox": {"Width": 0.0974268838763237, "Height": 0.012792697176337242, "Left": 0.2544495165348053, "Top": 0.7769339680671692}, "Polygon": [{"X": 0.25445055961608887, "Y": 0.7769381403923035}, {"X": 0.3518764078617096, "Y": 0.7769339680671692}, {"X": 0.351875364780426, "Y": 0.7897223830223083}, {"X": 0.2544495165348053, "Y": 0.7897266745567322}]}, "Id": "ab0a09ca-7faf-4214-956b-411d3c61b8be", "Relationships": [{"Type": "CHILD", "Ids": ["06f0df7b-ea3c-42ea-b577-d7bc3988f899"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$96.00", "Geometry": {"BoundingBox": {"Width": 0.0475647896528244, "Height": 0.01108519360423088, "Left": 0.43960142135620117, "Top": 0.7766140103340149}, "Polygon": [{"X": 0.4396022856235504, "Y": 0.7766160368919373}, {"X": 0.4871661961078644, "Y": 0.7766140103340149}, {"X": 0.48716533184051514, "Y": 0.7876970767974854}, {"X": 0.43960142135620117, "Y": 0.7876991629600525}]}, "Id": "fb54d900-1629-44fa-90b8-c1c0bcb12ef1", "Relationships": [{"Type": "CHILD", "Ids": ["1bfc69fa-7b3d-4692-8838-1bebe9e4fb2d"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "37882651", "Geometry": {"BoundingBox": {"Width": 0.06814589351415634, "Height": 0.010213878937065601, "Left": 0.2546252906322479, "Top": 0.7925258278846741}, "Polygon": [{"X": 0.2546261250972748, "Y": 0.7925288677215576}, {"X": 0.32277119159698486, "Y": 0.7925258278846741}, {"X": 0.322770357131958, "Y": 0.8027365803718567}, {"X": 0.2546252906322479, "Y": 0.8027397394180298}]}, "Id": "ead4aa42-2ce0-413c-b303-9422fa70feb3", "Relationships": [{"Type": "CHILD", "Ids": ["a857a0fb-5768-43b1-b579-c1b038720983"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Total Payments", "Geometry": {"BoundingBox": {"Width": 0.1099884882569313, "Height": 0.013123923912644386, "Left": 0.25400659441947937, "Top": 0.8177727460861206}, "Polygon": [{"X": 0.2540076673030853, "Y": 0.8177779912948608}, {"X": 0.3639950752258301, "Y": 0.8177727460861206}, {"X": 0.3639940321445465, "Y": 0.8308911919593811}, {"X": 0.25400659441947937, "Y": 0.8308966755867004}]}, "Id": "d691aed7-2c91-4103-81d7-29507736b532", "Relationships": [{"Type": "CHILD", "Ids": ["e0301fd8-8e67-44ab-a7a7-870f1deb8f5f", "73e3bd9d-27de-4637-b75b-20fd493672f0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$96.00", "Geometry": {"BoundingBox": {"Width": 0.047483135014772415, "Height": 0.011110220104455948, "Left": 0.43963417410850525, "Top": 0.8174865245819092}, "Polygon": [{"X": 0.4396350681781769, "Y": 0.8174887895584106}, {"X": 0.48711732029914856, "Y": 0.8174865245819092}, {"X": 0.4871164560317993, "Y": 0.8285943865776062}, {"X": 0.43963417410850525, "Y": 0.8285967111587524}]}, "Id": "e829288f-fb9e-4066-b1ce-b5a352f1b8c7", "Relationships": [{"Type": "CHILD", "Ids": ["c058cf31-2881-485b-80a6-c09f4220b072"]}]}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Capstone", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16354095935821533, "Height": 0.03181183710694313, "Left": 0.27555519342422485, "Top": 0.061316706240177155}, "Polygon": [{"X": 0.2755577862262726, "Y": 0.061316706240177155}, {"X": 0.4390961527824402, "Y": 0.061325717717409134}, {"X": 0.4390936493873596, "Y": 0.09312853962182999}, {"X": 0.27555519342422485, "Y": 0.09312023222446442}]}, "Id": "2299e846-ca6b-4655-b5a1-bfe78494566c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Logistics", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16097421944141388, "Height": 0.031857702881097794, "Left": 0.4502841532230377, "Top": 0.06102525442838669}, "Polygon": [{"X": 0.4502866864204407, "Y": 0.06102525442838669}, {"X": 0.6112583875656128, "Y": 0.06103413179516792}, {"X": 0.611255943775177, "Y": 0.09288295358419418}, {"X": 0.4502841532230377, "Y": 0.09287477284669876}]}, "Id": "ad50dcc1-8bdc-48ad-a33a-57374efb4c61"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017102370038628578, "Height": 0.010137176141142845, "Left": 0.2704474925994873, "Top": 0.11387565732002258}, "Polygon": [{"X": 0.27044832706451416, "Y": 0.11387565732002258}, {"X": 0.28754985332489014, "Y": 0.11387647688388824}, {"X": 0.28754904866218567, "Y": 0.12401282787322998}, {"X": 0.2704474925994873, "Y": 0.1240120381116867}]}, "Id": "3f123c90-b03e-4046-a067-7790712da5f1"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "TECHNOLOGY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1201789379119873, "Height": 0.01033220998942852, "Left": 0.2920070290565491, "Top": 0.1138039156794548}, "Polygon": [{"X": 0.2920078635215759, "Y": 0.1138039156794548}, {"X": 0.4121859669685364, "Y": 0.11380968242883682}, {"X": 0.4121851325035095, "Y": 0.12413612753152847}, {"X": 0.2920070290565491, "Y": 0.12413053214550018}]}, "Id": "ce9c4d9c-0c7d-4f5c-87ec-de2ab67f093d"}, {"BlockType": "WORD", "Confidence": 99.49138641357422, "Text": "PKWY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05143525078892708, "Height": 0.01017899438738823, "Left": 0.4166523218154907, "Top": 0.1138337180018425}, "Polygon": [{"X": 0.4166531264781952, "Y": 0.1138337180018425}, {"X": 0.4680875539779663, "Y": 0.11383618414402008}, {"X": 0.46808674931526184, "Y": 0.12401270866394043}, {"X": 0.4166523218154907, "Y": 0.12401031702756882}]}, "Id": "2f1f7121-471d-4212-aba6-b5ce33f46721"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "SOUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05784665793180466, "Height": 0.010483276098966599, "Left": 0.4735852777957916, "Top": 0.1138237789273262}, "Polygon": [{"X": 0.4735860824584961, "Y": 0.1138237789273262}, {"X": 0.5314319133758545, "Y": 0.11382655054330826}, {"X": 0.5314311385154724, "Y": 0.1243070513010025}, {"X": 0.4735852777957916, "Y": 0.12430436164140701}]}, "Id": "655caf33-85e8-4901-8f83-511ef392abda"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "SUITE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049421634525060654, "Height": 0.010433042421936989, "Left": 0.5362033247947693, "Top": 0.11377935856580734}, "Polygon": [{"X": 0.5362041592597961, "Y": 0.11377935856580734}, {"X": 0.5856249928474426, "Y": 0.11378172785043716}, {"X": 0.5856241583824158, "Y": 0.12421239912509918}, {"X": 0.5362033247947693, "Y": 0.12421010434627533}]}, "Id": "ba57520f-54ac-41f3-9351-38a593a4b6d2"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "200", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025516416877508163, "Height": 0.010115694254636765, "Left": 0.590597927570343, "Top": 0.11389131098985672}, "Polygon": [{"X": 0.5905987024307251, "Y": 0.11389131098985672}, {"X": 0.6161143183708191, "Y": 0.11389254033565521}, {"X": 0.616113543510437, "Y": 0.12400700896978378}, {"X": 0.590597927570343, "Y": 0.12400581687688828}]}, "Id": "4fd09fd3-7d7d-4e65-a1f6-ae0de2c5e3c8"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "PEACHTREE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10184837132692337, "Height": 0.010156061500310898, "Left": 0.3081398010253906, "Top": 0.1296650916337967}, "Polygon": [{"X": 0.3081406354904175, "Y": 0.1296650916337967}, {"X": 0.4099881649017334, "Y": 0.12966975569725037}, {"X": 0.40998736023902893, "Y": 0.1398211568593979}, {"X": 0.3081398010253906, "Y": 0.13981662690639496}]}, "Id": "863f2dd6-4a69-4e45-a44b-110e2925e846"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "CORNERS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08487289398908615, "Height": 0.012109900824725628, "Left": 0.4147542715072632, "Top": 0.12964026629924774}, "Polygon": [{"X": 0.4147552251815796, "Y": 0.12964026629924774}, {"X": 0.49962717294692993, "Y": 0.12964415550231934}, {"X": 0.4996262192726135, "Y": 0.14175017178058624}, {"X": 0.4147542715072632, "Y": 0.1417464315891266}]}, "Id": "3a8dc869-fcc7-4265-a719-bf26825d5e26"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "GA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024747062474489212, "Height": 0.010400954633951187, "Left": 0.5048477649688721, "Top": 0.12948086857795715}, "Polygon": [{"X": 0.5048485994338989, "Y": 0.12948086857795715}, {"X": 0.5295948386192322, "Y": 0.12948200106620789}, {"X": 0.5295940041542053, "Y": 0.13988181948661804}, {"X": 0.5048477649688721, "Y": 0.1398807168006897}]}, "Id": "8c2b652c-002d-48e3-b470-74f866ad189a"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "30092", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04357454925775528, "Height": 0.009955981746315956, "Left": 0.5342519283294678, "Top": 0.12966185808181763}, "Polygon": [{"X": 0.5342527031898499, "Y": 0.12966185808181763}, {"X": 0.5778264403343201, "Y": 0.1296638548374176}, {"X": 0.577825665473938, "Y": 0.13961784541606903}, {"X": 0.5342519283294678, "Y": 0.13961590826511383}]}, "Id": "702654c7-7587-472d-ac53-58cc5601a8c2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "770-414-1929", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09957974404096603, "Height": 0.01030945684760809, "Left": 0.39371198415756226, "Top": 0.1449764519929886}, "Polygon": [{"X": 0.3937127888202667, "Y": 0.1449764519929886}, {"X": 0.4932917058467865, "Y": 0.1449808031320572}, {"X": 0.49329090118408203, "Y": 0.15528590977191925}, {"X": 0.39371198415756226, "Y": 0.15528170764446259}]}, "Id": "d6df198b-16a9-4462-9e7c-76dbbdd530e3"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "FED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03291377052664757, "Height": 0.010191460140049458, "Left": 0.3663591742515564, "Top": 0.16078437864780426}, "Polygon": [{"X": 0.36636000871658325, "Y": 0.16078437864780426}, {"X": 0.39927294850349426, "Y": 0.1607857495546341}, {"X": 0.3992721438407898, "Y": 0.17097583413124084}, {"X": 0.3663591742515564, "Y": 0.1709745228290558}]}, "Id": "7c687a99-319c-4627-8571-23de381b0489"}, {"BlockType": "WORD", "Confidence": 98.984375, "Text": "ID#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028197363018989563, "Height": 0.010128968395292759, "Left": 0.40376314520835876, "Top": 0.160787895321846}, "Polygon": [{"X": 0.40376394987106323, "Y": 0.160787895321846}, {"X": 0.4319605231285095, "Y": 0.16078907251358032}, {"X": 0.43195971846580505, "Y": 0.1709168702363968}, {"X": 0.40376314520835876, "Y": 0.17091573774814606}]}, "Id": "91bb06d1-dc47-4827-af45-a7b048e424cc"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "45-3087555", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08452210575342178, "Height": 0.010258913971483707, "Left": 0.43542924523353577, "Top": 0.16073015332221985}, "Polygon": [{"X": 0.43543004989624023, "Y": 0.16073015332221985}, {"X": 0.519951343536377, "Y": 0.1607336699962616}, {"X": 0.5199505686759949, "Y": 0.17098906636238098}, {"X": 0.43542924523353577, "Y": 0.17098566889762878}]}, "Id": "4ffe5f44-b5d3-4216-b261-7c824d9cfe65"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "00:25:50", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09293828904628754, "Height": 0.014973710291087627, "Left": 0.28810542821884155, "Top": 0.19469571113586426}, "Polygon": [{"X": 0.28810665011405945, "Y": 0.19469571113586426}, {"X": 0.3810437321662903, "Y": 0.19469913840293884}, {"X": 0.3810425102710724, "Y": 0.20966942608356476}, {"X": 0.28810542821884155, "Y": 0.2096661776304245}]}, "Id": "a4d1388a-cae0-430a-9ba1-26bfd736d425"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "September", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11339987814426422, "Height": 0.019822662696242332, "Left": 0.3883318603038788, "Top": 0.19426584243774414}, "Polygon": [{"X": 0.3883334696292877, "Y": 0.19426584243774414}, {"X": 0.5017317533493042, "Y": 0.1942700445652008}, {"X": 0.50173020362854, "Y": 0.21408851444721222}, {"X": 0.3883318603038788, "Y": 0.21408462524414062}]}, "Id": "0b4866d3-bbf0-4bb6-b778-069d15a3588f"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "05,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031767696142196655, "Height": 0.017863094806671143, "Left": 0.5077479481697083, "Top": 0.19466523826122284}, "Polygon": [{"X": 0.5077493786811829, "Y": 0.19466523826122284}, {"X": 0.5395156741142273, "Y": 0.19466640055179596}, {"X": 0.5395143032073975, "Y": 0.21252833306789398}, {"X": 0.5077479481697083, "Y": 0.21252723038196564}]}, "Id": "4431ac33-0c90-4385-bfed-45b5782920cb"}, {"BlockType": "WORD", "Confidence": 99.638671875, "Text": "2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051557134836912155, "Height": 0.014745679683983326, "Left": 0.5465987920761108, "Top": 0.19487445056438446}, "Polygon": [{"X": 0.5465999841690063, "Y": 0.19487445056438446}, {"X": 0.5981559753417969, "Y": 0.19487635791301727}, {"X": 0.5981548428535461, "Y": 0.2096201330423355}, {"X": 0.5465987920761108, "Y": 0.20961833000183105}]}, "Id": "39d1bcc5-a611-4ead-830e-9add0fe7a2c0"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054387643933296204, "Height": 0.0131881944835186, "Left": 0.2186131626367569, "Top": 0.23392747342586517}, "Polygon": [{"X": 0.21861425042152405, "Y": 0.23392747342586517}, {"X": 0.2730008065700531, "Y": 0.23392920196056366}, {"X": 0.27299973368644714, "Y": 0.24711567163467407}, {"X": 0.2186131626367569, "Y": 0.24711404740810394}]}, "Id": "9ab9c463-d410-4c48-a9a6-77b399bd5851"}, {"BlockType": "WORD", "Confidence": 98.125, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013338038697838783, "Height": 0.010651719756424427, "Left": 0.27701136469841003, "Top": 0.233917698264122}, "Polygon": [{"X": 0.2770122289657593, "Y": 0.233917698264122}, {"X": 0.29034939408302307, "Y": 0.23391811549663544}, {"X": 0.29034852981567383, "Y": 0.24456942081451416}, {"X": 0.27701136469841003, "Y": 0.24456901848316193}]}, "Id": "b96abedb-bd63-4311-bb67-4c2171201fe6"}, {"BlockType": "WORD", "Confidence": 94.48680877685547, "Text": "elee7cal-bac0-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10993117839097977, "Height": 0.010394526645541191, "Left": 0.4390999376773834, "Top": 0.23406590521335602}, "Polygon": [{"X": 0.4391007721424103, "Y": 0.23406590521335602}, {"X": 0.549031138420105, "Y": 0.23406937718391418}, {"X": 0.5490303039550781, "Y": 0.24446043372154236}, {"X": 0.4390999376773834, "Y": 0.24445711076259613}]}, "Id": "99bf1258-d4df-4435-9531-79343cf20484"}, {"BlockType": "WORD", "Confidence": 98.49589538574219, "Text": "4a81-9eb3-b164a689dc0e", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.18350861966609955, "Height": 0.010509009473025799, "Left": 0.43838125467300415, "Top": 0.24979616701602936}, "Polygon": [{"X": 0.438382089138031, "Y": 0.24979616701602936}, {"X": 0.6218898892402649, "Y": 0.24980157613754272}, {"X": 0.621889054775238, "Y": 0.2603051960468292}, {"X": 0.43838125467300415, "Y": 0.26030004024505615}]}, "Id": "a5ac7080-9aa4-4f8c-be28-698e1dafa4c0"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Location:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06755780428647995, "Height": 0.010395477525889874, "Left": 0.21830002963542938, "Top": 0.2751118540763855}, "Polygon": [{"X": 0.21830089390277863, "Y": 0.2751118540763855}, {"X": 0.28585782647132874, "Y": 0.27511361241340637}, {"X": 0.2858569920063019, "Y": 0.28550732135772705}, {"X": 0.21830002963542938, "Y": 0.2855056822299957}]}, "Id": "cd596790-a43a-46fc-b5ee-af0c808400c4"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "KROGER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07161680608987808, "Height": 0.010134050622582436, "Left": 0.43942123651504517, "Top": 0.27530890703201294}, "Polygon": [{"X": 0.43942204117774963, "Y": 0.27530890703201294}, {"X": 0.5110380053520203, "Y": 0.275310754776001}, {"X": 0.5110372304916382, "Y": 0.28544294834136963}, {"X": 0.43942123651504517, "Y": 0.28544119000434875}]}, "Id": "54b52a44-3b6e-401c-8eba-56c77bbee708"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "HUTCHINSON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11488249897956848, "Height": 0.01045259553939104, "Left": 0.5151858925819397, "Top": 0.2751210629940033}, "Polygon": [{"X": 0.5151867270469666, "Y": 0.2751210629940033}, {"X": 0.6300684213638306, "Y": 0.27512404322624207}, {"X": 0.6300675868988037, "Y": 0.28557366132736206}, {"X": 0.5151858925819397, "Y": 0.2855708599090576}]}, "Id": "a71de139-eccd-478a-a194-199063b7b092"}, {"BlockType": "WORD", "Confidence": 98.544921875, "Text": "KS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022246818989515305, "Height": 0.010500321164727211, "Left": 0.6344998478889465, "Top": 0.2750934362411499}, "Polygon": [{"X": 0.6345006823539734, "Y": 0.2750934362411499}, {"X": 0.6567466855049133, "Y": 0.27509400248527527}, {"X": 0.6567458510398865, "Y": 0.28559374809265137}, {"X": 0.6344998478889465, "Y": 0.2855932116508484}]}, "Id": "c225b475-b078-4e7e-a0b9-38e135ce16de"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Work", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04094156250357628, "Height": 0.010376007296144962, "Left": 0.2187345325946808, "Top": 0.31574884057044983}, "Polygon": [{"X": 0.21873539686203003, "Y": 0.31574884057044983}, {"X": 0.25967609882354736, "Y": 0.3157496750354767}, {"X": 0.2596752643585205, "Y": 0.3261248469352722}, {"X": 0.2187345325946808, "Y": 0.32612407207489014}]}, "Id": "bc134b43-8cf7-4cc7-9163-edb927af447c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03766613081097603, "Height": 0.01042975764721632, "Left": 0.26323992013931274, "Top": 0.3158164918422699}, "Polygon": [{"X": 0.2632407546043396, "Y": 0.3158164918422699}, {"X": 0.3009060323238373, "Y": 0.3158172369003296}, {"X": 0.3009051978588104, "Y": 0.3262462317943573}, {"X": 0.26323992013931274, "Y": 0.32624551653862}]}, "Id": "19a6c252-a579-452e-811c-9a2ebf7cca9a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "2025-09-04", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0818161889910698, "Height": 0.010093054734170437, "Left": 0.4391773045063019, "Top": 0.31585755944252014}, "Polygon": [{"X": 0.43917810916900635, "Y": 0.31585755944252014}, {"X": 0.5209935307502747, "Y": 0.31585922837257385}, {"X": 0.5209926962852478, "Y": 0.32595062255859375}, {"X": 0.4391773045063019, "Y": 0.3259490430355072}]}, "Id": "82b197b2-f0b6-420d-b40a-59c6831ca668"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0263973381370306, "Height": 0.010433286428451538, "Left": 0.21868808567523956, "Top": 0.3359469175338745}, "Polygon": [{"X": 0.2186889499425888, "Y": 0.3359469175338745}, {"X": 0.24508541822433472, "Y": 0.3359473645687103}, {"X": 0.24508456885814667, "Y": 0.34638020396232605}, {"X": 0.21868808567523956, "Y": 0.34637975692749023}]}, "Id": "da3b13be-ff90-49ef-9b52-f7d96a1d07c3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Code:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04126163199543953, "Height": 0.01059779990464449, "Left": 0.2499171942472458, "Top": 0.3358539044857025}, "Polygon": [{"X": 0.24991805851459503, "Y": 0.3358539044857025}, {"X": 0.291178822517395, "Y": 0.3358546197414398}, {"X": 0.2911779582500458, "Y": 0.34645169973373413}, {"X": 0.2499171942472458, "Y": 0.3464510142803192}]}, "Id": "b0c81103-6164-4b22-8327-3c137ac8822b"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "RCOD40245", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09196632355451584, "Height": 0.0102922972291708, "Left": 0.43922024965286255, "Top": 0.33610281348228455}, "Polygon": [{"X": 0.439221054315567, "Y": 0.33610281348228455}, {"X": 0.531186580657959, "Y": 0.3361044228076935}, {"X": 0.5311857461929321, "Y": 0.3463951051235199}, {"X": 0.43922024965286255, "Y": 0.3463936150074005}]}, "Id": "d38f6b06-f0a3-42da-9f70-20aef462d7da"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Carrier:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05436030030250549, "Height": 0.010646925307810307, "Left": 0.21878451108932495, "Top": 0.35626542568206787}, "Polygon": [{"X": 0.2187853902578354, "Y": 0.35626542568206787}, {"X": 0.27314481139183044, "Y": 0.35626623034477234}, {"X": 0.2731439471244812, "Y": 0.3669123351573944}, {"X": 0.21878451108932495, "Y": 0.3669116199016571}]}, "Id": "03b15eca-9fe8-42ea-bada-2c5940968cf2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ADVANCE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08602768927812576, "Height": 0.01040840893983841, "Left": 0.43892502784729004, "Top": 0.3564685881137848}, "Polygon": [{"X": 0.4389258623123169, "Y": 0.3564685881137848}, {"X": 0.5249527096748352, "Y": 0.35646986961364746}, {"X": 0.5249518752098083, "Y": 0.366877019405365}, {"X": 0.43892502784729004, "Y": 0.36687585711479187}]}, "Id": "4c040239-96b9-4bc2-a165-856dbe6000ae"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042338691651821136, "Height": 0.010369798168540001, "Left": 0.21870021522045135, "Top": 0.37713855504989624}, "Polygon": [{"X": 0.2187010645866394, "Y": 0.37713855504989624}, {"X": 0.2610388994216919, "Y": 0.37713906168937683}, {"X": 0.26103803515434265, "Y": 0.387508362531662}, {"X": 0.21870021522045135, "Y": 0.3875078856945038}]}, "Id": "55304813-ed9a-41f6-b3f9-327bfaa9ac90"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FROZEN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06858398765325546, "Height": 0.010339030995965004, "Left": 0.438956081867218, "Top": 0.3772141635417938}, "Polygon": [{"X": 0.4389568865299225, "Y": 0.3772141635417938}, {"X": 0.5075400471687317, "Y": 0.3772149682044983}, {"X": 0.5075392723083496, "Y": 0.3875531852245331}, {"X": 0.438956081867218, "Y": 0.3875524699687958}]}, "Id": "af0a21e4-ae01-4a87-a945-e475ac7b3cfd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Door:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04090114310383797, "Height": 0.010565846227109432, "Left": 0.21875661611557007, "Top": 0.3975154459476471}, "Polygon": [{"X": 0.2187574952840805, "Y": 0.3975154459476471}, {"X": 0.25965777039527893, "Y": 0.39751583337783813}, {"X": 0.2596569061279297, "Y": 0.4080812931060791}, {"X": 0.21875661611557007, "Y": 0.40808096528053284}]}, "Id": "f3b8f640-db84-4690-94c8-c6dc38b69797"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "96", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01749410852789879, "Height": 0.010599376633763313, "Left": 0.43892908096313477, "Top": 0.397325724363327}, "Polygon": [{"X": 0.438929945230484, "Y": 0.397325724363327}, {"X": 0.45642319321632385, "Y": 0.39732587337493896}, {"X": 0.456422358751297, "Y": 0.4079250991344452}, {"X": 0.43892908096313477, "Y": 0.40792495012283325}]}, "Id": "3a262477-83ed-4364-979f-8755e0715ccf"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06391377002000809, "Height": 0.01036794949322939, "Left": 0.2184695601463318, "Top": 0.42592674493789673}, "Polygon": [{"X": 0.21847042441368103, "Y": 0.42592674493789673}, {"X": 0.2823833227157593, "Y": 0.425927072763443}, {"X": 0.2823824882507324, "Y": 0.43629467487335205}, {"X": 0.2184695601463318, "Y": 0.43629443645477295}]}, "Id": "bb11a6d1-1071-472a-a500-bd36473cfbe6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Orders", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04801815003156662, "Height": 0.01050114631652832, "Left": 0.2869671881198883, "Top": 0.4259336590766907}, "Polygon": [{"X": 0.28696805238723755, "Y": 0.4259336590766907}, {"X": 0.33498531579971313, "Y": 0.42593392729759216}, {"X": 0.3349844813346863, "Y": 0.436434805393219}, {"X": 0.2869671881198883, "Y": 0.43643462657928467}]}, "Id": "dabb2df3-8c46-4b1a-8906-f73a5a571fdb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053068265318870544, "Height": 0.010385178960859776, "Left": 0.4394688904285431, "Top": 0.42603445053100586}, "Polygon": [{"X": 0.43946969509124756, "Y": 0.42603445053100586}, {"X": 0.49253714084625244, "Y": 0.42603474855422974}, {"X": 0.492536336183548, "Y": 0.43641963601112366}, {"X": 0.4394688904285431, "Y": 0.43641942739486694}]}, "Id": "080b6b00-57ca-4970-b680-723270849a3f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "00303622196", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0970558226108551, "Height": 0.010610029101371765, "Left": 0.21857324242591858, "Top": 0.44622406363487244}, "Polygon": [{"X": 0.21857412159442902, "Y": 0.44622406363487244}, {"X": 0.3156290650367737, "Y": 0.4462243318557739}, {"X": 0.31562820076942444, "Y": 0.4568341076374054}, {"X": 0.21857324242591858, "Y": 0.45683398842811584}]}, "Id": "e5529f48-4578-4439-ae5c-f75ad9aeb076"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "VAN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037760160863399506, "Height": 0.010168827138841152, "Left": 0.43942663073539734, "Top": 0.4464660882949829}, "Polygon": [{"X": 0.4394274353981018, "Y": 0.4464660882949829}, {"X": 0.47718679904937744, "Y": 0.4464661777019501}, {"X": 0.477185994386673, "Y": 0.45663490891456604}, {"X": 0.43942663073539734, "Y": 0.45663487911224365}]}, "Id": "3f79f87e-60e4-4b9c-8cb5-5b5b849db5b8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "ORIENTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0873822495341301, "Height": 0.01031826063990593, "Left": 0.48200926184654236, "Top": 0.4463803172111511}, "Polygon": [{"X": 0.4820100665092468, "Y": 0.4463803172111511}, {"X": 0.5693914890289307, "Y": 0.44638052582740784}, {"X": 0.5693907141685486, "Y": 0.45669856667518616}, {"X": 0.48200926184654236, "Y": 0.4566984474658966}]}, "Id": "44903bd0-5911-4e8c-b1f9-41d3e8abb89a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "FOOD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04812382161617279, "Height": 0.010303064249455929, "Left": 0.5738187432289124, "Top": 0.4463531970977783}, "Polygon": [{"X": 0.5738195180892944, "Y": 0.4463531970977783}, {"X": 0.6219425201416016, "Y": 0.44635331630706787}, {"X": 0.6219417452812195, "Y": 0.45665624737739563}, {"X": 0.5738187432289124, "Y": 0.45665618777275085}]}, "Id": "48b88868-8138-4ee4-a3b0-dceb5e55c74f"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "INC.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03359198570251465, "Height": 0.010330392979085445, "Left": 0.43944695591926575, "Top": 0.4621643126010895}, "Polygon": [{"X": 0.4394477605819702, "Y": 0.4621643126010895}, {"X": 0.4730389416217804, "Y": 0.4621643126010895}, {"X": 0.47303810715675354, "Y": 0.4724946618080139}, {"X": 0.43944695591926575, "Y": 0.4724946916103363}]}, "Id": "ec022059-0308-4ef2-9d43-1f19be889af0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03751768916845322, "Height": 0.010559851303696632, "Left": 0.21847528219223022, "Top": 0.4953329861164093}, "Polygon": [{"X": 0.21847614645957947, "Y": 0.49533313512802124}, {"X": 0.25599297881126404, "Y": 0.4953329861164093}, {"X": 0.2559920847415924, "Y": 0.5058926343917847}, {"X": 0.21847528219223022, "Y": 0.5058928728103638}]}, "Id": "45c0b15d-ea3d-4112-a3b6-f955ff1f63af"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Initial", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04166829213500023, "Height": 0.010571570135653019, "Left": 0.260814905166626, "Top": 0.49508604407310486}, "Polygon": [{"X": 0.2608157992362976, "Y": 0.4950861930847168}, {"X": 0.3024832010269165, "Y": 0.49508604407310486}, {"X": 0.30248233675956726, "Y": 0.5056573748588562}, {"X": 0.260814905166626, "Y": 0.5056576132774353}]}, "Id": "6154c519-c2d6-41c7-a8ea-3053583628d3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Pallets:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05139709636569023, "Height": 0.010606919415295124, "Left": 0.3067905604839325, "Top": 0.4952484369277954}, "Polygon": [{"X": 0.30679142475128174, "Y": 0.4952486455440521}, {"X": 0.3581876754760742, "Y": 0.4952484369277954}, {"X": 0.358186811208725, "Y": 0.5058550834655762}, {"X": 0.3067905604839325, "Y": 0.5058553814888}]}, "Id": "042a7909-c752-4a51-ade7-389bf50f7aaa"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "4.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03148221597075462, "Height": 0.010406574234366417, "Left": 0.4385485053062439, "Top": 0.49536436796188354}, "Polygon": [{"X": 0.43854930996894836, "Y": 0.4953644871711731}, {"X": 0.4700307250022888, "Y": 0.49536436796188354}, {"X": 0.47002989053726196, "Y": 0.505770742893219}, {"X": 0.4385485053062439, "Y": 0.5057709217071533}]}, "Id": "f7674a85-8c22-4f0b-86c8-ffa305fbe941"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03734225779771805, "Height": 0.010379420593380928, "Left": 0.21851858496665955, "Top": 0.5156083106994629}, "Polygon": [{"X": 0.2185194343328476, "Y": 0.515608549118042}, {"X": 0.255860835313797, "Y": 0.5156083106994629}, {"X": 0.25585997104644775, "Y": 0.5259873867034912}, {"X": 0.21851858496665955, "Y": 0.5259876847267151}]}, "Id": "45611401-dc71-4017-a068-a898d5016dbd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Finished", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06065022200345993, "Height": 0.010340074077248573, "Left": 0.26044484972953796, "Top": 0.51558518409729}, "Polygon": [{"X": 0.2604456841945648, "Y": 0.5155856013298035}, {"X": 0.3210950791835785, "Y": 0.51558518409729}, {"X": 0.32109421491622925, "Y": 0.525924801826477}, {"X": 0.26044484972953796, "Y": 0.5259252786636353}]}, "Id": "297b8bca-6ca1-4059-be6e-c150be5cb156"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Pallets:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05121858790516853, "Height": 0.010353880934417248, "Left": 0.32547855377197266, "Top": 0.5155971050262451}, "Polygon": [{"X": 0.3254793882369995, "Y": 0.5155974626541138}, {"X": 0.3766971230506897, "Y": 0.5155971050262451}, {"X": 0.37669631838798523, "Y": 0.52595055103302}, {"X": 0.32547855377197266, "Y": 0.5259509682655334}]}, "Id": "825f2fc8-a9f1-47df-8af2-85ffa5e8160f"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "8", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008067325688898563, "Height": 0.010253123007714748, "Left": 0.4397270381450653, "Top": 0.5156369209289551}, "Polygon": [{"X": 0.4397278428077698, "Y": 0.5156369805335999}, {"X": 0.4477943480014801, "Y": 0.5156369209289551}, {"X": 0.44779354333877563, "Y": 0.5258899331092834}, {"X": 0.4397270381450653, "Y": 0.5258899927139282}]}, "Id": "a833f32d-5cb7-40e9-85b2-4b01f10b3da8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03718587011098862, "Height": 0.010487527586519718, "Left": 0.21855255961418152, "Top": 0.5358574986457825}, "Polygon": [{"X": 0.21855343878269196, "Y": 0.5358578562736511}, {"X": 0.25573843717575073, "Y": 0.5358574986457825}, {"X": 0.2557375729084015, "Y": 0.5463445782661438}, {"X": 0.21855255961418152, "Y": 0.5463449954986572}]}, "Id": "42d6db7c-e0fb-41bd-9989-b8fa90f6c6ea"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Case", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03407511115074158, "Height": 0.010309748351573944, "Left": 0.2607256770133972, "Top": 0.5359793901443481}, "Polygon": [{"X": 0.2607265114784241, "Y": 0.535979688167572}, {"X": 0.2948007881641388, "Y": 0.5359793901443481}, {"X": 0.29479992389678955, "Y": 0.5462887287139893}, {"X": 0.2607256770133972, "Y": 0.5462891459465027}]}, "Id": "871d13f6-32f9-4689-a78a-b06f7db692ba"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Count:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0470103994011879, "Height": 0.010431044735014439, "Left": 0.29946380853652954, "Top": 0.5359748005867004}, "Polygon": [{"X": 0.2994646728038788, "Y": 0.5359752178192139}, {"X": 0.34647423028945923, "Y": 0.5359748005867004}, {"X": 0.34647336602211, "Y": 0.5464053153991699}, {"X": 0.29946380853652954, "Y": 0.5464057922363281}]}, "Id": "d75d288b-4c44-4c87-a62b-bbf7ec660077"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02613457478582859, "Height": 0.010369951836764812, "Left": 0.4390515685081482, "Top": 0.5359799265861511}, "Polygon": [{"X": 0.43905240297317505, "Y": 0.5359801650047302}, {"X": 0.46518614888191223, "Y": 0.5359799265861511}, {"X": 0.46518534421920776, "Y": 0.5463495850563049}, {"X": 0.4390515685081482, "Y": 0.5463498830795288}]}, "Id": "47d2100a-b462-4306-9d92-96d1ee1e9e14"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04898539558053017, "Height": 0.010410278104245663, "Left": 0.21863505244255066, "Top": 0.5565599799156189}, "Polygon": [{"X": 0.2186359167098999, "Y": 0.5565605759620667}, {"X": 0.2676204442977905, "Y": 0.5565599799156189}, {"X": 0.2676195800304413, "Y": 0.5669695734977722}, {"X": 0.21863505244255066, "Y": 0.5669702291488647}]}, "Id": "c87a4ed3-7b6d-4ca1-94bc-0d762c89b50b"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062238845974206924, "Height": 0.010489423759281635, "Left": 0.27077147364616394, "Top": 0.5566340088844299}, "Polygon": [{"X": 0.2707723081111908, "Y": 0.556634783744812}, {"X": 0.33301031589508057, "Y": 0.5566340088844299}, {"X": 0.3330094516277313, "Y": 0.5671225786209106}, {"X": 0.27077147364616394, "Y": 0.5671234130859375}]}, "Id": "697e0fc1-7de4-4650-92d2-293a65e0f368"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "06", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017446059733629227, "Height": 0.010391131974756718, "Left": 0.43928879499435425, "Top": 0.5565201044082642}, "Polygon": [{"X": 0.4392896294593811, "Y": 0.5565203428268433}, {"X": 0.45673486590385437, "Y": 0.5565201044082642}, {"X": 0.4567340314388275, "Y": 0.566910982131958}, {"X": 0.43928879499435425, "Y": 0.5669112205505371}]}, "Id": "fb985a78-72c3-4598-b28c-8d65eb07f5d1"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Tractor", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0525386705994606, "Height": 0.01032547652721405, "Left": 0.21881103515625, "Top": 0.5769659876823425}, "Polygon": [{"X": 0.21881189942359924, "Y": 0.5769667625427246}, {"X": 0.27134969830513, "Y": 0.5769659876823425}, {"X": 0.27134886384010315, "Y": 0.5872905850410461}, {"X": 0.21881103515625, "Y": 0.587291419506073}]}, "Id": "d51643d8-b39d-449c-af92-198a5a587cae"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06258339434862137, "Height": 0.01063830778002739, "Left": 0.2746317982673645, "Top": 0.5768864154815674}, "Polygon": [{"X": 0.27463266253471375, "Y": 0.5768873691558838}, {"X": 0.3372151851654053, "Y": 0.5768864154815674}, {"X": 0.33721432089805603, "Y": 0.5875236392021179}, {"X": 0.2746317982673645, "Y": 0.5875247120857239}]}, "Id": "396de86c-cd71-44ab-8495-b00252aa62c7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "BOL:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039586108177900314, "Height": 0.010258309543132782, "Left": 0.2186710238456726, "Top": 0.5973156094551086}, "Polygon": [{"X": 0.21867187321186066, "Y": 0.597316324710846}, {"X": 0.258257120847702, "Y": 0.5973156094551086}, {"X": 0.25825628638267517, "Y": 0.6075731515884399}, {"X": 0.2186710238456726, "Y": 0.607573926448822}]}, "Id": "edc8fc4e-1427-417c-b02d-bb8f7be300d5"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Comments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08017195761203766, "Height": 0.010574756190180779, "Left": 0.21885061264038086, "Top": 0.617591917514801}, "Polygon": [{"X": 0.2188514769077301, "Y": 0.6175935864448547}, {"X": 0.2990225553512573, "Y": 0.617591917514801}, {"X": 0.2990216910839081, "Y": 0.6281648874282837}, {"X": 0.21885061264038086, "Y": 0.628166675567627}]}, "Id": "203a6eed-d066-48fa-bf57-6f2777b45a78"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Canned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0540044866502285, "Height": 0.010135171003639698, "Left": 0.218829944729805, "Top": 0.6381883025169373}, "Polygon": [{"X": 0.21883077919483185, "Y": 0.6381895542144775}, {"X": 0.2728344202041626, "Y": 0.6381883025169373}, {"X": 0.27283358573913574, "Y": 0.6483221054077148}, {"X": 0.218829944729805, "Y": 0.6483234763145447}]}, "Id": "aa85abf4-61c6-426f-aea3-6937bf9d8ffc"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Comments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08021660149097443, "Height": 0.010328784584999084, "Left": 0.27748551964759827, "Top": 0.6381996273994446}, "Polygon": [{"X": 0.2774863839149475, "Y": 0.6382015347480774}, {"X": 0.3577021360397339, "Y": 0.6381996273994446}, {"X": 0.35770130157470703, "Y": 0.6485263705253601}, {"X": 0.27748551964759827, "Y": 0.6485283970832825}]}, "Id": "d7a91600-d7f7-497a-8cb3-a5b1d341581f"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Unloaders:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0765959620475769, "Height": 0.01072867400944233, "Left": 0.21852576732635498, "Top": 0.658429741859436}, "Polygon": [{"X": 0.21852664649486542, "Y": 0.6584317088127136}, {"X": 0.2951217293739319, "Y": 0.658429741859436}, {"X": 0.29512083530426025, "Y": 0.6691562533378601}, {"X": 0.21852576732635498, "Y": 0.669158399105072}]}, "Id": "ae4769f7-7aaf-4786-96ee-104b141724cc"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008404986932873726, "Height": 0.009890802204608917, "Left": 0.4391712546348572, "Top": 0.6587877869606018}, "Polygon": [{"X": 0.43917202949523926, "Y": 0.6587880253791809}, {"X": 0.44757622480392456, "Y": 0.6587877869606018}, {"X": 0.4475754499435425, "Y": 0.668678343296051}, {"X": 0.4391712546348572, "Y": 0.6686785817146301}]}, "Id": "3345be7e-1b23-45a1-81a8-d6352842aff0"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Base", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03498202934861183, "Height": 0.010462837293744087, "Left": 0.2185664027929306, "Top": 0.6871616244316101}, "Polygon": [{"X": 0.21856726706027985, "Y": 0.6871626973152161}, {"X": 0.25354841351509094, "Y": 0.6871616244316101}, {"X": 0.2535475492477417, "Y": 0.6976233720779419}, {"X": 0.2185664027929306, "Y": 0.6976244449615479}]}, "Id": "cfc24bef-0d6b-448c-b40b-910466b3487d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Charge:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05506422370672226, "Height": 0.013000315986573696, "Left": 0.25747478008270264, "Top": 0.6871854066848755}, "Polygon": [{"X": 0.2574758529663086, "Y": 0.6871870756149292}, {"X": 0.3125390112400055, "Y": 0.6871854066848755}, {"X": 0.3125379681587219, "Y": 0.7001839280128479}, {"X": 0.25747478008270264, "Y": 0.7001857161521912}]}, "Id": "83af66d9-9d74-452e-a526-0255cbb8876d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "86.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03917253389954567, "Height": 0.010388154536485672, "Left": 0.4397062063217163, "Top": 0.6871433854103088}, "Polygon": [{"X": 0.43970704078674316, "Y": 0.6871445775032043}, {"X": 0.4788787364959717, "Y": 0.6871433854103088}, {"X": 0.4788779318332672, "Y": 0.6975303292274475}, {"X": 0.4397062063217163, "Y": 0.6975315809249878}]}, "Id": "a3379d9c-28a4-42a2-b3f8-34e0fed5ea27"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Convenience", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09211201220750809, "Height": 0.010644346475601196, "Left": 0.21889370679855347, "Top": 0.7071917057037354}, "Polygon": [{"X": 0.2188945859670639, "Y": 0.7071947455406189}, {"X": 0.31100571155548096, "Y": 0.7071917057037354}, {"X": 0.3110048472881317, "Y": 0.7178328633308411}, {"X": 0.21889370679855347, "Y": 0.7178360223770142}]}, "Id": "4168c777-d371-4d1b-a2c8-574705c39248"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Fee:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029496967792510986, "Height": 0.010037470608949661, "Left": 0.31537070870399475, "Top": 0.707607090473175}, "Polygon": [{"X": 0.3153715133666992, "Y": 0.7076080441474915}, {"X": 0.34486767649650574, "Y": 0.707607090473175}, {"X": 0.3448668420314789, "Y": 0.7176435589790344}, {"X": 0.31537070870399475, "Y": 0.7176445722579956}]}, "Id": "fa35a8b9-a24a-490c-b9b4-271a7cf6db29"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "10.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03766478970646858, "Height": 0.00986252911388874, "Left": 0.4409220814704895, "Top": 0.7077041268348694}, "Polygon": [{"X": 0.44092288613319397, "Y": 0.7077053785324097}, {"X": 0.478586882352829, "Y": 0.7077041268348694}, {"X": 0.4785861074924469, "Y": 0.7175653576850891}, {"X": 0.4409220814704895, "Y": 0.7175666093826294}]}, "Id": "8151e186-15c9-4012-a4a0-6fb2ac21e117"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03699994832277298, "Height": 0.01046303566545248, "Left": 0.2187347561120987, "Top": 0.7277708649635315}, "Polygon": [{"X": 0.21873562037944794, "Y": 0.7277721762657166}, {"X": 0.25573471188545227, "Y": 0.7277708649635315}, {"X": 0.255733847618103, "Y": 0.7382325530052185}, {"X": 0.2187347561120987, "Y": 0.7382339239120483}]}, "Id": "aa968cf6-e9cc-41e6-8417-40ea5160b2e7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Cost:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036282990127801895, "Height": 0.010249231941998005, "Left": 0.2608644366264343, "Top": 0.7277215123176575}, "Polygon": [{"X": 0.2608652710914612, "Y": 0.7277228236198425}, {"X": 0.2971474230289459, "Y": 0.7277215123176575}, {"X": 0.29714658856391907, "Y": 0.7379693984985352}, {"X": 0.2608644366264343, "Y": 0.737970769405365}]}, "Id": "07b8f091-d709-4962-b30f-5e7368794861"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "96.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039326343685388565, "Height": 0.010553417727351189, "Left": 0.4393634498119354, "Top": 0.7277570962905884}, "Polygon": [{"X": 0.4393642842769623, "Y": 0.727758526802063}, {"X": 0.4786897897720337, "Y": 0.7277570962905884}, {"X": 0.47868895530700684, "Y": 0.7383090853691101}, {"X": 0.4393634498119354, "Y": 0.7383105158805847}]}, "Id": "2b6f508f-91eb-4551-be93-0350499190e2"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Payments:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07276341319084167, "Height": 0.012906787917017937, "Left": 0.21860915422439575, "Top": 0.7566024661064148}, "Polygon": [{"X": 0.21861021220684052, "Y": 0.756605327129364}, {"X": 0.2913725674152374, "Y": 0.7566024661064148}, {"X": 0.29137149453163147, "Y": 0.7695062160491943}, {"X": 0.21860915422439575, "Y": 0.7695092558860779}]}, "Id": "df6bc41d-9f16-41d6-809e-3983abbde9e2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05814646556973457, "Height": 0.010385558009147644, "Left": 0.43913525342941284, "Top": 0.7565652132034302}, "Polygon": [{"X": 0.4391360580921173, "Y": 0.7565675377845764}, {"X": 0.4972817003726959, "Y": 0.7565652132034302}, {"X": 0.49728089570999146, "Y": 0.766948401927948}, {"X": 0.43913525342941284, "Y": 0.766950786113739}]}, "Id": "e505f893-d46c-4a57-81df-f97df4aa7065"}, {"BlockType": "WORD", "Confidence": 99.32398223876953, "Text": "CapstonePay-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0974268838763237, "Height": 0.012792697176337242, "Left": 0.2544495165348053, "Top": 0.7769339680671692}, "Polygon": [{"X": 0.25445055961608887, "Y": 0.7769381403923035}, {"X": 0.3518764078617096, "Y": 0.7769339680671692}, {"X": 0.351875364780426, "Y": 0.7897223830223083}, {"X": 0.2544495165348053, "Y": 0.7897266745567322}]}, "Id": "06f0df7b-ea3c-42ea-b577-d7bc3988f899"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$96.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0475647896528244, "Height": 0.01108519360423088, "Left": 0.43960142135620117, "Top": 0.7766140103340149}, "Polygon": [{"X": 0.4396022856235504, "Y": 0.7766160368919373}, {"X": 0.4871661961078644, "Y": 0.7766140103340149}, {"X": 0.48716533184051514, "Y": 0.7876970767974854}, {"X": 0.43960142135620117, "Y": 0.7876991629600525}]}, "Id": "1bfc69fa-7b3d-4692-8838-1bebe9e4fb2d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "37882651", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06814589351415634, "Height": 0.010213878937065601, "Left": 0.2546252906322479, "Top": 0.7925258278846741}, "Polygon": [{"X": 0.2546261250972748, "Y": 0.7925288677215576}, {"X": 0.32277119159698486, "Y": 0.7925258278846741}, {"X": 0.322770357131958, "Y": 0.8027365803718567}, {"X": 0.2546252906322479, "Y": 0.8027397394180298}]}, "Id": "a857a0fb-5768-43b1-b579-c1b038720983"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03692834824323654, "Height": 0.010248527862131596, "Left": 0.25400683283805847, "Top": 0.8177762627601624}, "Polygon": [{"X": 0.2540076673030853, "Y": 0.8177779912948608}, {"X": 0.2909351587295532, "Y": 0.8177762627601624}, {"X": 0.29093432426452637, "Y": 0.8280229568481445}, {"X": 0.25400683283805847, "Y": 0.8280247449874878}]}, "Id": "e0301fd8-8e67-44ab-a7a7-870f1deb8f5f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Payments", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06846725940704346, "Height": 0.012706962414085865, "Left": 0.29552778601646423, "Top": 0.8181875944137573}, "Polygon": [{"X": 0.2955288290977478, "Y": 0.8181909322738647}, {"X": 0.3639950454235077, "Y": 0.8181875944137573}, {"X": 0.3639940321445465, "Y": 0.8308911919593811}, {"X": 0.29552778601646423, "Y": 0.8308945894241333}]}, "Id": "73e3bd9d-27de-4637-b75b-20fd493672f0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$96.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047483135014772415, "Height": 0.011110220104455948, "Left": 0.43963417410850525, "Top": 0.8174865245819092}, "Polygon": [{"X": 0.4396350681781769, "Y": 0.8174887895584106}, {"X": 0.48711732029914856, "Y": 0.8174865245819092}, {"X": 0.4871164560317993, "Y": 0.8285943865776062}, {"X": 0.43963417410850525, "Y": 0.8285967111587524}]}, "Id": "c058cf31-2881-485b-80a6-c09f4220b072"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "2d4bbf32-cc07-46db-b3c0-31bf842b883a", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "2d4bbf32-cc07-46db-b3c0-31bf842b883a", "content-type": "application/x-amz-json-1.1", "content-length": "73826", "date": "Thu, 18 Sep 2025 10:34:03 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "93398f05-ae28-484f-92af-8e56ca188e36", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 10:34:07 GMT", "content-type": "application/json", "content-length": "2416", "connection": "keep-alive", "x-amzn-requestid": "93398f05-ae28-484f-92af-8e56ca188e36"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. Need classify. Let's examine content.\n\nThere is header \"Capstone Logistics\" with address. There's a Receipt #, Work Date, Bill Code, Carrier: ADVANCE, Dock, Door, Purchase Orders, Vendor, Total Initial Pallets, Total Finished Pallets, Total Case Count, Trailer Number, Tractor Number, BOL: (but no number?), Comments, Canned Comments, Unloaders, Base Charge, Convenience Fee, Total Cost, Payments, Amount. This seems like a \"Lumper Receipt\" or \"Carrier Invoice\"? It includes charges: Base Charge, Convenience Fee, Total Cost, Payment. It seems to be a receipt for services (loading/unloading). There's \"BOL:\" but missing. It might be a \"Lumper Receipt\" because it includes number of pallets, unloaders, base charge, convenience fee. The description of lumper receipt: \"Invoice or Receipt for services provided (loading/unloading labor). Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\".\n\nThere are no PO numbers. But there is Purchase Orders and Vendor. It lists \"Total Initial Pallets\" and \"Total Finished Pallets\". That aligns with lumper receipt. Also \"BOL:\" but no number. Might be a \"Carrier Invoice\"? Invoice includes charges and payment. But invoice also includes line charges, but here it's just base charge and convenience fee. Lumper receipt is more specific to unloading. So we classify as lumper_receipt.\n\nLet's double-check: Are there any indicators for invoice? \"Base Charge\" \"Convenience Fee\" \"Total Cost\". These are charges. But \"Invoice\" usually has \"Invoice No\", \"Total Due\". There is \"Receipt #:\" maybe like receipt number. But the label \"Receipt #\" suggests it's a receipt rather than invoice. Also \"Bill Code\" maybe referencing a billing code. But this page seems to be a \"Lumper Receipt\".\n\nThus we produce tool call classify_logistics_doc_type with documents array: page_no: 1, doc_type: \"lumper_receipt\".\n\nWe must output via tool call."}}}, {"toolUse": {"toolUseId": "tooluse_QO3mln_zTB6f8dIw_Q7YSA", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "lumper_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2387, "outputTokens": 479, "totalTokens": 2866}, "metrics": {"latencyMs": 2098}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/fe5708ac_21_lumper_receipt_9.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: Difference betweeen invoice and lumper_receipt: If invoice is receipt is having lumper related description or lumper related keywords then it is lumper_receipt else it is invoice.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nCapstone Logistics\n30 TECHNOLOGY PKWY SOUTH SUITE 200\nPEACHTREE CORNERS, GA 30092\n770-414-1929\nFED ID# 45-3087555\n00:25:50 September 05, 2025\nReceipt #:\nelee7cal-bac0-\n4a81-9eb3-b164a689dc0e\nLocation:\nKROGER HUTCHINSON KS\nWork Date:\n2025-09-04\nBill Code:\nRCOD40245\nCarrier:\nADVANCE\nDock:\nFROZEN\nDoor:\n96\nPurchase Orders\nVendor\n00303622196\nVAN ORIENTAL FOOD\nINC.\nTotal Initial Pallets:\n4.00\nTotal Finished Pallets:\n8\nTotal Case Count:\n420\nTrailer Number:\n06\nTractor Number:\nBOL:\nComments:\nCanned Comments:\nUnloaders:\n2\nBase Charge:\n86.00\nConvenience Fee:\n10.00\nTotal Cost:\n96.00\nPayments:\nAmount\nCapstonePay-\n$96.00\n37882651\nTotal Payments\n$96.00\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}