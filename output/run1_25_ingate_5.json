{"classification_result": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9998699426651001, "Height": 1.0, "Left": 0.0001300478761550039, "Top": 0.0}, "Polygon": [{"X": 0.0001300478761550039, "Y": 7.976692359079607e-06}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.000137454379000701, "Y": 1.0}]}, "Id": "3bc3b112-91a5-4813-83d1-c244399e6d56", "Relationships": [{"Type": "CHILD", "Ids": ["ab578430-049d-4129-84cb-cc2d1c98b630", "e2fd2eb2-b7f7-4409-a158-5d9e2efb23e0", "b2cebc43-531f-49cf-ad70-0c977961ea2f", "1c3538e5-4d6f-4222-ba9a-aacde0c4d577", "4270f260-89f1-4f5b-8416-ed5349756339", "83ac4f3c-eb52-4646-98e9-e2271265d275", "ab6834e0-84ae-484a-905b-01e8c085d50f", "0de4834b-c7df-4230-9d1f-dfc6aa05053b", "37bc2df5-59ab-443e-a8e8-a94345871af2", "63579b67-b150-40e0-af16-40627b165f0f", "c4ab9804-984d-43ad-9c67-78c3cd83711d", "1b43dd1a-4b74-4eb6-927a-60f80df4d093", "5beb7980-e0d3-44d4-92f5-067abc60b652", "da1b4b26-7580-47ad-b962-0e47ba5c108e", "c8747353-2a53-4ff6-ab4e-968b8f153247", "264ad32f-00e2-4d48-aa65-1d62bab59137", "cee4a9a9-e051-423d-a544-5c562a1010b3", "387f68ee-4d28-4844-94c7-818208a6478a", "095db6ef-24b4-459e-afb6-7dcd060161d1", "2558b675-4d61-49ec-86c8-4c1f9b0788ec", "7ed2d894-eb2c-4fe1-8b7d-b7b5ff49f840", "3069fc4b-5221-4112-9a32-938c2c58e40f", "a4b7bdf4-596a-4471-8786-ce4cccf8a88b", "ac4abbe7-87e9-4b1a-8218-debeb9d87714", "404c085a-90f0-4887-ac28-648fbe2caf00", "c0254d54-14da-442b-818d-404b0e184938", "8a7f3614-ebc0-45a6-9404-e31fff6cccec", "4e9c37ae-c33a-4e17-afcd-d5420b9b1aac", "4806d469-ee88-4692-a75c-332b7bdd2416", "3052c865-e2f8-447f-9f4e-551933be0a61", "358813ca-2c67-448b-8689-e74628a55b61", "fc959016-99d6-4796-a9ad-10fe997dd72d", "0ea68701-a6cb-431b-90ea-75a99dae4fc2", "ac28d115-eb12-4ab7-84fe-677796aca7f6", "918261b9-c0d8-4f25-8ddd-382d113297a5", "60ab8107-5d14-4041-97e9-000db9f0b011", "606038ba-b925-41be-9ed3-27942e373383", "16ce6890-f62e-4878-ba4e-4548225204ac", "109d6c77-8566-4849-98b1-232848fc5755", "a6bd5837-40c8-45ae-a2c4-1c3fecc540f2"]}]}, {"BlockType": "LINE", "Confidence": 99.89898681640625, "Text": "Chicago Intermodal Terminal, IL, Ingate Receipt", "Geometry": {"BoundingBox": {"Width": 0.49583205580711365, "Height": 0.01562735252082348, "Left": 0.15269553661346436, "Top": 0.04982413724064827}, "Polygon": [{"X": 0.15269553661346436, "Y": 0.04983185976743698}, {"X": 0.6485275626182556, "Y": 0.04982413724064827}, {"X": 0.6485275626182556, "Y": 0.0654437467455864}, {"X": 0.1526956409215927, "Y": 0.0654514878988266}]}, "Id": "ab578430-049d-4129-84cb-cc2d1c98b630", "Relationships": [{"Type": "CHILD", "Ids": ["bffba5e7-59d2-4a5e-b7f9-60c35b3696ad", "4d675cef-6fc5-441f-b279-086838e0da41", "1578bef3-c9a7-445f-a19d-ac9b6391c809", "e695f6ea-18fa-4d74-b728-50cbc0f53e7c", "5cd7159f-fbff-486f-9a83-10187e262481", "d70b7b9d-2e58-4092-bb2b-502c12eeeb6e"]}]}, {"BlockType": "LINE", "Confidence": 99.9109115600586, "Text": "Ingate Unit", "Geometry": {"BoundingBox": {"Width": 0.09686187654733658, "Height": 0.013709219172596931, "Left": 0.1528203934431076, "Top": 0.06999711692333221}, "Polygon": [{"X": 0.1528203934431076, "Y": 0.06999862939119339}, {"X": 0.24968218803405762, "Y": 0.06999711692333221}, {"X": 0.2496822625398636, "Y": 0.08370482176542282}, {"X": 0.15282048285007477, "Y": 0.0837063416838646}]}, "Id": "e2fd2eb2-b7f7-4409-a158-5d9e2efb23e0", "Relationships": [{"Type": "CHILD", "Ids": ["75d4a043-3b00-4ed4-93b9-12223fc3d71b", "30eb9c01-74dd-4a94-b19c-7d5c80b048d6"]}]}, {"BlockType": "LINE", "Confidence": 99.50933074951172, "Text": "OOLU 913481", "Geometry": {"BoundingBox": {"Width": 0.1266184151172638, "Height": 0.011314869858324528, "Left": 0.5037274360656738, "Top": 0.06996160745620728}, "Polygon": [{"X": 0.5037274360656738, "Y": 0.06996358931064606}, {"X": 0.6303458213806152, "Y": 0.06996160745620728}, {"X": 0.6303458213806152, "Y": 0.08127449452877045}, {"X": 0.5037274360656738, "Y": 0.08127647638320923}]}, "Id": "b2cebc43-531f-49cf-ad70-0c977961ea2f", "Relationships": [{"Type": "CHILD", "Ids": ["98906fe2-3778-435e-8e62-4a7ff2f7206e", "ec379744-2eef-4c06-bf90-7dff3e29462c"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Load/Empty", "Geometry": {"BoundingBox": {"Width": 0.1075294092297554, "Height": 0.01394609734416008, "Left": 0.15236808359622955, "Top": 0.08888286352157593}, "Polygon": [{"X": 0.15236808359622955, "Y": 0.08888454735279083}, {"X": 0.2598974108695984, "Y": 0.08888286352157593}, {"X": 0.25989750027656555, "Y": 0.1028272733092308}, {"X": 0.15236817300319672, "Y": 0.1028289645910263}]}, "Id": "1c3538e5-4d6f-4222-ba9a-aacde0c4d577", "Relationships": [{"Type": "CHILD", "Ids": ["f6f287b0-55e7-4520-88c0-5a8afa113946"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Empty", "Geometry": {"BoundingBox": {"Width": 0.055618591606616974, "Height": 0.013661827892065048, "Left": 0.5046724081039429, "Top": 0.08917401731014252}, "Polygon": [{"X": 0.5046724081039429, "Y": 0.08917488902807236}, {"X": 0.5602909326553345, "Y": 0.08917401731014252}, {"X": 0.5602909922599792, "Y": 0.10283496975898743}, {"X": 0.5046724081039429, "Y": 0.10283584147691727}]}, "Id": "4270f260-89f1-4f5b-8416-ed5349756339", "Relationships": [{"Type": "CHILD", "Ids": ["e04fe2a1-8c6f-4952-8285-ab35685078d7"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.07129623740911484, "Height": 0.011534560471773148, "Left": 0.15186075866222382, "Top": 0.10799863934516907}, "Polygon": [{"X": 0.15186075866222382, "Y": 0.1079997643828392}, {"X": 0.22315692901611328, "Y": 0.10799863934516907}, {"X": 0.22315698862075806, "Y": 0.11953207850456238}, {"X": 0.15186083316802979, "Y": 0.11953320354223251}]}, "Id": "83ac4f3c-eb52-4646-98e9-e2271265d275", "Relationships": [{"Type": "CHILD", "Ids": ["0fc3ba5c-08eb-4cba-b6de-0504aeddba93"]}]}, {"BlockType": "LINE", "Confidence": 99.794921875, "Text": "TSXZ 409824", "Geometry": {"BoundingBox": {"Width": 0.12408867478370667, "Height": 0.011146623641252518, "Left": 0.5035973191261292, "Top": 0.10825013369321823}, "Polygon": [{"X": 0.5035973191261292, "Y": 0.10825208574533463}, {"X": 0.6276859641075134, "Y": 0.10825013369321823}, {"X": 0.6276860237121582, "Y": 0.11939480900764465}, {"X": 0.5035973787307739, "Y": 0.11939676105976105}]}, "Id": "ab6834e0-84ae-484a-905b-01e8c085d50f", "Relationships": [{"Type": "CHILD", "Ids": ["b3e9a52e-d022-4622-99a2-026b179c9f99", "658f9cf1-51af-4ec2-ab65-e559f793ddd8"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.04145992547273636, "Height": 0.011203224770724773, "Left": 0.1527303159236908, "Top": 0.12711133062839508}, "Polygon": [{"X": 0.1527303159236908, "Y": 0.1271119862794876}, {"X": 0.19419017434120178, "Y": 0.12711133062839508}, {"X": 0.19419024884700775, "Y": 0.1383139044046402}, {"X": 0.15273039042949677, "Y": 0.13831456005573273}]}, "Id": "0de4834b-c7df-4230-9d1f-dfc6aa05053b", "Relationships": [{"Type": "CHILD", "Ids": ["e9c41bcb-7875-4e24-bc89-59216966ea01"]}]}, {"BlockType": "LINE", "Confidence": 99.41080474853516, "Text": "2025-08-11 12:18 PM", "Geometry": {"BoundingBox": {"Width": 0.19464243948459625, "Height": 0.01124124601483345, "Left": 0.5032579898834229, "Top": 0.12713231146335602}, "Polygon": [{"X": 0.5032579898834229, "Y": 0.12713536620140076}, {"X": 0.6979004144668579, "Y": 0.12713231146335602}, {"X": 0.6979004740715027, "Y": 0.13837048411369324}, {"X": 0.5032580494880676, "Y": 0.13837355375289917}]}, "Id": "37bc2df5-59ab-443e-a8e8-a94345871af2", "Relationships": [{"Type": "CHILD", "Ids": ["2b886339-3a4f-46f9-89ed-19eded4703c5", "6eb1fa83-43eb-4b63-afb3-32cb922813dc", "9d58d25b-5468-4a8f-b12c-2ea1665009e1"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Shipper", "Geometry": {"BoundingBox": {"Width": 0.06946475803852081, "Height": 0.013723649084568024, "Left": 0.15229694545269012, "Top": 0.1459631770849228}, "Polygon": [{"X": 0.15229694545269012, "Y": 0.14596427977085114}, {"X": 0.22176162898540497, "Y": 0.1459631770849228}, {"X": 0.22176170349121094, "Y": 0.15968573093414307}, {"X": 0.1522970348596573, "Y": 0.1596868336200714}]}, "Id": "63579b67-b150-40e0-af16-40627b165f0f", "Relationships": [{"Type": "CHILD", "Ids": ["8541fcf5-3ab7-4bfe-a491-873ddde5fdfe"]}]}, {"BlockType": "LINE", "Confidence": 90.43608093261719, "Text": "00 c L Canada Inc", "Geometry": {"BoundingBox": {"Width": 0.18296130001544952, "Height": 0.011481700465083122, "Left": 0.5037415027618408, "Top": 0.14589883387088776}, "Polygon": [{"X": 0.5037415027618408, "Y": 0.14590172469615936}, {"X": 0.6867027878761292, "Y": 0.14589883387088776}, {"X": 0.6867027878761292, "Y": 0.15737763047218323}, {"X": 0.5037415027618408, "Y": 0.15738053619861603}]}, "Id": "c4ab9804-984d-43ad-9c67-78c3cd83711d", "Relationships": [{"Type": "CHILD", "Ids": ["962bd6a8-d25e-46a2-ac67-321d06571168", "d8d0f445-2ac3-4382-99b6-264f6fe81b60", "6d8ead85-d3cd-45f1-9165-c1b60f64986a", "8b210b15-57a0-4954-97a0-2c7ba23b1f63", "adb7bcda-a39c-4192-b30f-295f96d0eb2a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Destination", "Geometry": {"BoundingBox": {"Width": 0.09932281076908112, "Height": 0.011118222959339619, "Left": 0.15262827277183533, "Top": 0.16528023779392242}, "Polygon": [{"X": 0.15262827277183533, "Y": 0.16528181731700897}, {"X": 0.25195103883743286, "Y": 0.16528023779392242}, {"X": 0.25195109844207764, "Y": 0.17639689147472382}, {"X": 0.1526283472776413, "Y": 0.17639845609664917}]}, "Id": "1b43dd1a-4b74-4eb6-927a-60f80df4d093", "Relationships": [{"Type": "CHILD", "Ids": ["ecd23547-b515-4786-a4e3-8d1c22951bf5"]}]}, {"BlockType": "LINE", "Confidence": 99.88082122802734, "Text": "Chicago Intermodal Terminal, IL", "Geometry": {"BoundingBox": {"Width": 0.2868441939353943, "Height": 0.014040261507034302, "Left": 0.5035564303398132, "Top": 0.16493967175483704}, "Polygon": [{"X": 0.5035564303398132, "Y": 0.16494421660900116}, {"X": 0.7904006242752075, "Y": 0.16493967175483704}, {"X": 0.7904006242752075, "Y": 0.17897537350654602}, {"X": 0.503556489944458, "Y": 0.17897993326187134}]}, "Id": "5beb7980-e0d3-44d4-92f5-067abc60b652", "Relationships": [{"Type": "CHILD", "Ids": ["4804ec9a-9455-49f3-ab7d-e247de3e75f8", "b5f3047b-b635-428e-8185-8fcd34c339e7", "29c39c46-ec7e-4349-a636-35dbc55caa4c", "f9f134b4-26b0-4209-a124-7b8901c57a19"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Carrier", "Geometry": {"BoundingBox": {"Width": 0.0618550181388855, "Height": 0.011178399436175823, "Left": 0.15199419856071472, "Top": 0.18396824598312378}, "Polygon": [{"X": 0.15199419856071472, "Y": 0.18396922945976257}, {"X": 0.21384914219379425, "Y": 0.18396824598312378}, {"X": 0.21384921669960022, "Y": 0.1951456516981125}, {"X": 0.1519942581653595, "Y": 0.19514663517475128}]}, "Id": "da1b4b26-7580-47ad-b962-0e47ba5c108e", "Relationships": [{"Type": "CHILD", "Ids": ["c30cc454-d835-4057-9992-02ba8189c10f"]}]}, {"BlockType": "LINE", "Confidence": 98.8777847290039, "Text": "SSRT - Super Star Freight Inc", "Geometry": {"BoundingBox": {"Width": 0.26818352937698364, "Height": 0.014011104591190815, "Left": 0.5043727159500122, "Top": 0.1838904172182083}, "Polygon": [{"X": 0.5043727159500122, "Y": 0.18389469385147095}, {"X": 0.7725561857223511, "Y": 0.1838904172182083}, {"X": 0.7725562453269958, "Y": 0.19789725542068481}, {"X": 0.5043727159500122, "Y": 0.19790153205394745}]}, "Id": "c8747353-2a53-4ff6-ab4e-968b8f153247", "Relationships": [{"Type": "CHILD", "Ids": ["31920676-407f-4e3e-a152-2104cf020278", "b9478e10-1767-4aef-8a00-a7d261bc65f7", "1e780249-f94b-4f06-8f0c-02c1469c6997", "ed6cd045-0877-46cd-b2cb-400628d00e3c", "492c7d0e-b0c4-4204-9902-3922746a98aa", "c84e8a1f-33f8-4932-9d7c-68803b8f37d8"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Tractor", "Geometry": {"BoundingBox": {"Width": 0.06495099514722824, "Height": 0.011011927388608456, "Left": 0.1510106921195984, "Top": 0.20327264070510864}, "Polygon": [{"X": 0.1510106921195984, "Y": 0.20327366888523102}, {"X": 0.21596162021160126, "Y": 0.20327264070510864}, {"X": 0.21596167981624603, "Y": 0.2142835259437561}, {"X": 0.15101075172424316, "Y": 0.21428456902503967}]}, "Id": "264ad32f-00e2-4d48-aa65-1d62bab59137", "Relationships": [{"Type": "CHILD", "Ids": ["5fed9995-9eaf-4229-9bfa-8dfd7cff1bc4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "423", "Geometry": {"BoundingBox": {"Width": 0.033292874693870544, "Height": 0.010811411775648594, "Left": 0.5033022165298462, "Top": 0.20351667702198029}, "Polygon": [{"X": 0.5033022165298462, "Y": 0.20351719856262207}, {"X": 0.5365950465202332, "Y": 0.20351667702198029}, {"X": 0.5365951061248779, "Y": 0.21432755887508392}, {"X": 0.5033022165298462, "Y": 0.2143280804157257}]}, "Id": "cee4a9a9-e051-423d-a544-5c562a1010b3", "Relationships": [{"Type": "CHILD", "Ids": ["ba1b5af7-3145-4c46-bd33-34e51477e83a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Seal", "Geometry": {"BoundingBox": {"Width": 0.03916960582137108, "Height": 0.01152646355330944, "Left": 0.15222540497779846, "Top": 0.22204536199569702}, "Polygon": [{"X": 0.15222540497779846, "Y": 0.22204598784446716}, {"X": 0.19139495491981506, "Y": 0.22204536199569702}, {"X": 0.19139501452445984, "Y": 0.23357120156288147}, {"X": 0.15222547948360443, "Y": 0.2335718274116516}]}, "Id": "387f68ee-4d28-4844-94c7-818208a6478a", "Relationships": [{"Type": "CHILD", "Ids": ["f4a61a52-81e7-4861-ab73-28ce6cc52ff2"]}]}, {"BlockType": "LINE", "Confidence": 99.94100952148438, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.07913655042648315, "Height": 0.01127813570201397, "Left": 0.15249396860599518, "Top": 0.24090851843357086}, "Polygon": [{"X": 0.15249396860599518, "Y": 0.24090978503227234}, {"X": 0.23163045942783356, "Y": 0.24090851843357086}, {"X": 0.23163051903247833, "Y": 0.2521853744983673}, {"X": 0.15249404311180115, "Y": 0.25218665599823}]}, "Id": "095db6ef-24b4-459e-afb6-7dcd060161d1", "Relationships": [{"Type": "CHILD", "Ids": ["354bae12-69b2-44cc-9c3d-5e3d26ecbd51", "e3eef2ef-3f1a-40c2-8963-e31104104f70"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Booking", "Geometry": {"BoundingBox": {"Width": 0.07135244458913803, "Height": 0.013848054222762585, "Left": 0.1527012139558792, "Top": 0.2601776421070099}, "Polygon": [{"X": 0.1527012139558792, "Y": 0.260178804397583}, {"X": 0.22405359148979187, "Y": 0.2601776421070099}, {"X": 0.22405366599559784, "Y": 0.2740245461463928}, {"X": 0.15270130336284637, "Y": 0.27402570843696594}]}, "Id": "2558b675-4d61-49ec-86c8-4c1f9b0788ec", "Relationships": [{"Type": "CHILD", "Ids": ["562e6df5-baa9-445d-bcad-0587425de540"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "66250833CY", "Geometry": {"BoundingBox": {"Width": 0.11820000410079956, "Height": 0.011210411787033081, "Left": 0.5030768513679504, "Top": 0.2601531744003296}, "Polygon": [{"X": 0.5030768513679504, "Y": 0.2601550817489624}, {"X": 0.6212767958641052, "Y": 0.2601531744003296}, {"X": 0.62127685546875, "Y": 0.27136167883872986}, {"X": 0.5030768513679504, "Y": 0.27136358618736267}]}, "Id": "7ed2d894-eb2c-4fe1-8b7d-b7b5ff49f840", "Relationships": [{"Type": "CHILD", "Ids": ["2c2ef724-5769-4f41-9ff6-5b97f14fed5d"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Reservation", "Geometry": {"BoundingBox": {"Width": 0.10623567551374435, "Height": 0.011256775818765163, "Left": 0.15275460481643677, "Top": 0.27919068932533264}, "Polygon": [{"X": 0.15275460481643677, "Y": 0.27919238805770874}, {"X": 0.25899022817611694, "Y": 0.27919068932533264}, {"X": 0.2589902877807617, "Y": 0.2904457449913025}, {"X": 0.15275466442108154, "Y": 0.2904474437236786}]}, "Id": "3069fc4b-5221-4112-9a32-938c2c58e40f", "Relationships": [{"Type": "CHILD", "Ids": ["1896a387-6af8-426c-897a-4181987ae8d2"]}]}, {"BlockType": "LINE", "Confidence": 99.9255599975586, "Text": "Interchange From", "Geometry": {"BoundingBox": {"Width": 0.15779997408390045, "Height": 0.013852443546056747, "Left": 0.15259434282779694, "Top": 0.29803261160850525}, "Polygon": [{"X": 0.15259434282779694, "Y": 0.2980351746082306}, {"X": 0.3103942573070526, "Y": 0.29803261160850525}, {"X": 0.3103943169116974, "Y": 0.31188249588012695}, {"X": 0.1525944173336029, "Y": 0.3118850588798523}]}, "Id": "a4b7bdf4-596a-4471-8786-ce4cccf8a88b", "Relationships": [{"Type": "CHILD", "Ids": ["7d434b47-6401-4a4e-800b-05f2e7f51f7e", "8816ea62-816d-4c6c-a76a-d3741b090dfb"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Type", "Geometry": {"BoundingBox": {"Width": 0.0448664166033268, "Height": 0.013424400240182877, "Left": 0.15140195190906525, "Top": 0.31723856925964355}, "Polygon": [{"X": 0.15140195190906525, "Y": 0.31723928451538086}, {"X": 0.19626829028129578, "Y": 0.31723856925964355}, {"X": 0.19626836478710175, "Y": 0.33066222071647644}, {"X": 0.15140202641487122, "Y": 0.33066296577453613}]}, "Id": "ac4abbe7-87e9-4b1a-8218-debeb9d87714", "Relationships": [{"Type": "CHILD", "Ids": ["2fbc3a17-ed99-4c20-beef-78d3f49bab61"]}]}, {"BlockType": "LINE", "Confidence": 99.88800048828125, "Text": "40' Dry Container High Cube (KC4)", "Geometry": {"BoundingBox": {"Width": 0.31479114294052124, "Height": 0.013988574035465717, "Left": 0.503400444984436, "Top": 0.31697899103164673}, "Polygon": [{"X": 0.503400444984436, "Y": 0.316984087228775}, {"X": 0.8181915879249573, "Y": 0.31697899103164673}, {"X": 0.8181915879249573, "Y": 0.3309624493122101}, {"X": 0.5034005045890808, "Y": 0.33096757531166077}]}, "Id": "404c085a-90f0-4887-ac28-648fbe2caf00", "Relationships": [{"Type": "CHILD", "Ids": ["438959c8-54aa-40ac-8f25-283ea4ea017f", "a537da4e-7ee7-4044-8943-562a1fd65882", "11133b50-a791-49bd-b06b-f14e63cc59f1", "00f586b9-e3da-419c-bee0-dd66dd08ba0b", "b41f21de-1230-4353-8646-c9417d6d651d", "89b3c388-2da5-4cb2-b679-ba1fa668a935"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Weight", "Geometry": {"BoundingBox": {"Width": 0.06370960175991058, "Height": 0.013696547597646713, "Left": 0.15130877494812012, "Top": 0.3360541760921478}, "Polygon": [{"X": 0.15130877494812012, "Y": 0.3360552191734314}, {"X": 0.21501830220222473, "Y": 0.3360541760921478}, {"X": 0.2150183767080307, "Y": 0.34974968433380127}, {"X": 0.1513088494539261, "Y": 0.34975072741508484}]}, "Id": "c0254d54-14da-442b-818d-404b0e184938", "Relationships": [{"Type": "CHILD", "Ids": ["b1202661-263e-4e04-9e55-97252ba0e076"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Reefer", "Geometry": {"BoundingBox": {"Width": 0.0595998615026474, "Height": 0.011167101562023163, "Left": 0.15300743281841278, "Top": 0.35504281520843506}, "Polygon": [{"X": 0.15300743281841278, "Y": 0.35504379868507385}, {"X": 0.2126072347164154, "Y": 0.35504281520843506}, {"X": 0.21260729432106018, "Y": 0.3662089407444}, {"X": 0.15300750732421875, "Y": 0.3662099242210388}]}, "Id": "8a7f3614-ebc0-45a6-9404-e31fff6cccec", "Relationships": [{"Type": "CHILD", "Ids": ["8877d6b5-3ccb-465b-bcae-b4867136cd5e"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "N", "Geometry": {"BoundingBox": {"Width": 0.012917900457978249, "Height": 0.011074339039623737, "Left": 0.5041767954826355, "Top": 0.3551388084888458}, "Polygon": [{"X": 0.5041767954826355, "Y": 0.35513901710510254}, {"X": 0.5170946717262268, "Y": 0.3551388084888458}, {"X": 0.5170946717262268, "Y": 0.3662129342556}, {"X": 0.5041767954826355, "Y": 0.3662131428718567}]}, "Id": "4e9c37ae-c33a-4e17-afcd-d5420b9b1aac", "Relationships": [{"Type": "CHILD", "Ids": ["476aa560-1826-4edb-81b0-fc702f20f513"]}]}, {"BlockType": "LINE", "Confidence": 99.88121795654297, "Text": "Haz/Danger", "Geometry": {"BoundingBox": {"Width": 0.10716263204813004, "Height": 0.014118482358753681, "Left": 0.15244866907596588, "Top": 0.3740488290786743}, "Polygon": [{"X": 0.15244866907596588, "Y": 0.3740505874156952}, {"X": 0.25961121916770935, "Y": 0.3740488290786743}, {"X": 0.2596113085746765, "Y": 0.38816553354263306}, {"X": 0.15244875848293304, "Y": 0.38816729187965393}]}, "Id": "4806d469-ee88-4692-a75c-332b7bdd2416", "Relationships": [{"Type": "CHILD", "Ids": ["e33853f7-9401-49a4-a443-3f1dbf8b0467"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "N", "Geometry": {"BoundingBox": {"Width": 0.01280538085848093, "Height": 0.011092813685536385, "Left": 0.5042539238929749, "Top": 0.37410902976989746}, "Polygon": [{"X": 0.5042539238929749, "Y": 0.3741092383861542}, {"X": 0.****************, "Y": 0.37410902976989746}, {"X": 0.517059326171875, "Y": 0.385201632976532}, {"X": 0.5042539834976196, "Y": 0.3852018415927887}]}, "Id": "3052c865-e2f8-447f-9f4e-551933be0a61", "Relationships": [{"Type": "CHILD", "Ids": ["db730a3d-ec14-4906-8d9a-277dfa6aaea5"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "Placard Code", "Geometry": {"BoundingBox": {"Width": 0.12079853564500809, "Height": 0.011331695131957531, "Left": 0.15279659628868103, "Top": 0.39314004778862}, "Polygon": [{"X": 0.15279659628868103, "Y": 0.39314204454421997}, {"X": 0.27359506487846375, "Y": 0.39314004778862}, {"X": 0.2735951244831085, "Y": 0.***************}, {"X": 0.1527966558933258, "Y": 0.***************}]}, "Id": "358813ca-2c67-448b-8689-e74628a55b61", "Relationships": [{"Type": "CHILD", "Ids": ["5ddafce4-d51d-4fbe-8d61-512f01d02c37", "a8abee99-c9a0-445a-80b7-77598ba483f8"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "Empty to return to", "Geometry": {"BoundingBox": {"Width": 0.15905915200710297, "Height": 0.013680873438715935, "Left": 0.1528162956237793, "Top": 0.*****************}, "Polygon": [{"X": 0.1528162956237793, "Y": 0.*****************}, {"X": 0.3118753731250763, "Y": 0.*****************}, {"X": 0.31187546253204346, "Y": 0.****************}, {"X": 0.15281638503074646, "Y": 0.*************0273}]}, "Id": "fc959016-99d6-4796-a9ad-10fe997dd72d", "Relationships": [{"Type": "CHILD", "Ids": ["559c3ad2-2e7d-4477-b06c-e835e4c95966", "4537cd98-2d3c-4500-80be-9314bf2dca50", "0b80bd16-a707-4d09-9dc2-3e7b04d5b729", "7e1c93a5-8e47-42a4-ae72-db02cb6ace84"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Inspection", "Geometry": {"BoundingBox": {"Width": 0.09002100676298141, "Height": 0.013618151657283306, "Left": 0.1529204249382019, "Top": 0.43107375502586365}, "Polygon": [{"X": 0.1529204249382019, "Y": 0.43107521533966064}, {"X": 0.24294134974479675, "Y": 0.43107375502586365}, {"X": 0.24294142425060272, "Y": 0.44469040632247925}, {"X": 0.15292049944400787, "Y": 0.44469189643859863}]}, "Id": "0ea68701-a6cb-431b-90ea-75a99dae4fc2", "Relationships": [{"Type": "CHILD", "Ids": ["69b32a47-ef97-4aec-92f2-aaac49f8c5a7"]}]}, {"BlockType": "LINE", "Confidence": 99.98421478271484, "Text": "No damage reported by driver.", "Geometry": {"BoundingBox": {"Width": 0.271012544631958, "Height": 0.013887617737054825, "Left": 0.5042057037353516, "Top": 0.43113264441490173}, "Polygon": [{"X": 0.5042057037353516, "Y": 0.4311371147632599}, {"X": 0.7752182483673096, "Y": 0.43113264441490173}, {"X": 0.7752182483673096, "Y": 0.4450157880783081}, {"X": 0.5042057633399963, "Y": 0.44502025842666626}]}, "Id": "ac28d115-eb12-4ab7-84fe-677796aca7f6", "Relationships": [{"Type": "CHILD", "Ids": ["bcca2d4a-abfc-445c-bcf8-f9d34616e9f1", "e676b64c-32e8-4fa1-9f42-74a20dfd4614", "04519c54-902b-4bdd-a08d-7053a09b8615", "6c57656c-a696-4655-9122-979025f232b4", "007d2802-91ec-4ce2-a870-0442034c549f"]}]}, {"BlockType": "LINE", "Confidence": 99.95525360107422, "Text": "Bill Reference", "Geometry": {"BoundingBox": {"Width": 0.12451453506946564, "Height": 0.011253082193434238, "Left": 0.1524718999862671, "Top": 0.4502384662628174}, "Polygon": [{"X": 0.1524718999862671, "Y": 0.45024052262306213}, {"X": 0.27698636054992676, "Y": 0.4502384662628174}, {"X": 0.27698642015457153, "Y": 0.4614894688129425}, {"X": 0.15247195959091187, "Y": 0.46149152517318726}]}, "Id": "918261b9-c0d8-4f25-8ddd-382d113297a5", "Relationships": [{"Type": "CHILD", "Ids": ["10d8cf1b-641e-49c1-9c8f-cf8530aa6216", "bab33483-91d9-47d9-8e64-e95af3185983"]}]}, {"BlockType": "LINE", "Confidence": 99.912109375, "Text": "Yard Lot", "Geometry": {"BoundingBox": {"Width": 0.07655864208936691, "Height": 0.01116599515080452, "Left": 0.1515934020280838, "Top": 0.4691433012485504}, "Polygon": [{"X": 0.1515934020280838, "Y": 0.4691445827484131}, {"X": 0.22815199196338654, "Y": 0.4691433012485504}, {"X": 0.2281520515680313, "Y": 0.48030802607536316}, {"X": 0.15159347653388977, "Y": 0.48030930757522583}]}, "Id": "60ab8107-5d14-4041-97e9-000db9f0b011", "Relationships": [{"Type": "CHILD", "Ids": ["b6bcf7f1-5c1f-49ca-86c2-ee45b6d9e9bd", "e73886ae-2317-4b6d-bba4-4b1df4d9c66e"]}]}, {"BlockType": "LINE", "Confidence": 99.5166015625, "Text": "FL Y2", "Geometry": {"BoundingBox": {"Width": 0.05234026163816452, "Height": 0.011185243725776672, "Left": 0.5042990446090698, "Top": 0.4689925014972687}, "Polygon": [{"X": 0.5042990446090698, "Y": 0.4689933657646179}, {"X": 0.5566393136978149, "Y": 0.4689925014972687}, {"X": 0.5566393136978149, "Y": 0.4801768660545349}, {"X": 0.5042991042137146, "Y": 0.48017773032188416}]}, "Id": "606038ba-b925-41be-9ed3-27942e373383", "Relationships": [{"Type": "CHILD", "Ids": ["3b6ef438-23bc-4e65-9ab3-3de4d3b8a199", "80169dab-7a59-412a-a39e-29fd5b003e5e"]}]}, {"BlockType": "LINE", "Confidence": 99.96054077148438, "Text": "Yard Row", "Geometry": {"BoundingBox": {"Width": 0.08897729963064194, "Height": 0.011279888451099396, "Left": 0.15159207582473755, "Top": 0.4881525933742523}, "Polygon": [{"X": 0.15159207582473755, "Y": 0.4881540834903717}, {"X": 0.2405693233013153, "Y": 0.4881525933742523}, {"X": 0.24056938290596008, "Y": 0.49943098425865173}, {"X": 0.15159215033054352, "Y": 0.4994324743747711}]}, "Id": "16ce6890-f62e-4878-ba4e-4548225204ac", "Relationships": [{"Type": "CHILD", "Ids": ["737a301f-47d6-4fe7-a73e-07e20ca46ae5", "658992fe-1310-4f46-8477-85297e13ab7f"]}]}, {"BlockType": "LINE", "Confidence": 99.9755859375, "Text": "Yard Spot", "Geometry": {"BoundingBox": {"Width": 0.08998400717973709, "Height": 0.013696301728487015, "Left": 0.15162904560565948, "Top": 0.5067781209945679}, "Polygon": [{"X": 0.15162904560565948, "Y": 0.506779670715332}, {"X": 0.2416129857301712, "Y": 0.5067781209945679}, {"X": 0.24161306023597717, "Y": 0.5204729437828064}, {"X": 0.15162913501262665, "Y": 0.5204744338989258}]}, "Id": "109d6c77-8566-4849-98b1-232848fc5755", "Relationships": [{"Type": "CHILD", "Ids": ["4f3fae91-7fb8-490b-a62e-f09be90fc3ba", "bd2d1b75-973d-4f84-8788-58b53bda792b"]}]}, {"BlockType": "LINE", "Confidence": 99.9280014038086, "Text": "This unit was delivered/received in a good condition except as noted above.", "Geometry": {"BoundingBox": {"Width": 0.6749632954597473, "Height": 0.014189865440130234, "Left": 0.15141282975673676, "Top": 0.5259920954704285}, "Polygon": [{"X": 0.15141282975673676, "Y": 0.5260034203529358}, {"X": 0.8263760805130005, "Y": 0.5259920954704285}, {"X": 0.8263761401176453, "Y": 0.5401706099510193}, {"X": 0.15141291916370392, "Y": 0.5401819944381714}]}, "Id": "a6bd5837-40c8-45ae-a2c4-1c3fecc540f2", "Relationships": [{"Type": "CHILD", "Ids": ["b3e791b8-9595-475c-86b1-ea1d845694fc", "14f5ba2a-9bb8-4bc6-8098-5d365d64bab6", "b4f10775-a31a-4aa7-874a-08896c6102f9", "f1019265-8eed-4c15-a3cf-19fa42338e70", "f4c1afd8-1bf3-4ec9-adfc-8dc088d79914", "465ca8ae-6277-4f8f-92e5-3b729d0b449f", "c5ad48c3-5ed5-4497-917f-10f6fc0363d5", "1ca34137-72bd-4f7e-8a68-528fdfef85b5", "3e8cee57-a605-45bb-a023-2bb8362a8380", "70771bf4-61d7-4526-9919-3019472648ba", "924220bd-8cf5-45da-afa8-6beb36cd7306", "e2bf9c79-c5b3-4d91-940d-82467747c9a8"]}]}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Chicago", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08134983479976654, "Height": 0.015620896592736244, "Left": 0.15269553661346436, "Top": 0.049830593168735504}, "Polygon": [{"X": 0.15269553661346436, "Y": 0.04983185976743698}, {"X": 0.23404528200626373, "Y": 0.049830593168735504}, {"X": 0.2340453714132309, "Y": 0.06545022130012512}, {"X": 0.1526956409215927, "Y": 0.0654514878988266}]}, "Id": "bffba5e7-59d2-4a5e-b7f9-60c35b3696ad"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Intermodal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1136450320482254, "Height": 0.012467877939343452, "Left": 0.24001146852970123, "Top": 0.0499759204685688}, "Polygon": [{"X": 0.24001146852970123, "Y": 0.04997768998146057}, {"X": 0.35365644097328186, "Y": 0.0499759204685688}, {"X": 0.35365650057792664, "Y": 0.06244202330708504}, {"X": 0.2400115430355072, "Y": 0.062443800270557404}]}, "Id": "4d675cef-6fc5-441f-b279-086838e0da41"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Terminal,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10007729381322861, "Height": 0.014293878339231014, "Left": 0.35928821563720703, "Top": 0.05000438168644905}, "Polygon": [{"X": 0.35928821563720703, "Y": 0.05000593885779381}, {"X": 0.45936545729637146, "Y": 0.05000438168644905}, {"X": 0.45936551690101624, "Y": 0.06429670006036758}, {"X": 0.3592882752418518, "Y": 0.06429825723171234}]}, "Id": "1578bef3-c9a7-445f-a19d-ac9b6391c809"}, {"BlockType": "WORD", "Confidence": 99.73233795166016, "Text": "IL,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031076952815055847, "Height": 0.014016172848641872, "Left": 0.4650920033454895, "Top": 0.05000946670770645}, "Polygon": [{"X": 0.4650920033454895, "Y": 0.05000995099544525}, {"X": 0.49616891145706177, "Y": 0.05000946670770645}, {"X": 0.49616897106170654, "Y": 0.0640251561999321}, {"X": 0.4650920629501343, "Y": 0.0640256404876709}]}, "Id": "e695f6ea-18fa-4d74-b728-50cbc0f53e7c"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Ingate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06427092850208282, "Height": 0.01498682051897049, "Left": 0.5019687414169312, "Top": 0.05018756911158562}, "Polygon": [{"X": 0.5019687414169312, "Y": 0.0501885712146759}, {"X": 0.566239595413208, "Y": 0.05018756911158562}, {"X": 0.5662396550178528, "Y": 0.06517338752746582}, {"X": 0.5019688010215759, "Y": 0.0651743933558464}]}, "Id": "5cd7159f-fbff-486f-9a83-10187e262481"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07656582444906235, "Height": 0.015443828888237476, "Left": 0.5719617605209351, "Top": 0.04998818412423134}, "Polygon": [{"X": 0.5719617605209351, "Y": 0.049989376217126846}, {"X": 0.6485275626182556, "Y": 0.04998818412423134}, {"X": 0.6485275626182556, "Y": 0.06543081998825073}, {"X": 0.5719618201255798, "Y": 0.06543201208114624}]}, "Id": "d70b7b9d-2e58-4092-bb2b-502c12eeeb6e"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Ingate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054776035249233246, "Height": 0.013708561658859253, "Left": 0.1528203934431076, "Top": 0.06999778002500534}, "Polygon": [{"X": 0.1528203934431076, "Y": 0.06999862939119339}, {"X": 0.20759634673595428, "Y": 0.06999778002500534}, {"X": 0.20759642124176025, "Y": 0.08370547741651535}, {"X": 0.15282048285007477, "Y": 0.0837063416838646}]}, "Id": "75d4a043-3b00-4ed4-93b9-12223fc3d71b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Unit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035401348024606705, "Height": 0.011157341301441193, "Left": 0.214280903339386, "Top": 0.07004156708717346}, "Polygon": [{"X": 0.214280903339386, "Y": 0.07004211843013763}, {"X": 0.2496822029352188, "Y": 0.07004156708717346}, {"X": 0.2496822625398636, "Y": 0.08119835704565048}, {"X": 0.21428096294403076, "Y": 0.08119890838861465}]}, "Id": "30eb9c01-74dd-4a94-b19c-7d5c80b048d6"}, {"BlockType": "WORD", "Confidence": 99.2237319946289, "Text": "OOLU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055462807416915894, "Height": 0.011282323859632015, "Left": 0.5037274360656738, "Top": 0.06996272504329681}, "Polygon": [{"X": 0.5037274360656738, "Y": 0.06996358931064606}, {"X": 0.5591902136802673, "Y": 0.06996272504329681}, {"X": 0.5591902136802673, "Y": 0.08124417811632156}, {"X": 0.5037274360656738, "Y": 0.0812450423836708}]}, "Id": "98906fe2-3778-435e-8e62-4a7ff2f7206e"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "913481", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06379661709070206, "Height": 0.010958624072372913, "Left": 0.5665492415428162, "Top": 0.0703168734908104}, "Polygon": [{"X": 0.5665492415428162, "Y": 0.07031787186861038}, {"X": 0.6303458213806152, "Y": 0.0703168734908104}, {"X": 0.6303458213806152, "Y": 0.08127449452877045}, {"X": 0.5665492415428162, "Y": 0.08127549290657043}]}, "Id": "ec379744-2eef-4c06-bf90-7dff3e29462c"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Load/Empty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1075294092297554, "Height": 0.01394609734416008, "Left": 0.15236808359622955, "Top": 0.08888286352157593}, "Polygon": [{"X": 0.15236808359622955, "Y": 0.08888454735279083}, {"X": 0.2598974108695984, "Y": 0.08888286352157593}, {"X": 0.25989750027656555, "Y": 0.1028272733092308}, {"X": 0.15236817300319672, "Y": 0.1028289645910263}]}, "Id": "f6f287b0-55e7-4520-88c0-5a8afa113946"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Empty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055618591606616974, "Height": 0.013661827892065048, "Left": 0.5046724081039429, "Top": 0.08917401731014252}, "Polygon": [{"X": 0.5046724081039429, "Y": 0.08917488902807236}, {"X": 0.5602909326553345, "Y": 0.08917401731014252}, {"X": 0.5602909922599792, "Y": 0.10283496975898743}, {"X": 0.5046724081039429, "Y": 0.10283584147691727}]}, "Id": "e04fe2a1-8c6f-4952-8285-ab35685078d7"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07129623740911484, "Height": 0.011534560471773148, "Left": 0.15186075866222382, "Top": 0.10799863934516907}, "Polygon": [{"X": 0.15186075866222382, "Y": 0.1079997643828392}, {"X": 0.22315692901611328, "Y": 0.10799863934516907}, {"X": 0.22315698862075806, "Y": 0.11953207850456238}, {"X": 0.15186083316802979, "Y": 0.11953320354223251}]}, "Id": "0fc3ba5c-08eb-4cba-b6de-0504aeddba93"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "TSXZ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05104022100567818, "Height": 0.010980297811329365, "Left": 0.5035973191261292, "Top": 0.10825128108263016}, "Polygon": [{"X": 0.5035973191261292, "Y": 0.10825208574533463}, {"X": 0.5546375513076782, "Y": 0.10825128108263016}, {"X": 0.5546375513076782, "Y": 0.11923077702522278}, {"X": 0.5035973787307739, "Y": 0.11923158168792725}]}, "Id": "b3e9a52e-d022-4622-99a2-026b179c9f99"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "409824", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06712084263563156, "Height": 0.010885646566748619, "Left": 0.560565173625946, "Top": 0.10851021856069565}, "Polygon": [{"X": 0.560565173625946, "Y": 0.10851126909255981}, {"X": 0.6276859641075134, "Y": 0.10851021856069565}, {"X": 0.6276860237121582, "Y": 0.11939480900764465}, {"X": 0.560565173625946, "Y": 0.11939585953950882}]}, "Id": "658f9cf1-51af-4ec2-ab65-e559f793ddd8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04145992547273636, "Height": 0.011203224770724773, "Left": 0.1527303159236908, "Top": 0.12711133062839508}, "Polygon": [{"X": 0.1527303159236908, "Y": 0.1271119862794876}, {"X": 0.19419017434120178, "Y": 0.12711133062839508}, {"X": 0.19419024884700775, "Y": 0.1383139044046402}, {"X": 0.15273039042949677, "Y": 0.13831456005573273}]}, "Id": "e9c41bcb-7875-4e24-bc89-59216966ea01"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "2025-08-11", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09993787854909897, "Height": 0.010924438014626503, "Left": 0.5032579898834229, "Top": 0.1272181123495102}, "Polygon": [{"X": 0.5032579898834229, "Y": 0.12721969187259674}, {"X": 0.60319584608078, "Y": 0.1272181123495102}, {"X": 0.6031959056854248, "Y": 0.1381409764289856}, {"X": 0.5032580494880676, "Y": 0.13814255595207214}]}, "Id": "2b886339-3a4f-46f9-89ed-19eded4703c5"}, {"BlockType": "WORD", "Confidence": 99.21875, "Text": "12:18", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0488390289247036, "Height": 0.011169340461492538, "Left": 0.6133981347084045, "Top": 0.12720246613025665}, "Polygon": [{"X": 0.6133981347084045, "Y": 0.12720324099063873}, {"X": 0.6622371077537537, "Y": 0.12720246613025665}, {"X": 0.6622371673583984, "Y": 0.1383710354566574}, {"X": 0.6133981347084045, "Y": 0.1383718103170395}]}, "Id": "6eb1fa83-43eb-4b63-afb3-32cb922813dc"}, {"BlockType": "WORD", "Confidence": 99.248046875, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028859799727797508, "Height": 0.010957137681543827, "Left": 0.6690406203269958, "Top": 0.12713231146335602}, "Polygon": [{"X": 0.6690406203269958, "Y": 0.12713275849819183}, {"X": 0.6979004144668579, "Y": 0.12713231146335602}, {"X": 0.6979004740715027, "Y": 0.13808898627758026}, {"X": 0.6690406799316406, "Y": 0.13808944821357727}]}, "Id": "9d58d25b-5468-4a8f-b12c-2ea1665009e1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Shipper", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06946475803852081, "Height": 0.013723649084568024, "Left": 0.15229694545269012, "Top": 0.1459631770849228}, "Polygon": [{"X": 0.15229694545269012, "Y": 0.14596427977085114}, {"X": 0.22176162898540497, "Y": 0.1459631770849228}, {"X": 0.22176170349121094, "Y": 0.15968573093414307}, {"X": 0.1522970348596573, "Y": 0.1596868336200714}]}, "Id": "8541fcf5-3ab7-4bfe-a491-873ddde5fdfe"}, {"BlockType": "WORD", "Confidence": 95.11985778808594, "Text": "00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03663026914000511, "Height": 0.01140626147389412, "Left": 0.5037415027618408, "Top": 0.1459011435508728}, "Polygon": [{"X": 0.5037415027618408, "Y": 0.14590172469615936}, {"X": 0.5403717160224915, "Y": 0.1459011435508728}, {"X": 0.5403717756271362, "Y": 0.15730683505535126}, {"X": 0.5037415027618408, "Y": 0.15730741620063782}]}, "Id": "962bd6a8-d25e-46a2-ac67-321d06571168"}, {"BlockType": "WORD", "Confidence": 58.684627532958984, "Text": "c", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013506543822586536, "Height": 0.011072486639022827, "Left": 0.5463709235191345, "Top": 0.14601056277751923}, "Polygon": [{"X": 0.5463709235191345, "Y": 0.14601077139377594}, {"X": 0.5598774552345276, "Y": 0.14601056277751923}, {"X": 0.5598774552345276, "Y": 0.15708282589912415}, {"X": 0.5463709235191345, "Y": 0.15708304941654205}]}, "Id": "d8d0f445-2ac3-4382-99b6-264f6fe81b60"}, {"BlockType": "WORD", "Confidence": 98.98197937011719, "Text": "L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011107669211924076, "Height": 0.011077220551669598, "Left": 0.5665974617004395, "Top": 0.14608898758888245}, "Polygon": [{"X": 0.5665974617004395, "Y": 0.14608916640281677}, {"X": 0.5777051448822021, "Y": 0.14608898758888245}, {"X": 0.5777051448822021, "Y": 0.1571660339832306}, {"X": 0.5665975213050842, "Y": 0.15716621279716492}]}, "Id": "6d8ead85-d3cd-45f1-9165-c1b60f64986a"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "Canada", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07017859816551208, "Height": 0.011418106034398079, "Left": 0.5831371545791626, "Top": 0.14596116542816162}, "Polygon": [{"X": 0.5831371545791626, "Y": 0.14596228301525116}, {"X": 0.6533157229423523, "Y": 0.14596116542816162}, {"X": 0.6533157825469971, "Y": 0.1573781669139862}, {"X": 0.5831372141838074, "Y": 0.15737928450107574}]}, "Id": "8b210b15-57a0-4954-97a0-2c7ba23b1f63"}, {"BlockType": "WORD", "Confidence": 99.51271057128906, "Text": "Inc", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02628912776708603, "Height": 0.011044995859265327, "Left": 0.6604136824607849, "Top": 0.14623695611953735}, "Polygon": [{"X": 0.6604136824607849, "Y": 0.14623737335205078}, {"X": 0.6867027878761292, "Y": 0.14623695611953735}, {"X": 0.6867027878761292, "Y": 0.1572815328836441}, {"X": 0.6604136824607849, "Y": 0.15728195011615753}]}, "Id": "adb7bcda-a39c-4192-b30f-295f96d0eb2a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Destination", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09932281076908112, "Height": 0.011118222959339619, "Left": 0.15262827277183533, "Top": 0.16528023779392242}, "Polygon": [{"X": 0.15262827277183533, "Y": 0.16528181731700897}, {"X": 0.25195103883743286, "Y": 0.16528023779392242}, {"X": 0.25195109844207764, "Y": 0.17639689147472382}, {"X": 0.1526283472776413, "Y": 0.17639845609664917}]}, "Id": "ecd23547-b515-4786-a4e3-8d1c22951bf5"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Chicago", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07363059371709824, "Height": 0.014036880806088448, "Left": 0.5035564303398132, "Top": 0.16494305431842804}, "Polygon": [{"X": 0.5035564303398132, "Y": 0.16494421660900116}, {"X": 0.5771870017051697, "Y": 0.16494305431842804}, {"X": 0.5771870613098145, "Y": 0.17897875607013702}, {"X": 0.503556489944458, "Y": 0.17897993326187134}]}, "Id": "4804ec9a-9455-49f3-ab7d-e247de3e75f8"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Intermodal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09356534481048584, "Height": 0.011159739457070827, "Left": 0.5836135149002075, "Top": 0.1652449667453766}, "Polygon": [{"X": 0.5836135149002075, "Y": 0.16524644196033478}, {"X": 0.6771788001060486, "Y": 0.1652449667453766}, {"X": 0.6771788597106934, "Y": 0.17640320956707}, {"X": 0.5836135149002075, "Y": 0.1764046996831894}]}, "Id": "b5f3047b-b635-428e-8185-8fcd34c339e7"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "Terminal,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08379904925823212, "Height": 0.013087660074234009, "Left": 0.6830273270606995, "Top": 0.16517184674739838}, "Polygon": [{"X": 0.6830273270606995, "Y": 0.16517317295074463}, {"X": 0.766826331615448, "Y": 0.16517184674739838}, {"X": 0.766826331615448, "Y": 0.17825816571712494}, {"X": 0.6830273270606995, "Y": 0.17825950682163239}]}, "Id": "29c39c46-ec7e-4349-a636-35dbc55caa4c"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015797464177012444, "Height": 0.010904872789978981, "Left": 0.7746031880378723, "Top": 0.16530025005340576}, "Polygon": [{"X": 0.7746031880378723, "Y": 0.16530050337314606}, {"X": 0.7904006242752075, "Y": 0.16530025005340576}, {"X": 0.7904006242752075, "Y": 0.1762048751115799}, {"X": 0.7746031880378723, "Y": 0.1762051284313202}]}, "Id": "f9f134b4-26b0-4209-a124-7b8901c57a19"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Carrier", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0618550181388855, "Height": 0.011178399436175823, "Left": 0.15199419856071472, "Top": 0.18396824598312378}, "Polygon": [{"X": 0.15199419856071472, "Y": 0.18396922945976257}, {"X": 0.21384914219379425, "Y": 0.18396824598312378}, {"X": 0.21384921669960022, "Y": 0.1951456516981125}, {"X": 0.1519942581653595, "Y": 0.19514663517475128}]}, "Id": "c30cc454-d835-4057-9992-02ba8189c10f"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "SSRT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05262090265750885, "Height": 0.011199632659554482, "Left": 0.5043727159500122, "Top": 0.1839727759361267}, "Polygon": [{"X": 0.5043727159500122, "Y": 0.18397361040115356}, {"X": 0.5569935441017151, "Y": 0.1839727759361267}, {"X": 0.5569936037063599, "Y": 0.1951715648174286}, {"X": 0.5043727159500122, "Y": 0.19517241418361664}]}, "Id": "31920676-407f-4e3e-a152-2104cf020278"}, {"BlockType": "WORD", "Confidence": 93.55149841308594, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006207122001796961, "Height": 0.002250595483928919, "Left": 0.5629886388778687, "Top": 0.18972846865653992}, "Polygon": [{"X": 0.5629886388778687, "Y": 0.18972857296466827}, {"X": 0.5691957473754883, "Y": 0.18972846865653992}, {"X": 0.5691957473754883, "Y": 0.19197896122932434}, {"X": 0.5629886388778687, "Y": 0.1919790655374527}]}, "Id": "b9478e10-1767-4aef-8a00-a7d261bc65f7"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Super", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054098185151815414, "Height": 0.01382072176784277, "Left": 0.5754562020301819, "Top": 0.18390411138534546}, "Polygon": [{"X": 0.5754562020301819, "Y": 0.1839049756526947}, {"X": 0.6295543313026428, "Y": 0.18390411138534546}, {"X": 0.6295543909072876, "Y": 0.19772396981716156}, {"X": 0.5754562020301819, "Y": 0.1977248340845108}]}, "Id": "1e780249-f94b-4f06-8f0c-02c1469c6997"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Star", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03673502057790756, "Height": 0.011264564469456673, "Left": 0.6352387070655823, "Top": 0.18389202654361725}, "Polygon": [{"X": 0.6352387070655823, "Y": 0.1838926076889038}, {"X": 0.671973705291748, "Y": 0.18389202654361725}, {"X": 0.671973705291748, "Y": 0.1951560080051422}, {"X": 0.6352387070655823, "Y": 0.19515658915042877}]}, "Id": "ed6cd045-0877-46cd-b2cb-400628d00e3c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06189645454287529, "Height": 0.013987567275762558, "Left": 0.6781958937644958, "Top": 0.18391118943691254}, "Polygon": [{"X": 0.6781958937644958, "Y": 0.18391217291355133}, {"X": 0.7400923371315002, "Y": 0.18391118943691254}, {"X": 0.7400923371315002, "Y": 0.1978977769613266}, {"X": 0.6781958937644958, "Y": 0.1978987604379654}]}, "Id": "492c7d0e-b0c4-4204-9902-3922746a98aa"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "Inc", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02620757929980755, "Height": 0.011110185645520687, "Left": 0.7463486194610596, "Top": 0.18396668136119843}, "Polygon": [{"X": 0.7463486194610596, "Y": 0.18396709859371185}, {"X": 0.7725561857223511, "Y": 0.18396668136119843}, {"X": 0.7725562453269958, "Y": 0.19507645070552826}, {"X": 0.7463486790657043, "Y": 0.1950768679380417}]}, "Id": "c84e8a1f-33f8-4932-9d7c-68803b8f37d8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Tractor", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06495099514722824, "Height": 0.011011927388608456, "Left": 0.1510106921195984, "Top": 0.20327264070510864}, "Polygon": [{"X": 0.1510106921195984, "Y": 0.20327366888523102}, {"X": 0.21596162021160126, "Y": 0.20327264070510864}, {"X": 0.21596167981624603, "Y": 0.2142835259437561}, {"X": 0.15101075172424316, "Y": 0.21428456902503967}]}, "Id": "5fed9995-9eaf-4229-9bfa-8dfd7cff1bc4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "423", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033292874693870544, "Height": 0.010811411775648594, "Left": 0.5033022165298462, "Top": 0.20351667702198029}, "Polygon": [{"X": 0.5033022165298462, "Y": 0.20351719856262207}, {"X": 0.5365950465202332, "Y": 0.20351667702198029}, {"X": 0.5365951061248779, "Y": 0.21432755887508392}, {"X": 0.5033022165298462, "Y": 0.2143280804157257}]}, "Id": "ba1b5af7-3145-4c46-bd33-34e51477e83a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Seal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03916960582137108, "Height": 0.01152646355330944, "Left": 0.15222540497779846, "Top": 0.22204536199569702}, "Polygon": [{"X": 0.15222540497779846, "Y": 0.22204598784446716}, {"X": 0.19139495491981506, "Y": 0.22204536199569702}, {"X": 0.19139501452445984, "Y": 0.23357120156288147}, {"X": 0.15222547948360443, "Y": 0.2335718274116516}]}, "Id": "f4a61a52-81e7-4861-ab73-28ce6cc52ff2"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03431059420108795, "Height": 0.011164787225425243, "Left": 0.15249396860599518, "Top": 0.24102185666561127}, "Polygon": [{"X": 0.15249396860599518, "Y": 0.24102240800857544}, {"X": 0.18680450320243835, "Y": 0.24102185666561127}, {"X": 0.18680456280708313, "Y": 0.2521860897541046}, {"X": 0.15249404311180115, "Y": 0.25218665599823}]}, "Id": "354bae12-69b2-44cc-9c3d-5e3d26ecbd51"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Seal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03892814740538597, "Height": 0.011244233697652817, "Left": 0.19270236790180206, "Top": 0.24090851843357086}, "Polygon": [{"X": 0.19270236790180206, "Y": 0.2409091293811798}, {"X": 0.23163045942783356, "Y": 0.24090851843357086}, {"X": 0.23163051903247833, "Y": 0.25215211510658264}, {"X": 0.19270244240760803, "Y": 0.2521527409553528}]}, "Id": "e3eef2ef-3f1a-40c2-8963-e31104104f70"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Booking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07135244458913803, "Height": 0.013848054222762585, "Left": 0.1527012139558792, "Top": 0.2601776421070099}, "Polygon": [{"X": 0.1527012139558792, "Y": 0.260178804397583}, {"X": 0.22405359148979187, "Y": 0.2601776421070099}, {"X": 0.22405366599559784, "Y": 0.2740245461463928}, {"X": 0.15270130336284637, "Y": 0.27402570843696594}]}, "Id": "562e6df5-baa9-445d-bcad-0587425de540"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "66250833CY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11820000410079956, "Height": 0.011210411787033081, "Left": 0.5030768513679504, "Top": 0.2601531744003296}, "Polygon": [{"X": 0.5030768513679504, "Y": 0.2601550817489624}, {"X": 0.6212767958641052, "Y": 0.2601531744003296}, {"X": 0.62127685546875, "Y": 0.27136167883872986}, {"X": 0.5030768513679504, "Y": 0.27136358618736267}]}, "Id": "2c2ef724-5769-4f41-9ff6-5b97f14fed5d"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Reservation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10623567551374435, "Height": 0.011256775818765163, "Left": 0.15275460481643677, "Top": 0.27919068932533264}, "Polygon": [{"X": 0.15275460481643677, "Y": 0.27919238805770874}, {"X": 0.25899022817611694, "Y": 0.27919068932533264}, {"X": 0.2589902877807617, "Y": 0.2904457449913025}, {"X": 0.15275466442108154, "Y": 0.2904474437236786}]}, "Id": "1896a387-6af8-426c-897a-4181987ae8d2"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Interchange", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10552258789539337, "Height": 0.01376951765269041, "Left": 0.15259434282779694, "Top": 0.2981155514717102}, "Polygon": [{"X": 0.15259434282779694, "Y": 0.2981172502040863}, {"X": 0.25811684131622314, "Y": 0.2981155514717102}, {"X": 0.2581169307231903, "Y": 0.3118833601474762}, {"X": 0.1525944173336029, "Y": 0.3118850588798523}]}, "Id": "7d434b47-6401-4a4e-800b-05f2e7f51f7e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "From", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04563988000154495, "Height": 0.011110947467386723, "Left": 0.26475441455841064, "Top": 0.29803261160850525}, "Polygon": [{"X": 0.26475441455841064, "Y": 0.29803335666656494}, {"X": 0.3103942573070526, "Y": 0.29803261160850525}, {"X": 0.3103943169116974, "Y": 0.3091428279876709}, {"X": 0.2647544741630554, "Y": 0.3091435730457306}]}, "Id": "8816ea62-816d-4c6c-a76a-d3741b090dfb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Type", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0448664166033268, "Height": 0.013424400240182877, "Left": 0.15140195190906525, "Top": 0.31723856925964355}, "Polygon": [{"X": 0.15140195190906525, "Y": 0.31723928451538086}, {"X": 0.19626829028129578, "Y": 0.31723856925964355}, {"X": 0.19626836478710175, "Y": 0.33066222071647644}, {"X": 0.15140202641487122, "Y": 0.33066296577453613}]}, "Id": "2fbc3a17-ed99-4c20-beef-78d3f49bab61"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "40'", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02613985165953636, "Height": 0.01134794857352972, "Left": 0.503400444984436, "Top": 0.31716638803482056}, "Polygon": [{"X": 0.503400444984436, "Y": 0.317166805267334}, {"X": 0.5295403003692627, "Y": 0.31716638803482056}, {"X": 0.5295403003692627, "Y": 0.32851389050483704}, {"X": 0.5034005045890808, "Y": 0.32851430773735046}]}, "Id": "438959c8-54aa-40ac-8f25-283ea4ea017f"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Dry", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03057485818862915, "Height": 0.013447295874357224, "Left": 0.5361612439155579, "Top": 0.3172645568847656}, "Polygon": [{"X": 0.5361612439155579, "Y": 0.3172650635242462}, {"X": 0.566736102104187, "Y": 0.3172645568847656}, {"X": 0.566736102104187, "Y": 0.33071136474609375}, {"X": 0.5361613035202026, "Y": 0.33071184158325195}]}, "Id": "a537da4e-7ee7-4044-8943-562a1fd65882"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "Container", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0873722955584526, "Height": 0.01147089060395956, "Left": 0.5728180408477783, "Top": 0.31698155403137207}, "Polygon": [{"X": 0.5728180408477783, "Y": 0.3169829845428467}, {"X": 0.6601903438568115, "Y": 0.31698155403137207}, {"X": 0.6601903438568115, "Y": 0.328451007604599}, {"X": 0.5728181004524231, "Y": 0.3284524381160736}]}, "Id": "11133b50-a791-49bd-b06b-f14e63cc59f1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "High", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0402262881398201, "Height": 0.013859902508556843, "Left": 0.6658156514167786, "Top": 0.3171050250530243}, "Polygon": [{"X": 0.6658156514167786, "Y": 0.3171056807041168}, {"X": 0.7060419321060181, "Y": 0.3171050250530243}, {"X": 0.7060419917106628, "Y": 0.33096426725387573}, {"X": 0.6658157110214233, "Y": 0.33096492290496826}]}, "Id": "00f586b9-e3da-419c-bee0-dd66dd08ba0b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "C<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046758364886045456, "Height": 0.011461007408797741, "Left": 0.712795078754425, "Top": 0.3169872760772705}, "Polygon": [{"X": 0.712795078754425, "Y": 0.3169880509376526}, {"X": 0.7595534324645996, "Y": 0.3169872760772705}, {"X": 0.7595534324645996, "Y": 0.32844752073287964}, {"X": 0.7127951383590698, "Y": 0.3284482955932617}]}, "Id": "b41f21de-1230-4353-8646-c9417d6d651d"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "(KC4)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05154622346162796, "Height": 0.013469689525663853, "Left": 0.7666453719139099, "Top": 0.31698521971702576}, "Polygon": [{"X": 0.7666453719139099, "Y": 0.3169860541820526}, {"X": 0.8181915879249573, "Y": 0.31698521971702576}, {"X": 0.8181915879249573, "Y": 0.3304540812969208}, {"X": 0.7666453719139099, "Y": 0.33045491576194763}]}, "Id": "89b3c388-2da5-4cb2-b679-ba1fa668a935"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06370960175991058, "Height": 0.013696547597646713, "Left": 0.15130877494812012, "Top": 0.3360541760921478}, "Polygon": [{"X": 0.15130877494812012, "Y": 0.3360552191734314}, {"X": 0.21501830220222473, "Y": 0.3360541760921478}, {"X": 0.2150183767080307, "Y": 0.34974968433380127}, {"X": 0.1513088494539261, "Y": 0.34975072741508484}]}, "Id": "b1202661-263e-4e04-9e55-97252ba0e076"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Reefer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0595998615026474, "Height": 0.011167101562023163, "Left": 0.15300743281841278, "Top": 0.35504281520843506}, "Polygon": [{"X": 0.15300743281841278, "Y": 0.35504379868507385}, {"X": 0.2126072347164154, "Y": 0.35504281520843506}, {"X": 0.21260729432106018, "Y": 0.3662089407444}, {"X": 0.15300750732421875, "Y": 0.3662099242210388}]}, "Id": "8877d6b5-3ccb-465b-bcae-b4867136cd5e"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "N", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012917900457978249, "Height": 0.011074339039623737, "Left": 0.5041767954826355, "Top": 0.3551388084888458}, "Polygon": [{"X": 0.5041767954826355, "Y": 0.35513901710510254}, {"X": 0.5170946717262268, "Y": 0.3551388084888458}, {"X": 0.5170946717262268, "Y": 0.3662129342556}, {"X": 0.5041767954826355, "Y": 0.3662131428718567}]}, "Id": "476aa560-1826-4edb-81b0-fc702f20f513"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "Haz/Danger", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10716263204813004, "Height": 0.014118482358753681, "Left": 0.15244866907596588, "Top": 0.3740488290786743}, "Polygon": [{"X": 0.15244866907596588, "Y": 0.3740505874156952}, {"X": 0.25961121916770935, "Y": 0.3740488290786743}, {"X": 0.2596113085746765, "Y": 0.38816553354263306}, {"X": 0.15244875848293304, "Y": 0.38816729187965393}]}, "Id": "e33853f7-9401-49a4-a443-3f1dbf8b0467"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "N", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01280538085848093, "Height": 0.011092813685536385, "Left": 0.5042539238929749, "Top": 0.37410902976989746}, "Polygon": [{"X": 0.5042539238929749, "Y": 0.3741092383861542}, {"X": 0.****************, "Y": 0.37410902976989746}, {"X": 0.517059326171875, "Y": 0.385201632976532}, {"X": 0.5042539834976196, "Y": 0.3852018415927887}]}, "Id": "db730a3d-ec14-4906-8d9a-277dfa6aaea5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Placard", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06672705709934235, "Height": 0.011291365139186382, "Left": 0.15279659628868103, "Top": 0.39318037033081055}, "Polygon": [{"X": 0.15279659628868103, "Y": 0.3931814730167389}, {"X": 0.2195235937833786, "Y": 0.39318037033081055}, {"X": 0.21952365338802338, "Y": 0.*****************}, {"X": 0.1527966558933258, "Y": 0.***************}]}, "Id": "5ddafce4-d51d-4fbe-8d61-512f01d02c37"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Code", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047560472041368484, "Height": 0.011277616024017334, "Left": 0.22603465616703033, "Top": 0.39314004778862}, "Polygon": [{"X": 0.22603465616703033, "Y": 0.3931408226490021}, {"X": 0.27359506487846375, "Y": 0.39314004778862}, {"X": 0.2735951244831085, "Y": 0.*****************}, {"X": 0.2260347157716751, "Y": 0.*****************}]}, "Id": "a8abee99-c9a0-445a-80b7-77598ba483f8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Empty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05605022609233856, "Height": 0.01367917750030756, "Left": 0.1528162956237793, "Top": 0.41200050711631775}, "Polygon": [{"X": 0.1528162956237793, "Y": 0.*****************}, {"X": 0.2088664472103119, "Y": 0.41200050711631775}, {"X": 0.20886652171611786, "Y": 0.4256787598133087}, {"X": 0.15281638503074646, "Y": 0.*************0273}]}, "Id": "559c3ad2-2e7d-4477-b06c-e835e4c95966"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016902673989534378, "Height": 0.010179564356803894, "Left": 0.21433855593204498, "Top": 0.4130086302757263}, "Polygon": [{"X": 0.21433855593204498, "Y": 0.4130088984966278}, {"X": 0.23124118149280548, "Y": 0.4130086302757263}, {"X": 0.23124122619628906, "Y": 0.42318791151046753}, {"X": 0.21433861553668976, "Y": 0.4231882095336914}]}, "Id": "4537cd98-2d3c-4500-80be-9314bf2dca50"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "return", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051569465547800064, "Height": 0.01028051134198904, "Left": 0.23750531673431396, "Top": 0.41308045387268066}, "Polygon": [{"X": 0.23750531673431396, "Y": 0.4130813181400299}, {"X": 0.28907471895217896, "Y": 0.41308045387268066}, {"X": 0.28907477855682373, "Y": 0.42336010932922363}, {"X": 0.23750537633895874, "Y": 0.4233609735965729}]}, "Id": "0b80bd16-a707-4d09-9dc2-3e7b04d5b729"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01693633571267128, "Height": 0.010140975005924702, "Left": 0.2949391007423401, "Top": 0.4130346477031708}, "Polygon": [{"X": 0.2949391007423401, "Y": 0.41303494572639465}, {"X": 0.3118754029273987, "Y": 0.4130346477031708}, {"X": 0.31187543272972107, "Y": 0.4231753647327423}, {"X": 0.29493916034698486, "Y": 0.4231756329536438}]}, "Id": "7e1c93a5-8e47-42a4-ae72-db02cb6ace84"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09002100676298141, "Height": 0.013618151657283306, "Left": 0.1529204249382019, "Top": 0.43107375502586365}, "Polygon": [{"X": 0.1529204249382019, "Y": 0.43107521533966064}, {"X": 0.24294134974479675, "Y": 0.43107375502586365}, {"X": 0.24294142425060272, "Y": 0.44469040632247925}, {"X": 0.15292049944400787, "Y": 0.44469189643859863}]}, "Id": "69b32a47-ef97-4aec-92f2-aaac49f8c5a7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "No", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024747736752033234, "Height": 0.011083640158176422, "Left": 0.5042057037353516, "Top": 0.4313015341758728}, "Polygon": [{"X": 0.5042057037353516, "Y": 0.43130192160606384}, {"X": 0.5289534330368042, "Y": 0.4313015341758728}, {"X": 0.5289534330368042, "Y": 0.4423847496509552}, {"X": 0.5042057633399963, "Y": 0.44238516688346863}]}, "Id": "bcca2d4a-abfc-445c-bcf8-f9d34616e9f1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "damage", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07263562083244324, "Height": 0.01385328359901905, "Left": 0.5346566438674927, "Top": 0.43116647005081177}, "Polygon": [{"X": 0.5346566438674927, "Y": 0.4311676621437073}, {"X": 0.6072922348976135, "Y": 0.43116647005081177}, {"X": 0.6072922945022583, "Y": 0.44501855969429016}, {"X": 0.5346567034721375, "Y": 0.44501975178718567}]}, "Id": "e676b64c-32e8-4fa1-9f42-74a20dfd4614"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "reported", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07374618202447891, "Height": 0.013382638804614544, "Left": 0.6137844920158386, "Top": 0.43127012252807617}, "Polygon": [{"X": 0.6137844920158386, "Y": 0.43127134442329407}, {"X": 0.6875306367874146, "Y": 0.43127012252807617}, {"X": 0.6875306367874146, "Y": 0.4446515440940857}, {"X": 0.6137844920158386, "Y": 0.4446527659893036}]}, "Id": "04519c54-902b-4bdd-a08d-7053a09b8615"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "by", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021052444353699684, "Height": 0.013545721769332886, "Left": 0.6941569447517395, "Top": 0.4311336278915405}, "Polygon": [{"X": 0.6941569447517395, "Y": 0.4311339855194092}, {"X": 0.715209424495697, "Y": 0.4311336278915405}, {"X": 0.715209424495697, "Y": 0.44467899203300476}, {"X": 0.6941570043563843, "Y": 0.4446793496608734}]}, "Id": "6c57656c-a696-4655-9122-979025f232b4"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "driver.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05390581861138344, "Height": 0.01096928957849741, "Left": 0.7213124632835388, "Top": 0.43134304881095886}, "Polygon": [{"X": 0.7213124632835388, "Y": 0.4313439428806305}, {"X": 0.7752182483673096, "Y": 0.43134304881095886}, {"X": 0.7752182483673096, "Y": 0.44231143593788147}, {"X": 0.7213124632835388, "Y": 0.4423123300075531}]}, "Id": "007d2802-91ec-4ce2-a870-0442034c549f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025605011731386185, "Height": 0.011236871592700481, "Left": 0.1524718999862671, "Top": 0.4502401053905487}, "Polygon": [{"X": 0.1524718999862671, "Y": 0.45024052262306213}, {"X": 0.178076833486557, "Y": 0.4502401053905487}, {"X": 0.17807690799236298, "Y": 0.46147653460502625}, {"X": 0.15247195959091187, "Y": 0.46147698163986206}]}, "Id": "10d8cf1b-641e-49c1-9c8f-cf8530aa6216"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Reference", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09185897558927536, "Height": 0.011236380785703659, "Left": 0.18512745201587677, "Top": 0.4502546191215515}, "Polygon": [{"X": 0.18512745201587677, "Y": 0.4502561390399933}, {"X": 0.27698636054992676, "Y": 0.4502546191215515}, {"X": 0.27698642015457153, "Y": 0.4614894688129425}, {"X": 0.18512752652168274, "Y": 0.4614909887313843}]}, "Id": "bab33483-91d9-47d9-8e64-e95af3185983"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Yard", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042197637259960175, "Height": 0.01116542425006628, "Left": 0.1515934020280838, "Top": 0.4691438674926758}, "Polygon": [{"X": 0.1515934020280838, "Y": 0.4691445827484131}, {"X": 0.1937909722328186, "Y": 0.4691438674926758}, {"X": 0.19379104673862457, "Y": 0.4803085923194885}, {"X": 0.15159347653388977, "Y": 0.48030930757522583}]}, "Id": "b6bcf7f1-5c1f-49ca-86c2-ee45b6d9e9bd"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "Lot", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027345728129148483, "Height": 0.010936134494841099, "Left": 0.20080631971359253, "Top": 0.4692132771015167}, "Polygon": [{"X": 0.20080631971359253, "Y": 0.46921372413635254}, {"X": 0.22815199196338654, "Y": 0.4692132771015167}, {"X": 0.2281520515680313, "Y": 0.48014894127845764}, {"X": 0.2008063793182373, "Y": 0.48014941811561584}]}, "Id": "e73886ae-2317-4b6d-bba4-4b1df4d9c66e"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "FL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022655939683318138, "Height": 0.011101917363703251, "Left": 0.5042990446090698, "Top": 0.4690758287906647}, "Polygon": [{"X": 0.5042990446090698, "Y": 0.4690761864185333}, {"X": 0.5269549489021301, "Y": 0.4690758287906647}, {"X": 0.5269550085067749, "Y": 0.4801773726940155}, {"X": 0.5042991042137146, "Y": 0.48017773032188416}]}, "Id": "3b6ef438-23bc-4e65-9ab3-3de4d3b8a199"}, {"BlockType": "WORD", "Confidence": 99.189453125, "Text": "Y2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024042777717113495, "Height": 0.010959801264107227, "Left": 0.5325965285301208, "Top": 0.4689925014972687}, "Polygon": [{"X": 0.5325965285301208, "Y": 0.4689928889274597}, {"X": 0.5566393136978149, "Y": 0.4689925014972687}, {"X": 0.5566393136978149, "Y": 0.4799518883228302}, {"X": 0.5325965881347656, "Y": 0.47995230555534363}]}, "Id": "80169dab-7a59-412a-a39e-29fd5b003e5e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Yard", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042321108281612396, "Height": 0.011279111728072166, "Left": 0.15159207582473755, "Top": 0.4881533682346344}, "Polygon": [{"X": 0.15159207582473755, "Y": 0.4881540834903717}, {"X": 0.19391311705112457, "Y": 0.4881533682346344}, {"X": 0.19391319155693054, "Y": 0.4994317889213562}, {"X": 0.15159215033054352, "Y": 0.4994324743747711}]}, "Id": "737a301f-47d6-4fe7-a73e-07e20ca46ae5"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Row", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039512887597084045, "Height": 0.011047457344830036, "Left": 0.20105649530887604, "Top": 0.48824170231819153}, "Polygon": [{"X": 0.20105649530887604, "Y": 0.48824235796928406}, {"X": 0.2405693233013153, "Y": 0.48824170231819153}, {"X": 0.24056938290596008, "Y": 0.49928849935531616}, {"X": 0.2010565549135208, "Y": 0.4992891550064087}]}, "Id": "658992fe-1310-4f46-8477-85297e13ab7f"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Yard", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042293041944503784, "Height": 0.01123172976076603, "Left": 0.15162906050682068, "Top": 0.5069928765296936}, "Polygon": [{"X": 0.15162906050682068, "Y": 0.5069935917854309}, {"X": 0.1939220279455185, "Y": 0.5069928765296936}, {"X": 0.19392210245132446, "Y": 0.5182238817214966}, {"X": 0.15162912011146545, "Y": 0.5182245969772339}]}, "Id": "4f3fae91-7fb8-490b-a62e-f09be90fc3ba"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Spot", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041045963764190674, "Height": 0.013695483095943928, "Left": 0.2005670964717865, "Top": 0.5067781209945679}, "Polygon": [{"X": 0.2005670964717865, "Y": 0.5067788362503052}, {"X": 0.2416129857301712, "Y": 0.5067781209945679}, {"X": 0.24161306023597717, "Y": 0.5204729437828064}, {"X": 0.20056717097759247, "Y": 0.5204735994338989}]}, "Id": "bd2d1b75-973d-4f84-8788-58b53bda792b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "This", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03824545815587044, "Height": 0.011292309500277042, "Left": 0.15141282975673676, "Top": 0.5260027647018433}, "Polygon": [{"X": 0.15141282975673676, "Y": 0.5260034203529358}, {"X": 0.18965822458267212, "Y": 0.5260027647018433}, {"X": 0.1896582990884781, "Y": 0.5372944474220276}, {"X": 0.15141290426254272, "Y": 0.5372950434684753}]}, "Id": "b3e791b8-9595-475c-86b1-ea1d845694fc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "unit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0318421944975853, "Height": 0.011060147546231747, "Left": 0.1962081342935562, "Top": 0.5262307524681091}, "Polygon": [{"X": 0.1962081342935562, "Y": 0.5262312889099121}, {"X": 0.22805027663707733, "Y": 0.5262307524681091}, {"X": 0.2280503362417221, "Y": 0.5372903943061829}, {"X": 0.196208193898201, "Y": 0.5372909307479858}]}, "Id": "14f5ba2a-9bb8-4bc6-8098-5d365d64bab6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "was", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03550582006573677, "Height": 0.008534399792551994, "Left": 0.2335229367017746, "Top": 0.5287865400314331}, "Polygon": [{"X": 0.2335229367017746, "Y": 0.5287871360778809}, {"X": 0.2690287232398987, "Y": 0.5287865400314331}, {"X": 0.26902875304222107, "Y": 0.5373203754425049}, {"X": 0.23352298140525818, "Y": 0.5373209714889526}]}, "Id": "b4f10775-a31a-4aa7-874a-08896c6102f9"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "delivered/received", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1628088653087616, "Height": 0.011379748582839966, "Left": 0.2753961384296417, "Top": 0.5260372161865234}, "Polygon": [{"X": 0.2753961384296417, "Y": 0.5260399580001831}, {"X": 0.43820494413375854, "Y": 0.5260372161865234}, {"X": 0.4382050037384033, "Y": 0.5374142527580261}, {"X": 0.2753961980342865, "Y": 0.5374169945716858}]}, "Id": "f1019265-8eed-4c15-a3cf-19fa42338e70"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014517153613269329, "Height": 0.011112806387245655, "Left": 0.44484788179397583, "Top": 0.5261196494102478}, "Polygon": [{"X": 0.44484788179397583, "Y": 0.5261198878288269}, {"X": 0.45936501026153564, "Y": 0.5261196494102478}, {"X": 0.45936504006385803, "Y": 0.5372321605682373}, {"X": 0.4448479413986206, "Y": 0.5372324585914612}]}, "Id": "f4c1afd8-1bf3-4ec9-adfc-8dc088d79914"}, {"BlockType": "WORD", "Confidence": 99.57290649414062, "Text": "a", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010491245426237583, "Height": 0.008580989204347134, "Left": 0.46587178111076355, "Top": 0.5287949442863464}, "Polygon": [{"X": 0.46587178111076355, "Y": 0.5287951231002808}, {"X": 0.47636300325393677, "Y": 0.5287949442863464}, {"X": 0.47636303305625916, "Y": 0.5373757481575012}, {"X": 0.46587181091308594, "Y": 0.5373759269714355}]}, "Id": "465ca8ae-6277-4f8f-92e5-3b729d0b449f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "good", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044053204357624054, "Height": 0.013921591453254223, "Left": 0.4824027717113495, "Top": 0.5261355638504028}, "Polygon": [{"X": 0.4824027717113495, "Y": 0.5261362791061401}, {"X": 0.5264559388160706, "Y": 0.5261355638504028}, {"X": 0.5264559984207153, "Y": 0.5400564074516296}, {"X": 0.4824028015136719, "Y": 0.5400571227073669}]}, "Id": "c5ad48c3-5ed5-4497-917f-10f6fc0363d5"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "condition", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07941550016403198, "Height": 0.01112903282046318, "Left": 0.5334160923957825, "Top": 0.526138186454773}, "Polygon": [{"X": 0.5334160923957825, "Y": 0.526139497756958}, {"X": 0.6128315329551697, "Y": 0.526138186454773}, {"X": 0.6128315925598145, "Y": 0.5372658967971802}, {"X": 0.5334160923957825, "Y": 0.5372672080993652}]}, "Id": "1ca34137-72bd-4f7e-8a68-528fdfef85b5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "except", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05928456038236618, "Height": 0.013072757050395012, "Left": 0.6194400787353516, "Top": 0.5271013379096985}, "Polygon": [{"X": 0.6194400787353516, "Y": 0.5271023511886597}, {"X": 0.6787246465682983, "Y": 0.5271013379096985}, {"X": 0.6787246465682983, "Y": 0.5401731133460999}, {"X": 0.6194401383399963, "Y": 0.540174126625061}]}, "Id": "3e8cee57-a605-45bb-a023-2bb8362a8380"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "as", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020721783861517906, "Height": 0.00849673617631197, "Left": 0.684427797794342, "Top": 0.5288328528404236}, "Polygon": [{"X": 0.684427797794342, "Y": 0.5288331508636475}, {"X": 0.7051495909690857, "Y": 0.5288328528404236}, {"X": 0.7051495909690857, "Y": 0.5373291969299316}, {"X": 0.684427797794342, "Y": 0.5373295545578003}]}, "Id": "70771bf4-61d7-4526-9919-3019472648ba"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "noted", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049362991005182266, "Height": 0.01100938394665718, "Left": 0.7115344405174255, "Top": 0.5262789130210876}, "Polygon": [{"X": 0.7115344405174255, "Y": 0.5262797474861145}, {"X": 0.7608973979949951, "Y": 0.5262789130210876}, {"X": 0.7608974575996399, "Y": 0.5372874736785889}, {"X": 0.7115344405174255, "Y": 0.5372883081436157}]}, "Id": "924220bd-8cf5-45da-afa8-6beb36cd7306"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "above.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05873280391097069, "Height": 0.010955565609037876, "Left": 0.7676433324813843, "Top": 0.5263872742652893}, "Polygon": [{"X": 0.7676433324813843, "Y": 0.5263882875442505}, {"X": 0.8263760805130005, "Y": 0.5263872742652893}, {"X": 0.8263761401176453, "Y": 0.5373418927192688}, {"X": 0.7676433324813843, "Y": 0.5373428463935852}]}, "Id": "e2bf9c79-c5b3-4d91-940d-82467747c9a8"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "1ba416e0-3930-4b6c-bbd2-03d5387101f0", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "1ba416e0-3930-4b6c-bbd2-03d5387101f0", "content-type": "application/x-amz-json-1.1", "content-length": "69812", "date": "Thu, 18 Sep 2025 07:52:57 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "33474bf5-0f46-40f0-aac5-b6e5d72b736e", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 07:52:59 GMT", "content-type": "application/json", "content-length": "841", "connection": "keep-alive", "x-amzn-requestid": "33474bf5-0f46-40f0-aac5-b6e5d72b736e"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "The user provided page 1 content. We need to classify this page's doc_type. This appears to be an \"ingate\" document: It says \"Ingate Receipt\" and mentions \"Equipment In-Gated\" etc. So doc_type = ingate. No other pages. So we must output a JSON array with one object: page_no:1, doc_type:\"ingate\". Use the tool.\n\nWe must call the function classify_logistics_doc_type with documents array containing {page_no:1, doc_type:\"ingate\"}."}}}, {"toolUse": {"toolUseId": "tooluse_6p2NkSSFSXukmBemA5bxUw", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "ingate"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2266, "outputTokens": 148, "totalTokens": 2414}, "metrics": {"latencyMs": 779}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/7657fcfb_25_ingate_5.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check for contination of previous page by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nChicago Intermodal Terminal, IL, Ingate Receipt\nIngate Unit\nOOLU 913481\nLoad/Empty\nEmpty\nChassis\nTSXZ 409824\nDate\n2025-08-11 12:18 PM\nShipper\n00 c L Canada Inc\nDestination\nChicago Intermodal Terminal, IL\nCarrier\nSSRT - Super Star Freight Inc\nTractor\n423\nSeal\nBolt Seal\nBooking\n66250833CY\nReservation\nInterchange From\nType\n40' Dry Container High Cube (KC4)\nWeight\nReefer\nN\nHaz/Danger\nN\nPlacard Code\nEmpty to return to\nInspection\nNo damage reported by driver.\nBill Reference\nYard Lot\nFL Y2\nYard Row\nYard Spot\nThis unit was delivered/received in a good condition except as noted above.\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}