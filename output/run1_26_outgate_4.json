{"classification_result": {"documents": [{"page_no": 1, "doc_type": "outgate"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "d0b6f606-c0d0-4ccc-8290-6ee5dec93adf", "Relationships": [{"Type": "CHILD", "Ids": ["bbc64014-a940-4e03-b219-95267d802b81", "b6d2777d-a1f2-42e6-a77e-9fbc9ba473a6", "6a2f8f26-5f7c-426b-b8f1-4897a6868d15", "9c46067e-bffc-4981-ba16-aaa2d0cb5e2d", "8ce130f3-4823-4bb8-9581-2e75d472c1f8", "7780c1b0-ee8f-4323-9f77-f853b11590f8", "6718cc24-211f-4524-b129-aaa038ba1f3c", "23d7277f-79c4-42c2-9b82-f98ef9f472c1", "9ccc5786-8adb-4c1f-9a2a-b955ddc4f831", "98cac759-39cb-4ade-8bfb-a15a97b7abd2", "1e4b8443-bfc8-436e-a003-bce1a66fa14d", "d1888d43-825d-4c95-805d-d6f730a9063b", "666e56b5-b6e5-4895-8433-9dfbd8735bf3", "45fd0b44-d86d-42d4-acf0-dc11810b791d", "73a2eec0-b7aa-4e6f-9e2f-4b08b3a3977f", "9ae63b49-b161-453a-b030-df4505b549c7", "a2d5a967-422e-4968-98e7-74afdbec8f0e", "bb09dcdb-a577-4a61-b826-e714af76c639", "c05c6a61-0837-40bf-9cab-c4bb3349d38f", "46cd8e75-b5b3-4e79-b728-6b9edeacb9f9", "36e95f15-2bb6-4c88-a4f7-7551d4600aba", "bb47c26e-89df-4b06-95ea-0d5720ea4ab1", "b1c12f72-6b8d-4918-9055-d1d4f369872a", "db7b2881-0d20-463b-8568-0da71a732921", "650cb793-938d-4d07-8ff3-917f1c41f451", "8031854d-1b8f-4281-82de-217a1200aa12", "83540760-9230-401e-8f54-9a60e9cff559", "00de74a4-7358-4397-8dcf-208e3d0a0083", "d6cc3c81-ffcc-47de-a562-1fd7f7960bec"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "9:37", "Geometry": {"BoundingBox": {"Width": 0.08897925168275833, "Height": 0.01561359129846096, "Left": 0.12805433571338654, "Top": 0.020380264148116112}, "Polygon": [{"X": 0.12805479764938354, "Y": 0.020382490009069443}, {"X": 0.21703357994556427, "Y": 0.020380264148116112}, {"X": 0.21703316271305084, "Y": 0.03599162399768829}, {"X": 0.12805433571338654, "Y": 0.03599385544657707}]}, "Id": "bbc64014-a940-4e03-b219-95267d802b81", "Relationships": [{"Type": "CHILD", "Ids": ["144d5403-b98a-4069-94d9-43cb44a3669c"]}]}, {"BlockType": "LINE", "Confidence": 99.34490203857422, "Text": "88", "Geometry": {"BoundingBox": {"Width": 0.041128236800432205, "Height": 0.011593172326683998, "Left": 0.8641988039016724, "Top": 0.021823443472385406}, "Polygon": [{"X": 0.8641988635063171, "Y": 0.021824471652507782}, {"X": 0.9053270220756531, "Y": 0.021823443472385406}, {"X": 0.9053269624710083, "Y": 0.03341558575630188}, {"X": 0.8641988039016724, "Y": 0.033416613936424255}]}, "Id": "b6d2777d-a1f2-42e6-a77e-9fbc9ba473a6", "Relationships": [{"Type": "CHILD", "Ids": ["1a09d840-8c5c-4bd7-89d3-d6c5f75c1282"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Done", "Geometry": {"BoundingBox": {"Width": 0.09228352457284927, "Height": 0.01440286822617054, "Left": 0.035744525492191315, "Top": 0.06478839367628098}, "Polygon": [{"X": 0.03574499115347862, "Y": 0.06479071825742722}, {"X": 0.1280280500650406, "Y": 0.06478839367628098}, {"X": 0.12802763283252716, "Y": 0.07918893545866013}, {"X": 0.035744525492191315, "Y": 0.07919126749038696}]}, "Id": "6a2f8f26-5f7c-426b-b8f1-4897a6868d15", "Relationships": [{"Type": "CHILD", "Ids": ["cf2d5e30-a455-45a0-ac01-910ef0a88fff"]}]}, {"BlockType": "LINE", "Confidence": 55.38414764404297, "Text": "8", "Geometry": {"BoundingBox": {"Width": 0.02538396790623665, "Height": 0.015450322069227695, "Left": 0.3197929859161377, "Top": 0.0638216882944107}, "Polygon": [{"X": 0.31979334354400635, "Y": 0.06382232904434204}, {"X": 0.34517696499824524, "Y": 0.0638216882944107}, {"X": 0.3451766073703766, "Y": 0.07927136868238449}, {"X": 0.3197929859161377, "Y": 0.07927200943231583}]}, "Id": "9c46067e-bffc-4981-ba16-aaa2d0cb5e2d", "Relationships": [{"Type": "CHILD", "Ids": ["932306d2-3e9e-43ad-b594-67b1c4db1f2a"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "next.shipcsx.com", "Geometry": {"BoundingBox": {"Width": 0.3251529335975647, "Height": 0.018522178754210472, "Left": 0.35909783840179443, "Top": 0.06374114751815796}, "Polygon": [{"X": 0.35909825563430786, "Y": 0.06374933570623398}, {"X": 0.6842507719993591, "Y": 0.06374114751815796}, {"X": 0.68425053358078, "Y": 0.08225511759519577}, {"X": 0.35909783840179443, "Y": 0.08226332813501358}]}, "Id": "8ce130f3-4823-4bb8-9581-2e75d472c1f8", "Relationships": [{"Type": "CHILD", "Ids": ["a4b68bb1-6966-429e-ad99-ed7a52fea438"]}]}, {"BlockType": "LINE", "Confidence": 83.12659454345703, "Text": "AA", "Geometry": {"BoundingBox": {"Width": 0.04726710170507431, "Height": 0.014265305362641811, "Left": 0.8228487372398376, "Top": 0.06483140587806702}, "Polygon": [{"X": 0.8228488564491272, "Y": 0.06483259797096252}, {"X": 0.8701158165931702, "Y": 0.06483140587806702}, {"X": 0.8701157569885254, "Y": 0.0790955200791359}, {"X": 0.8228487372398376, "Y": 0.0790967121720314}]}, "Id": "7780c1b0-ee8f-4323-9f77-f853b11590f8", "Relationships": [{"Type": "CHILD", "Ids": ["e0bc0961-4a04-42eb-bb6e-2a2200def192"]}]}, {"BlockType": "LINE", "Confidence": 99.73562622070312, "Text": "Gate Receipts (1) As of 09:37 on 07/04/25", "Geometry": {"BoundingBox": {"Width": 0.6844155788421631, "Height": 0.02146938443183899, "Left": 0.038811955600976944, "Top": 0.13997416198253632}, "Polygon": [{"X": 0.03881264477968216, "Y": 0.13999159634113312}, {"X": 0.7232275009155273, "Y": 0.13997416198253632}, {"X": 0.7232272624969482, "Y": 0.16142603754997253}, {"X": 0.038811955600976944, "Y": 0.1614435464143753}]}, "Id": "6718cc24-211f-4524-b129-aaa038ba1f3c", "Relationships": [{"Type": "CHILD", "Ids": ["6a2fde96-ea2b-427e-a159-9785f0b13d32", "b9813455-c288-4ad2-a119-4ec03e069ae0", "fda1fd0d-a1f3-43f4-b299-f098a11cef0d", "a4615d65-013c-4c7a-89ff-4be54ec06d9f", "27f65a06-deb5-4be3-b9ff-a45e38dd1646", "22ae7724-4450-4b67-bdd4-50e2a44e8d18", "79e369ac-c994-4bf6-81f3-ff67e7da5999", "ccb5bc5a-882f-48cc-bcd5-de8cd0b77138"]}]}, {"BlockType": "LINE", "Confidence": 90.70890808105469, "Text": "i", "Geometry": {"BoundingBox": {"Width": 0.009073248133063316, "Height": 0.01281918864697218, "Left": 0.06387272477149963, "Top": 0.1753341108560562}, "Polygon": [{"X": 0.06387312710285187, "Y": 0.17533434927463531}, {"X": 0.0729459747672081, "Y": 0.1753341108560562}, {"X": 0.07294557988643646, "Y": 0.18815305829048157}, {"X": 0.06387272477149963, "Y": 0.18815329670906067}]}, "Id": "23d7277f-79c4-42c2-9b82-f98ef9f472c1", "Relationships": [{"Type": "CHILD", "Ids": ["fcc52368-aeb0-4f7e-bcf1-53e93938e591"]}]}, {"BlockType": "LINE", "Confidence": 99.73280334472656, "Text": "CHICAGO 59TH ST", "Geometry": {"BoundingBox": {"Width": 0.26336994767189026, "Height": 0.01145999412983656, "Left": 0.14098046720027924, "Top": 0.20989686250686646}, "Polygon": [{"X": 0.1409807950258255, "Y": 0.20990364253520966}, {"X": 0.4043503999710083, "Y": 0.20989686250686646}, {"X": 0.4043501615524292, "Y": 0.2213500589132309}, {"X": 0.14098046720027924, "Y": 0.2213568538427353}]}, "Id": "9ccc5786-8adb-4c1f-9a2a-b955ddc4f831", "Relationships": [{"Type": "CHILD", "Ids": ["6a042079-478b-445d-acb1-c163377925f3", "e6144cf8-ebc8-4022-a5f6-d0b5431edeae", "7e50b272-d67c-46a0-95b3-273839b8cba7"]}]}, {"BlockType": "LINE", "Confidence": 99.7608413696289, "Text": "Terminal", "Geometry": {"BoundingBox": {"Width": 0.09785367548465729, "Height": 0.009993024170398712, "Left": 0.14026406407356262, "Top": 0.22674809396266937}, "Polygon": [{"X": 0.1402643620967865, "Y": 0.22675061225891113}, {"X": 0.2381177395582199, "Y": 0.22674809396266937}, {"X": 0.23811747133731842, "Y": 0.23673857748508453}, {"X": 0.14026406407356262, "Y": 0.2367411106824875}]}, "Id": "98cac759-39cb-4ade-8bfb-a15a97b7abd2", "Relationships": [{"Type": "CHILD", "Ids": ["4e80d1e3-afb2-4278-9e26-770d0f62388e"]}]}, {"BlockType": "LINE", "Confidence": 99.84335327148438, "Text": "TRLU 888813", "Geometry": {"BoundingBox": {"Width": 0.18758191168308258, "Height": 0.011437452398240566, "Left": 0.14035113155841827, "Top": 0.2531190514564514}, "Polygon": [{"X": 0.14035145938396454, "Y": 0.2531239092350006}, {"X": 0.32793304324150085, "Y": 0.2531190514564514}, {"X": 0.32793277502059937, "Y": 0.2645516097545624}, {"X": 0.14035113155841827, "Y": 0.26455649733543396}]}, "Id": "1e4b8443-bfc8-436e-a003-bce1a66fa14d", "Relationships": [{"Type": "CHILD", "Ids": ["6e4a4f33-7ee2-400a-b922-18635d01f4a1", "a7d29a1a-a01c-4e7a-b872-c2dbfe70a521"]}]}, {"BlockType": "LINE", "Confidence": 92.9795150756836, "Text": "Container I 020L 0799W 0857H", "Geometry": {"BoundingBox": {"Width": 0.36252719163894653, "Height": 0.01207028515636921, "Left": 0.14148744940757751, "Top": 0.26951083540916443}, "Polygon": [{"X": 0.14148779213428497, "Y": 0.26952025294303894}, {"X": 0.5040146112442017, "Y": 0.26951083540916443}, {"X": 0.5040144324302673, "Y": 0.2815716862678528}, {"X": 0.14148744940757751, "Y": 0.2815811336040497}]}, "Id": "d1888d43-825d-4c95-805d-d6f730a9063b", "Relationships": [{"Type": "CHILD", "Ids": ["46d3f273-1e5d-4b81-be2e-91681be68ccc", "1150875a-3071-4192-8b4c-3a54d5e765e7", "89ddc0c0-4713-4674-a625-cfb8fa88ee6a", "bdfe0365-bd34-4517-ab26-99bc71edb33a", "cbce020c-8d66-46f3-b08a-1cf6e7bfda12"]}]}, {"BlockType": "LINE", "Confidence": 99.9306869506836, "Text": "Desert Eagle Express Inc (DEEE)", "Geometry": {"BoundingBox": {"Width": 0.42728736996650696, "Height": 0.014289319515228271, "Left": 0.14245902001857758, "Top": 0.2970275580883026}, "Polygon": [{"X": 0.142459437251091, "Y": 0.2970387041568756}, {"X": 0.5697463750839233, "Y": 0.2970275580883026}, {"X": 0.5697461366653442, "Y": 0.3113057017326355}, {"X": 0.14245902001857758, "Y": 0.3113168776035309}]}, "Id": "666e56b5-b6e5-4895-8433-9dfbd8735bf3", "Relationships": [{"Type": "CHILD", "Ids": ["46d7a468-eb4c-4230-b6e4-efa29bdaa982", "d9e77495-e1db-46e6-bf73-96bcda270d19", "7a5506cc-4865-4377-ba68-d9d446d1b4cd", "4a9ebecc-e6d1-4e21-ab4f-ca255a1d4cb2", "c9389f0b-e057-49af-b3b4-fc81e903473d"]}]}, {"BlockType": "LINE", "Confidence": 99.24737548828125, "Text": "Interchange Carrier (Driver: <PERSON><PERSON>, <PERSON><PERSON><PERSON>)", "Geometry": {"BoundingBox": {"Width": 0.4784916043281555, "Height": 0.01250377856194973, "Left": 0.1424178034067154, "Top": 0.31367531418800354}, "Polygon": [{"X": 0.14241817593574524, "Y": 0.31368783116340637}, {"X": 0.6209094524383545, "Y": 0.31367531418800354}, {"X": 0.6209092736244202, "Y": 0.3261665403842926}, {"X": 0.1424178034067154, "Y": 0.3261790871620178}]}, "Id": "45fd0b44-d86d-42d4-acf0-dc11810b791d", "Relationships": [{"Type": "CHILD", "Ids": ["aaae4724-7f4a-4ceb-8910-dc1f580ae59e", "6dd99106-303f-4d05-8847-aa07ecd7f4ef", "209dde54-0710-4033-b3f1-df69babf4cd2", "5b597c51-f25e-4f67-af24-72de236f749a", "ee85e200-a11b-486f-8784-2ab6c0fd1efa"]}]}, {"BlockType": "LINE", "Confidence": 99.892578125, "Text": "Out-Gate", "Geometry": {"BoundingBox": {"Width": 0.1500757485628128, "Height": 0.012999093160033226, "Left": 0.42522189021110535, "Top": 0.369340181350708}, "Polygon": [{"X": 0.42522215843200684, "Y": 0.36934414505958557}, {"X": 0.5752976536750793, "Y": 0.369340181350708}, {"X": 0.5752974152565002, "Y": 0.3823353350162506}, {"X": 0.42522189021110535, "Y": 0.3823392987251282}]}, "Id": "73a2eec0-b7aa-4e6f-9e2f-4b08b3a3977f", "Relationships": [{"Type": "CHILD", "Ids": ["f97d9e17-ccb7-4a29-9f3e-d6845c4386d2"]}]}, {"BlockType": "LINE", "Confidence": 99.72549438476562, "Text": "Date & Time", "Geometry": {"BoundingBox": {"Width": 0.16275854408740997, "Height": 0.011685666628181934, "Left": 0.060987718403339386, "Top": 0.4069252014160156}, "Polygon": [{"X": 0.060988087207078934, "Y": 0.40692952275276184}, {"X": 0.22374625504016876, "Y": 0.4069252014160156}, {"X": 0.2237459421157837, "Y": 0.41860654950141907}, {"X": 0.060987718403339386, "Y": 0.4186108708381653}]}, "Id": "9ae63b49-b161-453a-b030-df4505b549c7", "Relationships": [{"Type": "CHILD", "Ids": ["375c154c-7c93-486e-a157-6a8aa218dd3d", "2c2cbcac-64fb-4998-8a02-4c2f831bc873", "28637b5e-2fa2-4801-ac62-d3eaa325435e"]}]}, {"BlockType": "LINE", "Confidence": 94.15006256103516, "Text": "07/03/25 - 18:22", "Geometry": {"BoundingBox": {"Width": 0.230703666806221, "Height": 0.012042014859616756, "Left": 0.7112100124359131, "Top": 0.40664735436439514}, "Polygon": [{"X": 0.7112101912498474, "Y": 0.406653493642807}, {"X": 0.9419137239456177, "Y": 0.40664735436439514}, {"X": 0.9419136643409729, "Y": 0.418683260679245}, {"X": 0.7112100124359131, "Y": 0.4186893701553345}]}, "Id": "a2d5a967-422e-4968-98e7-74afdbec8f0e", "Relationships": [{"Type": "CHILD", "Ids": ["58b6cd71-74e5-4567-95a9-d42db0f9619a", "ea8a4d73-60ce-4e96-b0a5-3bb5ceee587f", "a42de8ca-498d-4c23-a5ad-56d6f466a705"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Associated Equipment", "Geometry": {"BoundingBox": {"Width": 0.2967398166656494, "Height": 0.01363520696759224, "Left": 0.058935150504112244, "Top": 0.44134172797203064}, "Polygon": [{"X": 0.05893557891249657, "Y": 0.4413496255874634}, {"X": 0.35567498207092285, "Y": 0.44134172797203064}, {"X": 0.3556746542453766, "Y": 0.45496898889541626}, {"X": 0.058935150504112244, "Y": 0.4549769163131714}]}, "Id": "bb09dcdb-a577-4a61-b826-e714af76c639", "Relationships": [{"Type": "CHILD", "Ids": ["3c6b8b95-b935-46f6-bbb1-ea796465e654", "d739a56b-90d7-4348-8e9d-7bf84670544a"]}]}, {"BlockType": "LINE", "Confidence": 99.3603515625, "Text": "MCCZ 342337", "Geometry": {"BoundingBox": {"Width": 0.19885852932929993, "Height": 0.011303666979074478, "Left": 0.7435631155967712, "Top": 0.44134092330932617}, "Polygon": [{"X": 0.7435632348060608, "Y": 0.4413462281227112}, {"X": 0.9424216747283936, "Y": 0.44134092330932617}, {"X": 0.9424216151237488, "Y": 0.45263928174972534}, {"X": 0.7435631155967712, "Y": 0.45264458656311035}]}, "Id": "c05c6a61-0837-40bf-9cab-c4bb3349d38f", "Relationships": [{"Type": "CHILD", "Ids": ["10edde15-14ce-4778-84a3-2ee0e63dd272", "c54009e0-75b6-4f43-8689-7e2a287480ac"]}]}, {"BlockType": "LINE", "Confidence": 99.81126403808594, "Text": "L/E", "Geometry": {"BoundingBox": {"Width": 0.04215734452009201, "Height": 0.011545681394636631, "Left": 0.06085648760199547, "Top": 0.47608551383018494}, "Polygon": [{"X": 0.06085685268044472, "Y": 0.4760866165161133}, {"X": 0.10301382839679718, "Y": 0.47608551383018494}, {"X": 0.10301347821950912, "Y": 0.48763003945350647}, {"X": 0.06085648760199547, "Y": 0.4876311719417572}]}, "Id": "46cd8e75-b5b3-4e79-b728-6b9edeacb9f9", "Relationships": [{"Type": "CHILD", "Ids": ["c9ed085b-b50f-4fbd-a1ad-90b71a934f82"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Load", "Geometry": {"BoundingBox": {"Width": 0.0643196702003479, "Height": 0.011379961855709553, "Left": 0.8766753077507019, "Top": 0.4758877754211426}, "Polygon": [{"X": 0.8766754269599915, "Y": 0.4758894741535187}, {"X": 0.9409949779510498, "Y": 0.4758877754211426}, {"X": 0.940994918346405, "Y": 0.48726600408554077}, {"X": 0.8766753077507019, "Y": 0.48726773262023926}]}, "Id": "36e95f15-2bb6-4c88-a4f7-7551d4600aba", "Relationships": [{"Type": "CHILD", "Ids": ["274dc135-e3a8-4838-9cef-9ea639c810bd"]}]}, {"BlockType": "LINE", "Confidence": 98.9697265625, "Text": "Booking #", "Geometry": {"BoundingBox": {"Width": 0.13106825947761536, "Height": 0.013734519481658936, "Left": 0.0609896183013916, "Top": 0.5104458332061768}, "Polygon": [{"X": 0.06099005043506622, "Y": 0.5104493498802185}, {"X": 0.19205787777900696, "Y": 0.5104458332061768}, {"X": 0.19205749034881592, "Y": 0.524176836013794}, {"X": 0.0609896183013916, "Y": 0.5241803526878357}]}, "Id": "bb47c26e-89df-4b06-95ea-0d5720ea4ab1", "Relationships": [{"Type": "CHILD", "Ids": ["ef5dbc19-2f14-472c-9c85-e66805f2c8e7", "a6f82a30-4a49-4cdc-9dab-34a90ca3584b"]}]}, {"BlockType": "LINE", "Confidence": 91.92103576660156, "Text": "---", "Geometry": {"BoundingBox": {"Width": 0.032540373504161835, "Height": 0.003156771883368492, "Left": 0.9094561338424683, "Top": 0.5155411958694458}, "Polygon": [{"X": 0.909456193447113, "Y": 0.5155420899391174}, {"X": 0.9419965147972107, "Y": 0.5155411958694458}, {"X": 0.9419965147972107, "Y": 0.5186970829963684}, {"X": 0.9094561338424683, "Y": 0.51869797706604}]}, "Id": "b1c12f72-6b8d-4918-9055-d1d4f369872a", "Relationships": [{"Type": "CHILD", "Ids": ["3f7b9e7f-82c1-4f48-8ae2-974837c53302"]}]}, {"BlockType": "LINE", "Confidence": 99.88201141357422, "Text": "Seal(s)", "Geometry": {"BoundingBox": {"Width": 0.08506505936384201, "Height": 0.013724159449338913, "Left": 0.059612855315208435, "Top": 0.5445165038108826}, "Polygon": [{"X": 0.05961328744888306, "Y": 0.544518768787384}, {"X": 0.14467790722846985, "Y": 0.5445165038108826}, {"X": 0.14467750489711761, "Y": 0.5582383275032043}, {"X": 0.059612855315208435, "Y": 0.5582406520843506}]}, "Id": "db7b2881-0d20-463b-8568-0da71a732921", "Relationships": [{"Type": "CHILD", "Ids": ["9f4e468e-7957-4734-94e0-690d26868dcc"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "2402697", "Geometry": {"BoundingBox": {"Width": 0.12311472743749619, "Height": 0.011285274289548397, "Left": 0.8184822797775269, "Top": 0.5451086163520813}, "Polygon": [{"X": 0.8184823393821716, "Y": 0.5451119542121887}, {"X": 0.9415969848632812, "Y": 0.5451086163520813}, {"X": 0.9415969252586365, "Y": 0.5563905835151672}, {"X": 0.8184822797775269, "Y": 0.5563939213752747}]}, "Id": "650cb793-938d-4d07-8ff3-917f1c41f451", "Relationships": [{"Type": "CHILD", "Ids": ["e54f54f7-e360-41fc-9859-2601dead1465"]}]}, {"BlockType": "LINE", "Confidence": 99.8488540649414, "Text": "No images available for departures", "Geometry": {"BoundingBox": {"Width": 0.42427968978881836, "Height": 0.013139772228896618, "Left": 0.07282052934169769, "Top": 0.595406711101532}, "Polygon": [{"X": 0.07282093167304993, "Y": 0.5954182744026184}, {"X": 0.49710020422935486, "Y": 0.595406711101532}, {"X": 0.49709996581077576, "Y": 0.6085348725318909}, {"X": 0.07282052934169769, "Y": 0.6085464954376221}]}, "Id": "8031854d-1b8f-4281-82de-217a1200aa12", "Relationships": [{"Type": "CHILD", "Ids": ["ba84253f-a644-4309-9f84-6be9e6ec5a32", "df92d170-4d6f-4803-a69f-5048e550b490", "1323784b-d0b4-4aef-b622-2cfeb236a981", "261f4e82-a529-4fb4-b017-6db201ebc846", "35d8592a-0d73-4c4e-9d0e-6488c860deb5"]}]}, {"BlockType": "LINE", "Confidence": 99.931640625, "Text": "CSX.com", "Geometry": {"BoundingBox": {"Width": 0.12698812782764435, "Height": 0.012109027244150639, "Left": 0.4366007149219513, "Top": 0.8208789229393005}, "Polygon": [{"X": 0.4366009533405304, "Y": 0.8208824992179871}, {"X": 0.5635888576507568, "Y": 0.8208789229393005}, {"X": 0.5635886192321777, "Y": 0.8329843282699585}, {"X": 0.4366007149219513, "Y": 0.832987904548645}]}, "Id": "83540760-9230-401e-8f54-9a60e9cff559", "Relationships": [{"Type": "CHILD", "Ids": ["370536a7-397f-45f9-9525-b9d3522cb2d1"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Help", "Geometry": {"BoundingBox": {"Width": 0.06640863418579102, "Height": 0.014944499358534813, "Left": 0.46796175837516785, "Top": 0.8520155549049377}, "Polygon": [{"X": 0.46796202659606934, "Y": 0.8520174026489258}, {"X": 0.5343703627586365, "Y": 0.8520155549049377}, {"X": 0.5343701243400574, "Y": 0.8669581413269043}, {"X": 0.46796175837516785, "Y": 0.8669600486755371}]}, "Id": "00de74a4-7358-4397-8dcf-208e3d0a0083", "Relationships": [{"Type": "CHILD", "Ids": ["3bf9c87d-4e33-4b36-99be-060bcbc13f3a"]}]}, {"BlockType": "LINE", "Confidence": 99.08531951904297, "Text": "© 2025 CSX Technology, Inc. v.4.43.0", "Geometry": {"BoundingBox": {"Width": 0.4489292800426483, "Height": 0.013003556057810783, "Left": 0.27562594413757324, "Top": 0.8847061395645142}, "Polygon": [{"X": 0.2756262719631195, "Y": 0.8847188353538513}, {"X": 0.724555253982544, "Y": 0.8847061395645142}, {"X": 0.7245550751686096, "Y": 0.8976969122886658}, {"X": 0.27562594413757324, "Y": 0.8977096676826477}]}, "Id": "d6cc3c81-ffcc-47de-a562-1fd7f7960bec", "Relationships": [{"Type": "CHILD", "Ids": ["a2c7b81d-5f0d-40c0-a114-f7931328233b", "91863b7e-e63e-4869-ab5e-f2cb10249a2b", "ff68f938-6b14-4c46-ac20-33a63f798b58", "fbcfdb68-c40f-4e53-b75f-4146df91031f", "e3abfb46-d4cf-4aaf-94f6-93e412e91545", "d7a49657-e3d0-474e-b968-123fcb65835d"]}]}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "9:37", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08897925168275833, "Height": 0.01561359129846096, "Left": 0.12805433571338654, "Top": 0.020380264148116112}, "Polygon": [{"X": 0.12805479764938354, "Y": 0.020382490009069443}, {"X": 0.21703357994556427, "Y": 0.020380264148116112}, {"X": 0.21703316271305084, "Y": 0.03599162399768829}, {"X": 0.12805433571338654, "Y": 0.03599385544657707}]}, "Id": "144d5403-b98a-4069-94d9-43cb44a3669c"}, {"BlockType": "WORD", "Confidence": 99.34490203857422, "Text": "88", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041128236800432205, "Height": 0.011593172326683998, "Left": 0.8641988039016724, "Top": 0.021823443472385406}, "Polygon": [{"X": 0.8641988635063171, "Y": 0.021824471652507782}, {"X": 0.9053270220756531, "Y": 0.021823443472385406}, {"X": 0.9053269624710083, "Y": 0.03341558575630188}, {"X": 0.8641988039016724, "Y": 0.033416613936424255}]}, "Id": "1a09d840-8c5c-4bd7-89d3-d6c5f75c1282"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Done", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09228352457284927, "Height": 0.01440286822617054, "Left": 0.035744525492191315, "Top": 0.06478839367628098}, "Polygon": [{"X": 0.03574499115347862, "Y": 0.06479071825742722}, {"X": 0.1280280500650406, "Y": 0.06478839367628098}, {"X": 0.12802763283252716, "Y": 0.07918893545866013}, {"X": 0.035744525492191315, "Y": 0.07919126749038696}]}, "Id": "cf2d5e30-a455-45a0-ac01-910ef0a88fff"}, {"BlockType": "WORD", "Confidence": 55.38414764404297, "Text": "8", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02538396790623665, "Height": 0.015450322069227695, "Left": 0.3197929859161377, "Top": 0.0638216882944107}, "Polygon": [{"X": 0.31979334354400635, "Y": 0.06382232904434204}, {"X": 0.34517696499824524, "Y": 0.0638216882944107}, {"X": 0.3451766073703766, "Y": 0.07927136868238449}, {"X": 0.3197929859161377, "Y": 0.07927200943231583}]}, "Id": "932306d2-3e9e-43ad-b594-67b1c4db1f2a"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "next.shipcsx.com", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.3251529335975647, "Height": 0.018522178754210472, "Left": 0.35909783840179443, "Top": 0.06374114751815796}, "Polygon": [{"X": 0.35909825563430786, "Y": 0.06374933570623398}, {"X": 0.6842507719993591, "Y": 0.06374114751815796}, {"X": 0.68425053358078, "Y": 0.08225511759519577}, {"X": 0.35909783840179443, "Y": 0.08226332813501358}]}, "Id": "a4b68bb1-6966-429e-ad99-ed7a52fea438"}, {"BlockType": "WORD", "Confidence": 83.12659454345703, "Text": "AA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04726710170507431, "Height": 0.014265305362641811, "Left": 0.8228487372398376, "Top": 0.06483140587806702}, "Polygon": [{"X": 0.8228488564491272, "Y": 0.06483259797096252}, {"X": 0.8701158165931702, "Y": 0.06483140587806702}, {"X": 0.8701157569885254, "Y": 0.0790955200791359}, {"X": 0.8228487372398376, "Y": 0.0790967121720314}]}, "Id": "e0bc0961-4a04-42eb-bb6e-2a2200def192"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Gate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10045744478702545, "Height": 0.016699105501174927, "Left": 0.038812074810266495, "Top": 0.14102709293365479}, "Polygon": [{"X": 0.03881261125206947, "Y": 0.14102965593338013}, {"X": 0.13926951587200165, "Y": 0.14102709293365479}, {"X": 0.13926903903484344, "Y": 0.15772363543510437}, {"X": 0.038812074810266495, "Y": 0.1577262133359909}]}, "Id": "6a2fde96-ea2b-427e-a159-9785f0b13d32"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "Receipts", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17814847826957703, "Height": 0.020761361345648766, "Left": 0.15039387345314026, "Top": 0.14067932963371277}, "Polygon": [{"X": 0.150394469499588, "Y": 0.1406838744878769}, {"X": 0.3285423517227173, "Y": 0.14067932963371277}, {"X": 0.3285418748855591, "Y": 0.16143614053726196}, {"X": 0.15039387345314026, "Y": 0.1614406853914261}]}, "Id": "b9813455-c288-4ad2-a119-4ec03e069ae0"}, {"BlockType": "WORD", "Confidence": 99.560546875, "Text": "(1)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052631765604019165, "Height": 0.020356815308332443, "Left": 0.3388045132160187, "Top": 0.13998261094093323}, "Polygon": [{"X": 0.3388049900531769, "Y": 0.13998395204544067}, {"X": 0.39143627882003784, "Y": 0.13998261094093323}, {"X": 0.391435831785202, "Y": 0.16033807396888733}, {"X": 0.3388045132160187, "Y": 0.16033942997455597}]}, "Id": "fda1fd0d-a1f3-43f4-b299-f098a11cef0d"}, {"BlockType": "WORD", "Confidence": 99.77220153808594, "Text": "As", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03390023857355118, "Height": 0.010380209423601627, "Left": 0.4209888279438019, "Top": 0.14725926518440247}, "Polygon": [{"X": 0.4209890365600586, "Y": 0.1472601294517517}, {"X": 0.45488905906677246, "Y": 0.14725926518440247}, {"X": 0.45488885045051575, "Y": 0.15763860940933228}, {"X": 0.4209888279438019, "Y": 0.15763947367668152}]}, "Id": "a4615d65-013c-4c7a-89ff-4be54ec06d9f"}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028738079592585564, "Height": 0.01082333829253912, "Left": 0.4593435823917389, "Top": 0.1469346582889557}, "Polygon": [{"X": 0.459343820810318, "Y": 0.1469353884458542}, {"X": 0.4880816638469696, "Y": 0.1469346582889557}, {"X": 0.4880814850330353, "Y": 0.1577572524547577}, {"X": 0.4593435823917389, "Y": 0.15775799751281738}]}, "Id": "27f65a06-deb5-4be3-b9ff-a45e38dd1646"}, {"BlockType": "WORD", "Confidence": 99.365234375, "Text": "09:37", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07549779117107391, "Height": 0.010572199709713459, "Left": 0.4901532232761383, "Top": 0.14708447456359863}, "Polygon": [{"X": 0.49015340209007263, "Y": 0.14708639681339264}, {"X": 0.565650999546051, "Y": 0.14708447456359863}, {"X": 0.5656508207321167, "Y": 0.1576547473669052}, {"X": 0.4901532232761383, "Y": 0.15765666961669922}]}, "Id": "22ae7724-4450-4b67-bdd4-50e2a44e8d18"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "on", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031282782554626465, "Height": 0.008058078587055206, "Left": 0.5677705407142639, "Top": 0.14968964457511902}, "Polygon": [{"X": 0.5677706599235535, "Y": 0.1496904492378235}, {"X": 0.5990533232688904, "Y": 0.14968964457511902}, {"X": 0.5990532040596008, "Y": 0.15774692595005035}, {"X": 0.5677705407142639, "Y": 0.15774771571159363}]}, "Id": "79e369ac-c994-4bf6-81f3-ff67e7da5999"}, {"BlockType": "WORD", "Confidence": 99.76403045654297, "Text": "07/04/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.118653304874897, "Height": 0.011107823811471462, "Left": 0.6045741438865662, "Top": 0.14695490896701813}, "Polygon": [{"X": 0.6045743227005005, "Y": 0.14695793390274048}, {"X": 0.7232274413108826, "Y": 0.14695490896701813}, {"X": 0.723227322101593, "Y": 0.15805970132350922}, {"X": 0.6045741438865662, "Y": 0.15806272625923157}]}, "Id": "ccb5bc5a-882f-48cc-bcd5-de8cd0b77138"}, {"BlockType": "WORD", "Confidence": 90.70890808105469, "Text": "i", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009073248133063316, "Height": 0.01281918864697218, "Left": 0.06387272477149963, "Top": 0.1753341108560562}, "Polygon": [{"X": 0.06387312710285187, "Y": 0.17533434927463531}, {"X": 0.0729459747672081, "Y": 0.1753341108560562}, {"X": 0.07294557988643646, "Y": 0.18815305829048157}, {"X": 0.06387272477149963, "Y": 0.18815329670906067}]}, "Id": "fcc52368-aeb0-4f7e-bcf1-53e93938e591"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "CHICAGO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13924504816532135, "Height": 0.011456796899437904, "Left": 0.14098046720027924, "Top": 0.20990006625652313}, "Polygon": [{"X": 0.1409807950258255, "Y": 0.20990364253520966}, {"X": 0.2802255153656006, "Y": 0.20990006625652313}, {"X": 0.2802252173423767, "Y": 0.22135326266288757}, {"X": 0.14098046720027924, "Y": 0.2213568538427353}]}, "Id": "6a042079-478b-445d-acb1-c163377925f3"}, {"BlockType": "WORD", "Confidence": 99.580078125, "Text": "59TH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07510266453027725, "Height": 0.011194313876330853, "Left": 0.287117600440979, "Top": 0.21000732481479645}, "Polygon": [{"X": 0.2871178686618805, "Y": 0.21000926196575165}, {"X": 0.36222025752067566, "Y": 0.21000732481479645}, {"X": 0.36222001910209656, "Y": 0.22119970619678497}, {"X": 0.287117600440979, "Y": 0.22120164334774017}]}, "Id": "e6144cf8-ebc8-4022-a5f6-d0b5431edeae"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "ST", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03517308831214905, "Height": 0.011278709396719933, "Left": 0.36917731165885925, "Top": 0.20995600521564484}, "Polygon": [{"X": 0.36917757987976074, "Y": 0.20995691418647766}, {"X": 0.4043503999710083, "Y": 0.20995600521564484}, {"X": 0.4043501615524292, "Y": 0.2212338149547577}, {"X": 0.36917731165885925, "Y": 0.22123472392559052}]}, "Id": "7e50b272-d67c-46a0-95b3-273839b8cba7"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Terminal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09785367548465729, "Height": 0.009993024170398712, "Left": 0.14026406407356262, "Top": 0.22674809396266937}, "Polygon": [{"X": 0.1402643620967865, "Y": 0.22675061225891113}, {"X": 0.2381177395582199, "Y": 0.22674809396266937}, {"X": 0.23811747133731842, "Y": 0.23673857748508453}, {"X": 0.14026406407356262, "Y": 0.2367411106824875}]}, "Id": "4e80d1e3-afb2-4278-9e26-770d0f62388e"}, {"BlockType": "WORD", "Confidence": 99.84295654296875, "Text": "TRLU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07482900470495224, "Height": 0.011271804571151733, "Left": 0.14035113155841827, "Top": 0.2531777024269104}, "Polygon": [{"X": 0.14035145938396454, "Y": 0.2531796395778656}, {"X": 0.21518012881278992, "Y": 0.2531777024269104}, {"X": 0.21517983078956604, "Y": 0.26444756984710693}, {"X": 0.14035113155841827, "Y": 0.26444950699806213}]}, "Id": "6e4a4f33-7ee2-400a-b922-18635d01f4a1"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "888813", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10548894107341766, "Height": 0.011435320600867271, "Left": 0.2224441021680832, "Top": 0.2531190514564514}, "Polygon": [{"X": 0.22244441509246826, "Y": 0.2531217634677887}, {"X": 0.32793304324150085, "Y": 0.2531190514564514}, {"X": 0.32793277502059937, "Y": 0.2645516097545624}, {"X": 0.2224441021680832, "Y": 0.26455435156822205}]}, "Id": "a7d29a1a-a01c-4e7a-b872-c2dbfe70a521"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Container", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1130886822938919, "Height": 0.00982699729502201, "Left": 0.1414874941110611, "Top": 0.2699442207813263}, "Polygon": [{"X": 0.14148779213428497, "Y": 0.2699471414089203}, {"X": 0.254576176404953, "Y": 0.2699442207813263}, {"X": 0.2545759379863739, "Y": 0.2797682583332062}, {"X": 0.1414874941110611, "Y": 0.27977120876312256}]}, "Id": "46d3f273-1e5d-4b81-be2e-91681be68ccc"}, {"BlockType": "WORD", "Confidence": 71.1009292602539, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0048346384428441525, "Height": 0.012060984037816525, "Left": 0.2608829736709595, "Top": 0.26951703429222107}, "Polygon": [{"X": 0.26088327169418335, "Y": 0.2695171535015106}, {"X": 0.26571762561798096, "Y": 0.26951703429222107}, {"X": 0.2657172977924347, "Y": 0.2815778851509094}, {"X": 0.2608829736709595, "Y": 0.28157803416252136}]}, "Id": "1150875a-3071-4192-8b4c-3a54d5e765e7"}, {"BlockType": "WORD", "Confidence": 97.87049102783203, "Text": "020L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058512937277555466, "Height": 0.01000726968050003, "Left": 0.2715767025947571, "Top": 0.26985958218574524}, "Polygon": [{"X": 0.27157697081565857, "Y": 0.269861102104187}, {"X": 0.33008965849876404, "Y": 0.26985958218574524}, {"X": 0.33008942008018494, "Y": 0.2798653244972229}, {"X": 0.2715767025947571, "Y": 0.2798668444156647}]}, "Id": "89ddc0c0-4713-4674-a625-cfb8fa88ee6a"}, {"BlockType": "WORD", "Confidence": 96.63005828857422, "Text": "0799W", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08730519562959671, "Height": 0.009931672364473343, "Left": 0.3341968059539795, "Top": 0.2697235941886902}, "Polygon": [{"X": 0.3341970443725586, "Y": 0.26972585916519165}, {"X": 0.4215019941329956, "Y": 0.2697235941886902}, {"X": 0.4215018153190613, "Y": 0.27965301275253296}, {"X": 0.3341968059539795, "Y": 0.2796552777290344}]}, "Id": "bdfe0365-bd34-4517-ab26-99bc71edb33a"}, {"BlockType": "WORD", "Confidence": 99.345703125, "Text": "0857H", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07869853079319, "Height": 0.009966829791665077, "Left": 0.42531609535217285, "Top": 0.2697632610797882}, "Polygon": [{"X": 0.42531630396842957, "Y": 0.26976531744003296}, {"X": 0.5040146112442017, "Y": 0.2697632610797882}, {"X": 0.5040144324302673, "Y": 0.2797280550003052}, {"X": 0.42531609535217285, "Y": 0.2797301113605499}]}, "Id": "cbce020c-8d66-46f3-b08a-1cf6e7bfda12"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Desert", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09058579802513123, "Height": 0.011144384741783142, "Left": 0.14245909452438354, "Top": 0.29757165908813477}, "Polygon": [{"X": 0.1424594223499298, "Y": 0.2975740432739258}, {"X": 0.23304489254951477, "Y": 0.29757165908813477}, {"X": 0.2330445945262909, "Y": 0.3087137043476105}, {"X": 0.14245909452438354, "Y": 0.3087160587310791}]}, "Id": "46d7a468-eb4c-4230-b6e4-efa29bdaa982"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Eagle", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07207472622394562, "Height": 0.014032560400664806, "Left": 0.24010682106018066, "Top": 0.29728177189826965}, "Polygon": [{"X": 0.2401071935892105, "Y": 0.2972836494445801}, {"X": 0.3121815621852875, "Y": 0.29728177189826965}, {"X": 0.3121812045574188, "Y": 0.3113124370574951}, {"X": 0.24010682106018066, "Y": 0.31131431460380554}]}, "Id": "d9e77495-e1db-46e6-bf73-96bcda270d19"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Express", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10474773496389389, "Height": 0.01360432431101799, "Left": 0.31973928213119507, "Top": 0.2976062595844269}, "Polygon": [{"X": 0.31973960995674133, "Y": 0.29760900139808655}, {"X": 0.42448702454566956, "Y": 0.2976062595844269}, {"X": 0.42448675632476807, "Y": 0.3112078309059143}, {"X": 0.31973928213119507, "Y": 0.311210572719574}]}, "Id": "7a5506cc-4865-4377-ba68-d9d446d1b4cd"}, {"BlockType": "WORD", "Confidence": 99.73233795166016, "Text": "Inc", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03997587412595749, "Height": 0.011170496232807636, "Left": 0.43188512325286865, "Top": 0.2975783050060272}, "Polygon": [{"X": 0.43188536167144775, "Y": 0.2975793480873108}, {"X": 0.47186100482940674, "Y": 0.2975783050060272}, {"X": 0.47186079621315, "Y": 0.3087477684020996}, {"X": 0.43188512325286865, "Y": 0.3087488114833832}]}, "Id": "4a9ebecc-e6d1-4e21-ab4f-ca255a1d4cb2"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "(DEEE)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09172853827476501, "Height": 0.013675127178430557, "Left": 0.4780178666114807, "Top": 0.2970275580883026}, "Polygon": [{"X": 0.4780181050300598, "Y": 0.29702994227409363}, {"X": 0.5697463750839233, "Y": 0.2970275580883026}, {"X": 0.569746196269989, "Y": 0.31070026755332947}, {"X": 0.4780178666114807, "Y": 0.31070268154144287}]}, "Id": "c9389f0b-e057-49af-b3b4-fc81e903473d"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "Interchange", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13499601185321808, "Height": 0.012105481699109077, "Left": 0.1424178034067154, "Top": 0.3140735924243927}, "Polygon": [{"X": 0.14241816103458405, "Y": 0.31407713890075684}, {"X": 0.27741381525993347, "Y": 0.3140735924243927}, {"X": 0.2774135172367096, "Y": 0.3261755406856537}, {"X": 0.1424178034067154, "Y": 0.3261790871620178}]}, "Id": "aaae4724-7f4a-4ceb-8910-dc1f580ae59e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Carrier", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08128786087036133, "Height": 0.010056779719889164, "Left": 0.2820732593536377, "Top": 0.31411013007164}, "Polygon": [{"X": 0.2820734977722168, "Y": 0.31411224603652954}, {"X": 0.363361120223999, "Y": 0.31411013007164}, {"X": 0.3633608818054199, "Y": 0.3241647779941559}, {"X": 0.2820732593536377, "Y": 0.3241669237613678}]}, "Id": "6dd99106-303f-4d05-8847-aa07ecd7f4ef"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "(Driver:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08625180274248123, "Height": 0.011572504416108131, "Left": 0.36870911717414856, "Top": 0.31367963552474976}, "Polygon": [{"X": 0.36870938539505005, "Y": 0.3136819005012512}, {"X": 0.4549609422683716, "Y": 0.31367963552474976}, {"X": 0.4549607038497925, "Y": 0.32524988055229187}, {"X": 0.36870911717414856, "Y": 0.32525214552879333}]}, "Id": "209dde54-0710-4033-b3f1-df69babf4cd2"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "<PERSON><PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04413418099284172, "Height": 0.011361322365701199, "Left": 0.4596623182296753, "Top": 0.31399908661842346}, "Polygon": [{"X": 0.4596625566482544, "Y": 0.3140002489089966}, {"X": 0.5037965178489685, "Y": 0.31399908661842346}, {"X": 0.5037962794303894, "Y": 0.3253592550754547}, {"X": 0.4596623182296753, "Y": 0.32536041736602783}]}, "Id": "5b597c51-f25e-4f67-af24-72de236f749a"}, {"BlockType": "WORD", "Confidence": 96.77295684814453, "Text": "<PERSON><PERSON><PERSON>)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1122550219297409, "Height": 0.01168267335742712, "Left": 0.5086544156074524, "Top": 0.3137606084346771}, "Polygon": [{"X": 0.5086545944213867, "Y": 0.3137635290622711}, {"X": 0.6209094524383545, "Y": 0.3137606084346771}, {"X": 0.6209092736244202, "Y": 0.32544031739234924}, {"X": 0.5086544156074524, "Y": 0.3254432678222656}]}, "Id": "ee85e200-a11b-486f-8784-2ab6c0fd1efa"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "Out-Gate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1500757485628128, "Height": 0.012999093160033226, "Left": 0.42522189021110535, "Top": 0.369340181350708}, "Polygon": [{"X": 0.42522215843200684, "Y": 0.36934414505958557}, {"X": 0.5752976536750793, "Y": 0.369340181350708}, {"X": 0.5752974152565002, "Y": 0.3823353350162506}, {"X": 0.42522189021110535, "Y": 0.3823392987251282}]}, "Id": "f97d9e17-ccb7-4a29-9f3e-d6845c4386d2"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06315698474645615, "Height": 0.011475561186671257, "Left": 0.06098772585391998, "Top": 0.4069844186306}, "Polygon": [{"X": 0.060988087207078934, "Y": 0.4069861173629761}, {"X": 0.12414470314979553, "Y": 0.4069844186306}, {"X": 0.12414436787366867, "Y": 0.41845831274986267}, {"X": 0.06098772585391998, "Y": 0.4184599816799164}]}, "Id": "375c154c-7c93-486e-a157-6a8aa218dd3d"}, {"BlockType": "WORD", "Confidence": 99.29528045654297, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021548530086874962, "Height": 0.011633096262812614, "Left": 0.1306624859571457, "Top": 0.4069759249687195}, "Polygon": [{"X": 0.13066282868385315, "Y": 0.40697652101516724}, {"X": 0.1522110253572464, "Y": 0.4069759249687195}, {"X": 0.15221068263053894, "Y": 0.4186084568500519}, {"X": 0.1306624859571457, "Y": 0.41860902309417725}]}, "Id": "2c2cbcac-64fb-4998-8a02-4c2f831bc873"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06683371961116791, "Height": 0.011355794966220856, "Left": 0.15691253542900085, "Top": 0.4069252014160156}, "Polygon": [{"X": 0.15691286325454712, "Y": 0.4069269895553589}, {"X": 0.22374625504016876, "Y": 0.4069252014160156}, {"X": 0.22374595701694489, "Y": 0.418279230594635}, {"X": 0.15691253542900085, "Y": 0.41828101873397827}]}, "Id": "28637b5e-2fa2-4801-ac62-d3eaa325435e"}, {"BlockType": "WORD", "Confidence": 92.39795684814453, "Text": "07/03/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13808289170265198, "Height": 0.01203955803066492, "Left": 0.7112100124359131, "Top": 0.4066498279571533}, "Polygon": [{"X": 0.7112101912498474, "Y": 0.406653493642807}, {"X": 0.8492929339408875, "Y": 0.4066498279571533}, {"X": 0.8492928147315979, "Y": 0.4186857044696808}, {"X": 0.7112100124359131, "Y": 0.4186893701553345}]}, "Id": "58b6cd71-74e5-4567-95a9-d42db0f9619a"}, {"BlockType": "WORD", "Confidence": 90.82369995117188, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009526260197162628, "Height": 0.0024746893905103207, "Left": 0.8471529483795166, "Top": 0.41201403737068176}, "Polygon": [{"X": 0.8471530079841614, "Y": 0.41201427578926086}, {"X": 0.8566792011260986, "Y": 0.41201403737068176}, {"X": 0.8566792011260986, "Y": 0.41448846459388733}, {"X": 0.8471529483795166, "Y": 0.4144887328147888}]}, "Id": "ea8a4d73-60ce-4e96-b0a5-3bb5ceee587f"}, {"BlockType": "WORD", "Confidence": 99.228515625, "Text": "18:22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07645692676305771, "Height": 0.0115555040538311, "Left": 0.865456759929657, "Top": 0.4069211781024933}, "Polygon": [{"X": 0.8654568791389465, "Y": 0.40692320466041565}, {"X": 0.9419137239456177, "Y": 0.4069211781024933}, {"X": 0.9419136643409729, "Y": 0.41847464442253113}, {"X": 0.865456759929657, "Y": 0.4184766709804535}]}, "Id": "a42de8ca-498d-4c23-a5ad-56d6f466a705"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Associated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14463739097118378, "Height": 0.01153456512838602, "Left": 0.05893521383404732, "Top": 0.44134578108787537}, "Polygon": [{"X": 0.05893557891249657, "Y": 0.4413496255874634}, {"X": 0.203572615981102, "Y": 0.44134578108787537}, {"X": 0.20357230305671692, "Y": 0.452876478433609}, {"X": 0.05893521383404732, "Y": 0.4528803527355194}]}, "Id": "3c6b8b95-b935-46f6-bbb1-ea796465e654"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14389456808567047, "Height": 0.013552681542932987, "Left": 0.21178041398525238, "Top": 0.44142016768455505}, "Polygon": [{"X": 0.21178077161312103, "Y": 0.4414239823818207}, {"X": 0.35567498207092285, "Y": 0.44142016768455505}, {"X": 0.3556746542453766, "Y": 0.45496898889541626}, {"X": 0.21178041398525238, "Y": 0.4549728333950043}]}, "Id": "d739a56b-90d7-4348-8e9d-7bf84670544a"}, {"BlockType": "WORD", "Confidence": 99.6875, "Text": "MCCZ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08643659204244614, "Height": 0.011263337917625904, "Left": 0.7435631155967712, "Top": 0.44134393334388733}, "Polygon": [{"X": 0.7435632348060608, "Y": 0.4413462281227112}, {"X": 0.8299997448921204, "Y": 0.44134393334388733}, {"X": 0.8299996256828308, "Y": 0.4526049494743347}, {"X": 0.7435631155967712, "Y": 0.45260727405548096}]}, "Id": "10edde15-14ce-4778-84a3-2ee0e63dd272"}, {"BlockType": "WORD", "Confidence": 99.033203125, "Text": "342337", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10551197081804276, "Height": 0.011239794082939625, "Left": 0.8369097113609314, "Top": 0.4414023160934448}, "Polygon": [{"X": 0.8369097709655762, "Y": 0.44140511751174927}, {"X": 0.9424216747283936, "Y": 0.4414023160934448}, {"X": 0.9424216151237488, "Y": 0.45263928174972534}, {"X": 0.8369097113609314, "Y": 0.4526421129703522}]}, "Id": "c54009e0-75b6-4f43-8689-7e2a287480ac"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "L/E", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04215734452009201, "Height": 0.011545681394636631, "Left": 0.06085648760199547, "Top": 0.47608551383018494}, "Polygon": [{"X": 0.06085685268044472, "Y": 0.4760866165161133}, {"X": 0.10301382839679718, "Y": 0.47608551383018494}, {"X": 0.10301347821950912, "Y": 0.48763003945350647}, {"X": 0.06085648760199547, "Y": 0.4876311719417572}]}, "Id": "c9ed085b-b50f-4fbd-a1ad-90b71a934f82"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0643196702003479, "Height": 0.011379961855709553, "Left": 0.8766753077507019, "Top": 0.4758877754211426}, "Polygon": [{"X": 0.8766754269599915, "Y": 0.4758894741535187}, {"X": 0.9409949779510498, "Y": 0.4758877754211426}, {"X": 0.940994918346405, "Y": 0.48726600408554077}, {"X": 0.8766753077507019, "Y": 0.48726773262023926}]}, "Id": "274dc135-e3a8-4838-9cef-9ea639c810bd"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Booking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10760873556137085, "Height": 0.013733888044953346, "Left": 0.0609896183013916, "Top": 0.5104464888572693}, "Polygon": [{"X": 0.06099005043506622, "Y": 0.5104493498802185}, {"X": 0.16859835386276245, "Y": 0.5104464888572693}, {"X": 0.1685979664325714, "Y": 0.5241774320602417}, {"X": 0.0609896183013916, "Y": 0.5241803526878357}]}, "Id": "ef5dbc19-2f14-472c-9c85-e66805f2c8e7"}, {"BlockType": "WORD", "Confidence": 98.017578125, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017192652449011803, "Height": 0.010802436619997025, "Left": 0.1748652160167694, "Top": 0.5108093023300171}, "Polygon": [{"X": 0.1748655140399933, "Y": 0.5108097791671753}, {"X": 0.19205786287784576, "Y": 0.5108093023300171}, {"X": 0.1920575648546219, "Y": 0.5216112732887268}, {"X": 0.1748652160167694, "Y": 0.521611750125885}]}, "Id": "a6f82a30-4a49-4cdc-9dab-34a90ca3584b"}, {"BlockType": "WORD", "Confidence": 91.92103576660156, "Text": "---", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032540373504161835, "Height": 0.003156771883368492, "Left": 0.9094561338424683, "Top": 0.5155411958694458}, "Polygon": [{"X": 0.909456193447113, "Y": 0.5155420899391174}, {"X": 0.9419965147972107, "Y": 0.5155411958694458}, {"X": 0.9419965147972107, "Y": 0.5186970829963684}, {"X": 0.9094561338424683, "Y": 0.51869797706604}]}, "Id": "3f7b9e7f-82c1-4f48-8ae2-974837c53302"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "Seal(s)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08506505936384201, "Height": 0.013724159449338913, "Left": 0.059612855315208435, "Top": 0.5445165038108826}, "Polygon": [{"X": 0.05961328744888306, "Y": 0.544518768787384}, {"X": 0.14467790722846985, "Y": 0.5445165038108826}, {"X": 0.14467750489711761, "Y": 0.5582383275032043}, {"X": 0.059612855315208435, "Y": 0.5582406520843506}]}, "Id": "9f4e468e-7957-4734-94e0-690d26868dcc"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "2402697", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12311472743749619, "Height": 0.011285274289548397, "Left": 0.8184822797775269, "Top": 0.5451086163520813}, "Polygon": [{"X": 0.8184823393821716, "Y": 0.5451119542121887}, {"X": 0.9415969848632812, "Y": 0.5451086163520813}, {"X": 0.9415969252586365, "Y": 0.5563905835151672}, {"X": 0.8184822797775269, "Y": 0.5563939213752747}]}, "Id": "e54f54f7-e360-41fc-9859-2601dead1465"}, {"BlockType": "WORD", "Confidence": 99.47285461425781, "Text": "No", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03649570792913437, "Height": 0.010083253495395184, "Left": 0.07282061129808426, "Top": 0.5958209037780762}, "Polygon": [{"X": 0.07282092422246933, "Y": 0.5958219170570374}, {"X": 0.10931631922721863, "Y": 0.5958209037780762}, {"X": 0.10931601375341415, "Y": 0.605903148651123}, {"X": 0.07282061129808426, "Y": 0.6059041619300842}]}, "Id": "ba84253f-a644-4309-9f84-6be9e6ec5a32"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "images", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08518681675195694, "Height": 0.01278587058186531, "Left": 0.11508636176586151, "Top": 0.5957594513893127}, "Polygon": [{"X": 0.11508674174547195, "Y": 0.595761775970459}, {"X": 0.20027317106723785, "Y": 0.5957594513893127}, {"X": 0.2002728283405304, "Y": 0.6085429787635803}, {"X": 0.11508636176586151, "Y": 0.6085453033447266}]}, "Id": "df92d170-4d6f-4803-a69f-5048e550b490"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "available", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10902774333953857, "Height": 0.010579172521829605, "Left": 0.2053874135017395, "Top": 0.5955380797386169}, "Polygon": [{"X": 0.20538771152496338, "Y": 0.5955410599708557}, {"X": 0.3144151568412781, "Y": 0.5955380797386169}, {"X": 0.314414918422699, "Y": 0.6061142683029175}, {"X": 0.2053874135017395, "Y": 0.6061172485351562}]}, "Id": "1323784b-d0b4-4aef-b622-2cfeb236a981"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037976231426000595, "Height": 0.010698771104216576, "Left": 0.3193682134151459, "Top": 0.5954105257987976}, "Polygon": [{"X": 0.31936848163604736, "Y": 0.5954115390777588}, {"X": 0.35734444856643677, "Y": 0.5954105257987976}, {"X": 0.35734421014785767, "Y": 0.6061082482337952}, {"X": 0.3193682134151459, "Y": 0.6061092615127563}]}, "Id": "261f4e82-a529-4fb4-b017-6db201ebc846"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "departures", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13598108291625977, "Height": 0.01269662193953991, "Left": 0.3611191213130951, "Top": 0.5956160426139832}, "Polygon": [{"X": 0.3611193895339966, "Y": 0.595619797706604}, {"X": 0.49710020422935486, "Y": 0.5956160426139832}, {"X": 0.49709996581077576, "Y": 0.6083089709281921}, {"X": 0.3611191213130951, "Y": 0.6083126664161682}]}, "Id": "35d8592a-0d73-4c4e-9d0e-6488c860deb5"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "CSX.com", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12698812782764435, "Height": 0.012109027244150639, "Left": 0.4366007149219513, "Top": 0.8208789229393005}, "Polygon": [{"X": 0.4366009533405304, "Y": 0.8208824992179871}, {"X": 0.5635888576507568, "Y": 0.8208789229393005}, {"X": 0.5635886192321777, "Y": 0.8329843282699585}, {"X": 0.4366007149219513, "Y": 0.832987904548645}]}, "Id": "370536a7-397f-45f9-9525-b9d3522cb2d1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Help", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06640863418579102, "Height": 0.014944499358534813, "Left": 0.46796175837516785, "Top": 0.8520155549049377}, "Polygon": [{"X": 0.46796202659606934, "Y": 0.8520174026489258}, {"X": 0.5343703627586365, "Y": 0.8520155549049377}, {"X": 0.5343701243400574, "Y": 0.8669581413269043}, {"X": 0.46796175837516785, "Y": 0.8669600486755371}]}, "Id": "3bf9c87d-4e33-4b36-99be-060bcbc13f3a"}, {"BlockType": "WORD", "Confidence": 97.42207336425781, "Text": "©", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0228907261043787, "Height": 0.009708752855658531, "Left": 0.275626003742218, "Top": 0.8850932717323303}, "Polygon": [{"X": 0.2756262719631195, "Y": 0.8850939273834229}, {"X": 0.29851675033569336, "Y": 0.8850932717323303}, {"X": 0.29851651191711426, "Y": 0.8948013782501221}, {"X": 0.275626003742218, "Y": 0.8948020339012146}]}, "Id": "a2c7b81d-5f0d-40c0-a114-f7931328233b"}, {"BlockType": "WORD", "Confidence": 99.65581512451172, "Text": "2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0635436549782753, "Height": 0.009861528873443604, "Left": 0.304659366607666, "Top": 0.8852049112319946}, "Polygon": [{"X": 0.3046596050262451, "Y": 0.8852066993713379}, {"X": 0.3682030141353607, "Y": 0.8852049112319946}, {"X": 0.368202805519104, "Y": 0.895064651966095}, {"X": 0.304659366607666, "Y": 0.8950664401054382}]}, "Id": "91863b7e-e63e-4869-ab5e-f2cb10249a2b"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "CSX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052224595099687576, "Height": 0.009573076851665974, "Left": 0.37452858686447144, "Top": 0.8851069808006287}, "Polygon": [{"X": 0.37452879548072815, "Y": 0.885108470916748}, {"X": 0.4267531931400299, "Y": 0.8851069808006287}, {"X": 0.4267529845237732, "Y": 0.8946785926818848}, {"X": 0.37452858686447144, "Y": 0.8946800827980042}]}, "Id": "ff68f938-6b14-4c46-ac20-33a63f798b58"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Technology,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14501522481441498, "Height": 0.012994920834898949, "Left": 0.4305749833583832, "Top": 0.8847103714942932}, "Polygon": [{"X": 0.43057525157928467, "Y": 0.8847144842147827}, {"X": 0.575590193271637, "Y": 0.8847103714942932}, {"X": 0.5755900144577026, "Y": 0.8977011442184448}, {"X": 0.4305749833583832, "Y": 0.8977052569389343}]}, "Id": "fbcfdb68-c40f-4e53-b75f-4146df91031f"}, {"BlockType": "WORD", "Confidence": 99.65501403808594, "Text": "Inc.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041713204234838486, "Height": 0.010146277956664562, "Left": 0.5825296640396118, "Top": 0.8851140737533569}, "Polygon": [{"X": 0.5825297832489014, "Y": 0.8851152658462524}, {"X": 0.6242428421974182, "Y": 0.8851140737533569}, {"X": 0.6242427229881287, "Y": 0.8952591419219971}, {"X": 0.5825296640396118, "Y": 0.8952603340148926}]}, "Id": "e3abfb46-d4cf-4aaf-94f6-93e412e91545"}, {"BlockType": "WORD", "Confidence": 98.0556411743164, "Text": "v.4.43.0", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09524907171726227, "Height": 0.010087177157402039, "Left": 0.6293061375617981, "Top": 0.8851871490478516}, "Polygon": [{"X": 0.6293063163757324, "Y": 0.8851898908615112}, {"X": 0.724555253982544, "Y": 0.8851871490478516}, {"X": 0.7245551347732544, "Y": 0.8952716588973999}, {"X": 0.6293061375617981, "Y": 0.8952743411064148}]}, "Id": "d7a49657-e3d0-474e-b968-123fcb65835d"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "2d20c87d-62d4-4c5b-9f19-57e32733a2e4", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "2d20c87d-62d4-4c5b-9f19-57e32733a2e4", "content-type": "application/x-amz-json-1.1", "content-length": "50541", "date": "Thu, 18 Sep 2025 08:00:18 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "f5331ceb-de80-4c27-84e7-c48c3fdc7d0e", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 08:00:21 GMT", "content-type": "application/json", "content-length": "809", "connection": "keep-alive", "x-amzn-requestid": "f5331ceb-de80-4c27-84e7-c48c3fdc7d0e"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need classify each page.\n\nPage 1 content: \"Gate Receipts\", \"Out-Gate\", \"Date & Time\", \"Associated Equipment\", \"Container\", \"Interchange Carrier\", etc. So it seems an outgate document. Possibly \"outgate\" type.\n\nCheck keywords: \"Out-Gate\", \"Gate Receipts\", \"CSX\" maybe rail gate. It's outgate.\n\nThus doc_type: outgate. Output array with one object: page_no 1, doc_type \"outgate\"."}}}, {"toolUse": {"toolUseId": "tooluse_MWtleriKS7WHz-1f6KZeiA", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "outgate"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2253, "outputTokens": 142, "totalTokens": 2395}, "metrics": {"latencyMs": 812}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/00cd0334_26_outgate_4.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check for contination of previous page by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n9:37\n88\nDone\n8\nnext.shipcsx.com\nAA\nGate Receipts (1) As of 09:37 on 07/04/25\ni\nCHICAGO 59TH ST\nTerminal\nTRLU 888813\nContainer I 020L 0799W 0857H\nDesert Eagle Express Inc (DEEE)\nInterchange Carrier (Driver: <PERSON><PERSON>, <PERSON><PERSON><PERSON>)\nOut-Gate\nDate & Time\n07/03/25 - 18:22\nAssociated Equipment\nMCCZ 342337\nL/E\nLoad\nBooking #\n---\nSeal(s)\n2402697\nNo images available for departures\nCSX.com\nHelp\n© 2025 CSX Technology, Inc. v.4.43.0\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}