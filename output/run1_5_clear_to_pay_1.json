{"classification_result": {"documents": [{"page_no": 1, "doc_type": "clear_to_pay"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 1.1069502647842455e-07, "Y": 0.0}, {"X": 1.0, "Y": 1.4171428119880147e-07}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "e69d4ccb-7736-4ef9-99aa-87de415a49f5", "Relationships": [{"Type": "CHILD", "Ids": ["c642b86c-baf8-4037-bf39-7909b024feac", "c4d90df0-6c05-472f-b265-9610de1732e4", "957b8e54-6354-48db-b26a-f4bf032fcb3a", "67ec4885-375b-499b-a28b-c53553dee213", "d731c578-cbb9-4a91-8a08-b41396fd1c64", "3bae62e1-9e13-4d44-aebb-be7fc717aed9", "d1abc3f0-714d-489c-9031-9e37284f0ac3", "0f953695-bbea-4acc-8982-5d97552050aa", "2e4dbc78-7713-458e-9a38-d4dc693080d2", "bd0a4f57-4f4c-4ed0-8484-37a7a3722e9a", "477b0364-a3e2-43b5-b4c0-d00237f55f50", "3712c13e-5ef4-44a8-9449-b9593dc28b37", "158c5bdc-f6a8-4fd9-a7a8-2a7da86026c3", "641d4781-488d-4601-a622-fdf6f5b2b868", "89d83515-da71-4983-afba-e19d7e89f06e", "c8d3e9d5-31d7-4a8b-b848-971ac7f7ebd0", "d10b22c9-7cd4-41cf-9f45-ae5021e85f2d", "35388df8-79ea-4135-b7a2-a83473d274bc", "7122a7b6-c88c-4fa4-98d0-7e8e628ee5b4", "be4a9d06-923a-41f0-ad46-c0bf0264f798", "e2b1f9ac-6b2e-4571-a149-bf48be6f38ef", "277ffd2b-6bf7-4add-ad56-caa2e8738cec", "6bffb7bb-2a22-411e-9311-76689ae08ec7", "5bdece69-0dac-4d5b-a28a-f694fa5b36f5", "db28ce5d-8988-40a1-9384-a13c93e934be", "9fa9628d-ce0e-49b7-a001-f5f1f8b3376a", "9dd70c02-46c4-43d4-ac59-027d19b937a9", "cbdf5981-64de-4636-9b2f-993ff0415749", "be8cf8b7-9f9f-4402-8eae-714d5b8a7a29", "43f4681f-96ef-4508-9384-24ac001ce0ec", "968990d1-fcde-4b19-aae2-eaa6d5ba73ce", "d453cf67-ac7f-4b5d-9677-00ef464e03f1", "63d8b656-37c2-4cf9-98c7-f709ea8c1726", "a8a70c70-8288-4178-9c42-7bccf003ac53", "2d8597cd-02ab-47a5-96cc-8a0f5f9b143a", "38645989-cf6f-4134-b13d-18394c873665", "bf02b2eb-31cc-4c96-ac46-c636254666f1", "b9601c32-0d71-48e9-9446-af673e2a79e0", "d5a84f88-df90-4d1e-8ae5-876df7429929", "234bb397-2cf8-4017-8120-f6a61a07b7ea", "c50e791e-bdfb-4349-a269-8c42e731d307", "54db8215-8376-4408-a194-ccb48e05b76f"]}]}, {"BlockType": "LINE", "Confidence": 99.79412841796875, "Text": "Revised", "Geometry": {"BoundingBox": {"Width": 0.12010426074266434, "Height": 0.06455104053020477, "Left": 0.3497428297996521, "Top": 0.030088284984230995}, "Polygon": [{"X": 0.349761039018631, "Y": 0.030088284984230995}, {"X": 0.4698471128940582, "Y": 0.03011963702738285}, {"X": 0.4698289930820465, "Y": 0.09463932365179062}, {"X": 0.3497428297996521, "Y": 0.09460795670747757}]}, "Id": "c642b86c-baf8-4037-bf39-7909b024feac", "Relationships": [{"Type": "CHILD", "Ids": ["00948e61-43e0-4a7e-adaa-512f7f0a9443"]}]}, {"BlockType": "LINE", "Confidence": 99.91131591796875, "Text": "SAW-7116716", "Geometry": {"BoundingBox": {"Width": 0.15808148682117462, "Height": 0.014960047788918018, "Left": 0.08656389266252518, "Top": 0.07972919195890427}, "Polygon": [{"X": 0.08656814694404602, "Y": 0.07972919195890427}, {"X": 0.2446453869342804, "Y": 0.07977047562599182}, {"X": 0.24464115500450134, "Y": 0.09468923509120941}, {"X": 0.08656389266252518, "Y": 0.09464794397354126}]}, "Id": "c4d90df0-6c05-472f-b265-9610de1732e4", "Relationships": [{"Type": "CHILD", "Ids": ["f6f71743-0cf1-437e-bc19-88a137179555"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "REVISED", "Geometry": {"BoundingBox": {"Width": 0.10527349263429642, "Height": 0.014873035252094269, "Left": 0.11285440623760223, "Top": 0.10755269229412079}, "Polygon": [{"X": 0.11285863071680069, "Y": 0.10755269229412079}, {"X": 0.21812790632247925, "Y": 0.10758019238710403}, {"X": 0.2181236892938614, "Y": 0.12242572009563446}, {"X": 0.11285440623760223, "Y": 0.12239822000265121}]}, "Id": "957b8e54-6354-48db-b26a-f4bf032fcb3a", "Relationships": [{"Type": "CHILD", "Ids": ["d773fc19-bf51-47b4-8280-bbb5429e3486"]}]}, {"BlockType": "LINE", "Confidence": 23.563457489013672, "Text": "AMBVB", "Geometry": {"BoundingBox": {"Width": 0.22393830120563507, "Height": 0.0347960889339447, "Left": 0.3939111530780792, "Top": 0.13196684420108795}, "Polygon": [{"X": 0.3939209282398224, "Y": 0.13196684420108795}, {"X": 0.6178494691848755, "Y": 0.1320253610610962}, {"X": 0.6178397536277771, "Y": 0.16676294803619385}, {"X": 0.3939111530780792, "Y": 0.16670441627502441}]}, "Id": "67ec4885-375b-499b-a28b-c53553dee213", "Relationships": [{"Type": "CHILD", "Ids": ["7c72551d-8e4c-4cf8-96c0-882dbe7ec54b"]}]}, {"BlockType": "LINE", "Confidence": 91.91047668457031, "Text": "SAWYER NURSERY.", "Geometry": {"BoundingBox": {"Width": 0.4284513592720032, "Height": 0.025782587006688118, "Left": 0.29145359992980957, "Top": 0.1801413595676422}, "Polygon": [{"X": 0.29146087169647217, "Y": 0.1801413595676422}, {"X": 0.7199049592018127, "Y": 0.18025335669517517}, {"X": 0.7198978066444397, "Y": 0.20592394471168518}, {"X": 0.29145359992980957, "Y": 0.20581193268299103}]}, "Id": "d731c578-cbb9-4a91-8a08-b41396fd1c64", "Relationships": [{"Type": "CHILD", "Ids": ["5810a64f-6489-4ebe-8fec-f2f38447e76e", "b0cc7d9a-c098-45b8-89e4-158ea18276c6"]}]}, {"BlockType": "LINE", "Confidence": 99.8923110961914, "Text": "Truckload Check-in Form", "Geometry": {"BoundingBox": {"Width": 0.3428021967411041, "Height": 0.021768158301711082, "Left": 0.34269627928733826, "Top": 0.21328970789909363}, "Polygon": [{"X": 0.34270238876342773, "Y": 0.21328970789909363}, {"X": 0.6854984760284424, "Y": 0.21337933838367462}, {"X": 0.6854923963546753, "Y": 0.23505786061286926}, {"X": 0.34269627928733826, "Y": 0.23496821522712708}]}, "Id": "3bae62e1-9e13-4d44-aebb-be7fc717aed9", "Relationships": [{"Type": "CHILD", "Ids": ["0c59b4ed-324d-4797-be48-701d4ce7cac7", "a3004e8d-3d60-4366-94f5-468d25933d52", "2a5f90b4-bdd9-4a43-ad38-41314c053076"]}]}, {"BlockType": "LINE", "Confidence": 99.85720825195312, "Text": "Clear-to-Pay Voucher", "Geometry": {"BoundingBox": {"Width": 0.288891077041626, "Height": 0.025550564751029015, "Left": 0.36958086490631104, "Top": 0.24223844707012177}, "Polygon": [{"X": 0.36958804726600647, "Y": 0.24223844707012177}, {"X": 0.658471941947937, "Y": 0.24231399595737457}, {"X": 0.6584648489952087, "Y": 0.26778900623321533}, {"X": 0.36958086490631104, "Y": 0.26771342754364014}]}, "Id": "d1abc3f0-714d-489c-9031-9e37284f0ac3", "Relationships": [{"Type": "CHILD", "Ids": ["8ecc3589-7912-4355-8bb0-5f415dca8ff4", "cb1e6f1d-cd6a-4455-bcf6-d0b5a42a33bb"]}]}, {"BlockType": "LINE", "Confidence": 84.02765655517578, "Text": "Load # 20196", "Geometry": {"BoundingBox": {"Width": 0.17072196304798126, "Height": 0.024292057380080223, "Left": 0.10787399113178253, "Top": 0.276028573513031}, "Polygon": [{"X": 0.10788089036941528, "Y": 0.276028573513031}, {"X": 0.2785959541797638, "Y": 0.2760732173919678}, {"X": 0.2785890996456146, "Y": 0.3003206253051758}, {"X": 0.10787399113178253, "Y": 0.3002759516239166}]}, "Id": "0f953695-bbea-4acc-8982-5d97552050aa", "Relationships": [{"Type": "CHILD", "Ids": ["21fe2527-5fe8-4fc5-b9ad-820369d4b73b", "417ed8d4-6ed2-4a94-b9b0-3bf49118aca4", "1a020c62-c962-48d3-a0c5-311968549ba3"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Broker", "Geometry": {"BoundingBox": {"Width": 0.0553869754076004, "Height": 0.011374310590326786, "Left": 0.5570418238639832, "Top": 0.28892144560813904}, "Polygon": [{"X": 0.5570449829101562, "Y": 0.28892144560813904}, {"X": 0.6124287843704224, "Y": 0.28893592953681946}, {"X": 0.6124256253242493, "Y": 0.30029574036598206}, {"X": 0.5570418238639832, "Y": 0.30028125643730164}]}, "Id": "2e4dbc78-7713-458e-9a38-d4dc693080d2", "Relationships": [{"Type": "CHILD", "Ids": ["e3cad528-3667-4066-8e54-cb2cc1ec0657"]}]}, {"BlockType": "LINE", "Confidence": 98.77650451660156, "Text": "PF2", "Geometry": {"BoundingBox": {"Width": 0.09524461627006531, "Height": 0.03364512696862221, "Left": 0.6737791299819946, "Top": 0.2739748954772949}, "Polygon": [{"X": 0.6737884879112244, "Y": 0.2739748954772949}, {"X": 0.7690237760543823, "Y": 0.27399981021881104}, {"X": 0.7690144181251526, "Y": 0.30762001872062683}, {"X": 0.6737791299819946, "Y": 0.3075951039791107}]}, "Id": "bd0a4f57-4f4c-4ed0-8484-37a7a3722e9a", "Relationships": [{"Type": "CHILD", "Ids": ["25cffb86-d719-49f1-9d5f-4b2a2e39312f"]}]}, {"BlockType": "LINE", "Confidence": 99.76788330078125, "Text": "Date Processed 6/12/2023", "Geometry": {"BoundingBox": {"Width": 0.29194554686546326, "Height": 0.03026287630200386, "Left": 0.10720888525247574, "Top": 0.32827088236808777}, "Polygon": [{"X": 0.10721747577190399, "Y": 0.32827088236808777}, {"X": 0.3991544246673584, "Y": 0.3283472955226898}, {"X": 0.3991459310054779, "Y": 0.3585337698459625}, {"X": 0.10720888525247574, "Y": 0.3584573566913605}]}, "Id": "477b0364-a3e2-43b5-b4c0-d00237f55f50", "Relationships": [{"Type": "CHILD", "Ids": ["ede0aee9-41e8-4eb9-92ea-1b5657f312e8", "a1e4dda7-6b1e-4db0-82e7-383851ea1bb5", "7409b16c-5132-4e2b-8b13-b6f42cb8edf4"]}]}, {"BlockType": "LINE", "Confidence": 97.83914184570312, "Text": "Load Cost $3,869.-", "Geometry": {"BoundingBox": {"Width": 0.26343634724617004, "Height": 0.027651693671941757, "Left": 0.5601359009742737, "Top": 0.3361142873764038}, "Polygon": [{"X": 0.5601435899734497, "Y": 0.3361142873764038}, {"X": 0.8235722184181213, "Y": 0.33618322014808655}, {"X": 0.8235645890235901, "Y": 0.36376598477363586}, {"X": 0.5601359009742737, "Y": 0.36369702219963074}]}, "Id": "3712c13e-5ef4-44a8-9449-b9593dc28b37", "Relationships": [{"Type": "CHILD", "Ids": ["2800a181-15cf-43aa-ad82-55cf6c6f4a37", "52439a4f-734b-4e42-adf3-9a69bb951ca3", "f72855a1-269f-425b-be63-209d62bfad84"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON><PERSON> Returned", "Geometry": {"BoundingBox": {"Width": 0.12417621910572052, "Height": 0.012237363494932652, "Left": 0.11193739622831345, "Top": 0.37081378698349}, "Polygon": [{"X": 0.11194086819887161, "Y": 0.37081378698349}, {"X": 0.23611362278461456, "Y": 0.37084630131721497}, {"X": 0.2361101657152176, "Y": 0.38305115699768066}, {"X": 0.11193739622831345, "Y": 0.3830186426639557}]}, "Id": "158c5bdc-f6a8-4fd9-a7a8-2a7da86026c3", "Relationships": [{"Type": "CHILD", "Ids": ["ea240ced-62be-4e81-90eb-e908077074cb", "58f9a9fc-ad1a-45bd-903f-48e0d8960d72"]}]}, {"BlockType": "LINE", "Confidence": 64.2799301147461, "Text": "5.7", "Geometry": {"BoundingBox": {"Width": 0.041299495846033096, "Height": 0.025344818830490112, "Left": 0.2973567247390747, "Top": 0.36436423659324646}, "Polygon": [{"X": 0.29736387729644775, "Y": 0.36436423659324646}, {"X": 0.3386562168598175, "Y": 0.3643750250339508}, {"X": 0.33864909410476685, "Y": 0.3897090554237366}, {"X": 0.2973567247390747, "Y": 0.38969823718070984}]}, "Id": "641d4781-488d-4601-a622-fdf6f5b2b868", "Relationships": [{"Type": "CHILD", "Ids": ["5a2e2813-3ff3-415c-9f57-36b00edb0b35"]}]}, {"BlockType": "LINE", "Confidence": 99.7713394165039, "Text": "Surcharge + $", "Geometry": {"BoundingBox": {"Width": 0.11943857371807098, "Height": 0.014902562834322453, "Left": 0.5651573538780212, "Top": 0.39278605580329895}, "Polygon": [{"X": 0.5651615262031555, "Y": 0.39278605580329895}, {"X": 0.6845959424972534, "Y": 0.39281731843948364}, {"X": 0.6845917701721191, "Y": 0.40768861770629883}, {"X": 0.5651573538780212, "Y": 0.40765735507011414}]}, "Id": "89d83515-da71-4983-afba-e19d7e89f06e", "Relationships": [{"Type": "CHILD", "Ids": ["e69d17ca-5b9b-4e36-b8dc-5489eff856e1", "2cb34ec7-0c1a-41dd-8a6a-0c9d3da70582", "12bb2fb9-a26d-4473-b5fe-27981a557a1a"]}]}, {"BlockType": "LINE", "Confidence": 65.6993408203125, "Text": "-", "Geometry": {"BoundingBox": {"Width": 0.02328779175877571, "Height": 0.003738170489668846, "Left": 0.7546005249023438, "Top": 0.39373376965522766}, "Polygon": [{"X": 0.7546015381813049, "Y": 0.39373376965522766}, {"X": 0.777888298034668, "Y": 0.39373987913131714}, {"X": 0.7778872847557068, "Y": 0.39747193455696106}, {"X": 0.7546005249023438, "Y": 0.39746585488319397}]}, "Id": "c8d3e9d5-31d7-4a8b-b848-971ac7f7ebd0", "Relationships": [{"Type": "CHILD", "Ids": ["d1211597-92f1-45ee-8287-cf6f5cc09e52"]}]}, {"BlockType": "LINE", "Confidence": 58.209503173828125, "Text": "-", "Geometry": {"BoundingBox": {"Width": 0.023595619946718216, "Height": 0.002982028527185321, "Left": 0.7594481110572815, "Top": 0.4303312301635742}, "Polygon": [{"X": 0.7594488859176636, "Y": 0.4303312301635742}, {"X": 0.7830436825752258, "Y": 0.43033742904663086}, {"X": 0.783042848110199, "Y": 0.433313250541687}, {"X": 0.7594481110572815, "Y": 0.43330708146095276}]}, "Id": "d10b22c9-7cd4-41cf-9f45-ae5021e85f2d", "Relationships": [{"Type": "CHILD", "Ids": ["ddb86e07-4fe4-4809-a5ce-2f1da9452a8d"]}]}, {"BlockType": "LINE", "Confidence": 82.65968322753906, "Text": "57 X$ 4, - = $ 228,", "Geometry": {"BoundingBox": {"Width": 0.335521936416626, "Height": 0.030440453439950943, "Left": 0.1254984736442566, "Top": 0.42875978350639343}, "Polygon": [{"X": 0.12550710141658783, "Y": 0.42875978350639343}, {"X": 0.46102041006088257, "Y": 0.42884764075279236}, {"X": 0.4610118865966797, "Y": 0.4592002332210541}, {"X": 0.1254984736442566, "Y": 0.45911234617233276}]}, "Id": "35388df8-79ea-4135-b7a2-a83473d274bc", "Relationships": [{"Type": "CHILD", "Ids": ["cdd1c443-d32f-495b-899a-257fb4c769bf", "59a527b4-c661-4329-b787-44bfcaaceed9", "3782acb4-458f-4fbd-b155-2de1264fc010", "2602f040-fad8-4c3e-a441-50163f6b4806", "938433b5-a794-46f8-8ebd-e324dd24c373", "5fc8f285-8148-4c7e-a975-0e4cc946c254"]}]}, {"BlockType": "LINE", "Confidence": 97.48435974121094, "Text": "Deductions - $", "Geometry": {"BoundingBox": {"Width": 0.1250302642583847, "Height": 0.01347880344837904, "Left": 0.5643481016159058, "Top": 0.4280858039855957}, "Polygon": [{"X": 0.5643518567085266, "Y": 0.4280858039855957}, {"X": 0.6893783211708069, "Y": 0.4281185567378998}, {"X": 0.689374566078186, "Y": 0.4415645897388458}, {"X": 0.5643481016159058, "Y": 0.44153186678886414}]}, "Id": "7122a7b6-c88c-4fa4-98d0-7e8e628ee5b4", "Relationships": [{"Type": "CHILD", "Ids": ["97eda17d-cd6e-48b8-bad0-a4b87d1762a8", "514eab5c-e0d2-4419-8879-3d61d05a93ed"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "RACKS", "Geometry": {"BoundingBox": {"Width": 0.06488774716854095, "Height": 0.011576512828469276, "Left": 0.1056532934308052, "Top": 0.4585410952568054}, "Polygon": [{"X": 0.10565657913684845, "Y": 0.4585410952568054}, {"X": 0.17054103314876556, "Y": 0.4585580825805664}, {"X": 0.17053775489330292, "Y": 0.47011759877204895}, {"X": 0.1056532934308052, "Y": 0.47010061144828796}]}, "Id": "be4a9d06-923a-41f0-ad46-c0bf0264f798", "Relationships": [{"Type": "CHILD", "Ids": ["aa8883ba-6258-4151-b48e-f2f9252b43c3"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "AMOUNT", "Geometry": {"BoundingBox": {"Width": 0.08612602949142456, "Height": 0.011357786133885384, "Left": 0.22077137231826782, "Top": 0.459964394569397}, "Polygon": [{"X": 0.2207745909690857, "Y": 0.459964394569397}, {"X": 0.3068974018096924, "Y": 0.45998695492744446}, {"X": 0.3068942129611969, "Y": 0.4713221788406372}, {"X": 0.22077137231826782, "Y": 0.4712996184825897}]}, "Id": "e2b1f9ac-6b2e-4571-a149-bf48be6f38ef", "Relationships": [{"Type": "CHILD", "Ids": ["6353a328-9ca5-45b6-a73a-f587bbfc3381"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.06409244239330292, "Height": 0.011580505408346653, "Left": 0.3562673330307007, "Top": 0.4609709680080414}, "Polygon": [{"X": 0.35627058148384094, "Y": 0.4609709680080414}, {"X": 0.4203597605228424, "Y": 0.46098777651786804}, {"X": 0.42035651206970215, "Y": 0.47255149483680725}, {"X": 0.3562673330307007, "Y": 0.4725346863269806}]}, "Id": "277ffd2b-6bf7-4add-ad56-caa2e8738cec", "Relationships": [{"Type": "CHILD", "Ids": ["9e83fa2c-7ee4-42f8-ba60-19a764c5fa89"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.04755816236138344, "Height": 0.011530096642673016, "Left": 0.5673404932022095, "Top": 0.48040127754211426}, "Polygon": [{"X": 0.5673437118530273, "Y": 0.48040127754211426}, {"X": 0.6148986220359802, "Y": 0.4804137349128723}, {"X": 0.6148954629898071, "Y": 0.49193137884140015}, {"X": 0.5673404932022095, "Y": 0.4919189214706421}]}, "Id": "6bffb7bb-2a22-411e-9311-76689ae08ec7", "Relationships": [{"Type": "CHILD", "Ids": ["9a38f75b-a0bc-475d-81b2-67006d16024b"]}]}, {"BlockType": "LINE", "Confidence": 89.73200988769531, "Text": "+ $ 228,- $", "Geometry": {"BoundingBox": {"Width": 0.1564401239156723, "Height": 0.023144178092479706, "Left": 0.6697088479995728, "Top": 0.4711654484272003}, "Polygon": [{"X": 0.6697152853012085, "Y": 0.4711654484272003}, {"X": 0.8261489272117615, "Y": 0.4712064266204834}, {"X": 0.8261425495147705, "Y": 0.49430960416793823}, {"X": 0.6697088479995728, "Y": 0.49426862597465515}]}, "Id": "5bdece69-0dac-4d5b-a28a-f694fa5b36f5", "Relationships": [{"Type": "CHILD", "Ids": ["e7acd53d-618c-4bf0-99ff-d842d2524d28", "af807af9-2ebf-4c85-a75c-f7ffc0b3b850", "d6062ee8-ce13-4246-b3fd-35ce87ec3e70"]}]}, {"BlockType": "LINE", "Confidence": 99.99755859375, "Text": "(Rack checks will now be sent to the", "Geometry": {"BoundingBox": {"Width": 0.28916338086128235, "Height": 0.01408664882183075, "Left": 0.5677276849746704, "Top": 0.4976227879524231}, "Polygon": [{"X": 0.5677316188812256, "Y": 0.4976227879524231}, {"X": 0.8568910956382751, "Y": 0.4976985454559326}, {"X": 0.8568872213363647, "Y": 0.511709451675415}, {"X": 0.5677276849746704, "Y": 0.5116336345672607}]}, "Id": "db28ce5d-8988-40a1-9384-a13c93e934be", "Relationships": [{"Type": "CHILD", "Ids": ["1f51d5c6-4640-40a1-bda8-9675e663545c", "af025e05-c29d-4cd1-a652-0edc9eefd1cb", "950f4fb6-920c-45db-ab5f-e98e678bfa2c", "6c04536c-b68d-413d-8a7c-d6920f93566d", "ff2f5dca-f289-444a-a435-3544975fd40c", "df0047a4-647c-4445-85c8-0a62ab90c92f", "2c54d527-f47f-40d9-bb35-2b377196a966", "b550c3f9-8dfc-4010-babc-22142d4e07dd"]}]}, {"BlockType": "LINE", "Confidence": 99.87065887451172, "Text": "broker.)", "Geometry": {"BoundingBox": {"Width": 0.06206182762980461, "Height": 0.015006967820227146, "Left": 0.5781733989715576, "Top": 0.5148704648017883}, "Polygon": [{"X": 0.5781776309013367, "Y": 0.5148704648017883}, {"X": 0.6402352452278137, "Y": 0.514886736869812}, {"X": 0.6402310729026794, "Y": 0.5298774242401123}, {"X": 0.5781733989715576, "Y": 0.5298612117767334}]}, "Id": "9fa9628d-ce0e-49b7-a001-f5f1f8b3376a", "Relationships": [{"Type": "CHILD", "Ids": ["4752d90b-c18e-427e-815b-55f935254ed2"]}]}, {"BlockType": "LINE", "Confidence": 90.56237030029297, "Text": "Net Cost $ 4,097.-", "Geometry": {"BoundingBox": {"Width": 0.23499220609664917, "Height": 0.035291001200675964, "Left": 0.5665826797485352, "Top": 0.5381578803062439}, "Polygon": [{"X": 0.5665925145149231, "Y": 0.5381578803062439}, {"X": 0.8015748858451843, "Y": 0.5382195115089417}, {"X": 0.8015651106834412, "Y": 0.573448896408081}, {"X": 0.5665826797485352, "Y": 0.5733872652053833}]}, "Id": "9dd70c02-46c4-43d4-ac59-027d19b937a9", "Relationships": [{"Type": "CHILD", "Ids": ["58fa3239-402c-4091-a97d-d39824d2631f", "be77179d-1d6b-4688-8d7c-d5156d9b7248", "6aa04d9b-33fe-4898-9f1f-255542254688", "6e77d8e1-7c9d-414e-a1a6-ad6fa9e101a0"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Billable Shortages:", "Geometry": {"BoundingBox": {"Width": 0.2496323138475418, "Height": 0.025116505101323128, "Left": 0.10404948145151138, "Top": 0.5826233625411987}, "Polygon": [{"X": 0.10405661165714264, "Y": 0.5826233625411987}, {"X": 0.3536818027496338, "Y": 0.5826888084411621}, {"X": 0.3536747395992279, "Y": 0.6077398657798767}, {"X": 0.10404948145151138, "Y": 0.6076743602752686}]}, "Id": "cbdf5981-64de-4636-9b2f-993ff0415749", "Relationships": [{"Type": "CHILD", "Ids": ["db50ab32-c924-4355-a3f2-7c973c26fd4b", "341cdaa4-8069-4ff5-be37-40ff8edd8a06"]}]}, {"BlockType": "LINE", "Confidence": 99.54607391357422, "Text": "(equipment missing with insufficient documentation)", "Geometry": {"BoundingBox": {"Width": 0.27839750051498413, "Height": 0.012545272707939148, "Left": 0.10453862696886063, "Top": 0.6074943542480469}, "Polygon": [{"X": 0.10454218089580536, "Y": 0.6074943542480469}, {"X": 0.38293612003326416, "Y": 0.6075673699378967}, {"X": 0.3829326033592224, "Y": 0.6200396418571472}, {"X": 0.10453862696886063, "Y": 0.6199666261672974}]}, "Id": "be8cf8b7-9f9f-4402-8eae-714d5b8a7a29", "Relationships": [{"Type": "CHILD", "Ids": ["a95b53ac-4fdf-4aa6-acee-a4385fb58dda", "d0535489-5dbb-4f94-8253-eaf936c16d6a", "5fc13b2a-4751-40fb-b927-0395fcc35614", "f34c6235-5587-46ff-b78a-160795ae3729", "1aed3792-3570-40de-8ae0-f5724256ceca"]}]}, {"BlockType": "LINE", "Confidence": 92.26451873779297, "Text": "Racks: ( ) X $250.00 = $", "Geometry": {"BoundingBox": {"Width": 0.19929997622966766, "Height": 0.014706346206367016, "Left": 0.1041300818324089, "Top": 0.6321006417274475}, "Polygon": [{"X": 0.10413425415754318, "Y": 0.6321006417274475}, {"X": 0.30343005061149597, "Y": 0.6321529150009155}, {"X": 0.3034259080886841, "Y": 0.6468069553375244}, {"X": 0.1041300818324089, "Y": 0.6467546820640564}]}, "Id": "43f4681f-96ef-4508-9384-24ac001ce0ec", "Relationships": [{"Type": "CHILD", "Ids": ["cce5ab2b-4706-440b-a706-8529fb101794", "4d8f5b9b-140e-45c4-8496-999e21d6ffe9", "3db861c2-7398-4c65-aca5-fcf1463cbc7f", "91d09997-2111-42cb-a615-836b326d213a", "59d865f9-b13c-4b7f-8a30-22c40c844932", "36f45460-f5b3-426c-96ce-08a4c3e9274d", "f5cbb701-d5c6-4199-b6dd-276e6dbc572a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total Deductions", "Geometry": {"BoundingBox": {"Width": 0.13698498904705048, "Height": 0.012465212494134903, "Left": 0.5853772759437561, "Top": 0.6367524862289429}, "Polygon": [{"X": 0.5853807330131531, "Y": 0.6367524862289429}, {"X": 0.7223622798919678, "Y": 0.6367884278297424}, {"X": 0.7223588228225708, "Y": 0.6492177248001099}, {"X": 0.5853772759437561, "Y": 0.6491817831993103}]}, "Id": "968990d1-fcde-4b19-aae2-eaa6d5ba73ce", "Relationships": [{"Type": "CHILD", "Ids": ["7ab18753-ad0e-494e-830a-0c9869b44f2b", "817b987a-74b7-4705-9ab7-8f14358aa085"]}]}, {"BlockType": "LINE", "Confidence": 99.97030639648438, "Text": "For Load:", "Geometry": {"BoundingBox": {"Width": 0.07690969854593277, "Height": 0.011993291787803173, "Left": 0.5862170457839966, "Top": 0.654009997844696}, "Polygon": [{"X": 0.586220383644104, "Y": 0.654009997844696}, {"X": 0.6631267666816711, "Y": 0.6540302038192749}, {"X": 0.6631234288215637, "Y": 0.6660032868385315}, {"X": 0.5862170457839966, "Y": 0.6659830808639526}]}, "Id": "d453cf67-ac7f-4b5d-9677-00ef464e03f1", "Relationships": [{"Type": "CHILD", "Ids": ["3dddc731-847b-4b34-b751-06a1d7116196", "84816812-c2b4-417e-80f8-cb05d6654e34"]}]}, {"BlockType": "LINE", "Confidence": 91.26171112060547, "Text": "Load Locks: ( ) x $25.00 = $", "Geometry": {"BoundingBox": {"Width": 0.23503397405147552, "Height": 0.01545834168791771, "Left": 0.10372798144817352, "Top": 0.6668969392776489}, "Polygon": [{"X": 0.10373236984014511, "Y": 0.6668969392776489}, {"X": 0.33876195549964905, "Y": 0.6669585704803467}, {"X": 0.33875760436058044, "Y": 0.6823552846908569}, {"X": 0.10372798144817352, "Y": 0.6822935938835144}]}, "Id": "63d8b656-37c2-4cf9-98c7-f709ea8c1726", "Relationships": [{"Type": "CHILD", "Ids": ["f403e91e-84ba-4a71-b707-8633c5b29d6c", "213761c9-7a6a-4bb8-8296-1b2d098d0ac1", "0f537d5d-df2c-4af0-bf35-4ee525aa17b4", "1a48fe62-3b0d-413c-8ee7-25ed2b484924", "ba6d9ec2-dcdb-461f-b0b7-d3ca4b2b360f", "dbad3cb8-4d24-4415-91b5-59af0305c0d7", "f45d5da7-dfab-4cb3-90c0-40f7fbec12be"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "$", "Geometry": {"BoundingBox": {"Width": 0.008831953629851341, "Height": 0.012972169555723667, "Left": 0.5860373973846436, "Top": 0.6883605122566223}, "Polygon": [{"X": 0.5860409736633301, "Y": 0.6883605122566223}, {"X": 0.5948693156242371, "Y": 0.6883627772331238}, {"X": 0.5948656797409058, "Y": 0.7013326287269592}, {"X": 0.5860373973846436, "Y": 0.7013303637504578}]}, "Id": "a8a70c70-8288-4178-9c42-7bccf003ac53", "Relationships": [{"Type": "CHILD", "Ids": ["e45a0fc6-42a0-4a29-a427-c92c75c80bc0"]}]}, {"BlockType": "LINE", "Confidence": 92.7842025756836, "Text": "Pallet Jack: ( ) X $500.00 = $", "Geometry": {"BoundingBox": {"Width": 0.23608531057834625, "Height": 0.015229097567498684, "Left": 0.10328096896409988, "Top": 0.7013720870018005}, "Polygon": [{"X": 0.1032852903008461, "Y": 0.7013720870018005}, {"X": 0.33936628699302673, "Y": 0.7014340758323669}, {"X": 0.3393619954586029, "Y": 0.7166011929512024}, {"X": 0.10328096896409988, "Y": 0.716539204120636}]}, "Id": "2d8597cd-02ab-47a5-96cc-8a0f5f9b143a", "Relationships": [{"Type": "CHILD", "Ids": ["a1781956-eeb1-41c6-8137-8244c8cac7f0", "d7c2319e-1ff9-4054-a78d-970c46ab4a72", "2b3e7096-8c54-4940-a162-a40138ec42c4", "3a626c55-2bab-4c5e-b171-d81dd42c8c9e", "06802c8b-0b02-4eef-b9aa-2376fbd2da3e", "6f7ec002-8a78-4b1e-8c96-3278887b5c9c", "097d5453-6f39-40ac-b3f6-635f951de560"]}]}, {"BlockType": "LINE", "Confidence": 92.76622009277344, "Text": "Load Straps: ( ) X $25.00 = $", "Geometry": {"BoundingBox": {"Width": 0.23637445271015167, "Height": 0.015314376913011074, "Left": 0.10267525911331177, "Top": 0.7362170219421387}, "Polygon": [{"X": 0.10267960280179977, "Y": 0.7362170219421387}, {"X": 0.33904972672462463, "Y": 0.7362790703773499}, {"X": 0.3390454053878784, "Y": 0.7515314221382141}, {"X": 0.10267525911331177, "Y": 0.7514693140983582}]}, "Id": "38645989-cf6f-4134-b13d-18394c873665", "Relationships": [{"Type": "CHILD", "Ids": ["d3c19a05-3c78-46e5-8b7b-5adfe089a48f", "3e56a551-850f-4922-b037-4799fe68cfad", "e0c97a2a-8906-465f-8726-d8c016d6d04f", "7fedcb04-83bf-47bf-b0b6-155858f1e3c7", "47937ee4-2f54-4c2f-ac29-80cd48faa8e8", "9d48eabc-73c3-493e-a806-e47fa825414c", "d773db84-fbea-43d1-a160-09d4d3bf022b"]}]}, {"BlockType": "LINE", "Confidence": 99.88623809814453, "Text": "Paperwork & other Fines $", "Geometry": {"BoundingBox": {"Width": 0.21184882521629333, "Height": 0.014294496737420559, "Left": 0.10250668227672577, "Top": 0.7712881565093994}, "Polygon": [{"X": 0.1025107353925705, "Y": 0.7712881565093994}, {"X": 0.3143554925918579, "Y": 0.7713437676429749}, {"X": 0.31435146927833557, "Y": 0.7855826616287231}, {"X": 0.10250668227672577, "Y": 0.7855269908905029}]}, "Id": "bf02b2eb-31cc-4c96-ac46-c636254666f1", "Relationships": [{"Type": "CHILD", "Ids": ["fa5133b1-b6e7-470e-be27-3b697b7845f5", "f15e1c02-d5fc-44ef-a052-5d9564f31c3a", "597fc4a7-ef5e-4b39-9adb-b82078b77412", "20a7f1e3-6bfa-443e-954a-321df15d2890", "4415757f-9c54-489e-a69a-4490e496dec0"]}]}, {"BlockType": "LINE", "Confidence": 99.43870544433594, "Text": "(see attached documentation)", "Geometry": {"BoundingBox": {"Width": 0.22920021414756775, "Height": 0.015359519980847836, "Left": 0.10282855480909348, "Top": 0.7885953187942505}, "Polygon": [{"X": 0.10283291339874268, "Y": 0.7885953187942505}, {"X": 0.3320287764072418, "Y": 0.7886555194854736}, {"X": 0.3320244550704956, "Y": 0.8039548397064209}, {"X": 0.10282855480909348, "Y": 0.8038946390151978}]}, "Id": "b9601c32-0d71-48e9-9446-af673e2a79e0", "Relationships": [{"Type": "CHILD", "Ids": ["a93466c9-446c-4ebc-8580-eceef6ec810e", "0a3f1ddd-2b35-43b7-a8e0-cf068d109a10", "fcdf8b1b-0a6b-4546-bb78-54286688fccd"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Manager A<PERSON><PERSON><PERSON>:", "Geometry": {"BoundingBox": {"Width": 0.15386271476745605, "Height": 0.01543273776769638, "Left": 0.5294902324676514, "Top": 0.7926991581916809}, "Polygon": [{"X": 0.5294945240020752, "Y": 0.7926991581916809}, {"X": 0.6833529472351074, "Y": 0.7927395701408386}, {"X": 0.6833486557006836, "Y": 0.8081318736076355}, {"X": 0.5294902324676514, "Y": 0.8080914616584778}]}, "Id": "d5a84f88-df90-4d1e-8ae5-876df7429929", "Relationships": [{"Type": "CHILD", "Ids": ["e11ca6a8-ba6f-4545-9fdc-9290b898bc14", "3397d57d-8210-43fd-9bb2-c1230624aaec"]}]}, {"BlockType": "LINE", "Confidence": 93.8526382446289, "Text": "my", "Geometry": {"BoundingBox": {"Width": 0.09537752717733383, "Height": 0.07414773851633072, "Left": 0.7558115124702454, "Top": 0.7662332057952881}, "Polygon": [{"X": 0.7558320760726929, "Y": 0.7662332057952881}, {"X": 0.8511890172958374, "Y": 0.7662582397460938}, {"X": 0.8511685132980347, "Y": 0.8403809666633606}, {"X": 0.7558115124702454, "Y": 0.8403558731079102}]}, "Id": "234bb397-2cf8-4017-8120-f6a61a07b7ea", "Relationships": [{"Type": "CHILD", "Ids": ["93cc5a9a-2a59-4f9b-b51f-6e43e903df7f"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "Notes:", "Geometry": {"BoundingBox": {"Width": 0.050074320286512375, "Height": 0.011840011924505234, "Left": 0.10143722593784332, "Top": 0.8229336738586426}, "Polygon": [{"X": 0.10144059360027313, "Y": 0.8229336738586426}, {"X": 0.151511549949646, "Y": 0.8229467868804932}, {"X": 0.1515081822872162, "Y": 0.8347736597061157}, {"X": 0.10143722593784332, "Y": 0.8347604870796204}]}, "Id": "c50e791e-bdfb-4349-a269-8c42e731d307", "Relationships": [{"Type": "CHILD", "Ids": ["a4bea9d8-d4db-4c15-aa4d-9eba3f056b0f"]}]}, {"BlockType": "LINE", "Confidence": 99.79973602294922, "Text": "Any fine not appealed within 3 business days is final. Appeals may be made in writing and faxed to (616) 669-7468", "Geometry": {"BoundingBox": {"Width": 0.7608051300048828, "Height": 0.01820564456284046, "Left": 0.1012035459280014, "Top": 0.874683678150177}, "Polygon": [{"X": 0.10120867192745209, "Y": 0.874683678150177}, {"X": 0.8620086312294006, "Y": 0.8748836517333984}, {"X": 0.8620036840438843, "Y": 0.8928893208503723}, {"X": 0.1012035459280014, "Y": 0.8926892876625061}]}, "Id": "54db8215-8376-4408-a194-ccb48e05b76f", "Relationships": [{"Type": "CHILD", "Ids": ["337b9deb-92c3-4638-b408-b6eaa521bcb5", "a2f559b1-5d66-4122-9d93-594d203189a7", "afb74cd9-8cf1-4a84-944b-213c44a2f492", "c1671e37-3af5-4a42-8857-4ff708220080", "68d0b959-15e6-4bc7-b708-1d61de08ae58", "e0fe81c6-1302-429d-8ba8-9377b7b958ef", "b8a9ab3f-c26f-473b-835c-354aead93d98", "42ce396c-2fdf-476b-bd25-fa23adfde9d3", "066a139b-dc96-4249-8763-c75e668625d8", "02006bbc-c5e3-4007-a15b-82154895b1d7", "cd59abc7-4658-4c60-be8b-8156da90692c", "67664af4-300f-40ac-8e53-7eaab524f84e", "3089c89b-7c80-4c82-b29d-098d6ce4ebf3", "673459d5-238d-4d03-a668-557cde2f25e4", "d5badeb9-995b-43fb-b98f-1961ad0871c4", "f472b173-16fa-4744-84d3-47412cba51d4", "b21aed9d-51a1-4cef-a5f8-2672a3a3e37d", "6bdc37d4-e4d1-4b30-b80e-9cda83e44f98", "158dc3d4-e0f5-4c9f-8b92-1231b3f6aed1", "6b9ea90c-1bd2-4815-bac8-1d93530162c3", "a171ba5c-9f60-4f73-a590-de4c2be4fdf7"]}]}, {"BlockType": "WORD", "Confidence": 99.79412841796875, "Text": "Revised", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.12010426074266434, "Height": 0.06455104053020477, "Left": 0.3497428297996521, "Top": 0.030088284984230995}, "Polygon": [{"X": 0.349761039018631, "Y": 0.030088284984230995}, {"X": 0.4698471128940582, "Y": 0.03011963702738285}, {"X": 0.4698289930820465, "Y": 0.09463932365179062}, {"X": 0.3497428297996521, "Y": 0.09460795670747757}]}, "Id": "00948e61-43e0-4a7e-adaa-512f7f0a9443"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "SAW-7116716", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15808148682117462, "Height": 0.014960047788918018, "Left": 0.08656389266252518, "Top": 0.07972919195890427}, "Polygon": [{"X": 0.08656814694404602, "Y": 0.07972919195890427}, {"X": 0.2446453869342804, "Y": 0.07977047562599182}, {"X": 0.24464115500450134, "Y": 0.09468923509120941}, {"X": 0.08656389266252518, "Y": 0.09464794397354126}]}, "Id": "f6f71743-0cf1-437e-bc19-88a137179555"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "REVISED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10527349263429642, "Height": 0.014873035252094269, "Left": 0.11285440623760223, "Top": 0.10755269229412079}, "Polygon": [{"X": 0.11285863071680069, "Y": 0.10755269229412079}, {"X": 0.21812790632247925, "Y": 0.10758019238710403}, {"X": 0.2181236892938614, "Y": 0.12242572009563446}, {"X": 0.11285440623760223, "Y": 0.12239822000265121}]}, "Id": "d773fc19-bf51-47b4-8280-bbb5429e3486"}, {"BlockType": "WORD", "Confidence": 23.563457489013672, "Text": "AMBVB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22393830120563507, "Height": 0.0347960889339447, "Left": 0.3939111530780792, "Top": 0.13196684420108795}, "Polygon": [{"X": 0.3939209282398224, "Y": 0.13196684420108795}, {"X": 0.6178494691848755, "Y": 0.1320253610610962}, {"X": 0.6178397536277771, "Y": 0.16676294803619385}, {"X": 0.3939111530780792, "Y": 0.16670441627502441}]}, "Id": "7c72551d-8e4c-4cf8-96c0-882dbe7ec54b"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "SAWYER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.18482044339179993, "Height": 0.023052750155329704, "Left": 0.29145434498786926, "Top": 0.1801413595676422}, "Polygon": [{"X": 0.29146087169647217, "Y": 0.1801413595676422}, {"X": 0.4762748181819916, "Y": 0.18018966913223267}, {"X": 0.47626835107803345, "Y": 0.20319411158561707}, {"X": 0.29145434498786926, "Y": 0.20314578711986542}]}, "Id": "5810a64f-6489-4ebe-8fec-f2f38447e76e"}, {"BlockType": "WORD", "Confidence": 83.96743774414062, "Text": "NURSERY.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.21887606382369995, "Height": 0.02354927733540535, "Left": 0.501028299331665, "Top": 0.18237467110157013}, "Polygon": [{"X": 0.5010348558425903, "Y": 0.18237467110157013}, {"X": 0.719904363155365, "Y": 0.1824318766593933}, {"X": 0.7198978066444397, "Y": 0.20592394471168518}, {"X": 0.501028299331665, "Y": 0.2058667242527008}]}, "Id": "b0cc7d9a-c098-45b8-89e4-158ea18276c6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truckload", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13482268154621124, "Height": 0.019602399319410324, "Left": 0.342696875333786, "Top": 0.21328970789909363}, "Polygon": [{"X": 0.34270238876342773, "Y": 0.21328970789909363}, {"X": 0.47751954197883606, "Y": 0.21332494914531708}, {"X": 0.4775140583515167, "Y": 0.23289209604263306}, {"X": 0.342696875333786, "Y": 0.2328568398952484}]}, "Id": "0c59b4ed-324d-4797-be48-701d4ce7cac7"}, {"BlockType": "WORD", "Confidence": 99.72576141357422, "Text": "Check-in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12005899101495743, "Height": 0.019556405022740364, "Left": 0.4864963889122009, "Top": 0.21481220424175262}, "Polygon": [{"X": 0.48650187253952026, "Y": 0.21481220424175262}, {"X": 0.6065554022789001, "Y": 0.21484360098838806}, {"X": 0.6065499186515808, "Y": 0.23436860740184784}, {"X": 0.4864963889122009, "Y": 0.2343372106552124}]}, "Id": "a3004e8d-3d60-4366-94f5-468d25933d52"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Form", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0709509626030922, "Height": 0.018474437296390533, "Left": 0.6145465970039368, "Top": 0.21658343076705933}, "Polygon": [{"X": 0.6145517230033875, "Y": 0.21658343076705933}, {"X": 0.6854975819587708, "Y": 0.21660196781158447}, {"X": 0.6854923963546753, "Y": 0.23505786061286926}, {"X": 0.6145465970039368, "Y": 0.23503930866718292}]}, "Id": "2a5f90b4-bdd9-4a43-ad38-41314c053076"}, {"BlockType": "WORD", "Confidence": 99.79412841796875, "Text": "Clear-to-Pay", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16744175553321838, "Height": 0.02551879547536373, "Left": 0.36958086490631104, "Top": 0.24223844707012177}, "Polygon": [{"X": 0.36958804726600647, "Y": 0.24223844707012177}, {"X": 0.5370226502418518, "Y": 0.2422822266817093}, {"X": 0.5370154976844788, "Y": 0.26775723695755005}, {"X": 0.36958086490631104, "Y": 0.26771342754364014}]}, "Id": "8ecc3589-7912-4355-8bb0-5f415dca8ff4"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Voucher", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11382885277271271, "Height": 0.019818808883428574, "Left": 0.5446425080299377, "Top": 0.24437570571899414}, "Polygon": [{"X": 0.5446480512619019, "Y": 0.24437570571899414}, {"X": 0.6584713459014893, "Y": 0.24440547823905945}, {"X": 0.6584658622741699, "Y": 0.264194518327713}, {"X": 0.5446425080299377, "Y": 0.2641647458076477}]}, "Id": "cb1e6f1d-cd6a-4455-bcf6-d0b5a42a33bb"}, {"BlockType": "WORD", "Confidence": 99.833984375, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04008045792579651, "Height": 0.011653704568743706, "Left": 0.10787538439035416, "Top": 0.283732146024704}, "Polygon": [{"X": 0.10787869989871979, "Y": 0.283732146024704}, {"X": 0.14795583486557007, "Y": 0.28374263644218445}, {"X": 0.14795252680778503, "Y": 0.29538583755493164}, {"X": 0.10787538439035416, "Y": 0.29537537693977356}]}, "Id": "21fe2527-5fe8-4fc5-b9ad-820369d4b73b"}, {"BlockType": "WORD", "Confidence": 88.98845672607422, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01040648017078638, "Height": 0.01139341201633215, "Left": 0.1525583416223526, "Top": 0.2839999794960022}, "Polygon": [{"X": 0.15256157517433167, "Y": 0.2839999794960022}, {"X": 0.1629648208618164, "Y": 0.2840026915073395}, {"X": 0.16296158730983734, "Y": 0.2953933775424957}, {"X": 0.1525583416223526, "Y": 0.29539066553115845}]}, "Id": "417ed8d4-6ed2-4a94-b9b0-3bf49118aca4"}, {"BlockType": "WORD", "Confidence": 63.26052474975586, "Text": "20196", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.12223389744758606, "Height": 0.02427937462925911, "Left": 0.15636205673217773, "Top": 0.2760412395000458}, "Polygon": [{"X": 0.1563689410686493, "Y": 0.2760412395000458}, {"X": 0.2785959541797638, "Y": 0.2760732173919678}, {"X": 0.2785890996456146, "Y": 0.3003206253051758}, {"X": 0.15636205673217773, "Y": 0.3002886474132538}]}, "Id": "1a020c62-c962-48d3-a0c5-311968549ba3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Broker", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0553869754076004, "Height": 0.011374310590326786, "Left": 0.5570418238639832, "Top": 0.28892144560813904}, "Polygon": [{"X": 0.5570449829101562, "Y": 0.28892144560813904}, {"X": 0.6124287843704224, "Y": 0.28893592953681946}, {"X": 0.6124256253242493, "Y": 0.30029574036598206}, {"X": 0.5570418238639832, "Y": 0.30028125643730164}]}, "Id": "e3cad528-3667-4066-8e54-cb2cc1ec0657"}, {"BlockType": "WORD", "Confidence": 98.77650451660156, "Text": "PF2", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09524461627006531, "Height": 0.03364512696862221, "Left": 0.6737791299819946, "Top": 0.2739748954772949}, "Polygon": [{"X": 0.6737884879112244, "Y": 0.2739748954772949}, {"X": 0.7690237760543823, "Y": 0.27399981021881104}, {"X": 0.7690144181251526, "Y": 0.30762001872062683}, {"X": 0.6737791299819946, "Y": 0.3075951039791107}]}, "Id": "25cffb86-d719-49f1-9d5f-4b2a2e39312f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03677667677402496, "Height": 0.011300924234092236, "Left": 0.10721205919981003, "Top": 0.33602771162986755}, "Polygon": [{"X": 0.1072152704000473, "Y": 0.33602771162986755}, {"X": 0.1439887285232544, "Y": 0.3360373377799988}, {"X": 0.14398552477359772, "Y": 0.34732863306999207}, {"X": 0.10721205919981003, "Y": 0.34731900691986084}]}, "Id": "ede0aee9-41e8-4eb9-92ea-1b5657f312e8"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "Processed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07879949361085892, "Height": 0.01179544534534216, "Left": 0.14870893955230713, "Top": 0.3364959955215454}, "Polygon": [{"X": 0.14871229231357574, "Y": 0.3364959955215454}, {"X": 0.22750844061374664, "Y": 0.3365166187286377}, {"X": 0.22750510275363922, "Y": 0.34829142689704895}, {"X": 0.14870893955230713, "Y": 0.34827080368995667}]}, "Id": "a1e4dda7-6b1e-4db0-82e7-383851ea1bb5"}, {"BlockType": "WORD", "Confidence": 99.392333984375, "Text": "6/12/2023", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.17054027318954468, "Height": 0.03023110330104828, "Left": 0.22861416637897491, "Top": 0.32830265164375305}, "Polygon": [{"X": 0.22862271964550018, "Y": 0.32830265164375305}, {"X": 0.3991544246673584, "Y": 0.3283472955226898}, {"X": 0.3991459310054779, "Y": 0.3585337698459625}, {"X": 0.22861416637897491, "Y": 0.35848912596702576}]}, "Id": "7409b16c-5132-4e2b-8b13-b6f42cb8edf4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04002326726913452, "Height": 0.01123094279319048, "Left": 0.5601390600204468, "Top": 0.34115147590637207}, "Polygon": [{"X": 0.5601421594619751, "Y": 0.34115147590637207}, {"X": 0.6001623272895813, "Y": 0.34116196632385254}, {"X": 0.6001591682434082, "Y": 0.3523824214935303}, {"X": 0.5601390600204468, "Y": 0.3523719608783722}]}, "Id": "2800a181-15cf-43aa-ad82-55cf6c6f4a37"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0361410528421402, "Height": 0.01116812415421009, "Left": 0.606029212474823, "Top": 0.3416281044483185}, "Polygon": [{"X": 0.6060323119163513, "Y": 0.3416281044483185}, {"X": 0.642170250415802, "Y": 0.3416375517845154}, {"X": 0.6421671509742737, "Y": 0.3527962267398834}, {"X": 0.606029212474823, "Y": 0.35278674960136414}]}, "Id": "52439a4f-734b-4e42-adf3-9a69bb951ca3"}, {"BlockType": "WORD", "Confidence": 93.54671478271484, "Text": "$3,869.-", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.15141405165195465, "Height": 0.027622375637292862, "Left": 0.6721581816673279, "Top": 0.3361436128616333}, "Polygon": [{"X": 0.6721658706665039, "Y": 0.3361436128616333}, {"X": 0.8235722184181213, "Y": 0.33618322014808655}, {"X": 0.8235645890235901, "Y": 0.36376598477363586}, {"X": 0.6721581816673279, "Y": 0.36372634768486023}]}, "Id": "f72855a1-269f-425b-be63-209d62bfad84"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04748377576470375, "Height": 0.011517447419464588, "Left": 0.11193759739398956, "Top": 0.37081378698349}, "Polygon": [{"X": 0.11194086819887161, "Y": 0.37081378698349}, {"X": 0.15942136943340302, "Y": 0.37082621455192566}, {"X": 0.15941810607910156, "Y": 0.3823312520980835}, {"X": 0.11193759739398956, "Y": 0.38231879472732544}]}, "Id": "ea240ced-62be-4e81-90eb-e908077074cb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Returned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07146509736776352, "Height": 0.011675632558763027, "Left": 0.1646483689546585, "Top": 0.37137553095817566}, "Polygon": [{"X": 0.16465167701244354, "Y": 0.37137553095817566}, {"X": 0.23611345887184143, "Y": 0.37139421701431274}, {"X": 0.2361101657152176, "Y": 0.38305115699768066}, {"X": 0.1646483689546585, "Y": 0.3830324411392212}]}, "Id": "58f9a9fc-ad1a-45bd-903f-48e0d8960d72"}, {"BlockType": "WORD", "Confidence": 64.2799301147461, "Text": "5.7", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.041299495846033096, "Height": 0.025344818830490112, "Left": 0.2973567247390747, "Top": 0.36436423659324646}, "Polygon": [{"X": 0.29736387729644775, "Y": 0.36436423659324646}, {"X": 0.3386562168598175, "Y": 0.3643750250339508}, {"X": 0.33864909410476685, "Y": 0.3897090554237366}, {"X": 0.2973567247390747, "Y": 0.38969823718070984}]}, "Id": "5a2e2813-3ff3-415c-9f57-36b00edb0b35"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Surcharge", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0792873427271843, "Height": 0.014892049133777618, "Left": 0.5651573538780212, "Top": 0.39278605580329895}, "Polygon": [{"X": 0.5651615262031555, "Y": 0.39278605580329895}, {"X": 0.6444447040557861, "Y": 0.3928067982196808}, {"X": 0.6444405317306519, "Y": 0.40767809748649597}, {"X": 0.5651573538780212, "Y": 0.40765735507011414}]}, "Id": "e69d17ca-5b9b-4e36-b8dc-5489eff856e1"}, {"BlockType": "WORD", "Confidence": 99.42223358154297, "Text": "+", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01089914608746767, "Height": 0.00858529657125473, "Left": 0.6544398069381714, "Top": 0.3951205313205719}, "Polygon": [{"X": 0.6544421911239624, "Y": 0.3951205313205719}, {"X": 0.665338933467865, "Y": 0.3951233923435211}, {"X": 0.665336549282074, "Y": 0.4037058353424072}, {"X": 0.6544398069381714, "Y": 0.403702974319458}]}, "Id": "2cb34ec7-0c1a-41dd-8a6a-0c9d3da70582"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008997260592877865, "Height": 0.013065077364444733, "Left": 0.6755985617637634, "Top": 0.39330780506134033}, "Polygon": [{"X": 0.6756021976470947, "Y": 0.39330780506134033}, {"X": 0.6845958232879639, "Y": 0.39331015944480896}, {"X": 0.6845921874046326, "Y": 0.40637290477752686}, {"X": 0.6755985617637634, "Y": 0.4063705503940582}]}, "Id": "12bb2fb9-a26d-4473-b5fe-27981a557a1a"}, {"BlockType": "WORD", "Confidence": 65.6993408203125, "Text": "-", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.02328779175877571, "Height": 0.003738170489668846, "Left": 0.7546005249023438, "Top": 0.39373376965522766}, "Polygon": [{"X": 0.7546015381813049, "Y": 0.39373376965522766}, {"X": 0.777888298034668, "Y": 0.39373987913131714}, {"X": 0.7778872847557068, "Y": 0.39747193455696106}, {"X": 0.7546005249023438, "Y": 0.39746585488319397}]}, "Id": "d1211597-92f1-45ee-8287-cf6f5cc09e52"}, {"BlockType": "WORD", "Confidence": 58.209503173828125, "Text": "-", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.023595619946718216, "Height": 0.002982028527185321, "Left": 0.7594481110572815, "Top": 0.4303312301635742}, "Polygon": [{"X": 0.7594488859176636, "Y": 0.4303312301635742}, {"X": 0.7830436825752258, "Y": 0.43033742904663086}, {"X": 0.783042848110199, "Y": 0.433313250541687}, {"X": 0.7594481110572815, "Y": 0.43330708146095276}]}, "Id": "ddb86e07-4fe4-4809-a5ce-2f1da9452a8d"}, {"BlockType": "WORD", "Confidence": 98.7412338256836, "Text": "57", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.04895998165011406, "Height": 0.026760580018162727, "Left": 0.12549912929534912, "Top": 0.4300733804702759}, "Polygon": [{"X": 0.12550672888755798, "Y": 0.4300733804702759}, {"X": 0.17445911467075348, "Y": 0.4300861954689026}, {"X": 0.17445151507854462, "Y": 0.45683395862579346}, {"X": 0.12549912929534912, "Y": 0.45682114362716675}]}, "Id": "cdd1c443-d32f-495b-899a-257fb4c769bf"}, {"BlockType": "WORD", "Confidence": 93.23568725585938, "Text": "X$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02852391079068184, "Height": 0.013122919015586376, "Left": 0.18932393193244934, "Top": 0.44138863682746887}, "Polygon": [{"X": 0.1893276572227478, "Y": 0.44138863682746887}, {"X": 0.21784783899784088, "Y": 0.4413960874080658}, {"X": 0.2178441286087036, "Y": 0.4545115530490875}, {"X": 0.18932393193244934, "Y": 0.4545040726661682}]}, "Id": "59a527b4-c661-4329-b787-44bfcaaceed9"}, {"BlockType": "WORD", "Confidence": 70.68717956542969, "Text": "4, -", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05297304689884186, "Height": 0.0258595310151577, "Left": 0.233659029006958, "Top": 0.42878809571266174}, "Polygon": [{"X": 0.2336663454771042, "Y": 0.42878809571266174}, {"X": 0.28663206100463867, "Y": 0.4288019835948944}, {"X": 0.2866247594356537, "Y": 0.45464763045310974}, {"X": 0.233659029006958, "Y": 0.45463377237319946}]}, "Id": "3782acb4-458f-4fbd-b155-2de1264fc010"}, {"BlockType": "WORD", "Confidence": 97.23552703857422, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011345786042511463, "Height": 0.0037931855767965317, "Left": 0.3314719498157501, "Top": 0.4467155337333679}, "Polygon": [{"X": 0.3314730226993561, "Y": 0.4467155337333679}, {"X": 0.3428177535533905, "Y": 0.4467185139656067}, {"X": 0.34281668066978455, "Y": 0.4505087435245514}, {"X": 0.3314719498157501, "Y": 0.4505057632923126}]}, "Id": "2602f040-fad8-4c3e-a441-50163f6b4806"}, {"BlockType": "WORD", "Confidence": 90.29296875, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008453416638076305, "Height": 0.012880220077931881, "Left": 0.3480769693851471, "Top": 0.44281381368637085}, "Polygon": [{"X": 0.3480806052684784, "Y": 0.44281381368637085}, {"X": 0.35653039813041687, "Y": 0.44281601905822754}, {"X": 0.35652676224708557, "Y": 0.4556940197944641}, {"X": 0.3480769693851471, "Y": 0.4556918144226074}]}, "Id": "938433b5-a794-46f8-8ebd-e324dd24c373"}, {"BlockType": "WORD", "Confidence": 45.7655029296875, "Text": "228,", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10824482142925262, "Height": 0.0257553793489933, "Left": 0.3527742922306061, "Top": 0.4334448575973511}, "Polygon": [{"X": 0.3527815341949463, "Y": 0.4334448575973511}, {"X": 0.4610190987586975, "Y": 0.4334731996059418}, {"X": 0.4610118865966797, "Y": 0.4592002332210541}, {"X": 0.3527742922306061, "Y": 0.4591718912124634}]}, "Id": "5fc8f285-8148-4c7e-a975-0e4cc946c254"}, {"BlockType": "WORD", "Confidence": 95.04763793945312, "Text": "Deductions -", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10478176176548004, "Height": 0.012287451885640621, "Left": 0.5643483996391296, "Top": 0.4280858039855957}, "Polygon": [{"X": 0.5643518567085266, "Y": 0.4280858039855957}, {"X": 0.6691301465034485, "Y": 0.42811325192451477}, {"X": 0.6691267490386963, "Y": 0.4403732419013977}, {"X": 0.5643483996391296, "Y": 0.44034579396247864}]}, "Id": "97eda17d-cd6e-48b8-bad0-a4b87d1762a8"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008761180564761162, "Height": 0.013103093951940536, "Left": 0.6806170344352722, "Top": 0.4284615218639374}, "Polygon": [{"X": 0.6806207299232483, "Y": 0.4284615218639374}, {"X": 0.6893782615661621, "Y": 0.42846381664276123}, {"X": 0.689374566078186, "Y": 0.4415645897388458}, {"X": 0.6806170344352722, "Y": 0.441562294960022}]}, "Id": "514eab5c-e0d2-4419-8879-3d61d05a93ed"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "RACKS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06488774716854095, "Height": 0.011576512828469276, "Left": 0.1056532934308052, "Top": 0.4585410952568054}, "Polygon": [{"X": 0.10565657913684845, "Y": 0.4585410952568054}, {"X": 0.17054103314876556, "Y": 0.4585580825805664}, {"X": 0.17053775489330292, "Y": 0.47011759877204895}, {"X": 0.1056532934308052, "Y": 0.47010061144828796}]}, "Id": "aa8883ba-6258-4151-b48e-f2f9252b43c3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "AMOUNT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08612602949142456, "Height": 0.011357786133885384, "Left": 0.22077137231826782, "Top": 0.459964394569397}, "Polygon": [{"X": 0.2207745909690857, "Y": 0.459964394569397}, {"X": 0.3068974018096924, "Y": 0.45998695492744446}, {"X": 0.3068942129611969, "Y": 0.4713221788406372}, {"X": 0.22077137231826782, "Y": 0.4712996184825897}]}, "Id": "6353a328-9ca5-45b6-a73a-f587bbfc3381"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06409244239330292, "Height": 0.011580505408346653, "Left": 0.3562673330307007, "Top": 0.4609709680080414}, "Polygon": [{"X": 0.35627058148384094, "Y": 0.4609709680080414}, {"X": 0.4203597605228424, "Y": 0.46098777651786804}, {"X": 0.42035651206970215, "Y": 0.47255149483680725}, {"X": 0.3562673330307007, "Y": 0.4725346863269806}]}, "Id": "9e83fa2c-7ee4-42f8-ba60-19a764c5fa89"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04755816236138344, "Height": 0.011530096642673016, "Left": 0.5673404932022095, "Top": 0.48040127754211426}, "Polygon": [{"X": 0.5673437118530273, "Y": 0.48040127754211426}, {"X": 0.6148986220359802, "Y": 0.4804137349128723}, {"X": 0.6148954629898071, "Y": 0.49193137884140015}, {"X": 0.5673404932022095, "Y": 0.4919189214706421}]}, "Id": "9a38f75b-a0bc-475d-81b2-67006d16024b"}, {"BlockType": "WORD", "Confidence": 98.63042449951172, "Text": "+", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011124415323138237, "Height": 0.008663383312523365, "Left": 0.6697096228599548, "Top": 0.4827759265899658}, "Polygon": [{"X": 0.6697120070457458, "Y": 0.4827759265899658}, {"X": 0.6808340549468994, "Y": 0.4827788472175598}, {"X": 0.6808316111564636, "Y": 0.4914393126964569}, {"X": 0.6697096228599548, "Y": 0.4914364218711853}]}, "Id": "e7acd53d-618c-4bf0-99ff-d842d2524d28"}, {"BlockType": "WORD", "Confidence": 79.54021453857422, "Text": "$ 228,-", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1403646320104599, "Height": 0.023139966651797295, "Left": 0.6857843399047852, "Top": 0.471169650554657}, "Polygon": [{"X": 0.6857907176017761, "Y": 0.471169650554657}, {"X": 0.8261489272117615, "Y": 0.4712064266204834}, {"X": 0.8261425495147705, "Y": 0.49430960416793823}, {"X": 0.6857843399047852, "Y": 0.4942728281021118}]}, "Id": "af807af9-2ebf-4c85-a75c-f7ffc0b3b850"}, {"BlockType": "WORD", "Confidence": 91.025390625, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008650720119476318, "Height": 0.012699398212134838, "Left": 0.6860743165016174, "Top": 0.4811043441295624}, "Polygon": [{"X": 0.6860778331756592, "Y": 0.4811043441295624}, {"X": 0.6947250366210938, "Y": 0.48110660910606384}, {"X": 0.6947214603424072, "Y": 0.4938037395477295}, {"X": 0.6860743165016174, "Y": 0.493801474571228}]}, "Id": "d6062ee8-ce13-4246-b3fd-35ce87ec3e70"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "(<PERSON>ck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04601667448878288, "Height": 0.014022923074662685, "Left": 0.5677276849746704, "Top": 0.4976227879524231}, "Polygon": [{"X": 0.5677316188812256, "Y": 0.4976227879524231}, {"X": 0.6137443780899048, "Y": 0.4976348280906677}, {"X": 0.6137404441833496, "Y": 0.5116456747055054}, {"X": 0.5677276849746704, "Y": 0.5116336345672607}]}, "Id": "1f51d5c6-4640-40a1-bda8-9675e663545c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "checks", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05332710221409798, "Height": 0.011613125912845135, "Left": 0.6190704107284546, "Top": 0.4982061982154846}, "Polygon": [{"X": 0.6190736293792725, "Y": 0.4982061982154846}, {"X": 0.6723974943161011, "Y": 0.49822017550468445}, {"X": 0.6723942756652832, "Y": 0.5098193287849426}, {"X": 0.6190704107284546, "Y": 0.5098053216934204}]}, "Id": "af025e05-c29d-4cd1-a652-0edc9eefd1cb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "will", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0304739810526371, "Height": 0.011184360831975937, "Left": 0.677624523639679, "Top": 0.49880367517471313}, "Polygon": [{"X": 0.6776276230812073, "Y": 0.49880367517471313}, {"X": 0.7080984711647034, "Y": 0.49881166219711304}, {"X": 0.708095371723175, "Y": 0.509988009929657}, {"X": 0.677624523639679, "Y": 0.5099800229072571}]}, "Id": "950f4fb6-920c-45db-ab5f-e98e678bfa2c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "now", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034331925213336945, "Height": 0.008055171929299831, "Left": 0.7134994864463806, "Top": 0.502460777759552}, "Polygon": [{"X": 0.7135017514228821, "Y": 0.502460777759552}, {"X": 0.747831404209137, "Y": 0.5024697780609131}, {"X": 0.7478291988372803, "Y": 0.5105159878730774}, {"X": 0.7134994864463806, "Y": 0.5105069875717163}]}, "Id": "6c04536c-b68d-413d-8a7c-d6920f93566d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018297025933861732, "Height": 0.011341788806021214, "Left": 0.7526097297668457, "Top": 0.49943017959594727}, "Polygon": [{"X": 0.7526128888130188, "Y": 0.49943017959594727}, {"X": 0.7709067463874817, "Y": 0.4994349479675293}, {"X": 0.7709036469459534, "Y": 0.5107719302177429}, {"X": 0.7526097297668457, "Y": 0.5107671618461609}]}, "Id": "ff2f5dca-f289-444a-a435-3544975fd40c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "sent", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031473252922296524, "Height": 0.009411433711647987, "Left": 0.7767486572265625, "Top": 0.5016859173774719}, "Polygon": [{"X": 0.7767512798309326, "Y": 0.5016859173774719}, {"X": 0.8082218766212463, "Y": 0.5016941428184509}, {"X": 0.808219313621521, "Y": 0.5110973715782166}, {"X": 0.7767486572265625, "Y": 0.5110890865325928}]}, "Id": "df0047a4-647c-4445-85c8-0a62ab90c92f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014706337824463844, "Height": 0.009412862360477448, "Left": 0.8128605484962463, "Top": 0.5019479990005493}, "Polygon": [{"X": 0.8128631711006165, "Y": 0.5019479990005493}, {"X": 0.827566921710968, "Y": 0.5019518733024597}, {"X": 0.8275642991065979, "Y": 0.5113608837127686}, {"X": 0.8128605484962463, "Y": 0.5113570094108582}]}, "Id": "2c54d527-f47f-40d9-bb35-2b377196a966"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02361547201871872, "Height": 0.011221571825444698, "Left": 0.8332748413085938, "Top": 0.5004358887672424}, "Polygon": [{"X": 0.8332779407501221, "Y": 0.5004358887672424}, {"X": 0.8568903207778931, "Y": 0.5004420876502991}, {"X": 0.8568872213363647, "Y": 0.5116574764251709}, {"X": 0.8332748413085938, "Y": 0.5116512775421143}]}, "Id": "b550c3f9-8dfc-4010-babc-22142d4e07dd"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "broker.)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06206182762980461, "Height": 0.015006967820227146, "Left": 0.5781733989715576, "Top": 0.5148704648017883}, "Polygon": [{"X": 0.5781776309013367, "Y": 0.5148704648017883}, {"X": 0.6402352452278137, "Y": 0.514886736869812}, {"X": 0.6402310729026794, "Y": 0.5298774242401123}, {"X": 0.5781733989715576, "Y": 0.5298612117767334}]}, "Id": "4752d90b-c18e-427e-815b-55f935254ed2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029673943296074867, "Height": 0.011289125308394432, "Left": 0.5665860772132874, "Top": 0.5498335361480713}, "Polygon": [{"X": 0.5665892362594604, "Y": 0.5498335361480713}, {"X": 0.5962600111961365, "Y": 0.5498412847518921}, {"X": 0.5962568521499634, "Y": 0.5611226558685303}, {"X": 0.5665860772132874, "Y": 0.5611148476600647}]}, "Id": "58fa3239-402c-4091-a97d-d39824d2631f"}, {"BlockType": "WORD", "Confidence": 98.955078125, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036264412105083466, "Height": 0.011224978603422642, "Left": 0.6015302538871765, "Top": 0.5504708886146545}, "Polygon": [{"X": 0.6015333533287048, "Y": 0.5504708886146545}, {"X": 0.6377946734428406, "Y": 0.5504804253578186}, {"X": 0.6377915143966675, "Y": 0.5616958737373352}, {"X": 0.6015302538871765, "Y": 0.5616863965988159}]}, "Id": "be77179d-1d6b-4688-8d7c-d5156d9b7248"}, {"BlockType": "WORD", "Confidence": 96.75223541259766, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01098779495805502, "Height": 0.012702921405434608, "Left": 0.6425032019615173, "Top": 0.5503615140914917}, "Polygon": [{"X": 0.6425067782402039, "Y": 0.5503615140914917}, {"X": 0.6534910202026367, "Y": 0.5503643751144409}, {"X": 0.653487503528595, "Y": 0.5630643963813782}, {"X": 0.6425032019615173, "Y": 0.563061535358429}]}, "Id": "6aa04d9b-33fe-4898-9f1f-255542254688"}, {"BlockType": "WORD", "Confidence": 66.55193328857422, "Text": "4,097.-", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1428481489419937, "Height": 0.03526684641838074, "Left": 0.658726692199707, "Top": 0.5381820201873779}, "Polygon": [{"X": 0.658736526966095, "Y": 0.5381820201873779}, {"X": 0.8015748858451843, "Y": 0.5382195115089417}, {"X": 0.8015651106834412, "Y": 0.573448896408081}, {"X": 0.658726692199707, "Y": 0.5734114646911621}]}, "Id": "6e77d8e1-7c9d-414e-a1a6-ad6fa9e101a0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Billable", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10360633581876755, "Height": 0.01911480724811554, "Left": 0.10405118018388748, "Top": 0.5826233625411987}, "Polygon": [{"X": 0.10405661165714264, "Y": 0.5826233625411987}, {"X": 0.20765751600265503, "Y": 0.5826504826545715}, {"X": 0.20765209197998047, "Y": 0.6017381548881531}, {"X": 0.10405118018388748, "Y": 0.6017109751701355}]}, "Id": "db50ab32-c924-4355-a3f2-7c973c26fd4b"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Shortages:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13629022240638733, "Height": 0.024080608040094376, "Left": 0.21739128232002258, "Top": 0.5836592316627502}, "Polygon": [{"X": 0.21739810705184937, "Y": 0.5836592316627502}, {"X": 0.3536815047264099, "Y": 0.5836949944496155}, {"X": 0.3536747395992279, "Y": 0.6077398657798767}, {"X": 0.21739128232002258, "Y": 0.6077041029930115}]}, "Id": "341cdaa4-8069-4ff5-be37-40ff8edd8a06"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "(equipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05973955988883972, "Height": 0.010259738191962242, "Left": 0.10453926026821136, "Top": 0.6074943542480469}, "Polygon": [{"X": 0.10454218089580536, "Y": 0.6074943542480469}, {"X": 0.1642788201570511, "Y": 0.6075100302696228}, {"X": 0.1642759144306183, "Y": 0.6177541017532349}, {"X": 0.10453926026821136, "Y": 0.6177384257316589}]}, "Id": "a95b53ac-4fdf-4aa6-acee-a4385fb58dda"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "missing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04096987843513489, "Height": 0.010220779106020927, "Left": 0.16718989610671997, "Top": 0.6082467436790466}, "Polygon": [{"X": 0.16719280183315277, "Y": 0.6082467436790466}, {"X": 0.20815977454185486, "Y": 0.608257532119751}, {"X": 0.20815688371658325, "Y": 0.6184675693511963}, {"X": 0.16718989610671997, "Y": 0.6184567809104919}]}, "Id": "d0535489-5dbb-4f94-8253-eaf936c16d6a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "with", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0233994722366333, "Height": 0.008061401546001434, "Left": 0.21118542551994324, "Top": 0.6085819005966187}, "Polygon": [{"X": 0.2111877053976059, "Y": 0.6085819005966187}, {"X": 0.23458489775657654, "Y": 0.6085880398750305}, {"X": 0.23458261787891388, "Y": 0.6166433095932007}, {"X": 0.21118542551994324, "Y": 0.6166371703147888}]}, "Id": "5fc13b2a-4751-40fb-b927-0395fcc35614"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "insufficient", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06000994145870209, "Height": 0.008726710453629494, "Left": 0.23784571886062622, "Top": 0.6086386442184448}, "Polygon": [{"X": 0.2378481924533844, "Y": 0.6086386442184448}, {"X": 0.2978556752204895, "Y": 0.6086543798446655}, {"X": 0.2978532016277313, "Y": 0.6173653602600098}, {"X": 0.23784571886062622, "Y": 0.6173496246337891}]}, "Id": "f34c6235-5587-46ff-b78a-160795ae3729"}, {"BlockType": "WORD", "Confidence": 98.29659271240234, "Text": "documentation)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08168753236532211, "Height": 0.010375143960118294, "Left": 0.3012479841709137, "Top": 0.6096644997596741}, "Polygon": [{"X": 0.3012509047985077, "Y": 0.6096644997596741}, {"X": 0.3829355239868164, "Y": 0.6096859574317932}, {"X": 0.3829326033592224, "Y": 0.6200396418571472}, {"X": 0.3012479841709137, "Y": 0.6200182437896729}]}, "Id": "1aed3792-3570-40de-8ae0-f5724256ceca"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Racks:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052196480333805084, "Height": 0.01153563056141138, "Left": 0.10413096845149994, "Top": 0.6321006417274475}, "Polygon": [{"X": 0.10413425415754318, "Y": 0.6321006417274475}, {"X": 0.15632745623588562, "Y": 0.6321143507957458}, {"X": 0.15632417798042297, "Y": 0.6436362862586975}, {"X": 0.10413096845149994, "Y": 0.6436225771903992}]}, "Id": "cce5ab2b-4706-440b-a706-8529fb101794"}, {"BlockType": "WORD", "Confidence": 93.00382995605469, "Text": "(", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005879491101950407, "Height": 0.013976605609059334, "Left": 0.1627870798110962, "Top": 0.6324688196182251}, "Polygon": [{"X": 0.16279105842113495, "Y": 0.6324688196182251}, {"X": 0.16866657137870789, "Y": 0.6324703693389893}, {"X": 0.16866260766983032, "Y": 0.6464453935623169}, {"X": 0.1627870798110962, "Y": 0.6464439034461975}]}, "Id": "4d8f5b9b-140e-45c4-8496-999e21d6ffe9"}, {"BlockType": "WORD", "Confidence": 94.91051483154297, "Text": ")", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0064349304884672165, "Height": 0.014219776727259159, "Left": 0.1830146759748459, "Top": 0.6325572729110718}, "Polygon": [{"X": 0.18301869928836823, "Y": 0.6325572729110718}, {"X": 0.18944959342479706, "Y": 0.6325590014457703}, {"X": 0.18944557011127472, "Y": 0.6467770934104919}, {"X": 0.1830146759748459, "Y": 0.6467753648757935}]}, "Id": "3db861c2-7398-4c65-aca5-fcf1463cbc7f"}, {"BlockType": "WORD", "Confidence": 61.38452911376953, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009732652455568314, "Height": 0.008293069899082184, "Left": 0.1947522610425949, "Top": 0.6360498070716858}, "Polygon": [{"X": 0.19475461542606354, "Y": 0.6360498070716858}, {"X": 0.20448490977287292, "Y": 0.6360523700714111}, {"X": 0.2044825553894043, "Y": 0.6443428993225098}, {"X": 0.1947522610425949, "Y": 0.6443403363227844}]}, "Id": "91d09997-2111-42cb-a615-836b326d213a"}, {"BlockType": "WORD", "Confidence": 99.1015625, "Text": "$250.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06388027966022491, "Height": 0.012692749500274658, "Left": 0.20921581983566284, "Top": 0.6326292157173157}, "Polygon": [{"X": 0.20921941101551056, "Y": 0.6326292157173157}, {"X": 0.27309608459472656, "Y": 0.6326459646224976}, {"X": 0.27309250831604004, "Y": 0.6453219652175903}, {"X": 0.20921581983566284, "Y": 0.6453051567077637}]}, "Id": "59d865f9-b13c-4b7f-8a30-22c40c844932"}, {"BlockType": "WORD", "Confidence": 98.115234375, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011253313161432743, "Height": 0.003949320875108242, "Left": 0.2782721221446991, "Top": 0.6370541453361511}, "Polygon": [{"X": 0.27827325463294983, "Y": 0.6370541453361511}, {"X": 0.28952544927597046, "Y": 0.6370570659637451}, {"X": 0.2895243465900421, "Y": 0.641003429889679}, {"X": 0.2782721221446991, "Y": 0.641000509262085}]}, "Id": "36f45460-f5b3-426c-96ce-08a4c3e9274d"}, {"BlockType": "WORD", "Confidence": 99.404296875, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008738803677260876, "Height": 0.012683047913014889, "Left": 0.29469093680381775, "Top": 0.6332722902297974}, "Polygon": [{"X": 0.2946945130825043, "Y": 0.6332722902297974}, {"X": 0.3034297227859497, "Y": 0.6332745552062988}, {"X": 0.3034261465072632, "Y": 0.6459553241729736}, {"X": 0.29469093680381775, "Y": 0.6459529995918274}]}, "Id": "f5cbb701-d5c6-4199-b6dd-276e6dbc572a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041361600160598755, "Height": 0.01142930518835783, "Left": 0.58537757396698, "Top": 0.6367524862289429}, "Polygon": [{"X": 0.5853807330131531, "Y": 0.6367524862289429}, {"X": 0.6267391443252563, "Y": 0.636763334274292}, {"X": 0.6267359852790833, "Y": 0.6481817960739136}, {"X": 0.58537757396698, "Y": 0.6481709480285645}]}, "Id": "7ab18753-ad0e-494e-830a-0c9869b44f2b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Deductions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0904676541686058, "Height": 0.012030758894979954, "Left": 0.6318945288658142, "Top": 0.6371869444847107}, "Polygon": [{"X": 0.6318978667259216, "Y": 0.6371869444847107}, {"X": 0.7223621606826782, "Y": 0.6372106671333313}, {"X": 0.7223588228225708, "Y": 0.6492177248001099}, {"X": 0.6318945288658142, "Y": 0.6491940021514893}]}, "Id": "817b987a-74b7-4705-9ab7-8f14358aa085"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "For", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027825213968753815, "Height": 0.01125154085457325, "Left": 0.5862172245979309, "Top": 0.654009997844696}, "Polygon": [{"X": 0.586220383644104, "Y": 0.654009997844696}, {"X": 0.6140424609184265, "Y": 0.6540172696113586}, {"X": 0.6140393018722534, "Y": 0.6652615666389465}, {"X": 0.5862172245979309, "Y": 0.6652542352676392}]}, "Id": "3dddc731-847b-4b34-b751-06a1d7116196"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Load:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044957637786865234, "Height": 0.011889188550412655, "Left": 0.6181690692901611, "Top": 0.6541141271591187}, "Polygon": [{"X": 0.6181724071502686, "Y": 0.6541141271591187}, {"X": 0.6631267070770264, "Y": 0.6541258692741394}, {"X": 0.6631234288215637, "Y": 0.6660032868385315}, {"X": 0.6181690692901611, "Y": 0.665991485118866}]}, "Id": "84816812-c2b4-417e-80f8-cb05d6654e34"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03971274569630623, "Height": 0.011393449269235134, "Left": 0.10372912883758545, "Top": 0.6668969392776489}, "Polygon": [{"X": 0.10373236984014511, "Y": 0.6668969392776489}, {"X": 0.14344187080860138, "Y": 0.6669073104858398}, {"X": 0.14343863725662231, "Y": 0.6782903671264648}, {"X": 0.10372912883758545, "Y": 0.6782799363136292}]}, "Id": "f403e91e-84ba-4a71-b707-8633c5b29d6c"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "Locks:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052480727434158325, "Height": 0.011685303412377834, "Left": 0.14819388091564178, "Top": 0.667160153388977}, "Polygon": [{"X": 0.148197203874588, "Y": 0.667160153388977}, {"X": 0.2006746083498001, "Y": 0.6671739220619202}, {"X": 0.20067130029201508, "Y": 0.6788454651832581}, {"X": 0.14819388091564178, "Y": 0.6788316965103149}]}, "Id": "213761c9-7a6a-4bb8-8296-1b2d098d0ac1"}, {"BlockType": "WORD", "Confidence": 78.25123596191406, "Text": "( )", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026909176260232925, "Height": 0.01474826131016016, "Left": 0.20728890597820282, "Top": 0.6675795316696167}, "Polygon": [{"X": 0.2072930783033371, "Y": 0.6675795316696167}, {"X": 0.23419807851314545, "Y": 0.667586624622345}, {"X": 0.23419390618801117, "Y": 0.6823278069496155}, {"X": 0.20728890597820282, "Y": 0.682320773601532}]}, "Id": "0f537d5d-df2c-4af0-bf35-4ee525aa17b4"}, {"BlockType": "WORD", "Confidence": 64.36324310302734, "Text": "x", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009470703080296516, "Height": 0.007938466966152191, "Left": 0.2396055907011032, "Top": 0.6715644001960754}, "Polygon": [{"X": 0.23960784077644348, "Y": 0.6715644001960754}, {"X": 0.24907630681991577, "Y": 0.671566903591156}, {"X": 0.2490740567445755, "Y": 0.6795029044151306}, {"X": 0.2396055907011032, "Y": 0.67950040102005}]}, "Id": "1a48fe62-3b0d-413c-8ee7-25ed2b484924"}, {"BlockType": "WORD", "Confidence": 99.111328125, "Text": "$25.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053638458251953125, "Height": 0.012915635481476784, "Left": 0.25456297397613525, "Top": 0.6677073240280151}, "Polygon": [{"X": 0.25456660985946655, "Y": 0.6677073240280151}, {"X": 0.3082014322280884, "Y": 0.6677213907241821}, {"X": 0.3081977963447571, "Y": 0.680622935295105}, {"X": 0.25456297397613525, "Y": 0.680608868598938}]}, "Id": "ba6d9ec2-dcdb-461f-b0b7-d3ca4b2b360f"}, {"BlockType": "WORD", "Confidence": 98.0078125, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01128489151597023, "Height": 0.003972174134105444, "Left": 0.31357935070991516, "Top": 0.6722029447555542}, "Polygon": [{"X": 0.3135804533958435, "Y": 0.6722029447555542}, {"X": 0.3248642385005951, "Y": 0.6722058653831482}, {"X": 0.32486310601234436, "Y": 0.6761751174926758}, {"X": 0.31357935070991516, "Y": 0.676172137260437}]}, "Id": "dbad3cb8-4d24-4415-91b5-59af0305c0d7"}, {"BlockType": "WORD", "Confidence": 99.26677703857422, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008993108756840229, "Height": 0.01251017116010189, "Left": 0.32976844906806946, "Top": 0.6683945655822754}, "Polygon": [{"X": 0.3297719657421112, "Y": 0.6683945655822754}, {"X": 0.3387615382671356, "Y": 0.6683968901634216}, {"X": 0.33875802159309387, "Y": 0.680904746055603}, {"X": 0.32976844906806946, "Y": 0.680902361869812}]}, "Id": "f45d5da7-dfab-4cb3-90c0-40f7fbec12be"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008831953629851341, "Height": 0.012972169555723667, "Left": 0.5860373973846436, "Top": 0.6883605122566223}, "Polygon": [{"X": 0.5860409736633301, "Y": 0.6883605122566223}, {"X": 0.5948693156242371, "Y": 0.6883627772331238}, {"X": 0.5948656797409058, "Y": 0.7013326287269592}, {"X": 0.5860373973846436, "Y": 0.7013303637504578}]}, "Id": "e45a0fc6-42a0-4a29-a427-c92c75c80bc0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04453550651669502, "Height": 0.011631196364760399, "Left": 0.10328198224306107, "Top": 0.7013720870018005}, "Polygon": [{"X": 0.1032852903008461, "Y": 0.7013720870018005}, {"X": 0.1478174775838852, "Y": 0.7013837695121765}, {"X": 0.14781418442726135, "Y": 0.7130032777786255}, {"X": 0.10328198224306107, "Y": 0.7129915952682495}]}, "Id": "a1781956-eeb1-41c6-8137-8244c8cac7f0"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "<PERSON>:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03905046731233597, "Height": 0.01177502702921629, "Left": 0.1526443362236023, "Top": 0.7018449902534485}, "Polygon": [{"X": 0.15264767408370972, "Y": 0.7018449902534485}, {"X": 0.19169479608535767, "Y": 0.7018552422523499}, {"X": 0.19169145822525024, "Y": 0.7136200070381165}, {"X": 0.1526443362236023, "Y": 0.7136097550392151}]}, "Id": "d7c2319e-1ff9-4054-a78d-970c46ab4a72"}, {"BlockType": "WORD", "Confidence": 80.38903045654297, "Text": "( )", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026936139911413193, "Height": 0.014134184457361698, "Left": 0.19813194870948792, "Top": 0.7024369835853577}, "Polygon": [{"X": 0.19813595712184906, "Y": 0.7024369835853577}, {"X": 0.2250680923461914, "Y": 0.7024440765380859}, {"X": 0.22506408393383026, "Y": 0.7165712118148804}, {"X": 0.19813194870948792, "Y": 0.7165641188621521}]}, "Id": "2b3e7096-8c54-4940-a162-a40138ec42c4"}, {"BlockType": "WORD", "Confidence": 72.23831939697266, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009855682030320168, "Height": 0.007958687841892242, "Left": 0.23024819791316986, "Top": 0.7060620784759521}, "Polygon": [{"X": 0.23025044798851013, "Y": 0.7060620784759521}, {"X": 0.24010388553142548, "Y": 0.7060647010803223}, {"X": 0.2401016354560852, "Y": 0.7140207886695862}, {"X": 0.23024819791316986, "Y": 0.7140182256698608}]}, "Id": "3a626c55-2bab-4c5e-b171-d81dd42c8c9e"}, {"BlockType": "WORD", "Confidence": 99.638671875, "Text": "$500.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06374683231115341, "Height": 0.012306811287999153, "Left": 0.2455318421125412, "Top": 0.7026139497756958}, "Polygon": [{"X": 0.24553532898426056, "Y": 0.7026139497756958}, {"X": 0.309278666973114, "Y": 0.7026306390762329}, {"X": 0.30927520990371704, "Y": 0.7149207592010498}, {"X": 0.2455318421125412, "Y": 0.7149040102958679}]}, "Id": "06802c8b-0b02-4eef-b9aa-2376fbd2da3e"}, {"BlockType": "WORD", "Confidence": 98.00541687011719, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011076723225414753, "Height": 0.003844727762043476, "Left": 0.31443819403648376, "Top": 0.7069844007492065}, "Polygon": [{"X": 0.3144392967224121, "Y": 0.7069844007492065}, {"X": 0.32551491260528564, "Y": 0.7069873213768005}, {"X": 0.3255138397216797, "Y": 0.7108291387557983}, {"X": 0.31443819403648376, "Y": 0.7108262181282043}]}, "Id": "6f7ec002-8a78-4b1e-8c96-3278887b5c9c"}, {"BlockType": "WORD", "Confidence": 99.306640625, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008637991733849049, "Height": 0.012586875818669796, "Left": 0.330727756023407, "Top": 0.7033063769340515}, "Polygon": [{"X": 0.3307313024997711, "Y": 0.7033063769340515}, {"X": 0.33936575055122375, "Y": 0.703308641910553}, {"X": 0.3393622040748596, "Y": 0.7158932685852051}, {"X": 0.330727756023407, "Y": 0.7158910036087036}]}, "Id": "097d5453-6f39-40ac-b3f6-635f951de560"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Load", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04009317234158516, "Height": 0.011450164951384068, "Left": 0.10267634689807892, "Top": 0.7362170219421387}, "Polygon": [{"X": 0.10267960280179977, "Y": 0.7362170219421387}, {"X": 0.14276951551437378, "Y": 0.7362275719642639}, {"X": 0.14276626706123352, "Y": 0.7476671934127808}, {"X": 0.10267634689807892, "Y": 0.7476567029953003}]}, "Id": "d3c19a05-3c78-46e5-8b7b-5adfe089a48f"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Straps:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05276329815387726, "Height": 0.014221754856407642, "Left": 0.14830145239830017, "Top": 0.7367733716964722}, "Polygon": [{"X": 0.1483054906129837, "Y": 0.7367733716964722}, {"X": 0.20106475055217743, "Y": 0.7367871999740601}, {"X": 0.2010607272386551, "Y": 0.7509950995445251}, {"X": 0.14830145239830017, "Y": 0.7509812712669373}]}, "Id": "3e56a551-850f-4922-b037-4799fe68cfad"}, {"BlockType": "WORD", "Confidence": 80.3514404296875, "Text": "( )", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027033591642975807, "Height": 0.014414502307772636, "Left": 0.2075158804655075, "Top": 0.7370894551277161}, "Polygon": [{"X": 0.20751996338367462, "Y": 0.7370894551277161}, {"X": 0.23454947769641876, "Y": 0.7370965480804443}, {"X": 0.23454539477825165, "Y": 0.7515039443969727}, {"X": 0.2075158804655075, "Y": 0.7514968514442444}]}, "Id": "e0c97a2a-8906-465f-8726-d8c016d6d04f"}, {"BlockType": "WORD", "Confidence": 73.20552062988281, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00951108057051897, "Height": 0.007947040721774101, "Left": 0.23988425731658936, "Top": 0.7408584356307983}, "Polygon": [{"X": 0.23988649249076843, "Y": 0.7408584356307983}, {"X": 0.24939532577991486, "Y": 0.7408609390258789}, {"X": 0.24939307570457458, "Y": 0.7488054633140564}, {"X": 0.23988425731658936, "Y": 0.7488029599189758}]}, "Id": "7fedcb04-83bf-47bf-b0b6-155858f1e3c7"}, {"BlockType": "WORD", "Confidence": 98.90625, "Text": "$25.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05373100936412811, "Height": 0.012529346160590649, "Left": 0.2548576593399048, "Top": 0.7370584011077881}, "Polygon": [{"X": 0.2548612058162689, "Y": 0.7370584011077881}, {"X": 0.3085886836051941, "Y": 0.7370725274085999}, {"X": 0.30858513712882996, "Y": 0.7495877742767334}, {"X": 0.2548576593399048, "Y": 0.7495736479759216}]}, "Id": "47937ee4-2f54-4c2f-ac29-80cd48faa8e8"}, {"BlockType": "WORD", "Confidence": 97.96715545654297, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011440180242061615, "Height": 0.004257834982126951, "Left": 0.3137458562850952, "Top": 0.7413786053657532}, "Polygon": [{"X": 0.3137470483779907, "Y": 0.7413786053657532}, {"X": 0.3251860439777374, "Y": 0.7413816452026367}, {"X": 0.3251848518848419, "Y": 0.7456364631652832}, {"X": 0.3137458562850952, "Y": 0.7456334829330444}]}, "Id": "9d48eabc-73c3-493e-a806-e47fa825414c"}, {"BlockType": "WORD", "Confidence": 99.0625, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008892142213881016, "Height": 0.012787267565727234, "Left": 0.33015719056129456, "Top": 0.7376107573509216}, "Polygon": [{"X": 0.33016079664230347, "Y": 0.7376107573509216}, {"X": 0.3390493392944336, "Y": 0.7376130819320679}, {"X": 0.3390457332134247, "Y": 0.7503979802131653}, {"X": 0.33015719056129456, "Y": 0.750395655632019}]}, "Id": "d773db84-fbea-43d1-a160-09d4d3bf022b"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Paperwork", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08424881100654602, "Height": 0.014260981231927872, "Left": 0.10250668227672577, "Top": 0.7712881565093994}, "Polygon": [{"X": 0.1025107353925705, "Y": 0.7712881565093994}, {"X": 0.1867554932832718, "Y": 0.7713102698326111}, {"X": 0.18675145506858826, "Y": 0.7855491638183594}, {"X": 0.10250668227672577, "Y": 0.7855269908905029}]}, "Id": "fa5133b1-b6e7-470e-be27-3b697b7845f5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014471386559307575, "Height": 0.01148212794214487, "Left": 0.19220775365829468, "Top": 0.7714334726333618}, "Polygon": [{"X": 0.19221100211143494, "Y": 0.7714334726333618}, {"X": 0.20667913556098938, "Y": 0.7714372277259827}, {"X": 0.20667587220668793, "Y": 0.7829155921936035}, {"X": 0.19220775365829468, "Y": 0.7829117774963379}]}, "Id": "f15e1c02-d5fc-44ef-a052-5d9564f31c3a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "other", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04074765741825104, "Height": 0.011533897370100021, "Left": 0.21237309277057648, "Top": 0.7718266248703003}, "Polygon": [{"X": 0.21237635612487793, "Y": 0.7718266248703003}, {"X": 0.2531207501888275, "Y": 0.7718372941017151}, {"X": 0.25311747193336487, "Y": 0.783360481262207}, {"X": 0.21237309277057648, "Y": 0.7833498120307922}]}, "Id": "597fc4a7-ef5e-4b39-9adb-b82078b77412"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "Fines", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04204778000712395, "Height": 0.011754506267607212, "Left": 0.25790277123451233, "Top": 0.7721705436706543}, "Polygon": [{"X": 0.25790607929229736, "Y": 0.7721705436706543}, {"X": 0.2999505400657654, "Y": 0.7721815705299377}, {"X": 0.29994723200798035, "Y": 0.7839250564575195}, {"X": 0.25790277123451233, "Y": 0.7839140295982361}]}, "Id": "20a7f1e3-6bfa-443e-954a-321df15d2890"}, {"BlockType": "WORD", "Confidence": 99.638671875, "Text": "$", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008830703794956207, "Height": 0.012892141938209534, "Left": 0.3055245578289032, "Top": 0.7722118496894836}, "Polygon": [{"X": 0.3055281937122345, "Y": 0.7722118496894836}, {"X": 0.3143552541732788, "Y": 0.7722141742706299}, {"X": 0.3143516182899475, "Y": 0.7851040363311768}, {"X": 0.3055245578289032, "Y": 0.7851017117500305}]}, "Id": "4415757f-9c54-489e-a69a-4490e496dec0"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "(see", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03059753216803074, "Height": 0.013585284352302551, "Left": 0.10282904654741287, "Top": 0.7885953187942505}, "Polygon": [{"X": 0.10283291339874268, "Y": 0.7885953187942505}, {"X": 0.13342657685279846, "Y": 0.7886033654212952}, {"X": 0.13342271745204926, "Y": 0.8021806478500366}, {"X": 0.10282904654741287, "Y": 0.8021726012229919}]}, "Id": "a93466c9-446c-4ebc-8580-eceef6ec810e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "attached", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06497400999069214, "Height": 0.011331399902701378, "Left": 0.13855373859405518, "Top": 0.788995087146759}, "Polygon": [{"X": 0.13855695724487305, "Y": 0.788995087146759}, {"X": 0.20352774858474731, "Y": 0.7890121936798096}, {"X": 0.20352454483509064, "Y": 0.8003265261650085}, {"X": 0.13855373859405518, "Y": 0.800309419631958}]}, "Id": "0a3f1ddd-2b35-43b7-a8e0-cf068d109a10"}, {"BlockType": "WORD", "Confidence": 98.40560913085938, "Text": "documentation)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12331206351518631, "Height": 0.01453451532870531, "Left": 0.208716481924057, "Top": 0.7894203662872314}, "Polygon": [{"X": 0.2087205946445465, "Y": 0.7894203662872314}, {"X": 0.3320285379886627, "Y": 0.7894527316093445}, {"X": 0.3320244550704956, "Y": 0.8039548397064209}, {"X": 0.208716481924057, "Y": 0.8039224743843079}]}, "Id": "fcdf8b1b-0a6b-4546-bb78-54286688fccd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Manager", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07038367539644241, "Height": 0.014367942698299885, "Left": 0.5294905304908752, "Top": 0.7926991581916809}, "Polygon": [{"X": 0.5294945240020752, "Y": 0.7926991581916809}, {"X": 0.5998741984367371, "Y": 0.7927176356315613}, {"X": 0.5998702049255371, "Y": 0.8070670962333679}, {"X": 0.5294905304908752, "Y": 0.8070486187934875}]}, "Id": "e11ca6a8-ba6f-4545-9fdc-9290b898bc14"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Approval:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07877449691295624, "Height": 0.014289534650743008, "Left": 0.6045781373977661, "Top": 0.7938423752784729}, "Polygon": [{"X": 0.6045821309089661, "Y": 0.7938423752784729}, {"X": 0.6833526492118835, "Y": 0.79386305809021}, {"X": 0.6833486557006836, "Y": 0.8081318736076355}, {"X": 0.6045781373977661, "Y": 0.8081111907958984}]}, "Id": "3397d57d-8210-43fd-9bb2-c1230624aaec"}, {"BlockType": "WORD", "Confidence": 93.8526382446289, "Text": "my", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09537752717733383, "Height": 0.07414773851633072, "Left": 0.7558115124702454, "Top": 0.7662332057952881}, "Polygon": [{"X": 0.7558320760726929, "Y": 0.7662332057952881}, {"X": 0.8511890172958374, "Y": 0.7662582397460938}, {"X": 0.8511685132980347, "Y": 0.8403809666633606}, {"X": 0.7558115124702454, "Y": 0.8403558731079102}]}, "Id": "93cc5a9a-2a59-4f9b-b51f-6e43e903df7f"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Notes:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050074320286512375, "Height": 0.011840011924505234, "Left": 0.10143722593784332, "Top": 0.8229336738586426}, "Polygon": [{"X": 0.10144059360027313, "Y": 0.8229336738586426}, {"X": 0.151511549949646, "Y": 0.8229467868804932}, {"X": 0.1515081822872162, "Y": 0.8347736597061157}, {"X": 0.10143722593784332, "Y": 0.8347604870796204}]}, "Id": "a4bea9d8-d4db-4c15-aa4d-9eba3f056b0f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Any", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028146719560027122, "Height": 0.011835243552923203, "Left": 0.10120530426502228, "Top": 0.874683678150177}, "Polygon": [{"X": 0.10120867192745209, "Y": 0.874683678150177}, {"X": 0.12935201823711395, "Y": 0.8746910691261292}, {"X": 0.12934866547584534, "Y": 0.8865188956260681}, {"X": 0.10120530426502228, "Y": 0.886511504650116}]}, "Id": "337b9deb-92c3-4638-b408-b6eaa521bcb5"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "fine", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024955473840236664, "Height": 0.00933452695608139, "Left": 0.13370147347450256, "Top": 0.874703049659729}, "Polygon": [{"X": 0.13370412588119507, "Y": 0.874703049659729}, {"X": 0.15865693986415863, "Y": 0.8747096061706543}, {"X": 0.15865428745746613, "Y": 0.8840375542640686}, {"X": 0.13370147347450256, "Y": 0.8840309977531433}]}, "Id": "a2f559b1-5d66-4122-9d93-594d203189a7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "not", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021356552839279175, "Height": 0.007975341752171516, "Left": 0.16274508833885193, "Top": 0.8769745826721191}, "Polygon": [{"X": 0.1627473533153534, "Y": 0.8769745826721191}, {"X": 0.1841016411781311, "Y": 0.876980185508728}, {"X": 0.18409937620162964, "Y": 0.8849499225616455}, {"X": 0.16274508833885193, "Y": 0.8849442601203918}]}, "Id": "afb74cd9-8cf1-4a84-944b-213c44a2f492"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "appealed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05743373557925224, "Height": 0.011339223943650723, "Left": 0.18829938769340515, "Top": 0.8759498596191406}, "Polygon": [{"X": 0.18830260634422302, "Y": 0.8759498596191406}, {"X": 0.2457331269979477, "Y": 0.8759649991989136}, {"X": 0.24572990834712982, "Y": 0.8872891068458557}, {"X": 0.18829938769340515, "Y": 0.8872740268707275}]}, "Id": "c1671e37-3af5-4a42-8857-4ff708220080"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "within", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04148097708821297, "Height": 0.009379914030432701, "Left": 0.25016534328460693, "Top": 0.8759362697601318}, "Polygon": [{"X": 0.25016799569129944, "Y": 0.8759362697601318}, {"X": 0.2916463017463684, "Y": 0.8759471774101257}, {"X": 0.2916436493396759, "Y": 0.8853161931037903}, {"X": 0.25016534328460693, "Y": 0.8853052854537964}]}, "Id": "68d0b959-15e6-4bc7-b708-1d61de08ae58"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006971823051571846, "Height": 0.008811180479824543, "Left": 0.29622793197631836, "Top": 0.8771834969520569}, "Polygon": [{"X": 0.2962304353713989, "Y": 0.8771834969520569}, {"X": 0.30319976806640625, "Y": 0.8771853446960449}, {"X": 0.3031972646713257, "Y": 0.8859946727752686}, {"X": 0.29622793197631836, "Y": 0.8859928250312805}]}, "Id": "e0fe81c6-1302-429d-8ba8-9377b7b958ef"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "business", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05541035532951355, "Height": 0.009685353375971317, "Left": 0.30856409668922424, "Top": 0.8764307498931885}, "Polygon": [{"X": 0.3085668087005615, "Y": 0.8764307498931885}, {"X": 0.3639744520187378, "Y": 0.8764452934265137}, {"X": 0.3639717102050781, "Y": 0.886116087436676}, {"X": 0.30856409668922424, "Y": 0.8861015439033508}]}, "Id": "b8a9ab3f-c26f-473b-835c-354aead93d98"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "days", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029635759070515633, "Height": 0.011744442395865917, "Left": 0.3688366115093231, "Top": 0.877228856086731}, "Polygon": [{"X": 0.36883991956710815, "Y": 0.877228856086731}, {"X": 0.3984723687171936, "Y": 0.8772366642951965}, {"X": 0.39846906065940857, "Y": 0.8889732956886292}, {"X": 0.3688366115093231, "Y": 0.8889655470848083}]}, "Id": "42ce396c-2fdf-476b-bd25-fa23adfde9d3"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010109305381774902, "Height": 0.009348382242023945, "Left": 0.4031815826892853, "Top": 0.8774767518043518}, "Polygon": [{"X": 0.4031842350959778, "Y": 0.8774767518043518}, {"X": 0.4132908880710602, "Y": 0.8774793744087219}, {"X": 0.41328826546669006, "Y": 0.8868251442909241}, {"X": 0.4031815826892853, "Y": 0.8868224620819092}]}, "Id": "066a139b-dc96-4249-8763-c75e668625d8"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "final.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0332152396440506, "Height": 0.009277179837226868, "Left": 0.41792353987693787, "Top": 0.8778218626976013}, "Polygon": [{"X": 0.417926162481308, "Y": 0.8778218626976013}, {"X": 0.45113879442214966, "Y": 0.8778306245803833}, {"X": 0.45113617181777954, "Y": 0.8870990872383118}, {"X": 0.41792353987693787, "Y": 0.8870903253555298}]}, "Id": "02006bbc-c5e3-4007-a15b-82154895b1d7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Appeals", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053544994443655014, "Height": 0.011564582586288452, "Left": 0.4562409520149231, "Top": 0.878458559513092}, "Polygon": [{"X": 0.45624420046806335, "Y": 0.878458559513092}, {"X": 0.5097859501838684, "Y": 0.878472626209259}, {"X": 0.5097827315330505, "Y": 0.8900231122970581}, {"X": 0.4562409520149231, "Y": 0.8900090456008911}]}, "Id": "cd59abc7-4658-4c60-be8b-8156da90692c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "may", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02813555672764778, "Height": 0.00872416328638792, "Left": 0.5139931440353394, "Top": 0.8813297152519226}, "Polygon": [{"X": 0.5139955878257751, "Y": 0.8813297152519226}, {"X": 0.5421286821365356, "Y": 0.8813371062278748}, {"X": 0.5421262383460999, "Y": 0.8900538682937622}, {"X": 0.5139931440353394, "Y": 0.8900464773178101}]}, "Id": "67664af4-300f-40ac-8e53-7eaab524f84e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014869622886180878, "Height": 0.008860246278345585, "Left": 0.5467719435691833, "Top": 0.8790649771690369}, "Polygon": [{"X": 0.5467744469642639, "Y": 0.8790649771690369}, {"X": 0.5616415739059448, "Y": 0.879068911075592}, {"X": 0.561639130115509, "Y": 0.8879252076148987}, {"X": 0.5467719435691833, "Y": 0.8879213333129883}]}, "Id": "3089c89b-7c80-4c82-b29d-098d6ce4ebf3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "made", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035717904567718506, "Height": 0.009204147383570671, "Left": 0.5662292838096619, "Top": 0.8792698383331299}, "Polygon": [{"X": 0.566231906414032, "Y": 0.8792698383331299}, {"X": 0.6019471883773804, "Y": 0.8792791962623596}, {"X": 0.601944625377655, "Y": 0.8884739875793457}, {"X": 0.5662292838096619, "Y": 0.8884645700454712}]}, "Id": "673459d5-238d-4d03-a668-557cde2f25e4"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011774733662605286, "Height": 0.00925216730684042, "Left": 0.6064549684524536, "Top": 0.8794658780097961}, "Polygon": [{"X": 0.6064575910568237, "Y": 0.8794658780097961}, {"X": 0.6182296872138977, "Y": 0.8794689774513245}, {"X": 0.6182271242141724, "Y": 0.8887180685997009}, {"X": 0.6064549684524536, "Y": 0.8887149691581726}]}, "Id": "d5badeb9-995b-43fb-b98f-1961ad0871c4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "writing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0474671944975853, "Height": 0.012060522101819515, "Left": 0.6226381063461304, "Top": 0.8798673152923584}, "Polygon": [{"X": 0.6226414442062378, "Y": 0.8798673152923584}, {"X": 0.6701052784919739, "Y": 0.8798797726631165}, {"X": 0.6701019406318665, "Y": 0.8919278383255005}, {"X": 0.6226381063461304, "Y": 0.8919153213500977}]}, "Id": "f472b173-16fa-4744-84d3-47412cba51d4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023416584357619286, "Height": 0.009534206241369247, "Left": 0.6747363805770874, "Top": 0.879939615726471}, "Polygon": [{"X": 0.6747390627861023, "Y": 0.879939615726471}, {"X": 0.6981529593467712, "Y": 0.8799457550048828}, {"X": 0.6981503367424011, "Y": 0.8894737958908081}, {"X": 0.6747363805770874, "Y": 0.8894676566123962}]}, "Id": "b21aed9d-51a1-4cef-a5f8-2672a3a3e37d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "faxed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036382295191287994, "Height": 0.009393241256475449, "Left": 0.702537477016449, "Top": 0.8802896738052368}, "Polygon": [{"X": 0.7025400996208191, "Y": 0.8802896738052368}, {"X": 0.738919734954834, "Y": 0.8802992105484009}, {"X": 0.7389171719551086, "Y": 0.8896828889846802}, {"X": 0.702537477016449, "Y": 0.8896733522415161}]}, "Id": "6bdc37d4-e4d1-4b30-b80e-9cda83e44f98"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012575003318488598, "Height": 0.007848456501960754, "Left": 0.7430245876312256, "Top": 0.8824920654296875}, "Polygon": [{"X": 0.7430267930030823, "Y": 0.8824920654296875}, {"X": 0.7555996179580688, "Y": 0.8824953436851501}, {"X": 0.7555974125862122, "Y": 0.8903405070304871}, {"X": 0.7430245876312256, "Y": 0.8903372287750244}]}, "Id": "158dc3d4-e0f5-4c9f-8b92-1231b3f6aed1"}, {"BlockType": "WORD", "Confidence": 97.275390625, "Text": "(616)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035178396850824356, "Height": 0.011944434605538845, "Left": 0.7605318427085876, "Top": 0.8809274435043335}, "Polygon": [{"X": 0.7605351209640503, "Y": 0.8809274435043335}, {"X": 0.7957102060317993, "Y": 0.8809366822242737}, {"X": 0.7957069277763367, "Y": 0.8928718566894531}, {"X": 0.7605318427085876, "Y": 0.8928626179695129}]}, "Id": "6b9ea90c-1bd2-4815-bac8-1d93530162c3"}, {"BlockType": "WORD", "Confidence": 99.052734375, "Text": "669-7468", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06174119561910629, "Height": 0.00963629875332117, "Left": 0.8002656698226929, "Top": 0.881389319896698}, "Polygon": [{"X": 0.800268292427063, "Y": 0.881389319896698}, {"X": 0.8620068430900574, "Y": 0.8814055323600769}, {"X": 0.8620041608810425, "Y": 0.8910256028175354}, {"X": 0.8002656698226929, "Y": 0.8910093903541565}]}, "Id": "a171ba5c-9f60-4f73-a590-de4c2be4fdf7"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "4a75575f-f4f9-4c47-b832-2cfe6361697d", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "4a75575f-f4f9-4c47-b832-2cfe6361697d", "content-type": "application/x-amz-json-1.1", "content-length": "90258", "date": "Thu, 18 Sep 2025 08:54:23 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "67a6d09e-341c-4413-8fc6-503c1d8f5b6e", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 08:54:27 GMT", "content-type": "application/json", "content-length": "1600", "connection": "keep-alive", "x-amzn-requestid": "67a6d09e-341c-4413-8fc6-503c1d8f5b6e"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "The user provided a page, labeled <page1>. The content appears to be a \"Truckload Check-in Form\" and also \"Clear-to-Pay Voucher\" maybe. The header: \"Truckload Check-in Form\" \"Clear-to-Pay Voucher\". The lines mention \"Clear-to-Pay Voucher\" and \"Load # 20196\" \"Broker PF2\" \"Date Processed 6/12/2023\". There are fields: \"Load Cost\", \"Racks Returned\", \"Surcharge\", \"Deductions\", \"Rack checks will now be sent to the broker.\" \"Net Cost\", \"Billable Shortages\", \"Load Locks\", \"Pallet Jack\", \"Load Straps\", \"Paperwork & other Fines\", \"Manager Approval\", \"Notes\". This seems like a form used by a carrier for truckload check-in, and also a clear-to-pay voucher. The classification: we have \"clear_to_pay\" as a type. The doc type \"clear_to_pay\" includes keywords like \"Clear to Pay\", \"Approved for Payment\". The page also has \"Clear-to-Pay Voucher\" header. So it's a clear_to_pay document.\n\nWe need to output the result via the tool call \"classify_logistics_doc_type\". The input of the function: an array of objects each containing page_no and doc_type. For a single page, page_no 1, doc_type \"clear_to_pay\". We'll call the function accordingly."}}}, {"toolUse": {"toolUseId": "tooluse_tTLFYYYJSUG7LJi_h4r1ow", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "clear_to_pay"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2373, "outputTokens": 336, "totalTokens": 2709}, "metrics": {"latencyMs": 1390}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/c8e55f1e_5_clear_to_pay_1.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nRevised\nSAW-7116716\nREVISED\nAMBVB\nSAWYER NURSERY.\nTruckload Check-in Form\nClear-to-Pay Voucher\nLoad # 20196\nBroker\nPF2\nDate Processed 6/12/2023\nLoad Cost $3,869.-\nRacks Returned\n5.7\nSurcharge + $\n-\n-\n57 X$ 4, - = $ 228,\nDeductions - $\nRACKS\nAMOUNT\nTOTAL\nRacks\n+ $ 228,- $\n(Rack checks will now be sent to the\nbroker.)\nNet Cost $ 4,097.-\nBillable Shortages:\n(equipment missing with insufficient documentation)\nRacks: ( ) X $250.00 = $\nTotal Deductions\nFor Load:\nLoad Locks: ( ) x $25.00 = $\n$\nPallet Jack: ( ) X $500.00 = $\nLoad Straps: ( ) X $25.00 = $\nPaperwork & other Fines $\n(see attached documentation)\nManager Approval:\nmy\nNotes:\nAny fine not appealed within 3 business days is final. Appeals may be made in writing and faxed to (616) 669-7468\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}