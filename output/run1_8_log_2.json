{"classification_result": {"documents": [{"page_no": 1, "doc_type": "other"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "44068291-561c-4748-86a5-bd28b4df78ca", "Relationships": [{"Type": "CHILD", "Ids": ["7240481d-7e2d-4742-aa04-53b9f9c262a7", "fad59ada-b46b-4dfd-a236-acb44921892a", "5337627f-892c-47f5-a5f2-f296cc45dbef", "522bbc74-a493-40c7-9550-0c2639c0611a", "42d9dbb0-81dd-4fe4-b82a-28742d501b86", "64c60e83-eb20-4ea9-b24a-32ae301675c3", "8216121f-1a78-414d-890c-e0defbd4b252", "5969db1c-a6c1-4857-a074-b74700b9eef5", "a3b4afca-7554-4a07-99d7-484f8c55c3c5", "f8a418db-de65-4f34-9718-b8708269f23e", "478f2c7d-cc10-40fd-a220-780e8b68f83e", "662f28ac-c1b1-4c51-97c8-26bd4f366fcc", "2f780ba3-a101-48ac-b848-3823d0359289", "bcde57f4-715b-4ff0-8729-a155ef2abbcc", "d7ee84fb-932d-4659-afe2-d4e7fd3c1aa9", "686bb51b-565f-489a-88a8-98d363dc2747", "4849c5a6-4f08-4964-9855-d0294cd56aca", "b8021045-98f5-4e91-8b4f-42616745268e", "849ea92f-8682-4434-9f93-56e8dc09f651", "892621cf-5fef-4423-a3f0-bb4a1132f472", "7d8b7a35-54dc-4b31-a362-91c301edd35b", "2c5af4f1-b7b1-46b7-be41-fc4a3cb14edc", "93c5991e-91ac-4ca4-88c0-b0e7c15e1920", "33a038b7-0bd1-4761-b8cc-2f250378e85a", "6a625f16-5cc7-4f2c-ab62-96cb4ae3def3", "ec0b3495-e781-42ce-b506-32b4143030c0", "d9097e46-feb3-4c51-a6b5-d20ff2407dff", "c9ff9855-968f-415f-8186-0cd386cb5963", "84bb5995-744c-49b9-9fb2-280896bb2d06", "2eec8732-5b37-4fae-9262-acb3f991566a", "c4422cc8-e972-412b-a3c7-31ecfafdee5d", "633f0c46-df36-4c60-ac06-bbf9407e0458", "a594bc0e-5a4d-4d49-933e-5de68a4a6b0d", "c99c684c-adb0-44a3-ad65-02620ee8d6d0", "4d039a3a-9675-4f0a-897e-0405f10b5514", "ee77b28a-ca53-4d3e-9d33-16efeae1bc3f", "ece4b465-a1ef-45d5-a7fb-af86370a0528", "2185cff8-e284-4ecc-9e66-ab80d050c46d", "de199cb8-da6f-41fe-89f4-6a5db76b75de", "a2456cb0-d868-412b-8740-4d44e1231064", "563098b2-8418-410c-90ab-45d4657026de", "2afe7e20-38b4-4ced-a1cc-47453d0856c4", "2750365a-ae0f-4fb3-b017-784a7e63cfca", "fd3c4e53-710f-46ea-98d9-78fe7558838d", "b13351d2-bd36-4439-a251-6d03619ba7c0", "b967a1f4-a971-4cd3-9178-daa4150ae0b5", "faade09a-03a1-4753-93b5-d8ec519555b2", "7489493f-e470-4301-a6c3-ca050b54de15", "dee64e06-cad5-4dda-9f0d-67aa2113629d"]}]}, {"BlockType": "LINE", "Confidence": 99.892578125, "Text": "DATE: 19-Feb", "Geometry": {"BoundingBox": {"Width": 0.13736645877361298, "Height": 0.013747281394898891, "Left": 0.242889404296875, "Top": 0.10980033129453659}, "Polygon": [{"X": 0.2428894191980362, "Y": 0.10980033129453659}, {"X": 0.3802558481693268, "Y": 0.10980384796857834}, {"X": 0.38025587797164917, "Y": 0.12354760617017746}, {"X": 0.242889404296875, "Y": 0.1235441043972969}]}, "Id": "7240481d-7e2d-4742-aa04-53b9f9c262a7", "Relationships": [{"Type": "CHILD", "Ids": ["1114d610-ffa8-4ffa-bcd0-413df213d9ac", "444379a1-e625-4dab-a995-9862f1535aef"]}]}, {"BlockType": "LINE", "Confidence": 99.39453125, "Text": "UNIT #: 92644/283420", "Geometry": {"BoundingBox": {"Width": 0.220144584774971, "Height": 0.015344031155109406, "Left": 0.2269246131181717, "Top": 0.14510323107242584}, "Polygon": [{"X": 0.22692462801933289, "Y": 0.14510323107242584}, {"X": 0.4470691680908203, "Y": 0.14510881900787354}, {"X": 0.4470691978931427, "Y": 0.16044725477695465}, {"X": 0.2269246131181717, "Y": 0.16044169664382935}]}, "Id": "fad59ada-b46b-4dfd-a236-acb44921892a", "Relationships": [{"Type": "CHILD", "Ids": ["5d93c35f-b665-4212-8aa4-3c607e12ac0b", "f2936241-facc-4727-8695-842de5d20ad3", "2295f6fb-7231-4770-bade-c14ffd3eabfe"]}]}, {"BlockType": "LINE", "Confidence": 99.74061584472656, "Text": "Truck Ready Time: 5:00", "Geometry": {"BoundingBox": {"Width": 0.29648563265800476, "Height": 0.03428361937403679, "Left": 0.4656304717063904, "Top": 0.14309269189834595}, "Polygon": [{"X": 0.4656304717063904, "Y": 0.14309269189834595}, {"X": 0.7621158361434937, "Y": 0.14310021698474884}, {"X": 0.7621161341667175, "Y": 0.17737631499767303}, {"X": 0.46563059091567993, "Y": 0.1773688644170761}]}, "Id": "5337627f-892c-47f5-a5f2-f296cc45dbef", "Relationships": [{"Type": "CHILD", "Ids": ["130c8783-8a22-49e7-a9d9-80c35a16befc", "f31d283b-09e7-4cca-bca0-3c4dabf8df78", "21d965e7-7cb8-48bc-9bb6-4ee28db990a8", "66aa08fe-4c16-4dc6-90f8-50540dcb4680"]}]}, {"BlockType": "LINE", "Confidence": 99.96054077148438, "Text": "DRIVER: KARRA", "Geometry": {"BoundingBox": {"Width": 0.1634538322687149, "Height": 0.013153064996004105, "Left": 0.21907995641231537, "Top": 0.18155279755592346}, "Polygon": [{"X": 0.21907997131347656, "Y": 0.18155279755592346}, {"X": 0.3825337588787079, "Y": 0.18155691027641296}, {"X": 0.3825337886810303, "Y": 0.19470587372779846}, {"X": 0.21907995641231537, "Y": 0.19470177590847015}]}, "Id": "522bbc74-a493-40c7-9550-0c2639c0611a", "Relationships": [{"Type": "CHILD", "Ids": ["e8d3ada9-232d-45de-82ae-e56ebccd8aa0", "6283334f-e6c1-4f80-aa39-ae86fd0ae84d"]}]}, {"BlockType": "LINE", "Confidence": 99.46109771728516, "Text": "Truck Departure: 5:00", "Geometry": {"BoundingBox": {"Width": 0.29770052433013916, "Height": 0.0331379659473896, "Left": 0.4665309488773346, "Top": 0.17971383035182953}, "Polygon": [{"X": 0.4665309488773346, "Y": 0.17971383035182953}, {"X": 0.7642312049865723, "Y": 0.17972131073474884}, {"X": 0.7642314434051514, "Y": 0.21285179257392883}, {"X": 0.46653103828430176, "Y": 0.2128443866968155}]}, "Id": "42d9dbb0-81dd-4fe4-b82a-28742d501b86", "Relationships": [{"Type": "CHILD", "Ids": ["429bc6be-05e5-46a2-9284-5684d30ddab0", "40247fa4-d189-4ff1-b23d-48cc6f6fde2a", "cc5e2e41-3a12-42ce-9ce3-48573346ad29"]}]}, {"BlockType": "LINE", "Confidence": 99.75885009765625, "Text": "DEPARTURE TIME: 5am", "Geometry": {"BoundingBox": {"Width": 0.22939634323120117, "Height": 0.013709722086787224, "Left": 0.13103356957435608, "Top": 0.21678389608860016}, "Polygon": [{"X": 0.13103359937667847, "Y": 0.21678389608860016}, {"X": 0.36042988300323486, "Y": 0.2167896032333374}, {"X": 0.36042991280555725, "Y": 0.23049362003803253}, {"X": 0.13103356957435608, "Y": 0.23048794269561768}]}, "Id": "64c60e83-eb20-4ea9-b24a-32ae301675c3", "Relationships": [{"Type": "CHILD", "Ids": ["ae558f1a-34f4-4234-8cd6-6f52d4b5e1a9", "251cdb8b-7f92-4f6b-aa86-a3e3bbb86bdd", "e651addb-91e1-4d65-934d-4a9d4e611d85"]}]}, {"BlockType": "LINE", "Confidence": 99.69168090820312, "Text": "PICK-", "Geometry": {"BoundingBox": {"Width": 0.04785889387130737, "Height": 0.010938746854662895, "Left": 0.3347736895084381, "Top": 0.2478572279214859}, "Polygon": [{"X": 0.3347736895084381, "Y": 0.2478572279214859}, {"X": 0.3826325535774231, "Y": 0.24785840511322021}, {"X": 0.3826325535774231, "Y": 0.25879597663879395}, {"X": 0.3347736895084381, "Y": 0.2587948143482208}]}, "Id": "8216121f-1a78-414d-890c-e0defbd4b252", "Relationships": [{"Type": "CHILD", "Ids": ["114f0bfd-59b3-4848-9473-5a9b5f4ae1e2"]}]}, {"BlockType": "LINE", "Confidence": 99.931640625, "Text": "Dock Time", "Geometry": {"BoundingBox": {"Width": 0.09535559266805649, "Height": 0.01263468898832798, "Left": 0.8257697224617004, "Top": 0.2528533637523651}, "Polygon": [{"X": 0.8257697224617004, "Y": 0.2528533637523651}, {"X": 0.9211251735687256, "Y": 0.25285571813583374}, {"X": 0.9211253523826599, "Y": 0.26548805832862854}, {"X": 0.82576984167099, "Y": 0.2654857337474823}]}, "Id": "5969db1c-a6c1-4857-a074-b74700b9eef5", "Relationships": [{"Type": "CHILD", "Ids": ["3efe1baf-13c8-46ed-86ad-b4c64b7caebe", "26e3cba0-94a6-41cf-b2ec-f34f2ab876d4"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "CUSTOMER", "Geometry": {"BoundingBox": {"Width": 0.10067279636859894, "Height": 0.012615521438419819, "Left": 0.16310399770736694, "Top": 0.2676003575325012}, "Polygon": [{"X": 0.16310402750968933, "Y": 0.2676003575325012}, {"X": 0.2637767791748047, "Y": 0.267602801322937}, {"X": 0.2637767791748047, "Y": 0.280215859413147}, {"X": 0.16310399770736694, "Y": 0.2802134156227112}]}, "Id": "a3b4afca-7554-4a07-99d7-484f8c55c3c5", "Relationships": [{"Type": "CHILD", "Ids": ["b74c0c30-d311-480f-90bb-8746a89461a1"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "UP DEL", "Geometry": {"BoundingBox": {"Width": 0.09985791891813278, "Height": 0.012824908830225468, "Left": 0.3471435308456421, "Top": 0.26519033312797546}, "Polygon": [{"X": 0.3471435308456421, "Y": 0.26519033312797546}, {"X": 0.4470013976097107, "Y": 0.26519277691841125}, {"X": 0.44700145721435547, "Y": 0.27801522612571716}, {"X": 0.3471435308456421, "Y": 0.27801281213760376}]}, "Id": "f8a418db-de65-4f34-9718-b8708269f23e", "Relationships": [{"Type": "CHILD", "Ids": ["23cab94d-829c-43fe-86db-4e45bc14dee0", "6928bb43-9efb-484d-8328-e844d50073c7"]}]}, {"BlockType": "LINE", "Confidence": 99.79166412353516, "Text": "WORK ORDER #", "Geometry": {"BoundingBox": {"Width": 0.1428801417350769, "Height": 0.014172698371112347, "Left": 0.5687410831451416, "Top": 0.2624422609806061}, "Polygon": [{"X": 0.5687410831451416, "Y": 0.2624422609806061}, {"X": 0.711621105670929, "Y": 0.2624457776546478}, {"X": 0.7116212248802185, "Y": 0.2766149640083313}, {"X": 0.5687411427497864, "Y": 0.27661147713661194}]}, "Id": "478f2c7d-cc10-40fd-a220-780e8b68f83e", "Relationships": [{"Type": "CHILD", "Ids": ["7420959b-9adf-4b0d-82f5-bacce0f487e8", "3a6fd1d5-0f74-4696-9e63-a4acfe4c9bc6", "369a5713-6798-4407-bdf3-a2c7aea48d7e"]}]}, {"BlockType": "LINE", "Confidence": 60.5707893371582, "Text": "755h tempty", "Geometry": {"BoundingBox": {"Width": 0.25745174288749695, "Height": 0.031768668442964554, "Left": 0.34646841883659363, "Top": 0.29425063729286194}, "Polygon": [{"X": 0.34646841883659363, "Y": 0.29425063729286194}, {"X": 0.6039199829101562, "Y": 0.29425686597824097}, {"X": 0.6039201617240906, "Y": 0.326019287109375}, {"X": 0.346468448638916, "Y": 0.32601311802864075}]}, "Id": "662f28ac-c1b1-4c51-97c8-26bd4f366fcc", "Relationships": [{"Type": "CHILD", "Ids": ["04d98270-558f-4c47-b803-8164acfd1dd0", "d43c2281-c1ee-4b92-a28d-b2c23eae34ae"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "1230", "Geometry": {"BoundingBox": {"Width": 0.08113858103752136, "Height": 0.01772553101181984, "Left": 0.8646883368492126, "Top": 0.2991727888584137}, "Polygon": [{"X": 0.8646883368492126, "Y": 0.2991727888584137}, {"X": 0.9458267092704773, "Y": 0.2991747558116913}, {"X": 0.9458268880844116, "Y": 0.31689831614494324}, {"X": 0.864688515663147, "Y": 0.31689637899398804}]}, "Id": "2f780ba3-a101-48ac-b848-3823d0359289", "Relationships": [{"Type": "CHILD", "Ids": ["c19d23ba-33c6-4910-80b5-7d62a0557a56"]}]}, {"BlockType": "LINE", "Confidence": 99.6479721069336, "Text": "3) General Extrusion", "Geometry": {"BoundingBox": {"Width": 0.21305465698242188, "Height": 0.05147218704223633, "Left": 0.0483834333717823, "Top": 0.31561723351478577}, "Polygon": [{"X": 0.04838363081216812, "Y": 0.31561723351478577}, {"X": 0.2614381015300751, "Y": 0.31562235951423645}, {"X": 0.2614380717277527, "Y": 0.3670894205570221}, {"X": 0.0483834333717823, "Y": 0.3670843541622162}]}, "Id": "bcde57f4-715b-4ff0-8729-a155ef2abbcc", "Relationships": [{"Type": "CHILD", "Ids": ["9748b8d5-2f64-477b-9db5-8ca0b0a71f7a", "c9d093f3-91f9-4b54-aa00-f31566e8e4db", "6f2e9044-4124-4981-b836-059fce98c8fb"]}]}, {"BlockType": "LINE", "Confidence": 92.66522216796875, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.01097073033452034, "Height": 0.009613018482923508, "Left": 0.35543903708457947, "Top": 0.3360922932624817}, "Polygon": [{"X": 0.35543903708457947, "Y": 0.3360922932624817}, {"X": 0.3664097487926483, "Y": 0.3360925316810608}, {"X": 0.3664097785949707, "Y": 0.3457053005695343}, {"X": 0.35543903708457947, "Y": 0.3457050323486328}]}, "Id": "d7ee84fb-932d-4659-afe2-d4e7fd3c1aa9", "Relationships": [{"Type": "CHILD", "Ids": ["d5742992-71ec-477b-ad27-87946c1bcaa7"]}]}, {"BlockType": "LINE", "Confidence": 95.95553588867188, "Text": "X ***********", "Geometry": {"BoundingBox": {"Width": 0.13798269629478455, "Height": 0.012423958629369736, "Left": 0.42450857162475586, "Top": 0.3325625956058502}, "Polygon": [{"X": 0.42450857162475586, "Y": 0.3325625956058502}, {"X": 0.5624911785125732, "Y": 0.33256590366363525}, {"X": 0.562491238117218, "Y": 0.34498655796051025}, {"X": 0.42450860142707825, "Y": 0.3449832499027252}]}, "Id": "686bb51b-565f-489a-88a8-98d363dc2747", "Relationships": [{"Type": "CHILD", "Ids": ["b40f776d-06c1-4b0c-85f5-f483a2ef859c", "cec8c220-4bf0-4bfe-87c4-264a9eb10390"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "1305", "Geometry": {"BoundingBox": {"Width": 0.06859823316335678, "Height": 0.025042375549674034, "Left": 0.8614248633384705, "Top": 0.3425601124763489}, "Polygon": [{"X": 0.8614248633384705, "Y": 0.3425601124763489}, {"X": 0.9300227761268616, "Y": 0.3425617516040802}, {"X": 0.9300230741500854, "Y": 0.36760246753692627}, {"X": 0.8614251017570496, "Y": 0.36760085821151733}]}, "Id": "4849c5a6-4f08-4964-9855-d0294cd56aca", "Relationships": [{"Type": "CHILD", "Ids": ["9b224f00-c470-4672-a94c-7b03db8fab64"]}]}, {"BlockType": "LINE", "Confidence": 38.58457946777344, "Text": "x", "Geometry": {"BoundingBox": {"Width": 0.14296767115592957, "Height": 0.05359954759478569, "Left": 0.15001697838306427, "Top": 0.3605818748474121}, "Polygon": [{"X": 0.15001708269119263, "Y": 0.3605818748474121}, {"X": 0.29298463463783264, "Y": 0.3605852723121643}, {"X": 0.29298463463783264, "Y": 0.4141814112663269}, {"X": 0.15001697838306427, "Y": 0.4141780734062195}]}, "Id": "b8021045-98f5-4e91-8b4f-42616745268e", "Relationships": [{"Type": "CHILD", "Ids": ["a8e077af-e881-47a3-823c-dabc5ba2d770"]}]}, {"BlockType": "LINE", "Confidence": 36.12962341308594, "Text": "tsk", "Geometry": {"BoundingBox": {"Width": 0.08811807632446289, "Height": 0.048168737441301346, "Left": 0.3784377872943878, "Top": 0.41067251563072205}, "Polygon": [{"X": 0.3784377872943878, "Y": 0.41067251563072205}, {"X": 0.46655571460723877, "Y": 0.4106745719909668}, {"X": 0.4665558636188507, "Y": 0.4588412642478943}, {"X": 0.378437876701355, "Y": 0.4588392376899719}]}, "Id": "849ea92f-8682-4434-9f93-56e8dc09f651", "Relationships": [{"Type": "CHILD", "Ids": ["449382a5-0acb-411f-a027-154d41b4cc04"]}]}, {"BlockType": "LINE", "Confidence": 90.77068328857422, "Text": "2) Vari-Wall 5 SK Tube", "Geometry": {"BoundingBox": {"Width": 0.18958589434623718, "Height": 0.060547247529029846, "Left": 0.04938208684325218, "Top": 0.4360446333885193}, "Polygon": [{"X": 0.049382321536540985, "Y": 0.4360446333885193}, {"X": 0.23896798491477966, "Y": 0.43604904413223267}, {"X": 0.23896794021129608, "Y": 0.4965918958187103}, {"X": 0.04938208684325218, "Y": 0.4965875744819641}]}, "Id": "892621cf-5fef-4423-a3f0-bb4a1132f472", "Relationships": [{"Type": "CHILD", "Ids": ["fbec6635-4cc1-45dc-804f-456e8071fefa", "34cc9562-cf76-4f39-8ad9-0e3226a12291", "35e1cc24-6c77-4339-85ba-3510c6acd4d9", "c438b33d-7c6e-4176-8b04-ae9cfe35cd9e"]}]}, {"BlockType": "LINE", "Confidence": 87.46551513671875, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.01127795409411192, "Height": 0.009594044648110867, "Left": 0.35788819193840027, "Top": 0.4525049924850464}, "Polygon": [{"X": 0.35788819193840027, "Y": 0.4525049924850464}, {"X": 0.3691661059856415, "Y": 0.4525052607059479}, {"X": 0.36916613578796387, "Y": 0.4620990455150604}, {"X": 0.35788819193840027, "Y": 0.46209877729415894}]}, "Id": "7d8b7a35-54dc-4b31-a362-91c301edd35b", "Relationships": [{"Type": "CHILD", "Ids": ["ee73b7df-ba24-49c1-ad1e-bb01828c9309"]}]}, {"BlockType": "LINE", "Confidence": 94.29707336425781, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.010941325686872005, "Height": 0.009150148369371891, "Left": 0.42788010835647583, "Top": 0.45209845900535583}, "Polygon": [{"X": 0.42788010835647583, "Y": 0.45209845900535583}, {"X": 0.438821405172348, "Y": 0.4520987272262573}, {"X": 0.4388214349746704, "Y": 0.46124860644340515}, {"X": 0.4278801381587982, "Y": 0.46124836802482605}]}, "Id": "2c5af4f1-b7b1-46b7-be41-fc4a3cb14edc", "Relationships": [{"Type": "CHILD", "Ids": ["dd0606c1-47cf-486f-9f6b-c5bc228a0b61"]}]}, {"BlockType": "LINE", "Confidence": 87.66873931884766, "Text": "so 02115817 ***********", "Geometry": {"BoundingBox": {"Width": 0.195656880736351, "Height": 0.011820915155112743, "Left": 0.46836045384407043, "Top": 0.44820475578308105}, "Polygon": [{"X": 0.46836045384407043, "Y": 0.44820475578308105}, {"X": 0.6640172600746155, "Y": 0.448209285736084}, {"X": 0.6640173196792603, "Y": 0.4600256681442261}, {"X": 0.4683605134487152, "Y": 0.46002116799354553}]}, "Id": "93c5991e-91ac-4ca4-88c0-b0e7c15e1920", "Relationships": [{"Type": "CHILD", "Ids": ["dbd71ba0-a466-4a4a-b305-f974bb3cde06", "8188177e-5cac-4b5e-9408-3e1713552ec8", "d696d6ac-14cf-4595-a411-38233669bb69"]}]}, {"BlockType": "LINE", "Confidence": 49.876434326171875, "Text": "1000", "Geometry": {"BoundingBox": {"Width": 0.08367662876844406, "Height": 0.020543985068798065, "Left": 0.8451106548309326, "Top": 0.4423898756504059}, "Polygon": [{"X": 0.8451106548309326, "Y": 0.4423898756504059}, {"X": 0.9287870526313782, "Y": 0.4423918128013611}, {"X": 0.9287872314453125, "Y": 0.46293386816978455}, {"X": 0.8451108336448669, "Y": 0.46293193101882935}]}, "Id": "33a038b7-0bd1-4761-b8cc-2f250378e85a", "Relationships": [{"Type": "CHILD", "Ids": ["b1225a58-3f2b-45b0-8e77-48ad67cad150"]}]}, {"BlockType": "LINE", "Confidence": 62.83063888549805, "Text": "follow to", "Geometry": {"BoundingBox": {"Width": 0.2991342842578888, "Height": 0.05636539310216904, "Left": 0.507244884967804, "Top": 0.462825208902359}, "Polygon": [{"X": 0.507244884967804, "Y": 0.462825208902359}, {"X": 0.8063786625862122, "Y": 0.46283209323883057}, {"X": 0.8063791990280151, "Y": 0.5191906094551086}, {"X": 0.5072451233863831, "Y": 0.519183874130249}]}, "Id": "6a625f16-5cc7-4f2c-ab62-96cb4ae3def3", "Relationships": [{"Type": "CHILD", "Ids": ["a2f08c70-c456-47c4-a6ed-dfd3bc0b39b7", "17c1c2b9-8548-440a-9dbf-2d535109babb"]}]}, {"BlockType": "LINE", "Confidence": 98.583984375, "Text": "0936", "Geometry": {"BoundingBox": {"Width": 0.074602872133255, "Height": 0.017255874350667, "Left": 0.8730655312538147, "Top": 0.46400681138038635}, "Polygon": [{"X": 0.8730655312538147, "Y": 0.46400681138038635}, {"X": 0.947668194770813, "Y": 0.46400853991508484}, {"X": 0.9476684331893921, "Y": 0.4812626838684082}, {"X": 0.873065710067749, "Y": 0.4812609851360321}]}, "Id": "ec0b3495-e781-42ce-b506-32b4143030c0", "Relationships": [{"Type": "CHILD", "Ids": ["fd2e0518-9a91-4387-9897-74181d7bc02d"]}]}, {"BlockType": "LINE", "Confidence": 73.31373596191406, "Text": "1015", "Geometry": {"BoundingBox": {"Width": 0.09938754886388779, "Height": 0.024359211325645447, "Left": 0.8383670449256897, "Top": 0.48931023478507996}, "Polygon": [{"X": 0.8383670449256897, "Y": 0.48931023478507996}, {"X": 0.9377543330192566, "Y": 0.4893124997615814}, {"X": 0.9377545714378357, "Y": 0.5136694312095642}, {"X": 0.8383672833442688, "Y": 0.5136672258377075}]}, "Id": "d9097e46-feb3-4c51-a6b5-d20ff2407dff", "Relationships": [{"Type": "CHILD", "Ids": ["7d3d8f12-866f-4df3-bd2b-95b2759b8aa8"]}]}, {"BlockType": "LINE", "Confidence": 93.0676040649414, "Text": "1)", "Geometry": {"BoundingBox": {"Width": 0.02804512158036232, "Height": 0.04745045676827431, "Left": 0.06031886115670204, "Top": 0.5581309795379639}, "Polygon": [{"X": 0.06031903252005577, "Y": 0.5581309795379639}, {"X": 0.08836398273706436, "Y": 0.5581316351890564}, {"X": 0.08836383372545242, "Y": 0.6055814623832703}, {"X": 0.06031886115670204, "Y": 0.6055808067321777}]}, "Id": "c9ff9855-968f-415f-8186-0cd386cb5963", "Relationships": [{"Type": "CHILD", "Ids": ["e5249f01-52da-4097-8fe9-5e6613070182"]}]}, {"BlockType": "LINE", "Confidence": 63.26906204223633, "Text": "In Inlandplt plt", "Geometry": {"BoundingBox": {"Width": 0.09732144325971603, "Height": 0.015106776729226112, "Left": 0.12440148741006851, "Top": 0.5579501390457153}, "Polygon": [{"X": 0.1244015246629715, "Y": 0.5579501390457153}, {"X": 0.22172293066978455, "Y": 0.557952344417572}, {"X": 0.22172291576862335, "Y": 0.5730569362640381}, {"X": 0.12440148741006851, "Y": 0.5730547904968262}]}, "Id": "84bb5995-744c-49b9-9fb2-280896bb2d06", "Relationships": [{"Type": "CHILD", "Ids": ["374c0bf8-9b2f-4f28-bcea-0472120acaa1", "1348d37c-c956-4d7b-8ab0-d1c0d0253175", "5434aee3-9b55-4497-8960-d914d7a0211c"]}]}, {"BlockType": "LINE", "Confidence": 96.89147186279297, "Text": "<PERSON> -", "Geometry": {"BoundingBox": {"Width": 0.146476611495018, "Height": 0.013580741360783577, "Left": 0.11173728853464127, "Top": 0.5699862837791443}, "Polygon": [{"X": 0.11173732578754425, "Y": 0.5699862837791443}, {"X": 0.25821390748023987, "Y": 0.5699895024299622}, {"X": 0.2582138776779175, "Y": 0.5835670232772827}, {"X": 0.11173728853464127, "Y": 0.5835638046264648}]}, "Id": "2eec8732-5b37-4fae-9262-acb3f991566a", "Relationships": [{"Type": "CHILD", "Ids": ["02e47904-ac46-4909-a7c2-37992f85c9a0", "d110ef96-6f3a-4aac-ab65-b20d4dd78f6f", "4a7ad622-99ca-4b3d-b025-0426c03b9aa7"]}]}, {"BlockType": "LINE", "Confidence": 88.34123229980469, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.011289657093584538, "Height": 0.00977187417447567, "Left": 0.3594965636730194, "Top": 0.580009400844574}, "Polygon": [{"X": 0.3594965636730194, "Y": 0.580009400844574}, {"X": 0.370786190032959, "Y": 0.5800096988677979}, {"X": 0.37078621983528137, "Y": 0.5897812843322754}, {"X": 0.3594965636730194, "Y": 0.5897810459136963}]}, "Id": "c4422cc8-e972-412b-a3c7-31ecfafdee5d", "Relationships": [{"Type": "CHILD", "Ids": ["73771385-98d9-4e94-aa03-d415087eeee9"]}]}, {"BlockType": "LINE", "Confidence": 89.63150024414062, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.011617789044976234, "Height": 0.009949511848390102, "Left": 0.42950552701950073, "Top": 0.5791550278663635}, "Polygon": [{"X": 0.42950552701950073, "Y": 0.5791550278663635}, {"X": 0.4411233067512512, "Y": 0.5791552662849426}, {"X": 0.4411233067512512, "Y": 0.5891045331954956}, {"X": 0.4295055568218231, "Y": 0.5891042947769165}]}, "Id": "633f0c46-df36-4c60-ac06-bbf9407e0458", "Relationships": [{"Type": "CHILD", "Ids": ["8a9eb1f4-bf90-4bb7-a787-b31438ab791c"]}]}, {"BlockType": "LINE", "Confidence": 84.64356994628906, "Text": "so 02115450 *********** so 0211626", "Geometry": {"BoundingBox": {"Width": 0.293038547039032, "Height": 0.01309666596353054, "Left": 0.47128432989120483, "Top": 0.5739253759384155}, "Polygon": [{"X": 0.47128432989120483, "Y": 0.5739253759384155}, {"X": 0.764322817325592, "Y": 0.5739319324493408}, {"X": 0.7643228769302368, "Y": 0.587022066116333}, {"X": 0.4712843894958496, "Y": 0.5870155692100525}]}, "Id": "a594bc0e-5a4d-4d49-933e-5de68a4a6b0d", "Relationships": [{"Type": "CHILD", "Ids": ["7f9d055f-2462-4387-88f7-c2889fc8b597", "a1ac57ac-4b96-4fec-ab4a-c0a5725781ba", "fa476933-d68b-42a6-b72d-027efd97b983", "0cd611aa-4962-4611-ba0f-3a9057781ee7", "c40c7296-218c-44de-9531-dc0b80bfe301"]}]}, {"BlockType": "LINE", "Confidence": 99.69486999511719, "Text": "Metamora", "Geometry": {"BoundingBox": {"Width": 0.0857909545302391, "Height": 0.012538857758045197, "Left": 0.1119719073176384, "Top": 0.5901594161987305}, "Polygon": [{"X": 0.11197194457054138, "Y": 0.5901594161987305}, {"X": 0.1977628618478775, "Y": 0.5901613235473633}, {"X": 0.1977628469467163, "Y": 0.6026982665061951}, {"X": 0.1119719073176384, "Y": 0.602696418762207}]}, "Id": "c99c684c-adb0-44a3-ad65-02620ee8d6d0", "Relationships": [{"Type": "CHILD", "Ids": ["8301cd2b-da54-47b3-be56-c7855caa6ce9"]}]}, {"BlockType": "LINE", "Confidence": 87.93367004394531, "Text": "50-2116053", "Geometry": {"BoundingBox": {"Width": 0.21994942426681519, "Height": 0.023283783346414566, "Left": 0.47301778197288513, "Top": 0.5878908038139343}, "Polygon": [{"X": 0.47301778197288513, "Y": 0.5878908038139343}, {"X": 0.6929670572280884, "Y": 0.5878956913948059}, {"X": 0.6929671764373779, "Y": 0.6111745834350586}, {"X": 0.4730178415775299, "Y": 0.6111698150634766}]}, "Id": "4d039a3a-9675-4f0a-897e-0405f10b5514", "Relationships": [{"Type": "CHILD", "Ids": ["fd39f73c-b53f-4d61-acd8-0693618e5a48"]}]}, {"BlockType": "LINE", "Confidence": 97.95121002197266, "Text": "0540", "Geometry": {"BoundingBox": {"Width": 0.07629060745239258, "Height": 0.01622743532061577, "Left": 0.8599780201911926, "Top": 0.5859130620956421}, "Polygon": [{"X": 0.8599780201911926, "Y": 0.5859130620956421}, {"X": 0.9362684488296509, "Y": 0.5859147310256958}, {"X": 0.9362686276435852, "Y": 0.602140486240387}, {"X": 0.859978199005127, "Y": 0.6021388173103333}]}, "Id": "ee77b28a-ca53-4d3e-9d33-16efeae1bc3f", "Relationships": [{"Type": "CHILD", "Ids": ["e937094a-3fee-45df-954e-163503723719"]}]}, {"BlockType": "LINE", "Confidence": 52.90119171142578, "Text": "1 sm box", "Geometry": {"BoundingBox": {"Width": 0.117005355656147, "Height": 0.029255716130137444, "Left": 0.125104159116745, "Top": 0.6088439226150513}, "Polygon": [{"X": 0.12510423362255096, "Y": 0.6088439226150513}, {"X": 0.2421095222234726, "Y": 0.6088464856147766}, {"X": 0.2421095073223114, "Y": 0.6380996704101562}, {"X": 0.125104159116745, "Y": 0.6380971074104309}]}, "Id": "ece4b465-a1ef-45d5-a7fb-af86370a0528", "Relationships": [{"Type": "CHILD", "Ids": ["165194f0-817b-46a7-8fdb-7dd929ad98d2"]}]}, {"BlockType": "LINE", "Confidence": 99.521484375, "Text": "0600", "Geometry": {"BoundingBox": {"Width": 0.08056437224149704, "Height": 0.017424000427126884, "Left": 0.8577093482017517, "Top": 0.6094968914985657}, "Polygon": [{"X": 0.8577093482017517, "Y": 0.6094968914985657}, {"X": 0.938273549079895, "Y": 0.6094986200332642}, {"X": 0.9382737278938293, "Y": 0.6269208788871765}, {"X": 0.857709527015686, "Y": 0.6269190907478333}]}, "Id": "2185cff8-e284-4ecc-9e66-ab80d050c46d", "Relationships": [{"Type": "CHILD", "Ids": ["ed452a8c-441e-4184-a86c-247613a3448f"]}]}, {"BlockType": "LINE", "Confidence": 68.00103759765625, "Text": "Cempties aspots", "Geometry": {"BoundingBox": {"Width": 0.3441469073295593, "Height": 0.04259765148162842, "Left": 0.39649271965026855, "Top": 0.6148432493209839}, "Polygon": [{"X": 0.39649271965026855, "Y": 0.6148432493209839}, {"X": 0.7406392693519592, "Y": 0.6148508191108704}, {"X": 0.7406396269798279, "Y": 0.6574409008026123}, {"X": 0.3964928090572357, "Y": 0.6574335098266602}]}, "Id": "de199cb8-da6f-41fe-89f4-6a5db76b75de", "Relationships": [{"Type": "CHILD", "Ids": ["537c878e-3dfe-4e33-b7dc-1527baf82109", "2e783667-3c4c-47c6-b9c6-667ca4ff76b4"]}]}, {"BlockType": "LINE", "Confidence": 98.31871795654297, "Text": "4", "Geometry": {"BoundingBox": {"Width": 0.026098841801285744, "Height": 0.04726731777191162, "Left": 0.0359298475086689, "Top": 0.6634374260902405}, "Polygon": [{"X": 0.03593004122376442, "Y": 0.6634374260902405}, {"X": 0.06202869117259979, "Y": 0.6634379625320435}, {"X": 0.06202851980924606, "Y": 0.7107047438621521}, {"X": 0.0359298475086689, "Y": 0.7107042074203491}]}, "Id": "a2456cb0-d868-412b-8740-4d44e1231064", "Relationships": [{"Type": "CHILD", "Ids": ["6babebda-52c4-4a8e-b19d-a15f0257e656"]}]}, {"BlockType": "LINE", "Confidence": 81.10152435302734, "Text": "PeNNex", "Geometry": {"BoundingBox": {"Width": 0.17136432230472565, "Height": 0.03317827731370926, "Left": 0.1023721843957901, "Top": 0.6689649820327759}, "Polygon": [{"X": 0.10237228125333786, "Y": 0.6689649820327759}, {"X": 0.27373650670051575, "Y": 0.668968677520752}, {"X": 0.27373650670051575, "Y": 0.7021432518959045}, {"X": 0.1023721843957901, "Y": 0.7021396160125732}]}, "Id": "563098b2-8418-410c-90ab-45d4657026de", "Relationships": [{"Type": "CHILD", "Ids": ["9cb902c7-6c2d-4afc-9193-6f10c4849d1e"]}]}, {"BlockType": "LINE", "Confidence": 88.56365966796875, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.019124368205666542, "Height": 0.025946438312530518, "Left": 0.35739386081695557, "Top": 0.6663710474967957}, "Polygon": [{"X": 0.35739386081695557, "Y": 0.6663710474967957}, {"X": 0.376518189907074, "Y": 0.6663714647293091}, {"X": 0.37651821970939636, "Y": 0.6923174858093262}, {"X": 0.35739389061927795, "Y": 0.6923171281814575}]}, "Id": "2afe7e20-38b4-4ced-a1cc-47453d0856c4", "Relationships": [{"Type": "CHILD", "Ids": ["07e0d73f-93d8-4480-bd1b-5d9d9cb38e72"]}]}, {"BlockType": "LINE", "Confidence": 67.73497772216797, "Text": "Pickup", "Geometry": {"BoundingBox": {"Width": 0.17593133449554443, "Height": 0.027651553973555565, "Left": 0.45222336053848267, "Top": 0.6548234224319458}, "Polygon": [{"X": 0.45222336053848267, "Y": 0.6548234224319458}, {"X": 0.6281545162200928, "Y": 0.6548272371292114}, {"X": 0.6281546950340271, "Y": 0.6824749708175659}, {"X": 0.45222344994544983, "Y": 0.6824712157249451}]}, "Id": "2750365a-ae0f-4fb3-b017-784a7e63cfca", "Relationships": [{"Type": "CHILD", "Ids": ["8d8c54ad-c490-4036-880c-023efb468364"]}]}, {"BlockType": "LINE", "Confidence": 38.002681732177734, "Text": "$ HYS", "Geometry": {"BoundingBox": {"Width": 0.12047018855810165, "Height": 0.03520325571298599, "Left": 0.6848227977752686, "Top": 0.6698034405708313}, "Polygon": [{"X": 0.6848227977752686, "Y": 0.6698034405708313}, {"X": 0.8052926659584045, "Y": 0.6698060035705566}, {"X": 0.8052930235862732, "Y": 0.7050066590309143}, {"X": 0.6848230957984924, "Y": 0.705004096031189}]}, "Id": "fd3c4e53-710f-46ea-98d9-78fe7558838d", "Relationships": [{"Type": "CHILD", "Ids": ["16debd62-ce99-4dbf-99f5-dd84e1ca9c27", "e83c6fff-5812-4cda-a932-022a412d1115"]}]}, {"BlockType": "LINE", "Confidence": 74.37659454345703, "Text": "20sk", "Geometry": {"BoundingBox": {"Width": 0.11407884955406189, "Height": 0.030752021819353104, "Left": 0.14665275812149048, "Top": 0.719403088092804}, "Polygon": [{"X": 0.14665281772613525, "Y": 0.719403088092804}, {"X": 0.26073160767555237, "Y": 0.719405472278595}, {"X": 0.26073160767555237, "Y": 0.7501550912857056}, {"X": 0.14665275812149048, "Y": 0.7501527070999146}]}, "Id": "b13351d2-bd36-4439-a251-6d03619ba7c0", "Relationships": [{"Type": "CHILD", "Ids": ["bad855df-1b97-4cc9-8803-9ab92e64def9"]}]}, {"BlockType": "LINE", "Confidence": 23.12111473083496, "Text": "DSI", "Geometry": {"BoundingBox": {"Width": 0.07473167777061462, "Height": 0.0362405925989151, "Left": 0.7319081425666809, "Top": 0.6990905404090881}, "Polygon": [{"X": 0.7319081425666809, "Y": 0.6990905404090881}, {"X": 0.8066394925117493, "Y": 0.6990921497344971}, {"X": 0.8066398501396179, "Y": 0.735331118106842}, {"X": 0.7319084405899048, "Y": 0.7353295683860779}]}, "Id": "b967a1f4-a971-4cd3-9178-daa4150ae0b5", "Relationships": [{"Type": "CHILD", "Ids": ["578bfa0b-be1d-49b9-836e-9b33639970dd"]}]}, {"BlockType": "LINE", "Confidence": 55.92494583129883, "Text": "10 40", "Geometry": {"BoundingBox": {"Width": 0.07327038049697876, "Height": 0.024272389709949493, "Left": 0.6715286374092102, "Top": 0.74648118019104}, "Polygon": [{"X": 0.6715286374092102, "Y": 0.74648118019104}, {"X": 0.7447987794876099, "Y": 0.7464827299118042}, {"X": 0.744799017906189, "Y": 0.7707535624504089}, {"X": 0.6715287566184998, "Y": 0.7707520723342896}]}, "Id": "faade09a-03a1-4753-93b5-d8ec519555b2", "Relationships": [{"Type": "CHILD", "Ids": ["000cb658-0893-4042-980b-f28239951ae9"]}]}, {"BlockType": "LINE", "Confidence": 99.6484375, "Text": "1145", "Geometry": {"BoundingBox": {"Width": 0.06058167666196823, "Height": 0.02555784583091736, "Left": 0.6812065839767456, "Top": 0.7851274013519287}, "Polygon": [{"X": 0.6812065839767456, "Y": 0.7851274013519287}, {"X": 0.7417880296707153, "Y": 0.785128653049469}, {"X": 0.7417882680892944, "Y": 0.8106852173805237}, {"X": 0.6812067627906799, "Y": 0.8106839656829834}]}, "Id": "7489493f-e470-4301-a6c3-ca050b54de15", "Relationships": [{"Type": "CHILD", "Ids": ["9303bc26-38ca-4d86-a667-974a4835205f"]}]}, {"BlockType": "LINE", "Confidence": 99.84295654296875, "Text": "YOUNGSTOWN", "Geometry": {"BoundingBox": {"Width": 0.1360902637243271, "Height": 0.014133377000689507, "Left": 0.4435255229473114, "Top": 0.9436905980110168}, "Polygon": [{"X": 0.4435255229473114, "Y": 0.9436905980110168}, {"X": 0.5796157121658325, "Y": 0.943693220615387}, {"X": 0.5796157717704773, "Y": 0.9578239321708679}, {"X": 0.4435255527496338, "Y": 0.9578213095664978}]}, "Id": "dee64e06-cad5-4dda-9f0d-67aa2113629d", "Relationships": [{"Type": "CHILD", "Ids": ["98a7de6c-70da-48d2-97be-5f1e3c537c4d"]}]}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054820816963911057, "Height": 0.012857222929596901, "Left": 0.242889404296875, "Top": 0.11068827658891678}, "Polygon": [{"X": 0.2428894191980362, "Y": 0.11068827658891678}, {"X": 0.29771021008491516, "Y": 0.1106896847486496}, {"X": 0.29771021008491516, "Y": 0.12354550510644913}, {"X": 0.242889404296875, "Y": 0.1235441043972969}]}, "Id": "1114d610-ffa8-4ffa-bcd0-413df213d9ac"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "19-Feb", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059968773275613785, "Height": 0.013181901536881924, "Left": 0.3202871084213257, "Top": 0.10980231314897537}, "Polygon": [{"X": 0.3202871084213257, "Y": 0.10980231314897537}, {"X": 0.3802558481693268, "Y": 0.10980384796857834}, {"X": 0.38025587797164917, "Y": 0.12298421561717987}, {"X": 0.3202871084213257, "Y": 0.1229826807975769}]}, "Id": "444379a1-e625-4dab-a995-9862f1535aef"}, {"BlockType": "WORD", "Confidence": 99.013671875, "Text": "UNIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04843570664525032, "Height": 0.01270240917801857, "Left": 0.2269246131181717, "Top": 0.14646433293819427}, "Polygon": [{"X": 0.22692462801933289, "Y": 0.14646433293819427}, {"X": 0.2753603160381317, "Y": 0.14646556973457336}, {"X": 0.2753603160381317, "Y": 0.15916675329208374}, {"X": 0.2269246131181717, "Y": 0.15916551649570465}]}, "Id": "5d93c35f-b665-4212-8aa4-3c607e12ac0b"}, {"BlockType": "WORD", "Confidence": 99.21875, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020779017359018326, "Height": 0.01265506912022829, "Left": 0.2779847979545593, "Top": 0.14623339474201202}, "Polygon": [{"X": 0.2779847979545593, "Y": 0.14623339474201202}, {"X": 0.29876381158828735, "Y": 0.1462339162826538}, {"X": 0.29876381158828735, "Y": 0.15888845920562744}, {"X": 0.2779847979545593, "Y": 0.15888793766498566}]}, "Id": "f2936241-facc-4727-8695-842de5d20ad3"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "92644/283420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1269402801990509, "Height": 0.015341666527092457, "Left": 0.3201289176940918, "Top": 0.14510560035705566}, "Polygon": [{"X": 0.3201289176940918, "Y": 0.14510560035705566}, {"X": 0.4470691680908203, "Y": 0.14510881900787354}, {"X": 0.4470691978931427, "Y": 0.16044725477695465}, {"X": 0.3201289176940918, "Y": 0.16044405102729797}]}, "Id": "2295f6fb-7231-4770-bade-c14ffd3eabfe"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049054764211177826, "Height": 0.01320650428533554, "Left": 0.46563053131103516, "Top": 0.16198886930942535}, "Polygon": [{"X": 0.46563053131103516, "Y": 0.16198886930942535}, {"X": 0.5146852135658264, "Y": 0.16199010610580444}, {"X": 0.5146852731704712, "Y": 0.1751953810453415}, {"X": 0.46563059091567993, "Y": 0.1751941442489624}]}, "Id": "130c8783-8a22-49e7-a9d9-80c35a16befc"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Ready", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05667145922780037, "Height": 0.016442129388451576, "Left": 0.5181140303611755, "Top": 0.16092947125434875}, "Polygon": [{"X": 0.5181140303611755, "Y": 0.16092947125434875}, {"X": 0.5747854113578796, "Y": 0.16093090176582336}, {"X": 0.5747855305671692, "Y": 0.17737160623073578}, {"X": 0.5181141495704651, "Y": 0.17737017571926117}]}, "Id": "f31d283b-09e7-4cca-bca0-3c4dabf8df78"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049083780497312546, "Height": 0.012729278765618801, "Left": 0.5790734887123108, "Top": 0.16137921810150146}, "Polygon": [{"X": 0.5790734887123108, "Y": 0.16137921810150146}, {"X": 0.6281571984291077, "Y": 0.16138045489788055}, {"X": 0.6281572580337524, "Y": 0.17410849034786224}, {"X": 0.5790735483169556, "Y": 0.17410726845264435}]}, "Id": "21d965e7-7cb8-48bc-9bb6-4ee28db990a8"}, {"BlockType": "WORD", "Confidence": 99.02104949951172, "Text": "5:00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10507532209157944, "Height": 0.03219890594482422, "Left": 0.6570407748222351, "Top": 0.14309754967689514}, "Polygon": [{"X": 0.6570407748222351, "Y": 0.14309754967689514}, {"X": 0.7621158361434937, "Y": 0.14310021698474884}, {"X": 0.7621160745620728, "Y": 0.17529645562171936}, {"X": 0.6570409536361694, "Y": 0.17529381811618805}]}, "Id": "66aa08fe-4c16-4dc6-90f8-50540dcb4680"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "DRIVER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07543918490409851, "Height": 0.012693328782916069, "Left": 0.21907995641231537, "Top": 0.18201033771038055}, "Polygon": [{"X": 0.21907997131347656, "Y": 0.18201033771038055}, {"X": 0.2945191264152527, "Y": 0.18201223015785217}, {"X": 0.2945191562175751, "Y": 0.19470366835594177}, {"X": 0.21907995641231537, "Y": 0.19470177590847015}]}, "Id": "e8d3ada9-232d-45de-82ae-e56ebccd8aa0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "KARRA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060760632157325745, "Height": 0.01273215003311634, "Left": 0.32177314162254333, "Top": 0.18155537545681}, "Polygon": [{"X": 0.32177314162254333, "Y": 0.18155537545681}, {"X": 0.3825337588787079, "Y": 0.18155691027641296}, {"X": 0.3825337886810303, "Y": 0.1942875236272812}, {"X": 0.3217731714248657, "Y": 0.19428600370883942}]}, "Id": "6283334f-e6c1-4f80-aa39-ae86fd0ae84d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04893266782164574, "Height": 0.013104367069900036, "Left": 0.46653100848197937, "Top": 0.19756092131137848}, "Polygon": [{"X": 0.46653100848197937, "Y": 0.19756092131137848}, {"X": 0.5154635906219482, "Y": 0.19756214320659637}, {"X": 0.515463650226593, "Y": 0.2106652855873108}, {"X": 0.46653103828430176, "Y": 0.2106640785932541}]}, "Id": "429bc6be-05e5-46a2-9284-5684d30ddab0"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Departure:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09423540532588959, "Height": 0.01560566108673811, "Left": 0.5189868211746216, "Top": 0.1972423791885376}, "Polygon": [{"X": 0.5189868211746216, "Y": 0.1972423791885376}, {"X": 0.6132221221923828, "Y": 0.19724473357200623}, {"X": 0.6132222414016724, "Y": 0.21284803748130798}, {"X": 0.5189868807792664, "Y": 0.21284569799900055}]}, "Id": "40247fa4-d189-4ff1-b23d-48cc6f6fde2a"}, {"BlockType": "WORD", "Confidence": 98.43291473388672, "Text": "5:00", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10562808811664581, "Height": 0.027190543711185455, "Left": 0.658603310585022, "Top": 0.17971865832805634}, "Polygon": [{"X": 0.658603310585022, "Y": 0.17971865832805634}, {"X": 0.7642312049865723, "Y": 0.17972131073474884}, {"X": 0.7642314434051514, "Y": 0.2069091945886612}, {"X": 0.6586034893989563, "Y": 0.20690657198429108}]}, "Id": "cc5e2e41-3a12-42ce-9ce3-48573346ad29"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "DEPARTURE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1091952845454216, "Height": 0.01282552070915699, "Left": 0.13103356957435608, "Top": 0.21766513586044312}, "Polygon": [{"X": 0.13103359937667847, "Y": 0.21766513586044312}, {"X": 0.24022884666919708, "Y": 0.2176678478717804}, {"X": 0.24022884666919708, "Y": 0.23049065470695496}, {"X": 0.13103356957435608, "Y": 0.23048794269561768}]}, "Id": "ae558f1a-34f4-4234-8cd6-6f52d4b5e1a9"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "TIME:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049798086285591125, "Height": 0.012448420748114586, "Left": 0.24547803401947021, "Top": 0.21741876006126404}, "Polygon": [{"X": 0.2454780489206314, "Y": 0.21741876006126404}, {"X": 0.29527613520622253, "Y": 0.21741999685764313}, {"X": 0.29527613520622253, "Y": 0.22986717522144318}, {"X": 0.24547803401947021, "Y": 0.22986595332622528}]}, "Id": "251cdb8b-7f92-4f6b-aa86-a3e3bbb86bdd"}, {"BlockType": "WORD", "Confidence": 99.423828125, "Text": "5am", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0369638092815876, "Height": 0.012884262949228287, "Left": 0.32346609234809875, "Top": 0.21678867936134338}, "Polygon": [{"X": 0.32346609234809875, "Y": 0.21678867936134338}, {"X": 0.36042988300323486, "Y": 0.2167896032333374}, {"X": 0.36042991280555725, "Y": 0.22967293858528137}, {"X": 0.32346612215042114, "Y": 0.22967202961444855}]}, "Id": "e651addb-91e1-4d65-934d-4a9d4e611d85"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "PICK-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04785889387130737, "Height": 0.010938746854662895, "Left": 0.3347736895084381, "Top": 0.2478572279214859}, "Polygon": [{"X": 0.3347736895084381, "Y": 0.2478572279214859}, {"X": 0.3826325535774231, "Y": 0.24785840511322021}, {"X": 0.3826325535774231, "Y": 0.25879597663879395}, {"X": 0.3347736895084381, "Y": 0.2587948143482208}]}, "Id": "114f0bfd-59b3-4848-9473-5a9b5f4ae1e2"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04563652351498604, "Height": 0.0120528070256114, "Left": 0.8257697224617004, "Top": 0.25343403220176697}, "Polygon": [{"X": 0.8257697224617004, "Y": 0.25343403220176697}, {"X": 0.8714061379432678, "Y": 0.2534351646900177}, {"X": 0.8714062571525574, "Y": 0.26548683643341064}, {"X": 0.82576984167099, "Y": 0.2654857337474823}]}, "Id": "3efe1baf-13c8-46ed-86ad-b4c64b7caebe"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046147432178258896, "Height": 0.011875751428306103, "Left": 0.8749778866767883, "Top": 0.252854585647583}, "Polygon": [{"X": 0.8749778866767883, "Y": 0.252854585647583}, {"X": 0.9211251735687256, "Y": 0.25285571813583374}, {"X": 0.9211252927780151, "Y": 0.2647303342819214}, {"X": 0.8749780058860779, "Y": 0.26472920179367065}]}, "Id": "26e3cba0-94a6-41cf-b2ec-f34f2ab876d4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CUSTOMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10067279636859894, "Height": 0.012615521438419819, "Left": 0.16310399770736694, "Top": 0.2676003575325012}, "Polygon": [{"X": 0.16310402750968933, "Y": 0.2676003575325012}, {"X": 0.2637767791748047, "Y": 0.267602801322937}, {"X": 0.2637767791748047, "Y": 0.280215859413147}, {"X": 0.16310399770736694, "Y": 0.2802134156227112}]}, "Id": "b74c0c30-d311-480f-90bb-8746a89461a1"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "UP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023942895233631134, "Height": 0.012423768639564514, "Left": 0.3471435308456421, "Top": 0.2655896246433258}, "Polygon": [{"X": 0.3471435308456421, "Y": 0.2655896246433258}, {"X": 0.37108638882637024, "Y": 0.26559019088745117}, {"X": 0.3710864186286926, "Y": 0.2780133783817291}, {"X": 0.3471435308456421, "Y": 0.27801281213760376}]}, "Id": "23cab94d-829c-43fe-86db-4e45bc14dee0"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "DEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03438418731093407, "Height": 0.012267366982996464, "Left": 0.4126172661781311, "Top": 0.2651919424533844}, "Polygon": [{"X": 0.4126172661781311, "Y": 0.2651919424533844}, {"X": 0.4470013976097107, "Y": 0.26519277691841125}, {"X": 0.44700145721435547, "Y": 0.2774592936038971}, {"X": 0.4126172959804535, "Y": 0.27745845913887024}]}, "Id": "6928bb43-9efb-484d-8328-e844d50073c7"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "WORK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056625280529260635, "Height": 0.012728850357234478, "Left": 0.5687410831451416, "Top": 0.2638840079307556}, "Polygon": [{"X": 0.5687410831451416, "Y": 0.2638840079307556}, {"X": 0.6253662705421448, "Y": 0.26388540863990784}, {"X": 0.6253663301467896, "Y": 0.27661287784576416}, {"X": 0.5687411427497864, "Y": 0.27661147713661194}]}, "Id": "7420959b-9adf-4b0d-82f5-bacce0f487e8"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "ORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06312157213687897, "Height": 0.012895585037767887, "Left": 0.6303342580795288, "Top": 0.26302003860473633}, "Polygon": [{"X": 0.6303342580795288, "Y": 0.26302003860473633}, {"X": 0.6934557557106018, "Y": 0.2630215883255005}, {"X": 0.6934558153152466, "Y": 0.27591562271118164}, {"X": 0.6303343176841736, "Y": 0.27591410279273987}]}, "Id": "3a6fd1d5-0f74-4696-9e63-a4acfe4c9bc6"}, {"BlockType": "WORD", "Confidence": 99.541015625, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01235488522797823, "Height": 0.012175197713077068, "Left": 0.6992663145065308, "Top": 0.26244547963142395}, "Polygon": [{"X": 0.6992663145065308, "Y": 0.26244547963142395}, {"X": 0.711621105670929, "Y": 0.2624457776546478}, {"X": 0.7116211652755737, "Y": 0.2746206521987915}, {"X": 0.6992663741111755, "Y": 0.2746203541755676}]}, "Id": "369a5713-6798-4407-bdf3-a2c7aea48d7e"}, {"BlockType": "WORD", "Confidence": 28.024354934692383, "Text": "755h", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10852892696857452, "Height": 0.030838720500469208, "Left": 0.34646841883659363, "Top": 0.29517698287963867}, "Polygon": [{"X": 0.34646841883659363, "Y": 0.29517698287963867}, {"X": 0.4549972414970398, "Y": 0.2951796352863312}, {"X": 0.45499736070632935, "Y": 0.3260157108306885}, {"X": 0.346468448638916, "Y": 0.32601311802864075}]}, "Id": "04d98270-558f-4c47-b803-8164acfd1dd0"}, {"BlockType": "WORD", "Confidence": 93.11722564697266, "Text": "tempty", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13623382151126862, "Height": 0.028560955077409744, "Left": 0.46768632531166077, "Top": 0.29425355792045593}, "Polygon": [{"X": 0.46768632531166077, "Y": 0.29425355792045593}, {"X": 0.6039199829101562, "Y": 0.29425686597824097}, {"X": 0.6039201617240906, "Y": 0.3228145241737366}, {"X": 0.46768641471862793, "Y": 0.3228112459182739}]}, "Id": "d43c2281-c1ee-4b92-a28d-b2c23eae34ae"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "1230", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08113858103752136, "Height": 0.01772553101181984, "Left": 0.8646883368492126, "Top": 0.2991727888584137}, "Polygon": [{"X": 0.8646883368492126, "Y": 0.2991727888584137}, {"X": 0.9458267092704773, "Y": 0.2991747558116913}, {"X": 0.9458268880844116, "Y": 0.31689831614494324}, {"X": 0.864688515663147, "Y": 0.31689637899398804}]}, "Id": "c19d23ba-33c6-4910-80b5-7d62a0557a56"}, {"BlockType": "WORD", "Confidence": 99.02204132080078, "Text": "3)", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.045506466180086136, "Height": 0.05146821215748787, "Left": 0.0483834333717823, "Top": 0.31561723351478577}, "Polygon": [{"X": 0.04838363081216812, "Y": 0.31561723351478577}, {"X": 0.09388989955186844, "Y": 0.3156183063983917}, {"X": 0.0938897430896759, "Y": 0.36708542704582214}, {"X": 0.0483834333717823, "Y": 0.3670843541622162}]}, "Id": "9748b8d5-2f64-477b-9db5-8ca0b0a71f7a"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "General", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06725455820560455, "Height": 0.01306302659213543, "Left": 0.10845371335744858, "Top": 0.33443596959114075}, "Polygon": [{"X": 0.10845375061035156, "Y": 0.33443596959114075}, {"X": 0.17570827901363373, "Y": 0.3344375789165497}, {"X": 0.17570824921131134, "Y": 0.34749898314476013}, {"X": 0.10845371335744858, "Y": 0.3474973738193512}]}, "Id": "c9d093f3-91f9-4b54-aa00-f31566e8e4db"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Extrusion", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07922009378671646, "Height": 0.012516379356384277, "Left": 0.18221800029277802, "Top": 0.3345755636692047}, "Polygon": [{"X": 0.1822180151939392, "Y": 0.3345755636692047}, {"X": 0.2614381015300751, "Y": 0.3345774710178375}, {"X": 0.2614380717277527, "Y": 0.347091943025589}, {"X": 0.18221800029277802, "Y": 0.34709006547927856}]}, "Id": "6f2e9044-4124-4981-b836-059fce98c8fb"}, {"BlockType": "WORD", "Confidence": 92.66522216796875, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01097073033452034, "Height": 0.009613018482923508, "Left": 0.35543903708457947, "Top": 0.3360922932624817}, "Polygon": [{"X": 0.35543903708457947, "Y": 0.3360922932624817}, {"X": 0.3664097487926483, "Y": 0.3360925316810608}, {"X": 0.3664097785949707, "Y": 0.3457053005695343}, {"X": 0.35543903708457947, "Y": 0.3457050323486328}]}, "Id": "d5742992-71ec-477b-ad27-87946c1bcaa7"}, {"BlockType": "WORD", "Confidence": 92.70427703857422, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013096112757921219, "Height": 0.01223896536976099, "Left": 0.42450857162475586, "Top": 0.3327445983886719}, "Polygon": [{"X": 0.42450857162475586, "Y": 0.3327445983886719}, {"X": 0.4376046359539032, "Y": 0.33274492621421814}, {"X": 0.4376046657562256, "Y": 0.3449835479259491}, {"X": 0.42450860142707825, "Y": 0.3449832499027252}]}, "Id": "b40f776d-06c1-4b0c-85f5-f483a2ef859c"}, {"BlockType": "WORD", "Confidence": 99.20679473876953, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09623313695192337, "Height": 0.011271430179476738, "Left": 0.46625810861587524, "Top": 0.332563579082489}, "Polygon": [{"X": 0.46625810861587524, "Y": 0.332563579082489}, {"X": 0.5624911785125732, "Y": 0.33256590366363525}, {"X": 0.562491238117218, "Y": 0.3438350260257721}, {"X": 0.46625816822052, "Y": 0.34383273124694824}]}, "Id": "cec8c220-4bf0-4bfe-87c4-264a9eb10390"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "1305", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06859823316335678, "Height": 0.025042375549674034, "Left": 0.8614248633384705, "Top": 0.3425601124763489}, "Polygon": [{"X": 0.8614248633384705, "Y": 0.3425601124763489}, {"X": 0.9300227761268616, "Y": 0.3425617516040802}, {"X": 0.9300230741500854, "Y": 0.36760246753692627}, {"X": 0.8614251017570496, "Y": 0.36760085821151733}]}, "Id": "9b224f00-c470-4672-a94c-7b03db8fab64"}, {"BlockType": "WORD", "Confidence": 38.58457946777344, "Text": "x", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.14296767115592957, "Height": 0.05359954759478569, "Left": 0.15001697838306427, "Top": 0.3605818748474121}, "Polygon": [{"X": 0.15001708269119263, "Y": 0.3605818748474121}, {"X": 0.29298463463783264, "Y": 0.3605852723121643}, {"X": 0.29298463463783264, "Y": 0.4141814112663269}, {"X": 0.15001697838306427, "Y": 0.4141780734062195}]}, "Id": "a8e077af-e881-47a3-823c-dabc5ba2d770"}, {"BlockType": "WORD", "Confidence": 36.12962341308594, "Text": "tsk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08811807632446289, "Height": 0.048168737441301346, "Left": 0.3784377872943878, "Top": 0.41067251563072205}, "Polygon": [{"X": 0.3784377872943878, "Y": 0.41067251563072205}, {"X": 0.46655571460723877, "Y": 0.4106745719909668}, {"X": 0.4665558636188507, "Y": 0.4588412642478943}, {"X": 0.378437876701355, "Y": 0.4588392376899719}]}, "Id": "449382a5-0acb-411f-a027-154d41b4cc04"}, {"BlockType": "WORD", "Confidence": 98.17681884765625, "Text": "2)", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.053603459149599075, "Height": 0.05119029060006142, "Left": 0.04938212409615517, "Top": 0.4360446333885193}, "Polygon": [{"X": 0.049382321536540985, "Y": 0.4360446333885193}, {"X": 0.10298558324575424, "Y": 0.43604588508605957}, {"X": 0.1029854342341423, "Y": 0.4872349500656128}, {"X": 0.04938212409615517, "Y": 0.4872336983680725}]}, "Id": "fbec6635-4cc1-45dc-804f-456e8071fefa"}, {"BlockType": "WORD", "Confidence": 99.56791687011719, "Text": "Vari-Wall", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07831351459026337, "Height": 0.012987930327653885, "Left": 0.1102224662899971, "Top": 0.4515991806983948}, "Polygon": [{"X": 0.11022250354290009, "Y": 0.4515991806983948}, {"X": 0.18853597342967987, "Y": 0.4516009986400604}, {"X": 0.18853595852851868, "Y": 0.46458712220191956}, {"X": 0.1102224662899971, "Y": 0.4645853042602539}]}, "Id": "34cc9562-cf76-4f39-8ad9-0e3226a12291"}, {"BlockType": "WORD", "Confidence": 65.4942626953125, "Text": "5 SK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08004599064588547, "Height": 0.025789255276322365, "Left": 0.14935287833213806, "Top": 0.4708024263381958}, "Polygon": [{"X": 0.14935292303562164, "Y": 0.4708024263381958}, {"X": 0.22939886152744293, "Y": 0.47080424427986145}, {"X": 0.22939884662628174, "Y": 0.4965916872024536}, {"X": 0.14935287833213806, "Y": 0.4965898394584656}]}, "Id": "35e1cc24-6c77-4339-85ba-3510c6acd4d9"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045103128999471664, "Height": 0.012653177604079247, "Left": 0.1938648372888565, "Top": 0.4513533413410187}, "Polygon": [{"X": 0.1938648670911789, "Y": 0.4513533413410187}, {"X": 0.23896797001361847, "Y": 0.45135438442230225}, {"X": 0.23896797001361847, "Y": 0.4640065133571625}, {"X": 0.1938648372888565, "Y": 0.4640054702758789}]}, "Id": "c438b33d-7c6e-4176-8b04-ae9cfe35cd9e"}, {"BlockType": "WORD", "Confidence": 87.46551513671875, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01127795409411192, "Height": 0.009594044648110867, "Left": 0.35788819193840027, "Top": 0.4525049924850464}, "Polygon": [{"X": 0.35788819193840027, "Y": 0.4525049924850464}, {"X": 0.3691661059856415, "Y": 0.4525052607059479}, {"X": 0.36916613578796387, "Y": 0.4620990455150604}, {"X": 0.35788819193840027, "Y": 0.46209877729415894}]}, "Id": "ee73b7df-ba24-49c1-ad1e-bb01828c9309"}, {"BlockType": "WORD", "Confidence": 94.29707336425781, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010941325686872005, "Height": 0.009150148369371891, "Left": 0.42788010835647583, "Top": 0.45209845900535583}, "Polygon": [{"X": 0.42788010835647583, "Y": 0.45209845900535583}, {"X": 0.438821405172348, "Y": 0.4520987272262573}, {"X": 0.4388214349746704, "Y": 0.46124860644340515}, {"X": 0.4278801381587982, "Y": 0.46124836802482605}]}, "Id": "dd0606c1-47cf-486f-9f6b-c5bc228a0b61"}, {"BlockType": "WORD", "Confidence": 69.29528045654297, "Text": "so", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020275412127375603, "Height": 0.010189954191446304, "Left": 0.4683604836463928, "Top": 0.44983166456222534}, "Polygon": [{"X": 0.4683604836463928, "Y": 0.44983166456222534}, {"X": 0.4886358380317688, "Y": 0.44983214139938354}, {"X": 0.4886358678340912, "Y": 0.46002161502838135}, {"X": 0.4683605134487152, "Y": 0.46002116799354553}]}, "Id": "dbd71ba0-a466-4a4a-b305-f974bb3cde06"}, {"BlockType": "WORD", "Confidence": 98.4327163696289, "Text": "02115817", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06870908290147781, "Height": 0.01084152702242136, "Left": 0.4946866035461426, "Top": 0.4488900899887085}, "Polygon": [{"X": 0.4946866035461426, "Y": 0.4488900899887085}, {"X": 0.563395619392395, "Y": 0.44889166951179504}, {"X": 0.5633956789970398, "Y": 0.4597316086292267}, {"X": 0.49468663334846497, "Y": 0.45973002910614014}]}, "Id": "8188177e-5cac-4b5e-9408-3e1713552ec8"}, {"BlockType": "WORD", "Confidence": 95.2782211303711, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09501313418149948, "Height": 0.010701981373131275, "Left": 0.5690041780471802, "Top": 0.4482070803642273}, "Polygon": [{"X": 0.5690041780471802, "Y": 0.4482070803642273}, {"X": 0.6640172600746155, "Y": 0.448209285736084}, {"X": 0.6640173196792603, "Y": 0.4589090645313263}, {"X": 0.569004237651825, "Y": 0.4589068591594696}]}, "Id": "d696d6ac-14cf-4595-a411-38233669bb69"}, {"BlockType": "WORD", "Confidence": 49.876434326171875, "Text": "1000", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08367662876844406, "Height": 0.020543985068798065, "Left": 0.8451106548309326, "Top": 0.4423898756504059}, "Polygon": [{"X": 0.8451106548309326, "Y": 0.4423898756504059}, {"X": 0.9287870526313782, "Y": 0.4423918128013611}, {"X": 0.9287872314453125, "Y": 0.46293386816978455}, {"X": 0.8451108336448669, "Y": 0.46293193101882935}]}, "Id": "b1225a58-3f2b-45b0-8e77-48ad67cad150"}, {"BlockType": "WORD", "Confidence": 70.61602783203125, "Text": "follow", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1095709502696991, "Height": 0.03753760829567909, "Left": 0.5072449445724487, "Top": 0.481648713350296}, "Polygon": [{"X": 0.5072449445724487, "Y": 0.481648713350296}, {"X": 0.6168156862258911, "Y": 0.4816512167453766}, {"X": 0.6168159246444702, "Y": 0.5191863179206848}, {"X": 0.5072451233863831, "Y": 0.519183874130249}]}, "Id": "a2f08c70-c456-47c4-a6ed-dfd3bc0b39b7"}, {"BlockType": "WORD", "Confidence": 55.04524230957031, "Text": "to", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.040477603673934937, "Height": 0.019059455022215843, "Left": 0.7659012079238892, "Top": 0.46283116936683655}, "Polygon": [{"X": 0.7659012079238892, "Y": 0.46283116936683655}, {"X": 0.8063786625862122, "Y": 0.46283209323883057}, {"X": 0.8063788414001465, "Y": 0.48189061880111694}, {"X": 0.7659013867378235, "Y": 0.4818896949291229}]}, "Id": "17c1c2b9-8548-440a-9dbf-2d535109babb"}, {"BlockType": "WORD", "Confidence": 98.583984375, "Text": "0936", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.074602872133255, "Height": 0.017255874350667, "Left": 0.8730655312538147, "Top": 0.46400681138038635}, "Polygon": [{"X": 0.8730655312538147, "Y": 0.46400681138038635}, {"X": 0.947668194770813, "Y": 0.46400853991508484}, {"X": 0.9476684331893921, "Y": 0.4812626838684082}, {"X": 0.873065710067749, "Y": 0.4812609851360321}]}, "Id": "fd2e0518-9a91-4387-9897-74181d7bc02d"}, {"BlockType": "WORD", "Confidence": 73.31373596191406, "Text": "1015", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09938754886388779, "Height": 0.024359211325645447, "Left": 0.8383670449256897, "Top": 0.48931023478507996}, "Polygon": [{"X": 0.8383670449256897, "Y": 0.48931023478507996}, {"X": 0.9377543330192566, "Y": 0.4893124997615814}, {"X": 0.9377545714378357, "Y": 0.5136694312095642}, {"X": 0.8383672833442688, "Y": 0.5136672258377075}]}, "Id": "7d3d8f12-866f-4df3-bd2b-95b2759b8aa8"}, {"BlockType": "WORD", "Confidence": 93.0676040649414, "Text": "1)", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.02804512158036232, "Height": 0.04745045676827431, "Left": 0.06031886115670204, "Top": 0.5581309795379639}, "Polygon": [{"X": 0.06031903252005577, "Y": 0.5581309795379639}, {"X": 0.08836398273706436, "Y": 0.5581316351890564}, {"X": 0.08836383372545242, "Y": 0.6055814623832703}, {"X": 0.06031886115670204, "Y": 0.6055808067321777}]}, "Id": "e5249f01-52da-4097-8fe9-5e6613070182"}, {"BlockType": "WORD", "Confidence": 67.90657043457031, "Text": "In", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.022876592352986336, "Height": 0.011997882276773453, "Left": 0.12440148741006851, "Top": 0.5601868629455566}, "Polygon": [{"X": 0.1244015172123909, "Y": 0.5601868629455566}, {"X": 0.1472780853509903, "Y": 0.5601873397827148}, {"X": 0.1472780555486679, "Y": 0.5721847414970398}, {"X": 0.12440148741006851, "Y": 0.5721842050552368}]}, "Id": "374c0bf8-9b2f-4f28-bcea-0472120acaa1"}, {"BlockType": "WORD", "Confidence": 36.677093505859375, "Text": "Inlandplt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09715534001588821, "Height": 0.015106773003935814, "Left": 0.12456759065389633, "Top": 0.5579501390457153}, "Polygon": [{"X": 0.12456762790679932, "Y": 0.5579501390457153}, {"X": 0.22172293066978455, "Y": 0.557952344417572}, {"X": 0.22172291576862335, "Y": 0.5730569362640381}, {"X": 0.12456759065389633, "Y": 0.5730547904968262}]}, "Id": "1348d37c-c956-4d7b-8ab0-d1c0d0253175"}, {"BlockType": "WORD", "Confidence": 85.2235107421875, "Text": "plt", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.03245953097939491, "Height": 0.013446365483105183, "Left": 0.18900640308856964, "Top": 0.5581424236297607}, "Polygon": [{"X": 0.18900641798973083, "Y": 0.5581424236297607}, {"X": 0.22146593034267426, "Y": 0.558143138885498}, {"X": 0.22146591544151306, "Y": 0.5715887546539307}, {"X": 0.18900640308856964, "Y": 0.5715880393981934}]}, "Id": "5434aee3-9b55-4497-8960-d914d7a0211c"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056485556066036224, "Height": 0.011727984063327312, "Left": 0.11173728853464127, "Top": 0.5718370676040649}, "Polygon": [{"X": 0.11173731833696365, "Y": 0.5718370676040649}, {"X": 0.1682228446006775, "Y": 0.5718383193016052}, {"X": 0.1682228296995163, "Y": 0.5835649967193604}, {"X": 0.11173728853464127, "Y": 0.5835638046264648}]}, "Id": "02e47904-ac46-4909-a7c2-37992f85c9a0"}, {"BlockType": "WORD", "Confidence": 99.45890045166016, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07091508060693741, "Height": 0.012475800700485706, "Left": 0.17448420822620392, "Top": 0.5699876546859741}, "Polygon": [{"X": 0.1744842231273651, "Y": 0.5699876546859741}, {"X": 0.24539928138256073, "Y": 0.5699892640113831}, {"X": 0.24539928138256073, "Y": 0.5824634432792664}, {"X": 0.17448420822620392, "Y": 0.5824618935585022}]}, "Id": "d110ef96-6f3a-4aac-ab65-b20d4dd78f6f"}, {"BlockType": "WORD", "Confidence": 91.33271026611328, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007058866322040558, "Height": 0.0027441878337413073, "Left": 0.2511550188064575, "Top": 0.5749856233596802}, "Polygon": [{"X": 0.2511550188064575, "Y": 0.5749856233596802}, {"X": 0.25821390748023987, "Y": 0.5749857425689697}, {"X": 0.25821390748023987, "Y": 0.5777297616004944}, {"X": 0.2511550188064575, "Y": 0.5777296423912048}]}, "Id": "4a7ad622-99ca-4b3d-b025-0426c03b9aa7"}, {"BlockType": "WORD", "Confidence": 88.34123229980469, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011289657093584538, "Height": 0.00977187417447567, "Left": 0.3594965636730194, "Top": 0.580009400844574}, "Polygon": [{"X": 0.3594965636730194, "Y": 0.580009400844574}, {"X": 0.370786190032959, "Y": 0.5800096988677979}, {"X": 0.37078621983528137, "Y": 0.5897812843322754}, {"X": 0.3594965636730194, "Y": 0.5897810459136963}]}, "Id": "73771385-98d9-4e94-aa03-d415087eeee9"}, {"BlockType": "WORD", "Confidence": 89.63150024414062, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011617789044976234, "Height": 0.009949511848390102, "Left": 0.42950552701950073, "Top": 0.5791550278663635}, "Polygon": [{"X": 0.42950552701950073, "Y": 0.5791550278663635}, {"X": 0.4411233067512512, "Y": 0.5791552662849426}, {"X": 0.4411233067512512, "Y": 0.5891045331954956}, {"X": 0.4295055568218231, "Y": 0.5891042947769165}]}, "Id": "8a9eb1f4-bf90-4bb7-a787-b31438ab791c"}, {"BlockType": "WORD", "Confidence": 72.27778625488281, "Text": "so", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02025182917714119, "Height": 0.010049100033938885, "Left": 0.4712843596935272, "Top": 0.5769669413566589}, "Polygon": [{"X": 0.4712843596935272, "Y": 0.5769669413566589}, {"X": 0.49153614044189453, "Y": 0.5769674181938171}, {"X": 0.4915361702442169, "Y": 0.5870160460472107}, {"X": 0.4712843894958496, "Y": 0.5870155692100525}]}, "Id": "7f9d055f-2462-4387-88f7-c2889fc8b597"}, {"BlockType": "WORD", "Confidence": 98.29021453857422, "Text": "02115450", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07027614861726761, "Height": 0.010867350734770298, "Left": 0.4967392086982727, "Top": 0.5759356617927551}, "Polygon": [{"X": 0.4967392086982727, "Y": 0.5759356617927551}, {"X": 0.5670152902603149, "Y": 0.5759372711181641}, {"X": 0.5670153498649597, "Y": 0.5868030190467834}, {"X": 0.4967392385005951, "Y": 0.5868014693260193}]}, "Id": "a1ac57ac-4b96-4fec-ab4a-c0a5725781ba"}, {"BlockType": "WORD", "Confidence": 97.1534194946289, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09607934206724167, "Height": 0.010989418253302574, "Left": 0.5721423029899597, "Top": 0.5749853253364563}, "Polygon": [{"X": 0.5721423029899597, "Y": 0.5749853253364563}, {"X": 0.6682215332984924, "Y": 0.5749874711036682}, {"X": 0.668221652507782, "Y": 0.5859747529029846}, {"X": 0.5721423625946045, "Y": 0.5859726071357727}]}, "Id": "fa476933-d68b-42a6-b72d-027efd97b983"}, {"BlockType": "WORD", "Confidence": 66.57047271728516, "Text": "so", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020847702398896217, "Height": 0.010122407227754593, "Left": 0.6726579070091248, "Top": 0.5748129487037659}, "Polygon": [{"X": 0.6726579070091248, "Y": 0.5748129487037659}, {"X": 0.6935055255889893, "Y": 0.5748134255409241}, {"X": 0.6935056447982788, "Y": 0.5849353671073914}, {"X": 0.6726579666137695, "Y": 0.5849348902702332}]}, "Id": "0cd611aa-4962-4611-ba0f-3a9057781ee7"}, {"BlockType": "WORD", "Confidence": 88.92597961425781, "Text": "0211626", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06724178045988083, "Height": 0.010987631045281887, "Left": 0.6970810890197754, "Top": 0.5739304423332214}, "Polygon": [{"X": 0.6970810890197754, "Y": 0.5739304423332214}, {"X": 0.764322817325592, "Y": 0.5739319324493408}, {"X": 0.7643228769302368, "Y": 0.5849180221557617}, {"X": 0.6970812082290649, "Y": 0.5849165320396423}]}, "Id": "c40c7296-218c-44de-9531-dc0b80bfe301"}, {"BlockType": "WORD", "Confidence": 99.69486999511719, "Text": "Metamora", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0857909545302391, "Height": 0.012538857758045197, "Left": 0.1119719073176384, "Top": 0.5901594161987305}, "Polygon": [{"X": 0.11197194457054138, "Y": 0.5901594161987305}, {"X": 0.1977628618478775, "Y": 0.5901613235473633}, {"X": 0.1977628469467163, "Y": 0.6026982665061951}, {"X": 0.1119719073176384, "Y": 0.602696418762207}]}, "Id": "8301cd2b-da54-47b3-be56-c7855caa6ce9"}, {"BlockType": "WORD", "Confidence": 87.93367004394531, "Text": "50-2116053", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.21994942426681519, "Height": 0.023283783346414566, "Left": 0.47301778197288513, "Top": 0.5878908038139343}, "Polygon": [{"X": 0.47301778197288513, "Y": 0.5878908038139343}, {"X": 0.6929670572280884, "Y": 0.5878956913948059}, {"X": 0.6929671764373779, "Y": 0.6111745834350586}, {"X": 0.4730178415775299, "Y": 0.6111698150634766}]}, "Id": "fd39f73c-b53f-4d61-acd8-0693618e5a48"}, {"BlockType": "WORD", "Confidence": 97.95121002197266, "Text": "0540", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07629060745239258, "Height": 0.01622743532061577, "Left": 0.8599780201911926, "Top": 0.5859130620956421}, "Polygon": [{"X": 0.8599780201911926, "Y": 0.5859130620956421}, {"X": 0.9362684488296509, "Y": 0.5859147310256958}, {"X": 0.9362686276435852, "Y": 0.602140486240387}, {"X": 0.859978199005127, "Y": 0.6021388173103333}]}, "Id": "e937094a-3fee-45df-954e-163503723719"}, {"BlockType": "WORD", "Confidence": 52.90119171142578, "Text": "1 sm box", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.117005355656147, "Height": 0.029255716130137444, "Left": 0.125104159116745, "Top": 0.6088439226150513}, "Polygon": [{"X": 0.12510423362255096, "Y": 0.6088439226150513}, {"X": 0.2421095222234726, "Y": 0.6088464856147766}, {"X": 0.2421095073223114, "Y": 0.6380996704101562}, {"X": 0.125104159116745, "Y": 0.6380971074104309}]}, "Id": "165194f0-817b-46a7-8fdb-7dd929ad98d2"}, {"BlockType": "WORD", "Confidence": 99.521484375, "Text": "0600", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08056437224149704, "Height": 0.017424000427126884, "Left": 0.8577093482017517, "Top": 0.6094968914985657}, "Polygon": [{"X": 0.8577093482017517, "Y": 0.6094968914985657}, {"X": 0.938273549079895, "Y": 0.6094986200332642}, {"X": 0.9382737278938293, "Y": 0.6269208788871765}, {"X": 0.857709527015686, "Y": 0.6269190907478333}]}, "Id": "ed452a8c-441e-4184-a86c-247613a3448f"}, {"BlockType": "WORD", "Confidence": 64.70683288574219, "Text": "Cempties", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1722884476184845, "Height": 0.03803272545337677, "Left": 0.39649271965026855, "Top": 0.6194044947624207}, "Polygon": [{"X": 0.39649271965026855, "Y": 0.6194044947624207}, {"X": 0.5687810182571411, "Y": 0.6194082498550415}, {"X": 0.5687811970710754, "Y": 0.6574372053146362}, {"X": 0.3964928090572357, "Y": 0.6574335098266602}]}, "Id": "537c878e-3dfe-4e33-b7dc-1527baf82109"}, {"BlockType": "WORD", "Confidence": 71.29524230957031, "Text": "aspots", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.13270403444766998, "Height": 0.03328462317585945, "Left": 0.6079355478286743, "Top": 0.6148478984832764}, "Polygon": [{"X": 0.6079355478286743, "Y": 0.6148478984832764}, {"X": 0.7406392693519592, "Y": 0.6148508191108704}, {"X": 0.7406395673751831, "Y": 0.6481325030326843}, {"X": 0.6079357266426086, "Y": 0.6481296420097351}]}, "Id": "2e783667-3c4c-47c6-b9c6-667ca4ff76b4"}, {"BlockType": "WORD", "Confidence": 98.31871795654297, "Text": "4", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.026098841801285744, "Height": 0.04726731777191162, "Left": 0.0359298475086689, "Top": 0.6634374260902405}, "Polygon": [{"X": 0.03593004122376442, "Y": 0.6634374260902405}, {"X": 0.06202869117259979, "Y": 0.6634379625320435}, {"X": 0.06202851980924606, "Y": 0.7107047438621521}, {"X": 0.0359298475086689, "Y": 0.7107042074203491}]}, "Id": "6babebda-52c4-4a8e-b19d-a15f0257e656"}, {"BlockType": "WORD", "Confidence": 81.10152435302734, "Text": "PeNNex", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.17136432230472565, "Height": 0.03317827731370926, "Left": 0.1023721843957901, "Top": 0.6689649820327759}, "Polygon": [{"X": 0.10237228125333786, "Y": 0.6689649820327759}, {"X": 0.27373650670051575, "Y": 0.668968677520752}, {"X": 0.27373650670051575, "Y": 0.7021432518959045}, {"X": 0.1023721843957901, "Y": 0.7021396160125732}]}, "Id": "9cb902c7-6c2d-4afc-9193-6f10c4849d1e"}, {"BlockType": "WORD", "Confidence": 88.56365966796875, "Text": "X", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.019124368205666542, "Height": 0.025946438312530518, "Left": 0.35739386081695557, "Top": 0.6663710474967957}, "Polygon": [{"X": 0.35739386081695557, "Y": 0.6663710474967957}, {"X": 0.376518189907074, "Y": 0.6663714647293091}, {"X": 0.37651821970939636, "Y": 0.6923174858093262}, {"X": 0.35739389061927795, "Y": 0.6923171281814575}]}, "Id": "07e0d73f-93d8-4480-bd1b-5d9d9cb38e72"}, {"BlockType": "WORD", "Confidence": 67.73497772216797, "Text": "Pickup", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.17593133449554443, "Height": 0.027651553973555565, "Left": 0.45222336053848267, "Top": 0.6548234224319458}, "Polygon": [{"X": 0.45222336053848267, "Y": 0.6548234224319458}, {"X": 0.6281545162200928, "Y": 0.6548272371292114}, {"X": 0.6281546950340271, "Y": 0.6824749708175659}, {"X": 0.45222344994544983, "Y": 0.6824712157249451}]}, "Id": "8d8c54ad-c490-4036-880c-023efb468364"}, {"BlockType": "WORD", "Confidence": 60.226898193359375, "Text": "$", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.03814014419913292, "Height": 0.027876194566488266, "Left": 0.6848228573799133, "Top": 0.6771287322044373}, "Polygon": [{"X": 0.6848228573799133, "Y": 0.6771287322044373}, {"X": 0.7229627966880798, "Y": 0.6771295666694641}, {"X": 0.7229630351066589, "Y": 0.7050049304962158}, {"X": 0.6848230957984924, "Y": 0.705004096031189}]}, "Id": "16debd62-ce99-4dbf-99f5-dd84e1ca9c27"}, {"BlockType": "WORD", "Confidence": 15.778459548950195, "Text": "HYS", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08013866096735, "Height": 0.031022416427731514, "Left": 0.7251543402671814, "Top": 0.6698042750358582}, "Polygon": [{"X": 0.7251543402671814, "Y": 0.6698042750358582}, {"X": 0.8052926659584045, "Y": 0.6698060035705566}, {"X": 0.8052929639816284, "Y": 0.7008267045021057}, {"X": 0.7251545786857605, "Y": 0.7008249759674072}]}, "Id": "e83c6fff-5812-4cda-a932-022a412d1115"}, {"BlockType": "WORD", "Confidence": 74.37659454345703, "Text": "20sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.11407884955406189, "Height": 0.030752021819353104, "Left": 0.14665275812149048, "Top": 0.719403088092804}, "Polygon": [{"X": 0.14665281772613525, "Y": 0.719403088092804}, {"X": 0.26073160767555237, "Y": 0.719405472278595}, {"X": 0.26073160767555237, "Y": 0.7501550912857056}, {"X": 0.14665275812149048, "Y": 0.7501527070999146}]}, "Id": "bad855df-1b97-4cc9-8803-9ab92e64def9"}, {"BlockType": "WORD", "Confidence": 23.12111473083496, "Text": "DSI", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07473167777061462, "Height": 0.0362405925989151, "Left": 0.7319081425666809, "Top": 0.6990905404090881}, "Polygon": [{"X": 0.7319081425666809, "Y": 0.6990905404090881}, {"X": 0.8066394925117493, "Y": 0.6990921497344971}, {"X": 0.8066398501396179, "Y": 0.735331118106842}, {"X": 0.7319084405899048, "Y": 0.7353295683860779}]}, "Id": "578bfa0b-be1d-49b9-836e-9b33639970dd"}, {"BlockType": "WORD", "Confidence": 55.92494583129883, "Text": "10 40", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07327038049697876, "Height": 0.024272389709949493, "Left": 0.6715286374092102, "Top": 0.74648118019104}, "Polygon": [{"X": 0.6715286374092102, "Y": 0.74648118019104}, {"X": 0.7447987794876099, "Y": 0.7464827299118042}, {"X": 0.744799017906189, "Y": 0.7707535624504089}, {"X": 0.6715287566184998, "Y": 0.7707520723342896}]}, "Id": "000cb658-0893-4042-980b-f28239951ae9"}, {"BlockType": "WORD", "Confidence": 99.6484375, "Text": "1145", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06058167666196823, "Height": 0.02555784583091736, "Left": 0.6812065839767456, "Top": 0.7851274013519287}, "Polygon": [{"X": 0.6812065839767456, "Y": 0.7851274013519287}, {"X": 0.7417880296707153, "Y": 0.785128653049469}, {"X": 0.7417882680892944, "Y": 0.8106852173805237}, {"X": 0.6812067627906799, "Y": 0.8106839656829834}]}, "Id": "9303bc26-38ca-4d86-a667-974a4835205f"}, {"BlockType": "WORD", "Confidence": 99.84295654296875, "Text": "YOUNGSTOWN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1360902637243271, "Height": 0.014133377000689507, "Left": 0.4435255229473114, "Top": 0.9436905980110168}, "Polygon": [{"X": 0.4435255229473114, "Y": 0.9436905980110168}, {"X": 0.5796157121658325, "Y": 0.943693220615387}, {"X": 0.5796157717704773, "Y": 0.9578239321708679}, {"X": 0.4435255527496338, "Y": 0.9578213095664978}]}, "Id": "98a7de6c-70da-48d2-97be-5f1e3c537c4d"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "d2a45a9d-5454-4093-854b-310150759bc4", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "d2a45a9d-5454-4093-854b-310150759bc4", "content-type": "application/x-amz-json-1.1", "content-length": "70011", "date": "Thu, 18 Sep 2025 09:00:31 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "59b24d51-f6b1-43b8-9c5a-551a89837f33", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 09:00:34 GMT", "content-type": "application/json", "content-length": "2096", "connection": "keep-alive", "x-amzn-requestid": "59b24d51-f6b1-43b8-9c5a-551a89837f33"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page with a bunch of information. It includes details like \"WORK ORDER #\", \"***********\", \"UNIT #\", \"Truck Ready Time\", \"Driver\", \"Truck Departure\". It's likely a \"Work Order\" or \"Shipment\" document. But we need to classify as one of the predefined types. Evaluate if it matches any:\n\n- invoice: no invoice or amounts.\n\n- bol: Bill of Lading: Might have \"Shipper\", \"Consignee\", \"B/L No\". Not present.\n\n- pod: Proof of Delivery: has signature fields: no.\n\n- rate_confirmation: maybe \"Carrier Rate Confirmation\" but no mention.\n\n- cust_rate_confirmation: no.\n\n- clear_to_pay: no.\n\n- scale_ticket: weight? No weight.\n\n- log: driver log? maybe but no log of driver activity.\n\n- fuel_receipt: no.\n\n- combined_carrier_documents: no.\n\n- pack_list: no.\n\n- po: Purchase Order: not sure.\n\n- comm_invoice: no.\n\n- customs_doc: no.\n\n- nmfc_cert: no.\n\n- other: could be.\n\n- coa: no.\n\n- tender_from_cust: maybe \"Load Tender from Customer\"? But not explicit.\n\n- lumper_receipt: no.\n\n- so_confirmation: sales order confirmation? There's \"***********\" and others. Maybe it's an Order Confirmation? But typical sales order confirmation would have header \"Sales Order Confirmation\". Not present.\n\n- po_confirmation: no.\n\n- ingate: Ingate Document: may have truck arrival times. It has \"Truck Ready Time\", \"Truck Departure\". Might be an \"Ingate\" or \"Outgate\" but not explicit.\n\nGiven no explicit header, we classify as \"other\". But we also need to check if it's continuation of previous page? There's only page1? So proceed.\n\nWe must output a tool call with array of documents: page no 1, doc_type \"other\".\n\n"}}}, {"toolUse": {"toolUseId": "tooluse_0cONEgMCTMC7kLTJzmMNVg", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "other"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2338, "outputTokens": 436, "totalTokens": 2774}, "metrics": {"latencyMs": 1781}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/306cdafb_8_log_2.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nDATE: 19-Feb\nUNIT #: 92644/283420\nTruck Ready Time: 5:00\nDRIVER: KARRA\nTruck Departure: 5:00\nDEPARTURE TIME: 5am\nPICK-\nDock Time\nCUSTOMER\nUP DEL\nWORK ORDER #\n755h tempty\n1230\n3) General Extrusion\nX\nX ***********\n1305\nx\ntsk\n2) Vari-Wall 5 SK Tube\nX\nX\nso 02115817 ***********\n1000\nfollow to\n0936\n1015\n1)\nIn Inlandplt plt\nParker Hannifin -\nX\nX\nso 02115450 *********** so 0211626\nMetamora\n50-2116053\n0540\n1 sm box\n0600\nCempties aspots\n4\nPeNNex\nX\nPickup\n$ HYS\n20sk\nDSI\n10 40\n1145\nYOUNGSTOWN\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}