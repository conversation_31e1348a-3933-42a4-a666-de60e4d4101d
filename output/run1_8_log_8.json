{"classification_result": {"documents": [{"page_no": 1, "doc_type": "log"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 0.9979555606842041, "Left": 0.0, "Top": 0.002009193878620863}, "Polygon": [{"X": 0.0, "Y": 0.0026915324851870537}, {"X": 1.0, "Y": 0.002009193878620863}, {"X": 1.0, "Y": 0.9994205832481384}, {"X": 0.0, "Y": 0.9999647736549377}]}, "Id": "04749a51-55f1-4e6c-b22c-53ad7c10e3c9", "Relationships": [{"Type": "CHILD", "Ids": ["7dd502a6-6cb6-4d31-b9f0-cc7d12de4310", "05ea81f3-e0a4-4437-81e2-68a3296ffe60", "9a1aebb2-2bba-4202-a0f4-fb4252fe48c3", "04c14e27-cd3f-4f70-b9ea-d88039c18579", "98ef4138-e634-4714-864d-716e74454f7f", "6f06a157-b222-4baa-820e-a625a3a4d81f", "e27aacf1-8757-4766-a45a-7c67070c07a3", "de9799f2-fcdc-4a1c-8628-b9d0e14e3e4b", "00d96de6-9c39-4e55-9235-65bb1e122ef8", "cadacb85-3787-41f5-81ab-13505e10b75f", "95d15de1-4988-42cd-bb7a-60393be109d2", "cec57e10-4dde-4b56-aa4d-f06760418533", "bec3398f-f540-44cd-946b-812e8a74672c", "eb00020c-858c-43e3-b178-0f13e074b7f3", "537fc43b-3b00-4ba1-acc5-c0c1123d07bd", "ea241062-78e2-4c68-b90b-c8e8c1d5b381", "7be402c3-6851-46d0-99df-d28072d05bc5", "6f721533-048d-4cfd-adf6-16eaa341fc4c", "28bee48a-a37a-4f95-9430-e9b73d37a7a0", "4ab430a6-8651-40cd-8fdb-1ea93db3b734", "640bf618-95d4-4e13-9089-ac3b64fd050f", "9a9b264f-8ac6-40b9-b464-99f3da40fe7d", "2c4688e1-ae45-4ce7-a2e7-f2eacf11532a", "495f67b2-2ea6-4496-a902-f23a93505c90", "5b3641c1-f702-4e02-ab71-2daf9aaac763", "502a9556-b5c6-4508-9232-4f2067077a0a", "e50a3767-44a7-4599-9b29-4901b5692e39", "42011673-8168-49e8-a7cc-9fae0eca69bf", "db6ea485-5d4f-47f4-99d7-a52641128c02", "13d205e6-6fea-4cc3-be51-b27ff2939106", "fd79d1b3-d545-4790-9c62-ea7bb3769081", "79ff6ad2-ab0f-4e0b-b097-f30c7f0c3fd4", "51f90621-288d-4386-85f3-cdd7d82fe149", "a49b68c1-1d73-42c0-9640-a3aa86d44700", "af9ae908-1f6d-48ca-b660-c990b44f05e5", "37be5bbe-dc30-468a-8e2e-6e6f0908b92c", "f96b79e6-da7d-4d38-88e7-c84f76ddaf67", "c45400ae-7f91-4eaa-a62a-b5595d8fd42a", "9bd87941-f1c8-44f1-a7db-bc37d72dceb1", "06d18bca-ed5f-451d-8b82-39665e00b860", "99b476f8-93fe-4cf5-888e-e683a7d22ce7", "2f00b2ef-7441-4267-8679-0a26b97f4179"]}]}, {"BlockType": "LINE", "Confidence": 99.9755859375, "Text": "DATE: 2-Sep", "Geometry": {"BoundingBox": {"Width": 0.1343451291322708, "Height": 0.015511228702962399, "Left": 0.24907651543617249, "Top": 0.10191360116004944}, "Polygon": [{"X": 0.24907651543617249, "Y": 0.102003313601017}, {"X": 0.38340842723846436, "Y": 0.10191360116004944}, {"X": 0.3834216296672821, "Y": 0.11733540147542953}, {"X": 0.24908801913261414, "Y": 0.11742483079433441}]}, "Id": "7dd502a6-6cb6-4d31-b9f0-cc7d12de4310", "Relationships": [{"Type": "CHILD", "Ids": ["970f4a14-76a1-4f6e-9f33-d1b93bf0579b", "250cc41a-27a1-4c21-89c5-781b720310ad"]}]}, {"BlockType": "LINE", "Confidence": 99.80794525146484, "Text": "UNIT #: 6063/283420", "Geometry": {"BoundingBox": {"Width": 0.22098317742347717, "Height": 0.01477088313549757, "Left": 0.23158307373523712, "Top": 0.13764864206314087}, "Polygon": [{"X": 0.23158307373523712, "Y": 0.1377951204776764}, {"X": 0.4525528848171234, "Y": 0.13764864206314087}, {"X": 0.4525662362575531, "Y": 0.152273491024971}, {"X": 0.2315937727689743, "Y": 0.15241952240467072}]}, "Id": "05ea81f3-e0a4-4437-81e2-68a3296ffe60", "Relationships": [{"Type": "CHILD", "Ids": ["762ceb40-802f-4b31-a5a7-9954b6410929", "93f60ece-ed38-4b66-bb91-2ab6ba073b58", "d3d5fffc-53ff-4cd7-b919-011f26b4bf22"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Truck Ready Time:", "Geometry": {"BoundingBox": {"Width": 0.16915929317474365, "Height": 0.015419158153235912, "Left": 0.48266464471817017, "Top": 0.15561673045158386}, "Polygon": [{"X": 0.48266464471817017, "Y": 0.15572842955589294}, {"X": 0.6518074870109558, "Y": 0.15561673045158386}, {"X": 0.6518239378929138, "Y": 0.17092455923557281}, {"X": 0.48267900943756104, "Y": 0.17103590071201324}]}, "Id": "9a1aebb2-2bba-4202-a0f4-fb4252fe48c3", "Relationships": [{"Type": "CHILD", "Ids": ["b2430c1c-81d1-453c-863d-bd0ee9609068", "befc481e-075a-4167-ba85-6125758bd1ea", "eac08210-d6f2-4181-972a-fe12ff47c267"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "DRIVER: KARRA", "Geometry": {"BoundingBox": {"Width": 0.1736733615398407, "Height": 0.012899169698357582, "Left": 0.22103966772556305, "Top": 0.17417529225349426}, "Polygon": [{"X": 0.22103966772556305, "Y": 0.1742895394563675}, {"X": 0.3947019577026367, "Y": 0.17417529225349426}, {"X": 0.39471304416656494, "Y": 0.18696053326129913}, {"X": 0.22104892134666443, "Y": 0.1870744675397873}]}, "Id": "04c14e27-cd3f-4f70-b9ea-d88039c18579", "Relationships": [{"Type": "CHILD", "Ids": ["f16cd1f5-267c-4553-9057-620a4baf57e4", "a4a8faf2-c0cc-42d5-a48d-6f02f7b3c440"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Truck Departure:", "Geometry": {"BoundingBox": {"Width": 0.15370632708072662, "Height": 0.015318433754146099, "Left": 0.4835941791534424, "Top": 0.19150330126285553}, "Polygon": [{"X": 0.4835941791534424, "Y": 0.19160403311252594}, {"X": 0.6372842788696289, "Y": 0.19150330126285553}, {"X": 0.6373004913330078, "Y": 0.20672132074832916}, {"X": 0.4836084544658661, "Y": 0.2068217396736145}]}, "Id": "98ef4138-e634-4714-864d-716e74454f7f", "Relationships": [{"Type": "CHILD", "Ids": ["8d898f9a-a506-4291-ae8e-f63f99ef6e7e", "d5cd5849-c2b4-45e9-862d-c190724413fa"]}]}, {"BlockType": "LINE", "Confidence": 99.94466400146484, "Text": "DEPARTURE TIME: 5am", "Geometry": {"BoundingBox": {"Width": 0.24682378768920898, "Height": 0.013465484604239464, "Left": 0.12375569343566895, "Top": 0.2109547108411789}, "Polygon": [{"X": 0.12375569343566895, "Y": 0.21111582219600677}, {"X": 0.3705682158470154, "Y": 0.2109547108411789}, {"X": 0.37057948112487793, "Y": 0.22425955533981323}, {"X": 0.12376426160335541, "Y": 0.2244202047586441}]}, "Id": "6f06a157-b222-4baa-820e-a625a3a4d81f", "Relationships": [{"Type": "CHILD", "Ids": ["19fbe5b7-d564-492a-a076-3fd5395a9536", "e7b0a5f8-5ff2-4beb-b3ea-0291573feedb", "e703bc8a-03ca-46d5-aef9-129402ae02b1"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "PICK-", "Geometry": {"BoundingBox": {"Width": 0.051167819648981094, "Height": 0.012044334784150124, "Left": 0.34116190671920776, "Top": 0.24282944202423096}, "Polygon": [{"X": 0.34116190671920776, "Y": 0.24286261200904846}, {"X": 0.39231935143470764, "Y": 0.24282944202423096}, {"X": 0.39232972264289856, "Y": 0.2548407018184662}, {"X": 0.3411717712879181, "Y": 0.25487378239631653}]}, "Id": "e27aacf1-8757-4766-a45a-7c67070c07a3", "Relationships": [{"Type": "CHILD", "Ids": ["b1d5606e-a324-46f3-8d93-217a13f30664"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Dock Time", "Geometry": {"BoundingBox": {"Width": 0.10014614462852478, "Height": 0.012233363464474678, "Left": 0.8630748391151428, "Top": 0.2528887987136841}, "Polygon": [{"X": 0.8630748391151428, "Y": 0.25295355916023254}, {"X": 0.9632047414779663, "Y": 0.2528887987136841}, {"X": 0.9632209539413452, "Y": 0.2650575637817383}, {"X": 0.8630900382995605, "Y": 0.2651221752166748}]}, "Id": "de9799f2-fcdc-4a1c-8628-b9d0e14e3e4b", "Relationships": [{"Type": "CHILD", "Ids": ["6ae02070-b495-43ba-854f-15cf62f4225d", "04821ee1-cb01-461e-a6c1-6df7117bdacf"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CUSTOMER", "Geometry": {"BoundingBox": {"Width": 0.10767072439193726, "Height": 0.01283766608685255, "Left": 0.15752527117729187, "Top": 0.26422953605651855}, "Polygon": [{"X": 0.15752527117729187, "Y": 0.2642990052700043}, {"X": 0.2651863098144531, "Y": 0.26422953605651855}, {"X": 0.2651959955692291, "Y": 0.2769978940486908}, {"X": 0.15753385424613953, "Y": 0.2770671844482422}]}, "Id": "00d96de6-9c39-4e55-9235-65bb1e122ef8", "Relationships": [{"Type": "CHILD", "Ids": ["9572d8bb-f77b-4606-97a7-322967ed2bca"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "UP", "Geometry": {"BoundingBox": {"Width": 0.025493381544947624, "Height": 0.0126876225695014, "Left": 0.3544650375843048, "Top": 0.26230761408805847}, "Polygon": [{"X": 0.3544650375843048, "Y": 0.2623240649700165}, {"X": 0.37994760274887085, "Y": 0.26230761408805847}, {"X": 0.3799584209918976, "Y": 0.2749788463115692}, {"X": 0.35447558760643005, "Y": 0.27499523758888245}]}, "Id": "cadacb85-3787-41f5-81ab-13505e10b75f", "Relationships": [{"Type": "CHILD", "Ids": ["d9e92952-db3d-45a6-befb-730e7150e205"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DEL", "Geometry": {"BoundingBox": {"Width": 0.03618374466896057, "Height": 0.012243750505149364, "Left": 0.4246581196784973, "Top": 0.2618671655654907}, "Polygon": [{"X": 0.4246581196784973, "Y": 0.2618905007839203}, {"X": 0.4608306288719177, "Y": 0.2618671655654907}, {"X": 0.4608418643474579, "Y": 0.2740876078605652}, {"X": 0.4246689975261688, "Y": 0.27411091327667236}]}, "Id": "95d15de1-4988-42cd-bb7a-60393be109d2", "Relationships": [{"Type": "CHILD", "Ids": ["be6c5ee2-5517-4eeb-a06e-f479f70a40ec"]}]}, {"BlockType": "LINE", "Confidence": 99.91862487792969, "Text": "WORK ORDER #", "Geometry": {"BoundingBox": {"Width": 0.15203790366649628, "Height": 0.01299773808568716, "Left": 0.5928003787994385, "Top": 0.26100343465805054}, "Polygon": [{"X": 0.5928003787994385, "Y": 0.2611016035079956}, {"X": 0.7448233962059021, "Y": 0.26100343465805054}, {"X": 0.7448382377624512, "Y": 0.2739032804965973}, {"X": 0.5928136110305786, "Y": 0.27400118112564087}]}, "Id": "cec57e10-4dde-4b56-aa4d-f06760418533", "Relationships": [{"Type": "CHILD", "Ids": ["16ec054c-20b5-4cd9-a863-aa8a0106daf3", "2447153e-4231-458e-8536-d10b09ab0473", "80418287-e83b-4974-a39f-269ea208233c"]}]}, {"BlockType": "LINE", "Confidence": 99.79093933105469, "Text": "1025", "Geometry": {"BoundingBox": {"Width": 0.07541435956954956, "Height": 0.01827477477490902, "Left": 0.8736101984977722, "Top": 0.28849515318870544}, "Polygon": [{"X": 0.8736101984977722, "Y": 0.28854355216026306}, {"X": 0.9490005373954773, "Y": 0.28849515318870544}, {"X": 0.9490245580673218, "Y": 0.3067217469215393}, {"X": 0.8736331462860107, "Y": 0.3067699372768402}]}, "Id": "bec3398f-f540-44cd-946b-812e8a74672c", "Relationships": [{"Type": "CHILD", "Ids": ["319b5311-8f59-4d08-ac75-dd8db0fbd29c"]}]}, {"BlockType": "LINE", "Confidence": 71.41163635253906, "Text": "ISK", "Geometry": {"BoundingBox": {"Width": 0.051616184413433075, "Height": 0.022746475413441658, "Left": 0.5122780203819275, "Top": 0.3041526973247528}, "Polygon": [{"X": 0.5122780203819275, "Y": 0.304185688495636}, {"X": 0.5638713836669922, "Y": 0.3041526973247528}, {"X": 0.5638942122459412, "Y": 0.3268663287162781}, {"X": 0.5122998356819153, "Y": 0.3268991708755493}]}, "Id": "eb00020c-858c-43e3-b178-0f13e074b7f3", "Relationships": [{"Type": "CHILD", "Ids": ["4a55fc96-db2f-4577-a5c4-6056e08124af"]}]}, {"BlockType": "LINE", "Confidence": 99.91131591796875, "Text": "X-Rite", "Geometry": {"BoundingBox": {"Width": 0.05952225625514984, "Height": 0.013337465934455395, "Left": 0.0916421189904213, "Top": 0.3353727161884308}, "Polygon": [{"X": 0.0916421189904213, "Y": 0.33541053533554077}, {"X": 0.1511555165052414, "Y": 0.3353727161884308}, {"X": 0.15116438269615173, "Y": 0.348672479391098}, {"X": 0.0916503369808197, "Y": 0.34871017932891846}]}, "Id": "537fc43b-3b00-4ba1-acc5-c0c1123d07bd", "Relationships": [{"Type": "CHILD", "Ids": ["5476a406-3203-4baf-bcee-a5e93153c4f3"]}]}, {"BlockType": "LINE", "Confidence": 98.06879425048828, "Text": "X 1 BH **********", "Geometry": {"BoundingBox": {"Width": 0.18185415863990784, "Height": 0.013331868685781956, "Left": 0.4367141127586365, "Top": 0.33400195837020874}, "Polygon": [{"X": 0.4367141127586365, "Y": 0.3341175615787506}, {"X": 0.618554413318634, "Y": 0.33400195837020874}, {"X": 0.6185683012008667, "Y": 0.3472186028957367}, {"X": 0.43672600388526917, "Y": 0.3473338484764099}]}, "Id": "ea241062-78e2-4c68-b90b-c8e8c1d5b381", "Relationships": [{"Type": "CHILD", "Ids": ["7eb18a03-f401-4c33-8bb7-6d1f9450283f", "7dd6f14a-0618-4767-8cac-1c16848dda0b", "20f51a90-7cc1-485d-b9eb-9d71c1da913d", "baed4612-ad05-46cd-a0ca-8f41813ee23b"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "1035", "Geometry": {"BoundingBox": {"Width": 0.07742656767368317, "Height": 0.021021690219640732, "Left": 0.8656136393547058, "Top": 0.3259267210960388}, "Polygon": [{"X": 0.8656136393547058, "Y": 0.32597601413726807}, {"X": 0.9430126547813416, "Y": 0.3259267210960388}, {"X": 0.9430401921272278, "Y": 0.3468993604183197}, {"X": 0.865639865398407, "Y": 0.34694841504096985}]}, "Id": "7be402c3-6851-46d0-99df-d28072d05bc5", "Relationships": [{"Type": "CHILD", "Ids": ["44376694-28f4-4945-a3dc-168d29a94503"]}]}, {"BlockType": "LINE", "Confidence": 47.785396575927734, "Text": "1 SK", "Geometry": {"BoundingBox": {"Width": 0.05798918381333351, "Height": 0.021306835114955902, "Left": 0.4959995448589325, "Top": 0.43253424763679504}, "Polygon": [{"X": 0.4959995448589325, "Y": 0.43257030844688416}, {"X": 0.5539675354957581, "Y": 0.43253424763679504}, {"X": 0.5539887547492981, "Y": 0.45380520820617676}, {"X": 0.49601975083351135, "Y": 0.45384109020233154}]}, "Id": "6f721533-048d-4cfd-adf6-16eaa341fc4c", "Relationships": [{"Type": "CHILD", "Ids": ["93d4218b-49bd-412b-8a92-e8c632d057ff"]}]}, {"BlockType": "LINE", "Confidence": 99.80149841308594, "Text": "1250", "Geometry": {"BoundingBox": {"Width": 0.09510881453752518, "Height": 0.024248141795396805, "Left": 0.8821449875831604, "Top": 0.42209717631340027}, "Polygon": [{"X": 0.8821449875831604, "Y": 0.422156423330307}, {"X": 0.9772213101387024, "Y": 0.42209717631340027}, {"X": 0.977253794670105, "Y": 0.4462863504886627}, {"X": 0.8821755647659302, "Y": 0.4463452994823456}]}, "Id": "28bee48a-a37a-4f95-9430-e9b73d37a7a0", "Relationships": [{"Type": "CHILD", "Ids": ["54f68ac2-2332-424b-b218-415a94e19fd2"]}]}, {"BlockType": "LINE", "Confidence": 79.89607238769531, "Text": "MKE CUSTOM", "Geometry": {"BoundingBox": {"Width": 0.13366205990314484, "Height": 0.013280344195663929, "Left": 0.09239158034324646, "Top": 0.4537929892539978}, "Polygon": [{"X": 0.09239158034324646, "Y": 0.45387572050094604}, {"X": 0.22604404389858246, "Y": 0.4537929892539978}, {"X": 0.2260536402463913, "Y": 0.4669908285140991}, {"X": 0.09239973872900009, "Y": 0.46707332134246826}]}, "Id": "4ab430a6-8651-40cd-8fdb-1ea93db3b734", "Relationships": [{"Type": "CHILD", "Ids": ["a54fd267-20f0-44b6-8903-47d359703d75", "82403fed-4847-4f4d-b1b3-b6140134dd56"]}]}, {"BlockType": "LINE", "Confidence": 90.14569091796875, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.014294770546257496, "Height": 0.013029608875513077, "Left": 0.3634490966796875, "Top": 0.46200641989707947}, "Polygon": [{"X": 0.3634490966796875, "Y": 0.4620152711868286}, {"X": 0.3777327537536621, "Y": 0.46200641989707947}, {"X": 0.37774384021759033, "Y": 0.4750272333621979}, {"X": 0.3634600341320038, "Y": 0.47503605484962463}]}, "Id": "640bf618-95d4-4e13-9089-ac3b64fd050f", "Relationships": [{"Type": "CHILD", "Ids": ["cb2bab38-6f69-4e9a-9529-30d89ec5310a"]}]}, {"BlockType": "LINE", "Confidence": 98.44667053222656, "Text": "X **********", "Geometry": {"BoundingBox": {"Width": 0.13973268866539001, "Height": 0.013086776249110699, "Left": 0.4394785165786743, "Top": 0.4614695608615875}, "Polygon": [{"X": 0.4394785165786743, "Y": 0.4615558981895447}, {"X": 0.5791980028152466, "Y": 0.4614695608615875}, {"X": 0.5792112350463867, "Y": 0.47447025775909424}, {"X": 0.43949025869369507, "Y": 0.4745563268661499}]}, "Id": "9a9b264f-8ac6-40b9-b464-99f3da40fe7d", "Relationships": [{"Type": "CHILD", "Ids": ["55a38537-a023-4913-937c-98287d9d1486", "8888edc6-73ee-4e14-b3d1-49362a9cf6e0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "MACHINING", "Geometry": {"BoundingBox": {"Width": 0.11939875781536102, "Height": 0.01331488136202097, "Left": 0.09224744141101837, "Top": 0.47421929240226746}, "Polygon": [{"X": 0.09224744141101837, "Y": 0.4742928743362427}, {"X": 0.2116367220878601, "Y": 0.47421929240226746}, {"X": 0.2116461992263794, "Y": 0.48746082186698914}, {"X": 0.09225562959909439, "Y": 0.48753419518470764}]}, "Id": "2c4688e1-ae45-4ce7-a2e7-f2eacf11532a", "Relationships": [{"Type": "CHILD", "Ids": ["5557dece-863f-40b3-a239-c034c6682cb3"]}]}, {"BlockType": "LINE", "Confidence": 97.00155639648438, "Text": "1300", "Geometry": {"BoundingBox": {"Width": 0.08181269466876984, "Height": 0.02233395352959633, "Left": 0.8700106739997864, "Top": 0.4832892119884491}, "Polygon": [{"X": 0.8700106739997864, "Y": 0.48333948850631714}, {"X": 0.951793909072876, "Y": 0.4832892119884491}, {"X": 0.951823353767395, "Y": 0.5055731534957886}, {"X": 0.870038628578186, "Y": 0.5056231617927551}]}, "Id": "495f67b2-2ea6-4496-a902-f23a93505c90", "Relationships": [{"Type": "CHILD", "Ids": ["5557ee44-f1ba-4de1-a9a3-90ebcc241e0d"]}]}, {"BlockType": "LINE", "Confidence": 62.42087936401367, "Text": "Isk", "Geometry": {"BoundingBox": {"Width": 0.10366231948137283, "Height": 0.053209688514471054, "Left": 0.16321435570716858, "Top": 0.5355206727981567}, "Polygon": [{"X": 0.16321435570716858, "Y": 0.5355836749076843}, {"X": 0.2668362259864807, "Y": 0.5355206727981567}, {"X": 0.2668766677379608, "Y": 0.588668167591095}, {"X": 0.16325029730796814, "Y": 0.5887303948402405}]}, "Id": "5b3641c1-f702-4e02-ab71-2daf9aaac763", "Relationships": [{"Type": "CHILD", "Ids": ["5fe797ed-bac3-487e-ad25-26307f3cf5e9"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "1315", "Geometry": {"BoundingBox": {"Width": 0.0761420950293541, "Height": 0.021358123049139977, "Left": 0.8741515874862671, "Top": 0.5384814143180847}, "Polygon": [{"X": 0.8741515874862671, "Y": 0.5385276079177856}, {"X": 0.9502655863761902, "Y": 0.5384814143180847}, {"X": 0.9502937197685242, "Y": 0.5597935318946838}, {"X": 0.874178409576416, "Y": 0.5598395466804504}]}, "Id": "502a9556-b5c6-4508-9232-4f2067077a0a", "Relationships": [{"Type": "CHILD", "Ids": ["9ecca861-7df4-4b6d-9aa5-815160f7a851"]}]}, {"BlockType": "LINE", "Confidence": 64.61595153808594, "Text": "1sk", "Geometry": {"BoundingBox": {"Width": 0.05480049550533295, "Height": 0.02022732049226761, "Left": 0.5054538249969482, "Top": 0.5652329921722412}, "Polygon": [{"X": 0.5054538249969482, "Y": 0.5652660727500916}, {"X": 0.5602341294288635, "Y": 0.5652329921722412}, {"X": 0.5602543354034424, "Y": 0.5854274034500122}, {"X": 0.5054731369018555, "Y": 0.585460364818573}]}, "Id": "e50a3767-44a7-4599-9b29-4901b5692e39", "Relationships": [{"Type": "CHILD", "Ids": ["8bedae1a-fc4b-49c7-adca-c0c66b6c362b"]}]}, {"BlockType": "LINE", "Confidence": 99.93572998046875, "Text": "Zed Engineering", "Geometry": {"BoundingBox": {"Width": 0.15843461453914642, "Height": 0.017106346786022186, "Left": 0.09143631160259247, "Top": 0.5870837569236755}, "Polygon": [{"X": 0.09143631160259247, "Y": 0.5871788859367371}, {"X": 0.24985821545124054, "Y": 0.5870837569236755}, {"X": 0.2498709261417389, "Y": 0.6040953397750854}, {"X": 0.09144681692123413, "Y": 0.6041901111602783}]}, "Id": "42011673-8168-49e8-a7cc-9fae0eca69bf", "Relationships": [{"Type": "CHILD", "Ids": ["ba0ef90a-11a5-4dea-a27c-8ee5438731ce", "4efd6c62-6896-4f5e-a61d-1c76d51901a7"]}]}, {"BlockType": "LINE", "Confidence": 92.3582992553711, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.014318541623651981, "Height": 0.013645639643073082, "Left": 0.36545634269714355, "Top": 0.5938563346862793}, "Polygon": [{"X": 0.36545634269714355, "Y": 0.593864917755127}, {"X": 0.37976324558258057, "Y": 0.5938563346862793}, {"X": 0.37977486848831177, "Y": 0.6074934005737305}, {"X": 0.3654678165912628, "Y": 0.6075019836425781}]}, "Id": "db6ea485-5d4f-47f4-99d7-a52641128c02", "Relationships": [{"Type": "CHILD", "Ids": ["6390553e-3a03-4ba4-bed6-902dc6fe6995"]}]}, {"BlockType": "LINE", "Confidence": 95.47183990478516, "Text": "X **********", "Geometry": {"BoundingBox": {"Width": 0.14154808223247528, "Height": 0.013571676798164845, "Left": 0.4418497085571289, "Top": 0.5923745036125183}, "Polygon": [{"X": 0.4418497085571289, "Y": 0.5924594402313232}, {"X": 0.5833840370178223, "Y": 0.5923745036125183}, {"X": 0.5833978056907654, "Y": 0.6058616042137146}, {"X": 0.4418618977069855, "Y": 0.6059461832046509}]}, "Id": "13d205e6-6fea-4cc3-be51-b27ff2939106", "Relationships": [{"Type": "CHILD", "Ids": ["b6a2204d-ada0-43d7-82f3-39b5f3713392", "d34b8a22-9735-4db2-a5b0-1c41cba8b17f"]}]}, {"BlockType": "LINE", "Confidence": 99.9353256225586, "Text": "(formerly Renishaw)", "Geometry": {"BoundingBox": {"Width": 0.19534368813037872, "Height": 0.020287584513425827, "Left": 0.0931345596909523, "Top": 0.606086790561676}, "Polygon": [{"X": 0.0931345596909523, "Y": 0.6062036156654358}, {"X": 0.2884625494480133, "Y": 0.606086790561676}, {"X": 0.2884782552719116, "Y": 0.626258134841919}, {"X": 0.09314704686403275, "Y": 0.6263744235038757}]}, "Id": "fd79d1b3-d545-4790-9c62-ea7bb3769081", "Relationships": [{"Type": "CHILD", "Ids": ["ea5404df-8e9f-4814-85c4-0bda30c05097", "b2c04414-bdb5-416c-b5af-c96a0eebfa24"]}]}, {"BlockType": "LINE", "Confidence": 99.6314926147461, "Text": "1330", "Geometry": {"BoundingBox": {"Width": 0.07649876922369003, "Height": 0.025058606639504433, "Left": 0.8878089785575867, "Top": 0.6226698756217957}, "Polygon": [{"X": 0.8878089785575867, "Y": 0.622715413570404}, {"X": 0.9642744064331055, "Y": 0.6226698756217957}, {"X": 0.9643077254295349, "Y": 0.6476832032203674}, {"X": 0.8878406882286072, "Y": 0.6477285027503967}]}, "Id": "79ff6ad2-ab0f-4e0b-b097-f30c7f0c3fd4", "Relationships": [{"Type": "CHILD", "Ids": ["0b0e3a88-8cef-4764-b58f-7e21def07730"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "1140", "Geometry": {"BoundingBox": {"Width": 0.07566630095243454, "Height": 0.020148571580648422, "Left": 0.8858582973480225, "Top": 0.681958794593811}, "Polygon": [{"X": 0.8858582973480225, "Y": 0.6820032000541687}, {"X": 0.9614979028701782, "Y": 0.681958794593811}, {"X": 0.9615246057510376, "Y": 0.7020631432533264}, {"X": 0.8858838081359863, "Y": 0.7021073698997498}]}, "Id": "51f90621-288d-4386-85f3-cdd7d82fe149", "Relationships": [{"Type": "CHILD", "Ids": ["82fd2bfd-a1de-4628-bc77-6998bc57bbc0"]}]}, {"BlockType": "LINE", "Confidence": 97.86441802978516, "Text": "4 Bsk", "Geometry": {"BoundingBox": {"Width": 0.11494189500808716, "Height": 0.03051259182393551, "Left": 0.49482741951942444, "Top": 0.691970705986023}, "Polygon": [{"X": 0.49482741951942444, "Y": 0.6920379996299744}, {"X": 0.6097375750541687, "Y": 0.691970705986023}, {"X": 0.6097692847251892, "Y": 0.7224164009094238}, {"X": 0.4948562681674957, "Y": 0.7224832773208618}]}, "Id": "a49b68c1-1d73-42c0-9640-a3aa86d44700", "Relationships": [{"Type": "CHILD", "Ids": ["d8a17978-64b5-4f53-9ba7-fd30060358ab", "f3046dda-2dcb-4e66-82ed-3168367fe756"]}]}, {"BlockType": "LINE", "Confidence": 89.32311248779297, "Text": "Bucher 3BSK Hydraulics", "Geometry": {"BoundingBox": {"Width": 0.18080604076385498, "Height": 0.04770384728908539, "Left": 0.09192696958780289, "Top": 0.7091639637947083}, "Polygon": [{"X": 0.09192696958780289, "Y": 0.7092694640159607}, {"X": 0.2726965546607971, "Y": 0.7091639637947083}, {"X": 0.27273300290107727, "Y": 0.7567635178565979}, {"X": 0.09195637702941895, "Y": 0.7568678259849548}]}, "Id": "af9ae908-1f6d-48ca-b660-c990b44f05e5", "Relationships": [{"Type": "CHILD", "Ids": ["94843f04-8027-4965-921d-7587a8abc56c", "dffd9e47-1dd1-4e1a-bc8e-b5c08eb61707", "0080a865-eb4c-41e8-8fcc-9051c845c98e"]}]}, {"BlockType": "LINE", "Confidence": 89.76158905029297, "Text": "SO-2143111 ********** ********** SO=", "Geometry": {"BoundingBox": {"Width": 0.3502083122730255, "Height": 0.016775893047451973, "Left": 0.49170804023742676, "Top": 0.7192971706390381}, "Polygon": [{"X": 0.49170804023742676, "Y": 0.7195010185241699}, {"X": 0.8418959379196167, "Y": 0.7192971706390381}, {"X": 0.8419163227081299, "Y": 0.7358700037002563}, {"X": 0.4917236864566803, "Y": 0.7360730171203613}]}, "Id": "37be5bbe-dc30-468a-8e2e-6e6f0908b92c", "Relationships": [{"Type": "CHILD", "Ids": ["36a3b178-75ef-40c4-a02f-c67edc40fc91", "7220471e-b1b4-4786-83e4-c413285df5a8", "a411fe1e-8e9b-40e0-a14c-f8ee37d2a557", "81239a74-41c1-416c-a045-276ab55ea1a0"]}]}, {"BlockType": "LINE", "Confidence": 96.41581726074219, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.01211322657763958, "Height": 0.010712170973420143, "Left": 0.3677826225757599, "Top": 0.7383894920349121}, "Polygon": [{"X": 0.3677826225757599, "Y": 0.7383965253829956}, {"X": 0.3798867166042328, "Y": 0.7383894920349121}, {"X": 0.3798958659172058, "Y": 0.7490946650505066}, {"X": 0.36779165267944336, "Y": 0.7491016387939453}]}, "Id": "f96b79e6-da7d-4d38-88e7-c84f76ddaf67", "Relationships": [{"Type": "CHILD", "Ids": ["1966d38b-b195-4fdd-8db0-86756ddad46f"]}]}, {"BlockType": "LINE", "Confidence": 89.43878173828125, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.01176295056939125, "Height": 0.011210707016289234, "Left": 0.44537925720214844, "Top": 0.7361752390861511}, "Polygon": [{"X": 0.44537925720214844, "Y": 0.7361820936203003}, {"X": 0.457131952047348, "Y": 0.7361752390861511}, {"X": 0.4571422040462494, "Y": 0.7473791837692261}, {"X": 0.44538941979408264, "Y": 0.7473859786987305}]}, "Id": "c45400ae-7f91-4eaa-a62a-b5595d8fd42a", "Relationships": [{"Type": "CHILD", "Ids": ["b7748e84-1dbd-40c1-bc3b-7580dd795788"]}]}, {"BlockType": "LINE", "Confidence": 92.25824737548828, "Text": "1200", "Geometry": {"BoundingBox": {"Width": 0.0895143523812294, "Height": 0.020893357694149017, "Left": 0.8964565396308899, "Top": 0.725365161895752}, "Polygon": [{"X": 0.8964565396308899, "Y": 0.7254171967506409}, {"X": 0.9859427809715271, "Y": 0.725365161895752}, {"X": 0.9859709143638611, "Y": 0.7462067604064941}, {"X": 0.8964831233024597, "Y": 0.746258556842804}]}, "Id": "9bd87941-f1c8-44f1-a7db-bc37d72dceb1", "Relationships": [{"Type": "CHILD", "Ids": ["049d6a6d-a7a3-4b53-997a-3d4d9f1ba724"]}]}, {"BlockType": "LINE", "Confidence": 99.10076141357422, "Text": "2143114", "Geometry": {"BoundingBox": {"Width": 0.06698443740606308, "Height": 0.012590054422616959, "Left": 0.49198946356773376, "Top": 0.7416430115699768}, "Polygon": [{"X": 0.49198946356773376, "Y": 0.7416818141937256}, {"X": 0.5589613318443298, "Y": 0.7416430115699768}, {"X": 0.5589739084243774, "Y": 0.754194438457489}, {"X": 0.49200132489204407, "Y": 0.7542330622673035}]}, "Id": "06d18bca-ed5f-451d-8b82-39665e00b860", "Relationships": [{"Type": "CHILD", "Ids": ["e91b7d4f-1c47-4486-ba8d-5ce8dfae6d18"]}]}, {"BlockType": "LINE", "Confidence": 97.70348358154297, "Text": "50-2142101", "Geometry": {"BoundingBox": {"Width": 0.17951054871082306, "Height": 0.022924115881323814, "Left": 0.5686962008476257, "Top": 0.7474031448364258}, "Polygon": [{"X": 0.5686962008476257, "Y": 0.7475069761276245}, {"X": 0.7481803894042969, "Y": 0.7474031448364258}, {"X": 0.7482067346572876, "Y": 0.7702240347862244}, {"X": 0.568719208240509, "Y": 0.7703272700309753}]}, "Id": "99b476f8-93fe-4cf5-888e-e683a7d22ce7", "Relationships": [{"Type": "CHILD", "Ids": ["ce6cd6b4-3169-4da9-be3e-ad74e27724d8"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "GRAND RAPIDS", "Geometry": {"BoundingBox": {"Width": 0.15110214054584503, "Height": 0.016991661861538887, "Left": 0.460280179977417, "Top": 0.981703519821167}, "Polygon": [{"X": 0.460280179977417, "Y": 0.9817859530448914}, {"X": 0.6113647222518921, "Y": 0.981703519821167}, {"X": 0.6113823056221008, "Y": 0.9986130595207214}, {"X": 0.46029573678970337, "Y": 0.9986951351165771}]}, "Id": "2f00b2ef-7441-4267-8679-0a26b97f4179", "Relationships": [{"Type": "CHILD", "Ids": ["06035a36-4249-408d-9184-3bfde07ba525", "60a007d6-129a-47d8-bcdf-8a34417abcb6"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058128464967012405, "Height": 0.012404133565723896, "Left": 0.24907651543617249, "Top": 0.10196449607610703}, "Polygon": [{"X": 0.24907651543617249, "Y": 0.102003313601017}, {"X": 0.30719515681266785, "Y": 0.10196449607610703}, {"X": 0.3072049617767334, "Y": 0.11432991921901703}, {"X": 0.24908573925495148, "Y": 0.11436863243579865}]}, "Id": "970f4a14-76a1-4f6e-9f33-d1b93bf0579b"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "2-Sep", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05300193279981613, "Height": 0.015237096697092056, "Left": 0.33041971921920776, "Top": 0.10213357955217361}, "Polygon": [{"X": 0.33041971921920776, "Y": 0.1021689623594284}, {"X": 0.3834086060523987, "Y": 0.10213357955217361}, {"X": 0.3834216296672821, "Y": 0.11733540147542953}, {"X": 0.33043205738067627, "Y": 0.11737067252397537}]}, "Id": "250cc41a-27a1-4c21-89c5-781b720310ad"}, {"BlockType": "WORD", "Confidence": 99.6875, "Text": "UNIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051272813230752945, "Height": 0.012293647043406963, "Left": 0.23158347606658936, "Top": 0.13832055032253265}, "Polygon": [{"X": 0.23158347606658936, "Y": 0.13835452497005463}, {"X": 0.2828468084335327, "Y": 0.13832055032253265}, {"X": 0.282856285572052, "Y": 0.1505803018808365}, {"X": 0.23159246146678925, "Y": 0.1506141871213913}]}, "Id": "762ceb40-802f-4b31-a5a7-9954b6410929"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021893227472901344, "Height": 0.012132336385548115, "Left": 0.28561145067214966, "Top": 0.1384291797876358}, "Polygon": [{"X": 0.28561145067214966, "Y": 0.13844367861747742}, {"X": 0.3074950575828552, "Y": 0.1384291797876358}, {"X": 0.30750468373298645, "Y": 0.15054704248905182}, {"X": 0.28562086820602417, "Y": 0.15056151151657104}]}, "Id": "93f60ece-ed38-4b66-bb91-2ab6ba073b58"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "6063/283420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12212570011615753, "Height": 0.014705550856888294, "Left": 0.33044055104255676, "Top": 0.13764864206314087}, "Polygon": [{"X": 0.33044055104255676, "Y": 0.13772960007190704}, {"X": 0.4525528848171234, "Y": 0.13764864206314087}, {"X": 0.4525662362575531, "Y": 0.152273491024971}, {"X": 0.33045244216918945, "Y": 0.1523541957139969}]}, "Id": "d3d5fffc-53ff-4cd7-b919-011f26b4bf22"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050398245453834534, "Height": 0.01245021354407072, "Left": 0.48266467452049255, "Top": 0.15572160482406616}, "Polygon": [{"X": 0.48266467452049255, "Y": 0.15575487911701202}, {"X": 0.5330507755279541, "Y": 0.15572160482406616}, {"X": 0.5330629348754883, "Y": 0.16813862323760986}, {"X": 0.48267632722854614, "Y": 0.16817182302474976}]}, "Id": "b2430c1c-81d1-453c-863d-bd0ee9609068"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ready", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058782294392585754, "Height": 0.015346383675932884, "Left": 0.5372443795204163, "Top": 0.15565358102321625}, "Polygon": [{"X": 0.5372443795204163, "Y": 0.15569239854812622}, {"X": 0.5960108637809753, "Y": 0.15565358102321625}, {"X": 0.5960266590118408, "Y": 0.17096129059791565}, {"X": 0.5372593998908997, "Y": 0.17099997401237488}]}, "Id": "befc481e-075a-4167-ba85-6125758bd1ea"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050173934549093246, "Height": 0.011968777514994144, "Left": 0.6016469597816467, "Top": 0.15613901615142822}, "Polygon": [{"X": 0.6016469597816467, "Y": 0.15617212653160095}, {"X": 0.6518080234527588, "Y": 0.15613901615142822}, {"X": 0.6518208980560303, "Y": 0.16807475686073303}, {"X": 0.6016592979431152, "Y": 0.1681077927350998}]}, "Id": "eac08210-d6f2-4181-972a-fe12ff47c267"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "DRIVER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08015662431716919, "Height": 0.012394125573337078, "Left": 0.22103999555110931, "Top": 0.17468033730983734}, "Polygon": [{"X": 0.22103999555110931, "Y": 0.17473305761814117}, {"X": 0.30118685960769653, "Y": 0.17468033730983734}, {"X": 0.3011966049671173, "Y": 0.1870218813419342}, {"X": 0.22104892134666443, "Y": 0.1870744675397873}]}, "Id": "f16cd1f5-267c-4553-9057-620a4baf57e4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "KARRA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06346306204795837, "Height": 0.012359020300209522, "Left": 0.33124956488609314, "Top": 0.17417529225349426}, "Polygon": [{"X": 0.33124956488609314, "Y": 0.17421704530715942}, {"X": 0.3947019577026367, "Y": 0.17417529225349426}, {"X": 0.3947126269340515, "Y": 0.1864926815032959}, {"X": 0.3312595784664154, "Y": 0.1865343153476715}]}, "Id": "a4a8faf2-c0cc-42d5-a48d-6f02f7b3c440"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05074506625533104, "Height": 0.0126723637804389, "Left": 0.4835941791534424, "Top": 0.19157077372074127}, "Polygon": [{"X": 0.4835941791534424, "Y": 0.19160403311252594}, {"X": 0.5343268513679504, "Y": 0.19157077372074127}, {"X": 0.5343392491340637, "Y": 0.20420998334884644}, {"X": 0.4836060404777527, "Y": 0.20424313843250275}]}, "Id": "8d898f9a-a506-4291-ae8e-f63f99ef6e7e"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Departure:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0987502932548523, "Height": 0.015064951963722706, "Left": 0.5385501980781555, "Top": 0.1917208731174469}, "Polygon": [{"X": 0.5385501980781555, "Y": 0.19178558886051178}, {"X": 0.637284517288208, "Y": 0.1917208731174469}, {"X": 0.6373004913330078, "Y": 0.20672132074832916}, {"X": 0.5385649800300598, "Y": 0.20678582787513733}]}, "Id": "d5cd5849-c2b4-45e9-862d-c190724413fa"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DEPARTURE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11919339746236801, "Height": 0.012825488112866879, "Left": 0.1237560510635376, "Top": 0.21159471571445465}, "Polygon": [{"X": 0.1237560510635376, "Y": 0.21167249977588654}, {"X": 0.2429399937391281, "Y": 0.21159471571445465}, {"X": 0.2429494559764862, "Y": 0.22434262931346893}, {"X": 0.12376426160335541, "Y": 0.2244202047586441}]}, "Id": "19fbe5b7-d564-492a-a076-3fd5395a9536"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TIME:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05343512445688248, "Height": 0.012469845823943615, "Left": 0.2481691688299179, "Top": 0.21138478815555573}, "Polygon": [{"X": 0.2481691688299179, "Y": 0.21141965687274933}, {"X": 0.30159446597099304, "Y": 0.21138478815555573}, {"X": 0.301604300737381, "Y": 0.22381985187530518}, {"X": 0.24817843735218048, "Y": 0.22385463118553162}]}, "Id": "e7b0a5f8-5ff2-4beb-b3ea-0291573feedb"}, {"BlockType": "WORD", "Confidence": 99.833984375, "Text": "5am", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03893620893359184, "Height": 0.012485845945775509, "Left": 0.3316425681114197, "Top": 0.2109547108411789}, "Polygon": [{"X": 0.3316425681114197, "Y": 0.2109801173210144}, {"X": 0.3705682158470154, "Y": 0.2109547108411789}, {"X": 0.3705787658691406, "Y": 0.22341522574424744}, {"X": 0.3316527009010315, "Y": 0.22344055771827698}]}, "Id": "e703bc8a-03ca-46d5-aef9-129402ae02b1"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "PICK-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.051167819648981094, "Height": 0.012044334784150124, "Left": 0.34116190671920776, "Top": 0.24282944202423096}, "Polygon": [{"X": 0.34116190671920776, "Y": 0.24286261200904846}, {"X": 0.39231935143470764, "Y": 0.24282944202423096}, {"X": 0.39232972264289856, "Y": 0.2548407018184662}, {"X": 0.3411717712879181, "Y": 0.25487378239631653}]}, "Id": "b1d5606e-a324-46f3-8d93-217a13f30664"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04733435809612274, "Height": 0.011899909004569054, "Left": 0.8630751967430115, "Top": 0.25322225689888}, "Polygon": [{"X": 0.8630751967430115, "Y": 0.25325286388397217}, {"X": 0.9103942513465881, "Y": 0.25322225689888}, {"X": 0.9104095697402954, "Y": 0.2650916278362274}, {"X": 0.8630900382995605, "Y": 0.2651221752166748}]}, "Id": "6ae02070-b495-43ba-854f-15cf62f4225d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04834018647670746, "Height": 0.011118972674012184, "Left": 0.914879322052002, "Top": 0.2528887987136841}, "Polygon": [{"X": 0.914879322052002, "Y": 0.2529200613498688}, {"X": 0.9632047414779663, "Y": 0.2528887987136841}, {"X": 0.9632195234298706, "Y": 0.2639766037464142}, {"X": 0.9148936867713928, "Y": 0.2640077769756317}]}, "Id": "04821ee1-cb01-461e-a6c1-6df7117bdacf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CUSTOMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10767072439193726, "Height": 0.01283766608685255, "Left": 0.15752527117729187, "Top": 0.26422953605651855}, "Polygon": [{"X": 0.15752527117729187, "Y": 0.2642990052700043}, {"X": 0.2651863098144531, "Y": 0.26422953605651855}, {"X": 0.2651959955692291, "Y": 0.2769978940486908}, {"X": 0.15753385424613953, "Y": 0.2770671844482422}]}, "Id": "9572d8bb-f77b-4606-97a7-322967ed2bca"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "UP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025493381544947624, "Height": 0.0126876225695014, "Left": 0.3544650375843048, "Top": 0.26230761408805847}, "Polygon": [{"X": 0.3544650375843048, "Y": 0.2623240649700165}, {"X": 0.37994760274887085, "Y": 0.26230761408805847}, {"X": 0.3799584209918976, "Y": 0.2749788463115692}, {"X": 0.35447558760643005, "Y": 0.27499523758888245}]}, "Id": "d9e92952-db3d-45a6-befb-730e7150e205"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03618374466896057, "Height": 0.012243750505149364, "Left": 0.4246581196784973, "Top": 0.2618671655654907}, "Polygon": [{"X": 0.4246581196784973, "Y": 0.2618905007839203}, {"X": 0.4608306288719177, "Y": 0.2618671655654907}, {"X": 0.4608418643474579, "Y": 0.2740876078605652}, {"X": 0.4246689975261688, "Y": 0.27411091327667236}]}, "Id": "be6c5ee2-5517-4eeb-a06e-f479f70a40ec"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WORK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06004638597369194, "Height": 0.012375444173812866, "Left": 0.5928009152412415, "Top": 0.261625736951828}, "Polygon": [{"X": 0.5928009152412415, "Y": 0.2616645097732544}, {"X": 0.6528340578079224, "Y": 0.261625736951828}, {"X": 0.6528473496437073, "Y": 0.27396252751350403}, {"X": 0.5928136110305786, "Y": 0.27400118112564087}]}, "Id": "16ec054c-20b5-4cd9-a863-aa8a0106daf3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06737028062343597, "Height": 0.012443634681403637, "Left": 0.6583317518234253, "Top": 0.2611883580684662}, "Polygon": [{"X": 0.6583317518234253, "Y": 0.26123183965682983}, {"X": 0.7256879210472107, "Y": 0.2611883580684662}, {"X": 0.7257020473480225, "Y": 0.2735885977745056}, {"X": 0.6583451628684998, "Y": 0.2736319899559021}]}, "Id": "2447153e-4231-458e-8536-d10b09ab0473"}, {"BlockType": "WORD", "Confidence": 99.755859375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013204371556639671, "Height": 0.012118943966925144, "Left": 0.7316330075263977, "Top": 0.26100343465805054}, "Polygon": [{"X": 0.7316330075263977, "Y": 0.2610119581222534}, {"X": 0.7448233962059021, "Y": 0.26100343465805054}, {"X": 0.7448373436927795, "Y": 0.2731139063835144}, {"X": 0.7316467761993408, "Y": 0.2731224000453949}]}, "Id": "80418287-e83b-4974-a39f-269ea208233c"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "1025", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07541435956954956, "Height": 0.01827477477490902, "Left": 0.8736101984977722, "Top": 0.28849515318870544}, "Polygon": [{"X": 0.8736101984977722, "Y": 0.28854355216026306}, {"X": 0.9490005373954773, "Y": 0.28849515318870544}, {"X": 0.9490245580673218, "Y": 0.3067217469215393}, {"X": 0.8736331462860107, "Y": 0.3067699372768402}]}, "Id": "319b5311-8f59-4d08-ac75-dd8db0fbd29c"}, {"BlockType": "WORD", "Confidence": 71.41163635253906, "Text": "ISK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.051616184413433075, "Height": 0.022746475413441658, "Left": 0.5122780203819275, "Top": 0.3041526973247528}, "Polygon": [{"X": 0.5122780203819275, "Y": 0.304185688495636}, {"X": 0.5638713836669922, "Y": 0.3041526973247528}, {"X": 0.5638942122459412, "Y": 0.3268663287162781}, {"X": 0.5122998356819153, "Y": 0.3268991708755493}]}, "Id": "4a55fc96-db2f-4577-a5c4-6056e08124af"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "X-Rite", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05952225625514984, "Height": 0.013337465934455395, "Left": 0.0916421189904213, "Top": 0.3353727161884308}, "Polygon": [{"X": 0.0916421189904213, "Y": 0.33541053533554077}, {"X": 0.1511555165052414, "Y": 0.3353727161884308}, {"X": 0.15116438269615173, "Y": 0.348672479391098}, {"X": 0.0916503369808197, "Y": 0.34871017932891846}]}, "Id": "5476a406-3203-4baf-bcee-a5e93153c4f3"}, {"BlockType": "WORD", "Confidence": 94.9792709350586, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01338166743516922, "Height": 0.013218274340033531, "Left": 0.4367141127586365, "Top": 0.33411556482315063}, "Polygon": [{"X": 0.4367141127586365, "Y": 0.33412405848503113}, {"X": 0.45008376240730286, "Y": 0.33411556482315063}, {"X": 0.4500957727432251, "Y": 0.3473253548145294}, {"X": 0.43672600388526917, "Y": 0.3473338484764099}]}, "Id": "7eb18a03-f401-4c33-8bb7-6d1f9450283f"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007744918577373028, "Height": 0.01034898217767477, "Left": 0.48381149768829346, "Top": 0.3349463939666748}, "Polygon": [{"X": 0.48381149768829346, "Y": 0.3349513113498688}, {"X": 0.4915466606616974, "Y": 0.3349463939666748}, {"X": 0.49155643582344055, "Y": 0.34529048204421997}, {"X": 0.48382121324539185, "Y": 0.34529539942741394}]}, "Id": "7dd6f14a-0618-4767-8cac-1c16848dda0b"}, {"BlockType": "WORD", "Confidence": 97.96875, "Text": "BH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020745867863297462, "Height": 0.010655850172042847, "Left": 0.49813157320022583, "Top": 0.33464792370796204}, "Polygon": [{"X": 0.49813157320022583, "Y": 0.3346610963344574}, {"X": 0.5188671350479126, "Y": 0.33464792370796204}, {"X": 0.5188774466514587, "Y": 0.3452906310558319}, {"X": 0.49814167618751526, "Y": 0.3453037738800049}]}, "Id": "20f51a90-7cc1-485d-b9eb-9d71c1da913d"}, {"BlockType": "WORD", "Confidence": 99.4345932006836, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09354967623949051, "Height": 0.01111269649118185, "Left": 0.5250163078308105, "Top": 0.33400195837020874}, "Polygon": [{"X": 0.5250163078308105, "Y": 0.3340614140033722}, {"X": 0.618554413318634, "Y": 0.33400195837020874}, {"X": 0.6185660362243652, "Y": 0.34505534172058105}, {"X": 0.5250270962715149, "Y": 0.34511467814445496}]}, "Id": "baed4612-ad05-46cd-a0ca-8f41813ee23b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "1035", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07742656767368317, "Height": 0.021021690219640732, "Left": 0.8656136393547058, "Top": 0.3259267210960388}, "Polygon": [{"X": 0.8656136393547058, "Y": 0.32597601413726807}, {"X": 0.9430126547813416, "Y": 0.3259267210960388}, {"X": 0.9430401921272278, "Y": 0.3468993604183197}, {"X": 0.865639865398407, "Y": 0.34694841504096985}]}, "Id": "44376694-28f4-4945-a3dc-168d29a94503"}, {"BlockType": "WORD", "Confidence": 47.785396575927734, "Text": "1 SK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05798918381333351, "Height": 0.021306835114955902, "Left": 0.4959995448589325, "Top": 0.43253424763679504}, "Polygon": [{"X": 0.4959995448589325, "Y": 0.43257030844688416}, {"X": 0.5539675354957581, "Y": 0.43253424763679504}, {"X": 0.5539887547492981, "Y": 0.45380520820617676}, {"X": 0.49601975083351135, "Y": 0.45384109020233154}]}, "Id": "93d4218b-49bd-412b-8a92-e8c632d057ff"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "1250", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.09510881453752518, "Height": 0.024248141795396805, "Left": 0.8821449875831604, "Top": 0.42209717631340027}, "Polygon": [{"X": 0.8821449875831604, "Y": 0.422156423330307}, {"X": 0.9772213101387024, "Y": 0.42209717631340027}, {"X": 0.977253794670105, "Y": 0.4462863504886627}, {"X": 0.8821755647659302, "Y": 0.4463452994823456}]}, "Id": "54f68ac2-2332-424b-b218-415a94e19fd2"}, {"BlockType": "WORD", "Confidence": 59.79212951660156, "Text": "MKE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04230092093348503, "Height": 0.012976177036762238, "Left": 0.0923917293548584, "Top": 0.4540971517562866}, "Polygon": [{"X": 0.0923917293548584, "Y": 0.454123318195343}, {"X": 0.13468420505523682, "Y": 0.4540971517562866}, {"X": 0.13469265401363373, "Y": 0.46704721450805664}, {"X": 0.09239973872900009, "Y": 0.46707332134246826}]}, "Id": "a54fd267-20f0-44b6-8903-47d359703d75"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CUSTOM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08424998819828033, "Height": 0.013159574009478092, "Left": 0.1418035924434662, "Top": 0.4537929892539978}, "Polygon": [{"X": 0.1418035924434662, "Y": 0.45384514331817627}, {"X": 0.22604404389858246, "Y": 0.4537929892539978}, {"X": 0.22605358064174652, "Y": 0.4669005572795868}, {"X": 0.14181222021579742, "Y": 0.4669525623321533}]}, "Id": "82403fed-4847-4f4d-b1b3-b6140134dd56"}, {"BlockType": "WORD", "Confidence": 90.14569091796875, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014294770546257496, "Height": 0.013029608875513077, "Left": 0.3634490966796875, "Top": 0.46200641989707947}, "Polygon": [{"X": 0.3634490966796875, "Y": 0.4620152711868286}, {"X": 0.3777327537536621, "Y": 0.46200641989707947}, {"X": 0.37774384021759033, "Y": 0.4750272333621979}, {"X": 0.3634600341320038, "Y": 0.47503605484962463}]}, "Id": "cb2bab38-6f69-4e9a-9529-30d89ec5310a"}, {"BlockType": "WORD", "Confidence": 97.65963745117188, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013684896752238274, "Height": 0.012953940778970718, "Left": 0.4394785761833191, "Top": 0.4616023898124695}, "Polygon": [{"X": 0.4394785761833191, "Y": 0.4616108536720276}, {"X": 0.4531516432762146, "Y": 0.4616023898124695}, {"X": 0.4531634747982025, "Y": 0.4745479226112366}, {"X": 0.43949025869369507, "Y": 0.4745563268661499}]}, "Id": "55a38537-a023-4913-937c-98287d9d1486"}, {"BlockType": "WORD", "Confidence": 99.23369598388672, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09331873804330826, "Height": 0.011114976368844509, "Left": 0.4858905076980591, "Top": 0.4614695608615875}, "Polygon": [{"X": 0.4858905076980591, "Y": 0.4615272283554077}, {"X": 0.5791980028152466, "Y": 0.4614695608615875}, {"X": 0.5792092680931091, "Y": 0.47252702713012695}, {"X": 0.4859009087085724, "Y": 0.4725845456123352}]}, "Id": "8888edc6-73ee-4e14-b3d1-49362a9cf6e0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MACHINING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11939875781536102, "Height": 0.01331488136202097, "Left": 0.09224744141101837, "Top": 0.47421929240226746}, "Polygon": [{"X": 0.09224744141101837, "Y": 0.4742928743362427}, {"X": 0.2116367220878601, "Y": 0.47421929240226746}, {"X": 0.2116461992263794, "Y": 0.48746082186698914}, {"X": 0.09225562959909439, "Y": 0.48753419518470764}]}, "Id": "5557dece-863f-40b3-a239-c034c6682cb3"}, {"BlockType": "WORD", "Confidence": 97.00155639648438, "Text": "1300", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08181269466876984, "Height": 0.02233395352959633, "Left": 0.8700106739997864, "Top": 0.4832892119884491}, "Polygon": [{"X": 0.8700106739997864, "Y": 0.48333948850631714}, {"X": 0.951793909072876, "Y": 0.4832892119884491}, {"X": 0.951823353767395, "Y": 0.5055731534957886}, {"X": 0.870038628578186, "Y": 0.5056231617927551}]}, "Id": "5557ee44-f1ba-4de1-a9a3-90ebcc241e0d"}, {"BlockType": "WORD", "Confidence": 62.42087936401367, "Text": "Isk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.10366231948137283, "Height": 0.053209688514471054, "Left": 0.16321435570716858, "Top": 0.5355206727981567}, "Polygon": [{"X": 0.16321435570716858, "Y": 0.5355836749076843}, {"X": 0.2668362259864807, "Y": 0.5355206727981567}, {"X": 0.2668766677379608, "Y": 0.588668167591095}, {"X": 0.16325029730796814, "Y": 0.5887303948402405}]}, "Id": "5fe797ed-bac3-487e-ad25-26307f3cf5e9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1315", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0761420950293541, "Height": 0.021358123049139977, "Left": 0.8741515874862671, "Top": 0.5384814143180847}, "Polygon": [{"X": 0.8741515874862671, "Y": 0.5385276079177856}, {"X": 0.9502655863761902, "Y": 0.5384814143180847}, {"X": 0.9502937197685242, "Y": 0.5597935318946838}, {"X": 0.874178409576416, "Y": 0.5598395466804504}]}, "Id": "9ecca861-7df4-4b6d-9aa5-815160f7a851"}, {"BlockType": "WORD", "Confidence": 64.61595153808594, "Text": "1sk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05480049550533295, "Height": 0.02022732049226761, "Left": 0.5054538249969482, "Top": 0.5652329921722412}, "Polygon": [{"X": 0.5054538249969482, "Y": 0.5652660727500916}, {"X": 0.5602341294288635, "Y": 0.5652329921722412}, {"X": 0.5602543354034424, "Y": 0.5854274034500122}, {"X": 0.5054731369018555, "Y": 0.585460364818573}]}, "Id": "8bedae1a-fc4b-49c7-adca-c0c66b6c362b"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03627008944749832, "Height": 0.01377758290618658, "Left": 0.09143692255020142, "Top": 0.5881506204605103}, "Polygon": [{"X": 0.09143692255020142, "Y": 0.5881723761558533}, {"X": 0.1276981085538864, "Y": 0.5881506204605103}, {"X": 0.12770701944828033, "Y": 0.6019065380096436}, {"X": 0.09144541621208191, "Y": 0.6019282341003418}]}, "Id": "ba0ef90a-11a5-4dea-a27c-8ee5438731ce"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Engineering", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11407791823148727, "Height": 0.01707981340587139, "Left": 0.13579301536083221, "Top": 0.5870837569236755}, "Polygon": [{"X": 0.13579301536083221, "Y": 0.5871522426605225}, {"X": 0.24985821545124054, "Y": 0.5870837569236755}, {"X": 0.2498709261417389, "Y": 0.6040953397750854}, {"X": 0.13580413162708282, "Y": 0.6041635870933533}]}, "Id": "4efd6c62-6896-4f5e-a61d-1c76d51901a7"}, {"BlockType": "WORD", "Confidence": 92.3582992553711, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014318541623651981, "Height": 0.013645639643073082, "Left": 0.36545634269714355, "Top": 0.5938563346862793}, "Polygon": [{"X": 0.36545634269714355, "Y": 0.593864917755127}, {"X": 0.37976324558258057, "Y": 0.5938563346862793}, {"X": 0.37977486848831177, "Y": 0.6074934005737305}, {"X": 0.3654678165912628, "Y": 0.6075019836425781}]}, "Id": "6390553e-3a03-4ba4-bed6-902dc6fe6995"}, {"BlockType": "WORD", "Confidence": 91.42119598388672, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.013713042251765728, "Height": 0.013344217091798782, "Left": 0.44184985756874084, "Top": 0.592602014541626}, "Polygon": [{"X": 0.44184985756874084, "Y": 0.5926101803779602}, {"X": 0.4555506706237793, "Y": 0.592602014541626}, {"X": 0.45556288957595825, "Y": 0.6059380173683167}, {"X": 0.4418618977069855, "Y": 0.6059461832046509}]}, "Id": "b6a2204d-ada0-43d7-82f3-39b5f3713392"}, {"BlockType": "WORD", "Confidence": 99.5224838256836, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09485774487257004, "Height": 0.01150088757276535, "Left": 0.4885379672050476, "Top": 0.5923745036125183}, "Polygon": [{"X": 0.4885379672050476, "Y": 0.5924314260482788}, {"X": 0.5833840370178223, "Y": 0.5923745036125183}, {"X": 0.5833957195281982, "Y": 0.6038186550140381}, {"X": 0.48854875564575195, "Y": 0.6038753986358643}]}, "Id": "d34b8a22-9735-4db2-a5b0-1c41cba8b17f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "(formerly", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08938785642385483, "Height": 0.017818110063672066, "Left": 0.09313604980707169, "Top": 0.6085562705993652}, "Polygon": [{"X": 0.09313604980707169, "Y": 0.608609676361084}, {"X": 0.1825116127729416, "Y": 0.6085562705993652}, {"X": 0.1825239062309265, "Y": 0.6263211965560913}, {"X": 0.09314704686403275, "Y": 0.6263744235038757}]}, "Id": "ea5404df-8e9f-4814-85c4-0bda30c05097"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Renishaw)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09877799451351166, "Height": 0.016865188255906105, "Left": 0.18969763815402985, "Top": 0.606086790561676}, "Polygon": [{"X": 0.18969763815402985, "Y": 0.6061458587646484}, {"X": 0.2884625494480133, "Y": 0.606086790561676}, {"X": 0.2884756326675415, "Y": 0.622893214225769}, {"X": 0.1897093653678894, "Y": 0.6229519844055176}]}, "Id": "b2c04414-bdb5-416c-b5af-c96a0eebfa24"}, {"BlockType": "WORD", "Confidence": 99.6314926147461, "Text": "1330", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07649876922369003, "Height": 0.025058606639504433, "Left": 0.8878089785575867, "Top": 0.6226698756217957}, "Polygon": [{"X": 0.8878089785575867, "Y": 0.622715413570404}, {"X": 0.9642744064331055, "Y": 0.6226698756217957}, {"X": 0.9643077254295349, "Y": 0.6476832032203674}, {"X": 0.8878406882286072, "Y": 0.6477285027503967}]}, "Id": "0b0e3a88-8cef-4764-b58f-7e21def07730"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1140", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07566630095243454, "Height": 0.020148571580648422, "Left": 0.8858582973480225, "Top": 0.681958794593811}, "Polygon": [{"X": 0.8858582973480225, "Y": 0.6820032000541687}, {"X": 0.9614979028701782, "Y": 0.681958794593811}, {"X": 0.9615246057510376, "Y": 0.7020631432533264}, {"X": 0.8858838081359863, "Y": 0.7021073698997498}]}, "Id": "82fd2bfd-a1de-4628-bc77-6998bc57bbc0"}, {"BlockType": "WORD", "Confidence": 99.345703125, "Text": "4", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.01851477287709713, "Height": 0.024726958945393562, "Left": 0.494832843542099, "Top": 0.697756290435791}, "Polygon": [{"X": 0.494832843542099, "Y": 0.6977671384811401}, {"X": 0.5133238434791565, "Y": 0.697756290435791}, {"X": 0.5133476257324219, "Y": 0.7224725484848022}, {"X": 0.4948562681674957, "Y": 0.7224832773208618}]}, "Id": "d8a17978-64b5-4f53-9ba7-fd30060358ab"}, {"BlockType": "WORD", "Confidence": 96.38313293457031, "Text": "Bsk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07515483349561691, "Height": 0.026363402605056763, "Left": 0.5346101522445679, "Top": 0.691970705986023}, "Polygon": [{"X": 0.5346101522445679, "Y": 0.6920146942138672}, {"X": 0.6097375750541687, "Y": 0.691970705986023}, {"X": 0.6097649931907654, "Y": 0.7182903289794922}, {"X": 0.5346359610557556, "Y": 0.7183340787887573}]}, "Id": "f3046dda-2dcb-4e66-82ed-3168367fe756"}, {"BlockType": "WORD", "Confidence": 99.75186920166016, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06800682842731476, "Height": 0.013563943095505238, "Left": 0.0919470563530922, "Top": 0.7417404651641846}, "Polygon": [{"X": 0.0919470563530922, "Y": 0.7417798042297363}, {"X": 0.15994477272033691, "Y": 0.7417404651641846}, {"X": 0.15995387732982635, "Y": 0.7552651166915894}, {"X": 0.09195540845394135, "Y": 0.7553043961524963}]}, "Id": "94843f04-8027-4965-921d-7587a8abc56c"}, {"BlockType": "WORD", "Confidence": 68.40621185302734, "Text": "3BSK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1396639347076416, "Height": 0.03767422214150429, "Left": 0.13306140899658203, "Top": 0.7091639637947083}, "Polygon": [{"X": 0.13306140899658203, "Y": 0.7092454433441162}, {"X": 0.2726965546607971, "Y": 0.7091639637947083}, {"X": 0.27272534370422363, "Y": 0.746757447719574}, {"X": 0.13308589160442352, "Y": 0.7468382120132446}]}, "Id": "dffd9e47-1dd1-4e1a-bc8e-b5c08eb61707"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Hydraulics", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09953176975250244, "Height": 0.018175505101680756, "Left": 0.16749006509780884, "Top": 0.7386487126350403}, "Polygon": [{"X": 0.16749006509780884, "Y": 0.7387064099311829}, {"X": 0.2670080363750458, "Y": 0.7386487126350403}, {"X": 0.2670218348503113, "Y": 0.7567667961120605}, {"X": 0.16750237345695496, "Y": 0.756824254989624}]}, "Id": "0080a865-eb4c-41e8-8fcc-9051c845c98e"}, {"BlockType": "WORD", "Confidence": 99.294677734375, "Text": "SO-2143111", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09486491978168488, "Height": 0.01254759170114994, "Left": 0.49171188473701477, "Top": 0.7235254645347595}, "Polygon": [{"X": 0.49171188473701477, "Y": 0.7235805988311768}, {"X": 0.5865640640258789, "Y": 0.7235254645347595}, {"X": 0.5865768194198608, "Y": 0.7360180616378784}, {"X": 0.4917236864566803, "Y": 0.7360730171203613}]}, "Id": "36a3b178-75ef-40c4-a02f-c67edc40fc91"}, {"BlockType": "WORD", "Confidence": 83.07398223876953, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.099741630256176, "Height": 0.012578452005982399, "Left": 0.5981636047363281, "Top": 0.7217389941215515}, "Polygon": [{"X": 0.5981636047363281, "Y": 0.721796989440918}, {"X": 0.6978912949562073, "Y": 0.7217389941215515}, {"X": 0.6979052424430847, "Y": 0.7342596054077148}, {"X": 0.5981765389442444, "Y": 0.734317421913147}]}, "Id": "7220471e-b1b4-4786-83e4-c413285df5a8"}, {"BlockType": "WORD", "Confidence": 99.15557861328125, "Text": "**********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09574781358242035, "Height": 0.012426207773387432, "Left": 0.705679714679718, "Top": 0.720318615436554}, "Polygon": [{"X": 0.705679714679718, "Y": 0.720374345779419}, {"X": 0.8014127016067505, "Y": 0.720318615436554}, {"X": 0.8014275431632996, "Y": 0.7326892614364624}, {"X": 0.7056935429573059, "Y": 0.7327448129653931}]}, "Id": "a411fe1e-8e9b-40e0-a14c-f8ee37d2a557"}, {"BlockType": "WORD", "Confidence": 77.52212524414062, "Text": "SO=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029408393427729607, "Height": 0.01174697745591402, "Left": 0.8125019669532776, "Top": 0.7192971706390381}, "Polygon": [{"X": 0.8125019669532776, "Y": 0.7193142771720886}, {"X": 0.8418959379196167, "Y": 0.7192971706390381}, {"X": 0.8419103622436523, "Y": 0.7310270667076111}, {"X": 0.8125161528587341, "Y": 0.7310441136360168}]}, "Id": "81239a74-41c1-416c-a045-276ab55ea1a0"}, {"BlockType": "WORD", "Confidence": 96.41581726074219, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01211322657763958, "Height": 0.010712170973420143, "Left": 0.3677826225757599, "Top": 0.7383894920349121}, "Polygon": [{"X": 0.3677826225757599, "Y": 0.7383965253829956}, {"X": 0.3798867166042328, "Y": 0.7383894920349121}, {"X": 0.3798958659172058, "Y": 0.7490946650505066}, {"X": 0.36779165267944336, "Y": 0.7491016387939453}]}, "Id": "1966d38b-b195-4fdd-8db0-86756ddad46f"}, {"BlockType": "WORD", "Confidence": 89.43878173828125, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01176295056939125, "Height": 0.011210707016289234, "Left": 0.44537925720214844, "Top": 0.7361752390861511}, "Polygon": [{"X": 0.44537925720214844, "Y": 0.7361820936203003}, {"X": 0.457131952047348, "Y": 0.7361752390861511}, {"X": 0.4571422040462494, "Y": 0.7473791837692261}, {"X": 0.44538941979408264, "Y": 0.7473859786987305}]}, "Id": "b7748e84-1dbd-40c1-bc3b-7580dd795788"}, {"BlockType": "WORD", "Confidence": 92.25824737548828, "Text": "1200", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0895143523812294, "Height": 0.020893357694149017, "Left": 0.8964565396308899, "Top": 0.725365161895752}, "Polygon": [{"X": 0.8964565396308899, "Y": 0.7254171967506409}, {"X": 0.9859427809715271, "Y": 0.725365161895752}, {"X": 0.9859709143638611, "Y": 0.7462067604064941}, {"X": 0.8964831233024597, "Y": 0.746258556842804}]}, "Id": "049d6a6d-a7a3-4b53-997a-3d4d9f1ba724"}, {"BlockType": "WORD", "Confidence": 99.10076141357422, "Text": "2143114", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06698443740606308, "Height": 0.012590054422616959, "Left": 0.49198946356773376, "Top": 0.7416430115699768}, "Polygon": [{"X": 0.49198946356773376, "Y": 0.7416818141937256}, {"X": 0.5589613318443298, "Y": 0.7416430115699768}, {"X": 0.5589739084243774, "Y": 0.754194438457489}, {"X": 0.49200132489204407, "Y": 0.7542330622673035}]}, "Id": "e91b7d4f-1c47-4486-ba8d-5ce8dfae6d18"}, {"BlockType": "WORD", "Confidence": 97.70348358154297, "Text": "50-2142101", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.17951054871082306, "Height": 0.022924115881323814, "Left": 0.5686962008476257, "Top": 0.7474031448364258}, "Polygon": [{"X": 0.5686962008476257, "Y": 0.7475069761276245}, {"X": 0.7481803894042969, "Y": 0.7474031448364258}, {"X": 0.7482067346572876, "Y": 0.7702240347862244}, {"X": 0.568719208240509, "Y": 0.7703272700309753}]}, "Id": "ce6cd6b4-3169-4da9-be3e-ad74e27724d8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "GRAND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07095491886138916, "Height": 0.014665629714727402, "Left": 0.4602822959423065, "Top": 0.9840295314788818}, "Polygon": [{"X": 0.4602822959423065, "Y": 0.9840682148933411}, {"X": 0.531222939491272, "Y": 0.9840295314788818}, {"X": 0.5312371850013733, "Y": 0.9986566305160522}, {"X": 0.46029573678970337, "Y": 0.9986951351165771}]}, "Id": "06035a36-4249-408d-9184-3bfde07ba525"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "RAPIDS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07331916689872742, "Height": 0.015713829547166824, "Left": 0.5380618572235107, "Top": 0.981703519821167}, "Polygon": [{"X": 0.5380618572235107, "Y": 0.9817435145378113}, {"X": 0.6113647222518921, "Y": 0.981703519821167}, {"X": 0.6113810539245605, "Y": 0.9973775148391724}, {"X": 0.5380772948265076, "Y": 0.9974173307418823}]}, "Id": "60a007d6-129a-47d8-bcdf-8a34417abcb6"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "d9bb981d-5768-4184-880e-7f34ffadfd86", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "d9bb981d-5768-4184-880e-7f34ffadfd86", "content-type": "application/x-amz-json-1.1", "content-length": "58407", "date": "Thu, 18 Sep 2025 09:00:38 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "c9f5a7de-08f6-49e4-b5a9-ba120d578621", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 09:00:40 GMT", "content-type": "application/json", "content-length": "1393", "connection": "keep-alive", "x-amzn-requestid": "c9f5a7de-08f6-49e4-b5a9-ba120d578621"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. The user gave only page1. We need to output classification for page 1. Let's examine content.\n\nIt looks like a schedule or work order list. No explicit header. Words like \"WORK ORDER #\", \"UNIT #\", \"Truck Departure\", \"Truck Ready Time\", \"Driver\". It doesn't look like invoice or BOL, etc. Could be a \"log\" or \"ingate/outgate\"? It lists times, driver, work orders. It looks like a \"log\" (driver log or activity log). The header \"UNIT #\" and times, maybe a driver log or \"gate log\". The page doesn't contain \"Ingate\" or \"Outgate\" but could be a \"log\" type. But log is defined as activity record. This looks like a schedule.\n\nWe need to see if there's \"Schedule\" or \"log\". The key fields: timestamped activity rows, driver/vehicle IDs, mileage or status entries. This page contains times (e.g., 5am, 1035, 1250, etc.). It also has driver name. So it's a log.\n\nThus doc_type: log.\n\nReturn tool call with page_no 1 and doc_type log."}}}, {"toolUse": {"toolUseId": "tooluse_CFPPhEwLQJKeg8vdcb_KLg", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "log"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2309, "outputTokens": 286, "totalTokens": 2595}, "metrics": {"latencyMs": 1299}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/2d5960ff_8_log_8.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nDATE: 2-Sep\nUNIT #: 6063/283420\nTruck Ready Time:\nDRIVER: KARRA\nTruck Departure:\nDEPARTURE TIME: 5am\nPICK-\nDock Time\nCUSTOMER\nUP\nDEL\nWORK ORDER #\n1025\nISK\nX-Rite\nX 1 BH **********\n1035\n1 SK\n1250\nMKE CUSTOM\nX\nX **********\nMACHINING\n1300\nIsk\n1315\n1sk\nZed Engineering\nX\nX **********\n(formerly Renishaw)\n1330\n1140\n4 Bsk\nBucher 3BSK Hydraulics\nSO-2143111 ********** ********** SO=\nX\nX\n1200\n2143114\n50-2142101\nGRAND RAPIDS\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}