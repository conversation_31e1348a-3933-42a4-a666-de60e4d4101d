{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 4.4505437557518235e-08}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "4d0b79fc-181c-4840-a2bb-fdd97b69f6ff", "Relationships": [{"Type": "CHILD", "Ids": ["911b1d61-6a7f-475b-bd93-37d7051923c9", "3dc7db1b-548f-4b72-aafe-46278d151d36", "8b12c344-26bf-47c3-851f-def1d46a35d4", "712b1b25-4519-436a-849b-5fbabb4fb3c7", "964f333d-fd36-40e8-b068-817e96c86f80", "921ed5ee-6eee-4de0-97de-93179f3a437e", "b093ff7e-2241-42ea-91f6-9fd76918ea06", "1bc255dc-747e-4c81-85c9-7d4320098cad", "537777ee-8b83-4192-8efe-84badd5f01dd", "e6d6d425-d6a9-43a7-aa6c-4f1a3ee0f867", "ea2c52a3-0ab8-4c38-8974-79178dbcbfaa", "613fb7cb-fcdb-4d8f-8706-2861388aec35", "684318cc-9b9a-42f7-a30d-6a111448a992", "b20e6064-1b7d-497d-8a6d-c256d120847d", "bb0e7bd2-a2f1-429b-8fa6-e2061cb1ffa7", "4fada13a-e90b-4f47-9e68-577b2534e51d", "2e803c36-f722-435b-bdcf-5d64752f304d", "c9ed9ee9-3fd8-458c-8064-40cd8f339636", "31b05459-f3bf-40b1-a977-dc2bac7f1479", "bedb2067-94c0-45ad-91ff-819f002e5a4b", "ea38d02f-311d-4f67-a785-f2b81c56d383", "4fc3b68b-66b4-4a75-a639-dfae34c154df", "22a14fec-fe9b-4ba1-97a9-cff24d47590d", "0d0c5e96-e60e-41d4-8f5d-7ec3b15c00a1", "082d6eee-afa0-482a-bd45-e23107cd5e26", "df559b14-656d-453b-8df7-87eef0e2cf5f", "38821196-6d2a-4df8-89a7-90884266cdeb", "bc246469-e117-47d2-8e2c-e01255cb37a1", "cc1a4d57-e5eb-407a-a058-d1330c55772c", "453bd317-9329-488c-9e52-1ae9bc719a60", "5c513dc9-41b7-4922-a1bf-1ecb8744b6ac", "13bfa84a-d94d-4771-a19f-5943d98454d1", "40040f81-6aaa-4ace-afe5-d237f4c276ee", "f574b828-e1e4-4fc0-b344-38752bd67f12", "16315a7c-1d60-4e7d-bf7d-cda5b29e3ea8", "2cc54408-a1cc-49fe-ad19-4dfc26e11924", "ca7f66bb-1b58-4e0f-9771-4b06e4c7abd2", "753804aa-8fc2-4fd8-8e3c-a6a388152b2f", "68265f0a-d1f7-4827-a2c4-90b73d323c4e", "70b12ad8-56ba-4b22-bcfa-4a41e6fb3cee", "6ce2c231-f881-4f29-a086-84668f7f96cc", "d5306fa4-6e3b-462f-8b36-b6994334ced3", "f77b1a55-27ee-4a6a-bc0b-f3ef2f542193", "14e441a9-8148-458d-8672-26a21428edae", "2a4b1146-f3c3-4582-bdfa-f6f575d5c29e", "5021e53d-20bf-494a-9fc2-9c3d9451c134"]}]}, {"BlockType": "LINE", "Confidence": 57.021881103515625, "Text": "Loves", "Geometry": {"BoundingBox": {"Width": 0.36615806818008423, "Height": 0.04778224229812622, "Left": 0.4147644639015198, "Top": 0.013307332061231136}, "Polygon": [{"X": 0.414764940738678, "Y": 0.013307332061231136}, {"X": 0.780922532081604, "Y": 0.013315043412148952}, {"X": 0.7809218764305115, "Y": 0.06108957156538963}, {"X": 0.4147644639015198, "Y": 0.061081670224666595}]}, "Id": "911b1d61-6a7f-475b-bd93-37d7051923c9", "Relationships": [{"Type": "CHILD", "Ids": ["2128189a-da5c-45ca-978b-764095ec545c"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "STORE 456", "Geometry": {"BoundingBox": {"Width": 0.21541710197925568, "Height": 0.01371179148554802, "Left": 0.40471282601356506, "Top": 0.08028418570756912}, "Polygon": [{"X": 0.4047129452228546, "Y": 0.08028418570756912}, {"X": 0.6201299428939819, "Y": 0.08028887957334518}, {"X": 0.6201297640800476, "Y": 0.09399598091840744}, {"X": 0.40471282601356506, "Y": 0.0939912497997284}]}, "Id": "3dc7db1b-548f-4b72-aafe-46278d151d36", "Relationships": [{"Type": "CHILD", "Ids": ["bcfee136-c814-41ec-b08f-5e38cd8d1589", "3633a586-39ec-4de9-b038-de983047e58c"]}]}, {"BlockType": "LINE", "Confidence": 99.9471206665039, "Text": "26530 Baker Rd", "Geometry": {"BoundingBox": {"Width": 0.3367886245250702, "Height": 0.014347302727401257, "Left": 0.3444235026836395, "Top": 0.09991276264190674}, "Polygon": [{"X": 0.3444236218929291, "Y": 0.09991276264190674}, {"X": 0.6812121272087097, "Y": 0.09992017596960068}, {"X": 0.6812119483947754, "Y": 0.11426007002592087}, {"X": 0.3444235026836395, "Y": 0.11425260454416275}]}, "Id": "8b12c344-26bf-47c3-851f-def1d46a35d4", "Relationships": [{"Type": "CHILD", "Ids": ["61667f08-664e-4db3-a4c2-558c5378dc4e", "700186ce-7a4f-4db9-a98d-560c9f358956", "1eacd2c5-e33b-446d-86eb-a99faf8d80d6"]}]}, {"BlockType": "LINE", "Confidence": 98.13662719726562, "Text": "Perrysburg, OH 43551", "Geometry": {"BoundingBox": {"Width": 0.4794825613498688, "Height": 0.018202967941761017, "Left": 0.27228686213493347, "Top": 0.11988001316785812}, "Polygon": [{"X": 0.2722870111465454, "Y": 0.11988001316785812}, {"X": 0.7517694234848022, "Y": 0.11989066749811172}, {"X": 0.7517691850662231, "Y": 0.13808298110961914}, {"X": 0.27228686213493347, "Y": 0.13807223737239838}]}, "Id": "712b1b25-4519-436a-849b-5fbabb4fb3c7", "Relationships": [{"Type": "CHILD", "Ids": ["8fc4a5c5-fc14-4cc9-882d-454f9a31e679", "8e9b2513-037e-4f43-8954-b450e208afc1", "ef8af549-358d-4cac-aed5-ecf63cd7837c"]}]}, {"BlockType": "LINE", "Confidence": 99.37948608398438, "Text": "**************", "Geometry": {"BoundingBox": {"Width": 0.3322758674621582, "Height": 0.0170759204775095, "Left": 0.3485596776008606, "Top": 0.13970772922039032}, "Polygon": [{"X": 0.3485598564147949, "Y": 0.13970772922039032}, {"X": 0.6808355450630188, "Y": 0.13971517980098724}, {"X": 0.6808353662490845, "Y": 0.15678365528583527}, {"X": 0.3485596776008606, "Y": 0.15677613019943237}]}, "Id": "964f333d-fd36-40e8-b068-817e96c86f80", "Relationships": [{"Type": "CHILD", "Ids": ["f1828bc1-6e73-4ea4-b781-4aab10ac5c99", "e703a680-9e73-42a0-9cbb-38fff0b96a23"]}]}, {"BlockType": "LINE", "Confidence": 99.89397430419922, "Text": "02/19/2025 Tkt #99350427", "Geometry": {"BoundingBox": {"Width": 0.5784692764282227, "Height": 0.01631264016032219, "Left": 0.024728834629058838, "Top": 0.17707499861717224}, "Polygon": [{"X": 0.024728924036026, "Y": 0.17707499861717224}, {"X": 0.6031981110572815, "Y": 0.17708820104599}, {"X": 0.6031979322433472, "Y": 0.19338762760162354}, {"X": 0.024728834629058838, "Y": 0.19337432086467743}]}, "Id": "921ed5ee-6eee-4de0-97de-93179f3a437e", "Relationships": [{"Type": "CHILD", "Ids": ["8b8a5a1c-79a8-4615-b822-acfb4f98ff33", "176ea8fd-bdef-44a1-adaf-8ed259951d4b", "a685c09d-aced-4bef-badb-68a227d0bbe5"]}]}, {"BlockType": "LINE", "Confidence": 99.9233169555664, "Text": "Type: SALE (MOBILE COPY)", "Geometry": {"BoundingBox": {"Width": 0.5746573805809021, "Height": 0.018367933109402657, "Left": 0.02416856773197651, "Top": 0.222758948802948}, "Polygon": [{"X": 0.024168668314814568, "Y": 0.222758948802948}, {"X": 0.5988259315490723, "Y": 0.22277235984802246}, {"X": 0.5988256931304932, "Y": 0.2411268949508667}, {"X": 0.02416856773197651, "Y": 0.24111336469650269}]}, "Id": "b093ff7e-2241-42ea-91f6-9fd76918ea06", "Relationships": [{"Type": "CHILD", "Ids": ["bd478feb-90ef-4b04-b4e1-6c80ec57a18b", "02ade84f-4496-4867-9a71-ccce4d20577c", "b0695218-7e1e-491a-8732-123e799b8cb2", "0173a9ae-20b0-4295-af5f-96432f4e23cf"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Qty Name", "Geometry": {"BoundingBox": {"Width": 0.20815055072307587, "Height": 0.017344756051898003, "Left": 0.03349582478404045, "Top": 0.273633748292923}, "Polygon": [{"X": 0.03349592164158821, "Y": 0.273633748292923}, {"X": 0.24164637923240662, "Y": 0.27363869547843933}, {"X": 0.24164624512195587, "Y": 0.29097849130630493}, {"X": 0.03349582478404045, "Y": 0.2909734845161438}]}, "Id": "1bc255dc-747e-4c81-85c9-7d4320098cad", "Relationships": [{"Type": "CHILD", "Ids": ["8cd9614b-5d60-4f6f-b100-480a93edb85a", "6b73aab6-3b73-487d-a7e6-1ff0fa6002e8"]}]}, {"BlockType": "LINE", "Confidence": 99.80069732666016, "Text": "Price", "Geometry": {"BoundingBox": {"Width": 0.11965912580490112, "Height": 0.01481472235172987, "Left": 0.5720095634460449, "Top": 0.27286213636398315}, "Polygon": [{"X": 0.5720096826553345, "Y": 0.27286213636398315}, {"X": 0.691668689250946, "Y": 0.2728649973869324}, {"X": 0.6916684508323669, "Y": 0.2876768708229065}, {"X": 0.5720095634460449, "Y": 0.2876739799976349}]}, "Id": "537777ee-8b83-4192-8efe-84badd5f01dd", "Relationships": [{"Type": "CHILD", "Ids": ["be724ee0-7961-481f-8437-fc5b8f3aa026"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.11947587877511978, "Height": 0.014876419678330421, "Left": 0.8725711703300476, "Top": 0.27265599370002747}, "Polygon": [{"X": 0.8725713491439819, "Y": 0.27265599370002747}, {"X": 0.9920470118522644, "Y": 0.2726588547229767}, {"X": 0.9920467734336853, "Y": 0.28753241896629333}, {"X": 0.8725711703300476, "Y": 0.2875295579433441}]}, "Id": "e6d6d425-d6a9-43a7-aa6c-4f1a3ee0f867", "Relationships": [{"Type": "CHILD", "Ids": ["d58e5a1d-b239-4917-8722-797655ec5e3e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TRKDS", "Geometry": {"BoundingBox": {"Width": 0.12086191773414612, "Height": 0.013951790519058704, "Left": 0.14444011449813843, "Top": 0.3234573006629944}, "Polygon": [{"X": 0.1444402039051056, "Y": 0.3234573006629944}, {"X": 0.26530203223228455, "Y": 0.32346025109291077}, {"X": 0.265301913022995, "Y": 0.3374090790748596}, {"X": 0.14444011449813843, "Y": 0.33740609884262085}]}, "Id": "ea2c52a3-0ab8-4c38-8974-79178dbcbfaa", "Relationships": [{"Type": "CHILD", "Ids": ["264548b5-ef9d-4b4a-859d-c13bdb0f8592"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "392.89", "Geometry": {"BoundingBox": {"Width": 0.14412324130535126, "Height": 0.014073347672820091, "Left": 0.8481264710426331, "Top": 0.3234585225582123}, "Polygon": [{"X": 0.8481267094612122, "Y": 0.3234585225582123}, {"X": 0.9922497272491455, "Y": 0.32346203923225403}, {"X": 0.9922495484352112, "Y": 0.3375318646430969}, {"X": 0.8481264710426331, "Y": 0.3375283479690552}]}, "Id": "613fb7cb-fcdb-4d8f-8706-2861388aec35", "Relationships": [{"Type": "CHILD", "Ids": ["837a0279-91a5-4257-abf4-acdcdcda1fac"]}]}, {"BlockType": "LINE", "Confidence": 99.3369369506836, "Text": "Pump:", "Geometry": {"BoundingBox": {"Width": 0.11214932799339294, "Height": 0.01727992109954357, "Left": 0.17258475720882416, "Top": 0.34820595383644104}, "Polygon": [{"X": 0.1725848764181137, "Y": 0.34820595383644104}, {"X": 0.2847341001033783, "Y": 0.3482087254524231}, {"X": 0.28473395109176636, "Y": 0.36548587679862976}, {"X": 0.17258475720882416, "Y": 0.3654830753803253}]}, "Id": "684318cc-9b9a-42f7-a30d-6a111448a992", "Relationships": [{"Type": "CHILD", "Ids": ["10af51e1-d774-444c-9933-3efb9f48cb95"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "24", "Geometry": {"BoundingBox": {"Width": 0.047286681830883026, "Height": 0.01400380302220583, "Left": 0.5714358687400818, "Top": 0.348408043384552}, "Polygon": [{"X": 0.5714360475540161, "Y": 0.348408043384552}, {"X": 0.6187225580215454, "Y": 0.3484092056751251}, {"X": 0.6187223792076111, "Y": 0.36241185665130615}, {"X": 0.5714358687400818, "Y": 0.36241069436073303}]}, "Id": "b20e6064-1b7d-497d-8a6d-c256d120847d", "Relationships": [{"Type": "CHILD", "Ids": ["ef2790c9-2191-4de4-86e5-274802f73b7f"]}]}, {"BlockType": "LINE", "Confidence": 98.80879211425781, "Text": "Gallons:", "Geometry": {"BoundingBox": {"Width": 0.1859770268201828, "Height": 0.015281463973224163, "Left": 0.17136044800281525, "Top": 0.37197884917259216}, "Polygon": [{"X": 0.1713605523109436, "Y": 0.37197884917259216}, {"X": 0.35733747482299805, "Y": 0.37198349833488464}, {"X": 0.3573373258113861, "Y": 0.3872603178024292}, {"X": 0.17136044800281525, "Y": 0.3872556686401367}]}, "Id": "bb0e7bd2-a2f1-429b-8fa6-e2061cb1ffa7", "Relationships": [{"Type": "CHILD", "Ids": ["4016d404-3163-4fcf-b438-420bf004fc1c"]}]}, {"BlockType": "LINE", "Confidence": 99.89178466796875, "Text": "103.691", "Geometry": {"BoundingBox": {"Width": 0.16592863202095032, "Height": 0.014257281087338924, "Left": 0.5733498930931091, "Top": 0.3731890618801117}, "Polygon": [{"X": 0.5733500123023987, "Y": 0.3731890618801117}, {"X": 0.7392784953117371, "Y": 0.3731932044029236}, {"X": 0.7392783164978027, "Y": 0.3874463438987732}, {"X": 0.5733498930931091, "Y": 0.3874421715736389}]}, "Id": "4fada13a-e90b-4f47-9e68-577b2534e51d", "Relationships": [{"Type": "CHILD", "Ids": ["dc4f6cf0-9aa4-4241-ae7c-ee6a30b7785b"]}]}, {"BlockType": "LINE", "Confidence": 99.60784912109375, "Text": "Price / Gal:", "Geometry": {"BoundingBox": {"Width": 0.2825375497341156, "Height": 0.01689288206398487, "Left": 0.1723049134016037, "Top": 0.3969845473766327}, "Polygon": [{"X": 0.17230503261089325, "Y": 0.3969845473766327}, {"X": 0.4548424780368805, "Y": 0.39699167013168335}, {"X": 0.45484229922294617, "Y": 0.4138774275779724}, {"X": 0.1723049134016037, "Y": 0.413870245218277}]}, "Id": "2e803c36-f722-435b-bdcf-5d64752f304d", "Relationships": [{"Type": "CHILD", "Ids": ["2a74f208-918a-464a-9a7e-531423ca370f", "5860501c-b7ef-4d16-8103-9722a8ae0e8a", "5eb43c5b-1198-4b80-a3c1-c7257d623e5a"]}]}, {"BlockType": "LINE", "Confidence": 99.84215545654297, "Text": "3.789", "Geometry": {"BoundingBox": {"Width": 0.11925562471151352, "Height": 0.013878420926630497, "Left": 0.5715514421463013, "Top": 0.3981725573539734}, "Polygon": [{"X": 0.5715516209602356, "Y": 0.3981725573539734}, {"X": 0.6908071041107178, "Y": 0.39817556738853455}, {"X": 0.6908069252967834, "Y": 0.4120509624481201}, {"X": 0.5715514421463013, "Y": 0.41204795241355896}]}, "Id": "c9ed9ee9-3fd8-458c-8064-40cd8f339636", "Relationships": [{"Type": "CHILD", "Ids": ["0a475121-fbbe-49c7-b4a4-6a46bbd4369e"]}]}, {"BlockType": "LINE", "Confidence": 99.12308502197266, "Text": "Subtotal", "Geometry": {"BoundingBox": {"Width": 0.19056780636310577, "Height": 0.015173632651567459, "Left": 0.03396288678050041, "Top": 0.44210904836654663}, "Polygon": [{"X": 0.03396296873688698, "Y": 0.44210904836654663}, {"X": 0.22453069686889648, "Y": 0.4421139359474182}, {"X": 0.22453057765960693, "Y": 0.4572826623916626}, {"X": 0.03396288678050041, "Y": 0.45727774500846863}]}, "Id": "31b05459-f3bf-40b1-a977-dc2bac7f1479", "Relationships": [{"Type": "CHILD", "Ids": ["a491f31d-6640-402b-a9c8-108823b6fa81"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "392.89", "Geometry": {"BoundingBox": {"Width": 0.14390680193901062, "Height": 0.014079509302973747, "Left": 0.847923755645752, "Top": 0.4432080388069153}, "Polygon": [{"X": 0.8479239344596863, "Y": 0.4432080388069153}, {"X": 0.9918305277824402, "Y": 0.44321176409721375}, {"X": 0.9918302893638611, "Y": 0.4572875499725342}, {"X": 0.847923755645752, "Y": 0.4572838246822357}]}, "Id": "bedb2067-94c0-45ad-91ff-819f002e5a4b", "Relationships": [{"Type": "CHILD", "Ids": ["d153e7ec-4caf-4efe-b444-04c3974da24d"]}]}, {"BlockType": "LINE", "Confidence": 99.60140228271484, "Text": "Sales Tax", "Geometry": {"BoundingBox": {"Width": 0.21675916016101837, "Height": 0.015179254114627838, "Left": 0.03404952213168144, "Top": 0.46707290410995483}, "Polygon": [{"X": 0.03404960781335831, "Y": 0.46707290410995483}, {"X": 0.2508086860179901, "Y": 0.4670785367488861}, {"X": 0.25080856680870056, "Y": 0.4822521507740021}, {"X": 0.03404952213168144, "Y": 0.4822464883327484}]}, "Id": "ea38d02f-311d-4f67-a785-f2b81c56d383", "Relationships": [{"Type": "CHILD", "Ids": ["bfef54b3-2653-40dd-8854-980788d56ce0", "ff840c54-28ae-4efa-b317-b886767f44a6"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "0.00", "Geometry": {"BoundingBox": {"Width": 0.09478122740983963, "Height": 0.013655280694365501, "Left": 0.8968883752822876, "Top": 0.4684244394302368}, "Polygon": [{"X": 0.8968886137008667, "Y": 0.4684244394302368}, {"X": 0.9916695952415466, "Y": 0.468426913022995}, {"X": 0.9916694164276123, "Y": 0.48207971453666687}, {"X": 0.8968883752822876, "Y": 0.4820772409439087}]}, "Id": "4fc3b68b-66b4-4a75-a639-dfae34c154df", "Relationships": [{"Type": "CHILD", "Ids": ["96d06d15-9a43-43f7-9cbc-d5a5d8c42ef0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.11935877799987793, "Height": 0.014880355447530746, "Left": 0.03258077800273895, "Top": 0.49193114042282104}, "Polygon": [{"X": 0.03258085995912552, "Y": 0.49193114042282104}, {"X": 0.15193955600261688, "Y": 0.49193426966667175}, {"X": 0.15193945169448853, "Y": 0.5068114995956421}, {"X": 0.03258077800273895, "Y": 0.506808340549469}]}, "Id": "22a14fec-fe9b-4ba1-97a9-cff24d47590d", "Relationships": [{"Type": "CHILD", "Ids": ["9b26a134-55df-430c-af31-46d77d138e06"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "392.89", "Geometry": {"BoundingBox": {"Width": 0.14459869265556335, "Height": 0.014108043164014816, "Left": 0.8476577401161194, "Top": 0.4930901527404785}, "Polygon": [{"X": 0.8476579785346985, "Y": 0.4930901527404785}, {"X": 0.9922564625740051, "Y": 0.49309396743774414}, {"X": 0.992256224155426, "Y": 0.5071982145309448}, {"X": 0.8476577401161194, "Y": 0.5071943998336792}]}, "Id": "0d0c5e96-e60e-41d4-8f5d-7ec3b15c00a1", "Relationships": [{"Type": "CHILD", "Ids": ["3bc06aba-fdf8-46a6-9b71-cea42fa9e664"]}]}, {"BlockType": "LINE", "Confidence": 99.68511199951172, "Text": "Received:", "Geometry": {"BoundingBox": {"Width": 0.21157892048358917, "Height": 0.0153798284009099, "Left": 0.025389736518263817, "Top": 0.5334579944610596}, "Polygon": [{"X": 0.02538982219994068, "Y": 0.5334579944610596}, {"X": 0.23696865141391754, "Y": 0.5334636569023132}, {"X": 0.236968532204628, "Y": 0.5488378405570984}, {"X": 0.025389736518263817, "Y": 0.5488321781158447}]}, "Id": "082d6eee-afa0-482a-bd45-e23107cd5e26", "Relationships": [{"Type": "CHILD", "Ids": ["ad8fcb97-0f7f-4435-973c-51d1618d7603"]}]}, {"BlockType": "LINE", "Confidence": 99.91131591796875, "Text": "FLEETONE", "Geometry": {"BoundingBox": {"Width": 0.19068774580955505, "Height": 0.013925242237746716, "Left": 0.07271936535835266, "Top": 0.5583653450012207}, "Polygon": [{"X": 0.07271944731473923, "Y": 0.5583653450012207}, {"X": 0.2634071111679077, "Y": 0.5583704710006714}, {"X": 0.26340699195861816, "Y": 0.5722905397415161}, {"X": 0.07271936535835266, "Y": 0.5722854137420654}]}, "Id": "df559b14-656d-453b-8df7-87eef0e2cf5f", "Relationships": [{"Type": "CHILD", "Ids": ["d823d0db-aef9-403d-a9a6-a93b042ce1f9"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "392.89", "Geometry": {"BoundingBox": {"Width": 0.1442166417837143, "Height": 0.014035806991159916, "Left": 0.8479160070419312, "Top": 0.558347761631012}, "Polygon": [{"X": 0.8479161858558655, "Y": 0.558347761631012}, {"X": 0.9921326637268066, "Y": 0.5583516359329224}, {"X": 0.9921324253082275, "Y": 0.5723835825920105}, {"X": 0.8479160070419312, "Y": 0.5723796486854553}]}, "Id": "38821196-6d2a-4df8-89a7-90884266cdeb", "Relationships": [{"Type": "CHILD", "Ids": ["3e2e72d7-55f6-4fef-9749-25d1c1b409a2"]}]}, {"BlockType": "LINE", "Confidence": 99.65292358398438, "Text": "0180 WAVED", "Geometry": {"BoundingBox": {"Width": 0.24129439890384674, "Height": 0.013896645046770573, "Left": 0.4331113398075104, "Top": 0.5832433104515076}, "Polygon": [{"X": 0.4331114888191223, "Y": 0.5832433104515076}, {"X": 0.6744057536125183, "Y": 0.5832498669624329}, {"X": 0.674405574798584, "Y": 0.5971399545669556}, {"X": 0.4331113398075104, "Y": 0.5971333384513855}]}, "Id": "bc246469-e117-47d2-8e2c-e01255cb37a1", "Relationships": [{"Type": "CHILD", "Ids": ["c0482d36-3de9-469e-a04f-c8d99f4c1b76", "60adf204-eca8-4b04-ae9d-2f4cf19eaf30"]}]}, {"BlockType": "LINE", "Confidence": 96.96847534179688, "Text": "Auth No: 118916", "Geometry": {"BoundingBox": {"Width": 0.3392660319805145, "Height": 0.014921617694199085, "Left": 0.121185801923275, "Top": 0.6072020530700684}, "Polygon": [{"X": 0.12118589878082275, "Y": 0.6072020530700684}, {"X": 0.4604518413543701, "Y": 0.6072113513946533}, {"X": 0.4604516923427582, "Y": 0.622123658657074}, {"X": 0.121185801923275, "Y": 0.6221142411231995}]}, "Id": "cc1a4d57-e5eb-407a-a058-d1330c55772c", "Relationships": [{"Type": "CHILD", "Ids": ["0fad2ab1-6530-40d5-95d7-dc41a32f016d", "dea1e84c-eb0e-4b49-bb05-6485876c372f", "b884a36f-23e3-4c77-85ad-8f4e380087dd"]}]}, {"BlockType": "LINE", "Confidence": 99.71609497070312, "Text": "Company: AMERILUX", "Geometry": {"BoundingBox": {"Width": 0.40973222255706787, "Height": 0.017232399433851242, "Left": 0.07159576565027237, "Top": 0.632896363735199}, "Polygon": [{"X": 0.07159586995840073, "Y": 0.632896363735199}, {"X": 0.48132798075675964, "Y": 0.6329077482223511}, {"X": 0.4813278019428253, "Y": 0.6501287817955017}, {"X": 0.07159576565027237, "Y": 0.6501173377037048}]}, "Id": "453bd317-9329-488c-9e52-1ae9bc719a60", "Relationships": [{"Type": "CHILD", "Ids": ["a992a968-9063-4293-8b45-a6abb86a1e46", "c0e1aed9-2bdc-40f2-b611-8f968a1288c5"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "TRANSPORTATION", "Geometry": {"BoundingBox": {"Width": 0.3393649458885193, "Height": 0.013967436738312244, "Left": 0.06996220350265503, "Top": 0.6530441045761108}, "Polygon": [{"X": 0.06996229290962219, "Y": 0.6530441045761108}, {"X": 0.4093271493911743, "Y": 0.6530536413192749}, {"X": 0.4093270003795624, "Y": 0.667011559009552}, {"X": 0.06996220350265503, "Y": 0.6670019626617432}]}, "Id": "5c513dc9-41b7-4922-a1bf-1ecb8744b6ac", "Relationships": [{"Type": "CHILD", "Ids": ["96da7bba-e3de-4c1e-abdd-5b69673da3c7"]}]}, {"BlockType": "LINE", "Confidence": 99.85601043701172, "Text": "INVOICE# 44166", "Geometry": {"BoundingBox": {"Width": 0.33454275131225586, "Height": 0.01404799334704876, "Left": 0.07197312265634537, "Top": 0.6778979301452637}, "Polygon": [{"X": 0.07197320461273193, "Y": 0.6778979301452637}, {"X": 0.406515896320343, "Y": 0.677907407283783}, {"X": 0.4065157473087311, "Y": 0.6919459700584412}, {"X": 0.07197312265634537, "Y": 0.6919364333152771}]}, "Id": "13bfa84a-d94d-4771-a19f-5943d98454d1", "Relationships": [{"Type": "CHILD", "Ids": ["df1af6fd-460b-4c66-bdaa-a0c604387170", "f8ea9e1f-27b0-4f5a-9e40-3df993d64ec9"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "TripNumber", "Geometry": {"BoundingBox": {"Width": 0.2429015338420868, "Height": 0.01809234544634819, "Left": 0.032340798527002335, "Top": 0.745031476020813}, "Polygon": [{"X": 0.03234089910984039, "Y": 0.745031476020813}, {"X": 0.27524232864379883, "Y": 0.7450385093688965}, {"X": 0.2752421796321869, "Y": 0.7631238102912903}, {"X": 0.032340798527002335, "Y": 0.763116717338562}]}, "Id": "40040f81-6aaa-4ace-afe5-d237f4c276ee", "Relationships": [{"Type": "CHILD", "Ids": ["b3de6448-7e3a-49a1-802a-03816d0136d7"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "CLEVELAND", "Geometry": {"BoundingBox": {"Width": 0.21618646383285522, "Height": 0.013634724542498589, "Left": 0.7754725813865662, "Top": 0.7457865476608276}, "Polygon": [{"X": 0.7754727602005005, "Y": 0.7457865476608276}, {"X": 0.9916590452194214, "Y": 0.7457928657531738}, {"X": 0.9916588664054871, "Y": 0.7594212889671326}, {"X": 0.7754725813865662, "Y": 0.7594149708747864}]}, "Id": "f574b828-e1e4-4fc0-b344-38752bd67f12", "Relationships": [{"Type": "CHILD", "Ids": ["efe97672-6122-4ca5-a067-2a8f1103401c"]}]}, {"BlockType": "LINE", "Confidence": 57.75928497314453, "Text": "Hub0dometer", "Geometry": {"BoundingBox": {"Width": 0.26556143164634705, "Height": 0.014417048543691635, "Left": 0.03357602283358574, "Top": 0.7699764966964722}, "Polygon": [{"X": 0.03357610106468201, "Y": 0.7699764966964722}, {"X": 0.2991374433040619, "Y": 0.7699843049049377}, {"X": 0.29913732409477234, "Y": 0.7843935489654541}, {"X": 0.03357602283358574, "Y": 0.7843857407569885}]}, "Id": "16315a7c-1d60-4e7d-bf7d-cda5b29e3ea8", "Relationships": [{"Type": "CHILD", "Ids": ["4a727dd1-4452-4349-9c1a-c77b1cf9eacc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "32000", "Geometry": {"BoundingBox": {"Width": 0.11763736605644226, "Height": 0.013297347351908684, "Left": 0.8748686909675598, "Top": 0.7709243893623352}, "Polygon": [{"X": 0.8748688697814941, "Y": 0.7709243893623352}, {"X": 0.9925060272216797, "Y": 0.7709278464317322}, {"X": 0.9925058484077454, "Y": 0.7842217087745667}, {"X": 0.8748686909675598, "Y": 0.7842182517051697}]}, "Id": "2cc54408-a1cc-49fe-ad19-4dfc26e11924", "Relationships": [{"Type": "CHILD", "Ids": ["261f140b-e619-40b1-b011-798c23861deb"]}]}, {"BlockType": "LINE", "Confidence": 98.80580139160156, "Text": "VehicleID", "Geometry": {"BoundingBox": {"Width": 0.21793395280838013, "Height": 0.01474472600966692, "Left": 0.032365307211875916, "Top": 0.794425904750824}, "Polygon": [{"X": 0.03236538916826248, "Y": 0.794425904750824}, {"X": 0.25029924511909485, "Y": 0.7944323420524597}, {"X": 0.2502991259098053, "Y": 0.809170663356781}, {"X": 0.032365307211875916, "Y": 0.8091641664505005}]}, "Id": "ca7f66bb-1b58-4e0f-9771-4b06e4c7abd2", "Relationships": [{"Type": "CHILD", "Ids": ["dee4c9a0-515c-4183-9ecf-72fa0f0b11af"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "6063", "Geometry": {"BoundingBox": {"Width": 0.09416894614696503, "Height": 0.013708856888115406, "Left": 0.8972878456115723, "Top": 0.795677661895752}, "Polygon": [{"X": 0.8972880244255066, "Y": 0.795677661895752}, {"X": 0.9914568066596985, "Y": 0.7956804633140564}, {"X": 0.9914565682411194, "Y": 0.8093864917755127}, {"X": 0.8972878456115723, "Y": 0.8093836903572083}]}, "Id": "753804aa-8fc2-4fd8-8e3c-a6a388152b2f", "Relationships": [{"Type": "CHILD", "Ids": ["dbf006b8-2c83-42d6-b6fb-192ac2b75a08"]}]}, {"BlockType": "LINE", "Confidence": 99.85112762451172, "Text": "AMERILUX", "Geometry": {"BoundingBox": {"Width": 0.19543160498142242, "Height": 0.013423096388578415, "Left": 0.8001055121421814, "Top": 0.8206210732460022}, "Polygon": [{"X": 0.8001056909561157, "Y": 0.8206210732460022}, {"X": 0.9955371022224426, "Y": 0.8206268548965454}, {"X": 0.9955368638038635, "Y": 0.8340441584587097}, {"X": 0.8001055121421814, "Y": 0.8340383172035217}]}, "Id": "68265f0a-d1f7-4827-a2c4-90b73d323c4e", "Relationships": [{"Type": "CHILD", "Ids": ["2f95dc5f-f9ad-4fd0-9162-a2ab510eade2"]}]}, {"BlockType": "LINE", "Confidence": 84.24964141845703, "Text": "TruckingCompanyName", "Geometry": {"BoundingBox": {"Width": 0.4615255296230316, "Height": 0.01760481484234333, "Left": 0.03254159167408943, "Top": 0.8294249176979065}, "Polygon": [{"X": 0.03254168853163719, "Y": 0.8294249176979065}, {"X": 0.49406713247299194, "Y": 0.8294386863708496}, {"X": 0.49406692385673523, "Y": 0.8470296859741211}, {"X": 0.03254159167408943, "Y": 0.8470157980918884}]}, "Id": "70b12ad8-56ba-4b22-bcfa-4a41e6fb3cee", "Relationships": [{"Type": "CHILD", "Ids": ["d4e42058-3645-4728-a65d-741478187faa"]}]}, {"BlockType": "LINE", "Confidence": 71.1318130493164, "Text": "TRANSPORTATION", "Geometry": {"BoundingBox": {"Width": 0.3385436534881592, "Height": 0.013585027307271957, "Left": 0.6540789604187012, "Top": 0.840741753578186}, "Polygon": [{"X": 0.6540791392326355, "Y": 0.840741753578186}, {"X": 0.9926226139068604, "Y": 0.8407519459724426}, {"X": 0.9926223754882812, "Y": 0.8543267846107483}, {"X": 0.6540789604187012, "Y": 0.8543165326118469}]}, "Id": "6ce2c231-f881-4f29-a086-84668f7f96cc", "Relationships": [{"Type": "CHILD", "Ids": ["f0712683-dabf-4f7a-8ae0-cc2a8ed01a13"]}]}, {"BlockType": "LINE", "Confidence": 99.60179901123047, "Text": "Pos: #99", "Geometry": {"BoundingBox": {"Width": 0.19092269241809845, "Height": 0.013669390231370926, "Left": 0.026448041200637817, "Top": 0.8643727898597717}, "Polygon": [{"X": 0.026448117569088936, "Y": 0.8643727898597717}, {"X": 0.21737073361873627, "Y": 0.8643786311149597}, {"X": 0.2173706293106079, "Y": 0.8780422210693359}, {"X": 0.026448041200637817, "Y": 0.878036379814148}]}, "Id": "d5306fa4-6e3b-462f-8b36-b6994334ced3", "Relationships": [{"Type": "CHILD", "Ids": ["0515f158-fcf0-4f1e-91be-12ec75837573", "9ea3c57b-500a-4966-aa94-8c65567273f5"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Date: 02/19/2025", "Geometry": {"BoundingBox": {"Width": 0.3844708204269409, "Height": 0.01591789908707142, "Left": 0.025099296122789383, "Top": 0.8843050003051758}, "Polygon": [{"X": 0.025099381804466248, "Y": 0.8843050003051758}, {"X": 0.4095701277256012, "Y": 0.8843167424201965}, {"X": 0.40956997871398926, "Y": 0.900222897529602}, {"X": 0.025099296122789383, "Y": 0.9002110958099365}]}, "Id": "f77b1a55-27ee-4a6a-bc0b-f3ef2f542193", "Relationships": [{"Type": "CHILD", "Ids": ["516f80b9-d4c7-4d70-8d94-6ef5e402a547", "a6c6a546-5c3f-4109-b7a9-5041740044c3"]}]}, {"BlockType": "LINE", "Confidence": 99.40210723876953, "Text": "Total Sale:", "Geometry": {"BoundingBox": {"Width": 0.2596619129180908, "Height": 0.014684749767184258, "Left": 0.03238503262400627, "Top": 0.9250006675720215}, "Polygon": [{"X": 0.03238511458039284, "Y": 0.9250006675720215}, {"X": 0.2920469641685486, "Y": 0.9250087141990662}, {"X": 0.29204681515693665, "Y": 0.9396854043006897}, {"X": 0.03238503262400627, "Y": 0.939677357673645}]}, "Id": "14e441a9-8148-458d-8672-26a21428edae", "Relationships": [{"Type": "CHILD", "Ids": ["ced824af-f23a-4d53-b1f0-69161b3d211f", "515b62f1-bca1-4a06-8baf-384cd57a1259"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "$392.89", "Geometry": {"BoundingBox": {"Width": 0.16717977821826935, "Height": 0.01643204875290394, "Left": 0.8248246908187866, "Top": 0.9252015352249146}, "Polygon": [{"X": 0.8248249292373657, "Y": 0.9252015352249146}, {"X": 0.9920045137405396, "Y": 0.9252066612243652}, {"X": 0.9920042157173157, "Y": 0.9416335821151733}, {"X": 0.8248246908187866, "Y": 0.9416283369064331}]}, "Id": "2a4b1146-f3c3-4582-bdfa-f6f575d5c29e", "Relationships": [{"Type": "CHILD", "Ids": ["2f7ad4f7-5cf4-449d-a002-2e4feba5bf74"]}]}, {"BlockType": "LINE", "Confidence": 99.94725036621094, "Text": "Thank you for shopping at Love's", "Geometry": {"BoundingBox": {"Width": 0.7735617756843567, "Height": 0.018666258081793785, "Left": 0.02347639761865139, "Top": 0.9653551578521729}, "Polygon": [{"X": 0.02347649820148945, "Y": 0.9653551578521729}, {"X": 0.797038197517395, "Y": 0.9653794765472412}, {"X": 0.7970378994941711, "Y": 0.9840214252471924}, {"X": 0.02347639761865139, "Y": 0.9839969873428345}]}, "Id": "5021e53d-20bf-494a-9fc2-9c3d9451c134", "Relationships": [{"Type": "CHILD", "Ids": ["37c2a135-3342-4fa6-8a6f-f8fe42441f78", "b96f7786-7331-4f71-baa8-133c153d2e51", "f6c17956-9353-48b7-b1fb-83ca7e96e822", "26d3606e-4c63-4dd8-864d-54cbc7702be8", "5475db14-ce1c-4f13-a19b-751ae588397e", "4d25cdd8-c015-4c30-811e-4d34d97e655e"]}]}, {"BlockType": "WORD", "Confidence": 57.021881103515625, "Text": "Loves", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.36615806818008423, "Height": 0.04778224229812622, "Left": 0.4147644639015198, "Top": 0.013307332061231136}, "Polygon": [{"X": 0.414764940738678, "Y": 0.013307332061231136}, {"X": 0.780922532081604, "Y": 0.013315043412148952}, {"X": 0.7809218764305115, "Y": 0.06108957156538963}, {"X": 0.4147644639015198, "Y": 0.061081670224666595}]}, "Id": "2128189a-da5c-45ca-978b-764095ec545c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "STORE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11999460309743881, "Height": 0.013705122284591198, "Left": 0.40471282601356506, "Top": 0.08028876036405563}, "Polygon": [{"X": 0.4047129452228546, "Y": 0.08028876036405563}, {"X": 0.5247074365615845, "Y": 0.08029137551784515}, {"X": 0.5247072577476501, "Y": 0.0939938873052597}, {"X": 0.40471282601356506, "Y": 0.0939912497997284}]}, "Id": "bcfee136-c814-41ec-b08f-5e38cd8d1589"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "456", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07259245961904526, "Height": 0.013686072081327438, "Left": 0.5475374460220337, "Top": 0.08028730005025864}, "Polygon": [{"X": 0.547537624835968, "Y": 0.08028730005025864}, {"X": 0.6201299428939819, "Y": 0.08028887957334518}, {"X": 0.6201297640800476, "Y": 0.09397336840629578}, {"X": 0.5475374460220337, "Y": 0.09397177398204803}]}, "Id": "3633a586-39ec-4de9-b038-de983047e58c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "26530", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12000799179077148, "Height": 0.013565028086304665, "Left": 0.3444235026836395, "Top": 0.10069023817777634}, "Polygon": [{"X": 0.3444236218929291, "Y": 0.10069023817777634}, {"X": 0.464431494474411, "Y": 0.10069288313388824}, {"X": 0.46443137526512146, "Y": 0.11425526440143585}, {"X": 0.3444235026836395, "Y": 0.11425260454416275}]}, "Id": "61667f08-664e-4db3-a4c2-558c5378dc4e"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12010466307401657, "Height": 0.014124789275228977, "Left": 0.48925986886024475, "Top": 0.09991595149040222}, "Polygon": [{"X": 0.4892600178718567, "Y": 0.09991595149040222}, {"X": 0.6093645691871643, "Y": 0.09991859644651413}, {"X": 0.60936439037323, "Y": 0.11404073983430862}, {"X": 0.48925986886024475, "Y": 0.11403807997703552}]}, "Id": "700186ce-7a4f-4db9-a98d-560c9f358956"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Rd", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04732764884829521, "Height": 0.014253673143684864, "Left": 0.6338844895362854, "Top": 0.10000424087047577}, "Polygon": [{"X": 0.6338846683502197, "Y": 0.10000424087047577}, {"X": 0.6812121272087097, "Y": 0.10000528395175934}, {"X": 0.6812119483947754, "Y": 0.11425790935754776}, {"X": 0.6338844895362854, "Y": 0.11425686627626419}]}, "Id": "1eacd2c5-e33b-446d-86eb-a99faf8d80d6"}, {"BlockType": "WORD", "Confidence": 99.12308502197266, "Text": "Perrysburg,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.25922033190727234, "Height": 0.018198030069470406, "Left": 0.27228686213493347, "Top": 0.11988001316785812}, "Polygon": [{"X": 0.2722870111465454, "Y": 0.11988001316785812}, {"X": 0.5315071940422058, "Y": 0.11988577246665955}, {"X": 0.5315070152282715, "Y": 0.13807804882526398}, {"X": 0.27228686213493347, "Y": 0.13807223737239838}]}, "Id": "8fc4a5c5-fc14-4cc9-882d-454f9a31e679"}, {"BlockType": "WORD", "Confidence": 95.28678894042969, "Text": "OH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04894016310572624, "Height": 0.013424569740891457, "Left": 0.5590285062789917, "Top": 0.1207745373249054}, "Polygon": [{"X": 0.5590286254882812, "Y": 0.1207745373249054}, {"X": 0.6079686284065247, "Y": 0.12077562510967255}, {"X": 0.6079685091972351, "Y": 0.1341991126537323}, {"X": 0.5590285062789917, "Y": 0.13419800996780396}]}, "Id": "8e9b2513-037e-4f43-8954-b450e208afc1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "43551", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1199643462896347, "Height": 0.013771843165159225, "Left": 0.6318050622940063, "Top": 0.12065798044204712}, "Polygon": [{"X": 0.6318052411079407, "Y": 0.12065798044204712}, {"X": 0.7517694234848022, "Y": 0.12066064774990082}, {"X": 0.7517692446708679, "Y": 0.13442982733249664}, {"X": 0.6318050622940063, "Y": 0.13442714512348175}]}, "Id": "ef8af549-358d-4cac-aed5-ecf63cd7837c"}, {"BlockType": "WORD", "Confidence": 99.53125, "Text": "(419)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10856886208057404, "Height": 0.017070859670639038, "Left": 0.3485596776008606, "Top": 0.13970772922039032}, "Polygon": [{"X": 0.3485598564147949, "Y": 0.13970772922039032}, {"X": 0.4571285545825958, "Y": 0.13971015810966492}, {"X": 0.4571283757686615, "Y": 0.15677858889102936}, {"X": 0.3485596776008606, "Y": 0.15677613019943237}]}, "Id": "f1828bc1-6e73-4ea4-b781-4aab10ac5c99"}, {"BlockType": "WORD", "Confidence": 99.22772216796875, "Text": "837-0071", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19259946048259735, "Height": 0.013895104639232159, "Left": 0.48823609948158264, "Top": 0.1408262550830841}, "Polygon": [{"X": 0.4882362484931946, "Y": 0.1408262550830841}, {"X": 0.6808355450630188, "Y": 0.14083057641983032}, {"X": 0.6808353662490845, "Y": 0.15472134947776794}, {"X": 0.48823609948158264, "Y": 0.15471699833869934}]}, "Id": "e703a680-9e73-42a0-9cbb-38fff0b96a23"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "02/19/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24179041385650635, "Height": 0.016167884692549706, "Left": 0.024728834629058838, "Top": 0.17721199989318848}, "Polygon": [{"X": 0.024728924036026, "Y": 0.17721199989318848}, {"X": 0.2665192484855652, "Y": 0.1772175282239914}, {"X": 0.26651909947395325, "Y": 0.19337987899780273}, {"X": 0.024728834629058838, "Y": 0.19337432086467743}]}, "Id": "8b8a5a1c-79a8-4615-b822-acfb4f98ff33"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "Tkt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07210371643304825, "Height": 0.01439411286264658, "Left": 0.28835541009902954, "Top": 0.17708101868629456}, "Polygon": [{"X": 0.2883555293083191, "Y": 0.17708101868629456}, {"X": 0.3604591190814972, "Y": 0.17708265781402588}, {"X": 0.36045899987220764, "Y": 0.19147512316703796}, {"X": 0.28835541009902954, "Y": 0.19147346913814545}]}, "Id": "176ea8fd-bdef-44a1-adaf-8ed259951d4b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "#99350427", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.218807190656662, "Height": 0.013821110129356384, "Left": 0.3843909204006195, "Top": 0.1776130646467209}, "Polygon": [{"X": 0.38439103960990906, "Y": 0.1776130646467209}, {"X": 0.6031981110572815, "Y": 0.17761805653572083}, {"X": 0.6031979322433472, "Y": 0.19143417477607727}, {"X": 0.3843909204006195, "Y": 0.19142913818359375}]}, "Id": "a685c09d-aced-4bef-badb-68a227d0bbe5"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11323602497577667, "Height": 0.01735232211649418, "Left": 0.02416856773197651, "Top": 0.22376370429992676}, "Polygon": [{"X": 0.02416866272687912, "Y": 0.22376370429992676}, {"X": 0.13740459084510803, "Y": 0.22376635670661926}, {"X": 0.13740447163581848, "Y": 0.24111603200435638}, {"X": 0.02416856773197651, "Y": 0.24111336469650269}]}, "Id": "bd478feb-90ef-4b04-b4e1-6c80ec57a18b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SALE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09598682820796967, "Height": 0.013576441444456577, "Left": 0.16909296810626984, "Top": 0.22406163811683655}, "Polygon": [{"X": 0.169093057513237, "Y": 0.22406163811683655}, {"X": 0.2650797963142395, "Y": 0.22406387329101562}, {"X": 0.26507967710494995, "Y": 0.23763808608055115}, {"X": 0.16909296810626984, "Y": 0.23763582110404968}]}, "Id": "02ade84f-4496-4867-9a71-ccce4d20577c"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "(MOBILE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1631726324558258, "Height": 0.01679506152868271, "Left": 0.2963535189628601, "Top": 0.22276531159877777}, "Polygon": [{"X": 0.29635366797447205, "Y": 0.22276531159877777}, {"X": 0.4595261514186859, "Y": 0.2227691113948822}, {"X": 0.4595259726047516, "Y": 0.23956036567687988}, {"X": 0.2963535189628601, "Y": 0.23955653607845306}]}, "Id": "b0695218-7e1e-491a-8732-123e799b8cb2"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "COPY)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11441733688116074, "Height": 0.016666600480675697, "Left": 0.48440858721733093, "Top": 0.22293800115585327}, "Polygon": [{"X": 0.48440876603126526, "Y": 0.22293800115585327}, {"X": 0.5988259315490723, "Y": 0.22294066846370697}, {"X": 0.5988257527351379, "Y": 0.23960460722446442}, {"X": 0.48440858721733093, "Y": 0.23960191011428833}]}, "Id": "0173a9ae-20b0-4295-af5f-96432f4e23cf"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Qty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07232528924942017, "Height": 0.017174631357192993, "Left": 0.03349582478404045, "Top": 0.2738005816936493}, "Polygon": [{"X": 0.03349592164158821, "Y": 0.2738005816936493}, {"X": 0.10582111030817032, "Y": 0.2738023102283478}, {"X": 0.10582100600004196, "Y": 0.2909752130508423}, {"X": 0.03349582478404045, "Y": 0.2909734845161438}]}, "Id": "8cd9614b-5d60-4f6f-b100-480a93edb85a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Name", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09576063603162766, "Height": 0.01379395928233862, "Left": 0.14588573575019836, "Top": 0.27363643050193787}, "Polygon": [{"X": 0.14588584005832672, "Y": 0.27363643050193787}, {"X": 0.24164637923240662, "Y": 0.27363869547843933}, {"X": 0.24164627492427826, "Y": 0.28743037581443787}, {"X": 0.14588573575019836, "Y": 0.287428081035614}]}, "Id": "6b73aab6-3b73-487d-a7e6-1ff0fa6002e8"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11965912580490112, "Height": 0.01481472235172987, "Left": 0.5720095634460449, "Top": 0.27286213636398315}, "Polygon": [{"X": 0.5720096826553345, "Y": 0.27286213636398315}, {"X": 0.691668689250946, "Y": 0.2728649973869324}, {"X": 0.6916684508323669, "Y": 0.2876768708229065}, {"X": 0.5720095634460449, "Y": 0.2876739799976349}]}, "Id": "be724ee0-7961-481f-8437-fc5b8f3aa026"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11947587877511978, "Height": 0.014876419678330421, "Left": 0.8725711703300476, "Top": 0.27265599370002747}, "Polygon": [{"X": 0.8725713491439819, "Y": 0.27265599370002747}, {"X": 0.9920470118522644, "Y": 0.2726588547229767}, {"X": 0.9920467734336853, "Y": 0.28753241896629333}, {"X": 0.8725711703300476, "Y": 0.2875295579433441}]}, "Id": "d58e5a1d-b239-4917-8722-797655ec5e3e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRKDS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12086191773414612, "Height": 0.013951790519058704, "Left": 0.14444011449813843, "Top": 0.3234573006629944}, "Polygon": [{"X": 0.1444402039051056, "Y": 0.3234573006629944}, {"X": 0.26530203223228455, "Y": 0.32346025109291077}, {"X": 0.265301913022995, "Y": 0.3374090790748596}, {"X": 0.14444011449813843, "Y": 0.33740609884262085}]}, "Id": "264548b5-ef9d-4b4a-859d-c13bdb0f8592"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "392.89", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14412324130535126, "Height": 0.014073347672820091, "Left": 0.8481264710426331, "Top": 0.3234585225582123}, "Polygon": [{"X": 0.8481267094612122, "Y": 0.3234585225582123}, {"X": 0.9922497272491455, "Y": 0.32346203923225403}, {"X": 0.9922495484352112, "Y": 0.3375318646430969}, {"X": 0.8481264710426331, "Y": 0.3375283479690552}]}, "Id": "837a0279-91a5-4257-abf4-acdcdcda1fac"}, {"BlockType": "WORD", "Confidence": 99.3369369506836, "Text": "Pump:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11214932799339294, "Height": 0.01727992109954357, "Left": 0.17258475720882416, "Top": 0.34820595383644104}, "Polygon": [{"X": 0.1725848764181137, "Y": 0.34820595383644104}, {"X": 0.2847341001033783, "Y": 0.3482087254524231}, {"X": 0.28473395109176636, "Y": 0.36548587679862976}, {"X": 0.17258475720882416, "Y": 0.3654830753803253}]}, "Id": "10af51e1-d774-444c-9933-3efb9f48cb95"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "24", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047286681830883026, "Height": 0.01400380302220583, "Left": 0.5714358687400818, "Top": 0.348408043384552}, "Polygon": [{"X": 0.5714360475540161, "Y": 0.348408043384552}, {"X": 0.6187225580215454, "Y": 0.3484092056751251}, {"X": 0.6187223792076111, "Y": 0.36241185665130615}, {"X": 0.5714358687400818, "Y": 0.36241069436073303}]}, "Id": "ef2790c9-2191-4de4-86e5-274802f73b7f"}, {"BlockType": "WORD", "Confidence": 98.80879211425781, "Text": "Gallons:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1859770268201828, "Height": 0.015281463973224163, "Left": 0.17136044800281525, "Top": 0.37197884917259216}, "Polygon": [{"X": 0.1713605523109436, "Y": 0.37197884917259216}, {"X": 0.35733747482299805, "Y": 0.37198349833488464}, {"X": 0.3573373258113861, "Y": 0.3872603178024292}, {"X": 0.17136044800281525, "Y": 0.3872556686401367}]}, "Id": "4016d404-3163-4fcf-b438-420bf004fc1c"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "103.691", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16592863202095032, "Height": 0.014257281087338924, "Left": 0.5733498930931091, "Top": 0.3731890618801117}, "Polygon": [{"X": 0.5733500123023987, "Y": 0.3731890618801117}, {"X": 0.7392784953117371, "Y": 0.3731932044029236}, {"X": 0.7392783164978027, "Y": 0.3874463438987732}, {"X": 0.5733498930931091, "Y": 0.3874421715736389}]}, "Id": "dc4f6cf0-9aa4-4241-ae7c-ee6a30b7785b"}, {"BlockType": "WORD", "Confidence": 99.71440124511719, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1196020171046257, "Height": 0.014968041330575943, "Left": 0.1723049283027649, "Top": 0.3973214328289032}, "Polygon": [{"X": 0.17230503261089325, "Y": 0.3973214328289032}, {"X": 0.2919069528579712, "Y": 0.39732447266578674}, {"X": 0.29190683364868164, "Y": 0.41228947043418884}, {"X": 0.1723049283027649, "Y": 0.4122864305973053}]}, "Id": "2a74f208-918a-464a-9a7e-531423ca370f"}, {"BlockType": "WORD", "Confidence": 99.43778228759766, "Text": "/", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022034792229533195, "Height": 0.01555083692073822, "Left": 0.31704679131507874, "Top": 0.398323655128479}, "Polygon": [{"X": 0.3170469403266907, "Y": 0.398323655128479}, {"X": 0.3390815854072571, "Y": 0.398324191570282}, {"X": 0.33908143639564514, "Y": 0.41387447714805603}, {"X": 0.31704679131507874, "Y": 0.41387391090393066}]}, "Id": "5860501c-b7ef-4d16-8103-9722a8ae0e8a"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "Gal:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09015601873397827, "Height": 0.015208970755338669, "Left": 0.3646864593029022, "Top": 0.3969894051551819}, "Polygon": [{"X": 0.3646865785121918, "Y": 0.3969894051551819}, {"X": 0.4548424780368805, "Y": 0.39699167013168335}, {"X": 0.45484232902526855, "Y": 0.41219836473464966}, {"X": 0.3646864593029022, "Y": 0.4121960699558258}]}, "Id": "5eb43c5b-1198-4b80-a3c1-c7257d623e5a"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "3.789", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11925562471151352, "Height": 0.013878420926630497, "Left": 0.5715514421463013, "Top": 0.3981725573539734}, "Polygon": [{"X": 0.5715516209602356, "Y": 0.3981725573539734}, {"X": 0.6908071041107178, "Y": 0.39817556738853455}, {"X": 0.6908069252967834, "Y": 0.4120509624481201}, {"X": 0.5715514421463013, "Y": 0.41204795241355896}]}, "Id": "0a475121-fbbe-49c7-b4a4-6a46bbd4369e"}, {"BlockType": "WORD", "Confidence": 99.12308502197266, "Text": "Subtotal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19056780636310577, "Height": 0.015173632651567459, "Left": 0.03396288678050041, "Top": 0.44210904836654663}, "Polygon": [{"X": 0.03396296873688698, "Y": 0.44210904836654663}, {"X": 0.22453069686889648, "Y": 0.4421139359474182}, {"X": 0.22453057765960693, "Y": 0.4572826623916626}, {"X": 0.03396288678050041, "Y": 0.45727774500846863}]}, "Id": "a491f31d-6640-402b-a9c8-108823b6fa81"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "392.89", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14390680193901062, "Height": 0.014079509302973747, "Left": 0.847923755645752, "Top": 0.4432080388069153}, "Polygon": [{"X": 0.8479239344596863, "Y": 0.4432080388069153}, {"X": 0.9918305277824402, "Y": 0.44321176409721375}, {"X": 0.9918302893638611, "Y": 0.4572875499725342}, {"X": 0.847923755645752, "Y": 0.4572838246822357}]}, "Id": "d153e7ec-4caf-4efe-b444-04c3974da24d"}, {"BlockType": "WORD", "Confidence": 99.20280456542969, "Text": "Sales", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11787708848714828, "Height": 0.015022771432995796, "Left": 0.03404952213168144, "Top": 0.46707290410995483}, "Polygon": [{"X": 0.03404960781335831, "Y": 0.46707290410995483}, {"X": 0.15192660689353943, "Y": 0.46707597374916077}, {"X": 0.15192650258541107, "Y": 0.4820956885814667}, {"X": 0.03404952213168144, "Y": 0.48209258913993835}]}, "Id": "bfef54b3-2653-40dd-8854-980788d56ce0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07297555357217789, "Height": 0.014130211435258389, "Left": 0.17783312499523163, "Top": 0.4681219458580017}, "Polygon": [{"X": 0.1778332144021988, "Y": 0.4681219458580017}, {"X": 0.2508086860179901, "Y": 0.4681238532066345}, {"X": 0.25080856680870056, "Y": 0.4822521507740021}, {"X": 0.17783312499523163, "Y": 0.48225024342536926}]}, "Id": "ff840c54-28ae-4efa-b317-b886767f44a6"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "0.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09478122740983963, "Height": 0.013655280694365501, "Left": 0.8968883752822876, "Top": 0.4684244394302368}, "Polygon": [{"X": 0.8968886137008667, "Y": 0.4684244394302368}, {"X": 0.9916695952415466, "Y": 0.468426913022995}, {"X": 0.9916694164276123, "Y": 0.48207971453666687}, {"X": 0.8968883752822876, "Y": 0.4820772409439087}]}, "Id": "96d06d15-9a43-43f7-9cbc-d5a5d8c42ef0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11935877799987793, "Height": 0.014880355447530746, "Left": 0.03258077800273895, "Top": 0.49193114042282104}, "Polygon": [{"X": 0.03258085995912552, "Y": 0.49193114042282104}, {"X": 0.15193955600261688, "Y": 0.49193426966667175}, {"X": 0.15193945169448853, "Y": 0.5068114995956421}, {"X": 0.03258077800273895, "Y": 0.506808340549469}]}, "Id": "9b26a134-55df-430c-af31-46d77d138e06"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "392.89", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14459869265556335, "Height": 0.014108043164014816, "Left": 0.8476577401161194, "Top": 0.4930901527404785}, "Polygon": [{"X": 0.8476579785346985, "Y": 0.4930901527404785}, {"X": 0.9922564625740051, "Y": 0.49309396743774414}, {"X": 0.992256224155426, "Y": 0.5071982145309448}, {"X": 0.8476577401161194, "Y": 0.5071943998336792}]}, "Id": "3bc06aba-fdf8-46a6-9b71-cea42fa9e664"}, {"BlockType": "WORD", "Confidence": 99.68511199951172, "Text": "Received:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.21157892048358917, "Height": 0.0153798284009099, "Left": 0.025389736518263817, "Top": 0.5334579944610596}, "Polygon": [{"X": 0.02538982219994068, "Y": 0.5334579944610596}, {"X": 0.23696865141391754, "Y": 0.5334636569023132}, {"X": 0.236968532204628, "Y": 0.5488378405570984}, {"X": 0.025389736518263817, "Y": 0.5488321781158447}]}, "Id": "ad8fcb97-0f7f-4435-973c-51d1618d7603"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "FLEETONE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19068774580955505, "Height": 0.013925242237746716, "Left": 0.07271936535835266, "Top": 0.5583653450012207}, "Polygon": [{"X": 0.07271944731473923, "Y": 0.5583653450012207}, {"X": 0.2634071111679077, "Y": 0.5583704710006714}, {"X": 0.26340699195861816, "Y": 0.5722905397415161}, {"X": 0.07271936535835266, "Y": 0.5722854137420654}]}, "Id": "d823d0db-aef9-403d-a9a6-a93b042ce1f9"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "392.89", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1442166417837143, "Height": 0.014035806991159916, "Left": 0.8479160070419312, "Top": 0.558347761631012}, "Polygon": [{"X": 0.8479161858558655, "Y": 0.558347761631012}, {"X": 0.9921326637268066, "Y": 0.5583516359329224}, {"X": 0.9921324253082275, "Y": 0.5723835825920105}, {"X": 0.8479160070419312, "Y": 0.5723796486854553}]}, "Id": "3e2e72d7-55f6-4fef-9749-25d1c1b409a2"}, {"BlockType": "WORD", "Confidence": 99.30584716796875, "Text": "0180", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09767990559339523, "Height": 0.013892708346247673, "Left": 0.4331113398075104, "Top": 0.5832433104515076}, "Polygon": [{"X": 0.4331114888191223, "Y": 0.5832433104515076}, {"X": 0.5307912826538086, "Y": 0.5832459926605225}, {"X": 0.5307911038398743, "Y": 0.5971360206604004}, {"X": 0.4331113398075104, "Y": 0.5971333384513855}]}, "Id": "c0482d36-3de9-469e-a04f-c8d99f4c1b76"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WAVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1230725422501564, "Height": 0.013705093413591385, "Left": 0.5513331890106201, "Top": 0.5832860469818115}, "Polygon": [{"X": 0.5513333678245544, "Y": 0.5832860469818115}, {"X": 0.6744057536125183, "Y": 0.583289384841919}, {"X": 0.674405574798584, "Y": 0.5969911217689514}, {"X": 0.5513331890106201, "Y": 0.596987783908844}]}, "Id": "60adf204-eca8-4b04-ae9d-2f4cf19eaf30"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0977666825056076, "Height": 0.01485839206725359, "Left": 0.121185801923275, "Top": 0.6072020530700684}, "Polygon": [{"X": 0.12118589878082275, "Y": 0.6072020530700684}, {"X": 0.2189524918794632, "Y": 0.6072047352790833}, {"X": 0.21895237267017365, "Y": 0.6220604181289673}, {"X": 0.121185801923275, "Y": 0.6220577359199524}]}, "Id": "0fad2ab1-6530-40d5-95d7-dc41a32f016d"}, {"BlockType": "WORD", "Confidence": 93.56166076660156, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06621161848306656, "Height": 0.01390654407441616, "Left": 0.24335405230522156, "Top": 0.608212947845459}, "Polygon": [{"X": 0.2433541715145111, "Y": 0.608212947845459}, {"X": 0.3095656633377075, "Y": 0.6082147359848022}, {"X": 0.30956554412841797, "Y": 0.6221194863319397}, {"X": 0.24335405230522156, "Y": 0.6221176385879517}]}, "Id": "dea1e84c-eb0e-4b49-bb05-6485876c372f"}, {"BlockType": "WORD", "Confidence": 97.34375, "Text": "118916", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14247813820838928, "Height": 0.013702395372092724, "Left": 0.31797370314598083, "Top": 0.6080334782600403}, "Polygon": [{"X": 0.3179738223552704, "Y": 0.6080334782600403}, {"X": 0.4604518413543701, "Y": 0.6080374121665955}, {"X": 0.4604516923427582, "Y": 0.6217358708381653}, {"X": 0.31797370314598083, "Y": 0.6217319369316101}]}, "Id": "b884a36f-23e3-4c77-85ad-8f4e380087dd"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "Company:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.18433818221092224, "Height": 0.016999706625938416, "Left": 0.07159576565027237, "Top": 0.6331227421760559}, "Polygon": [{"X": 0.07159586995840073, "Y": 0.6331227421760559}, {"X": 0.255933940410614, "Y": 0.6331278681755066}, {"X": 0.25593382120132446, "Y": 0.6501224637031555}, {"X": 0.07159576565027237, "Y": 0.6501173377037048}]}, "Id": "a992a968-9063-4293-8b45-a6abb86a1e46"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "AMERILUX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.196120485663414, "Height": 0.014199135825037956, "Left": 0.28520750999450684, "Top": 0.6329023241996765}, "Polygon": [{"X": 0.2852076292037964, "Y": 0.6329023241996765}, {"X": 0.48132798075675964, "Y": 0.6329077482223511}, {"X": 0.4813278317451477, "Y": 0.6471014618873596}, {"X": 0.28520750999450684, "Y": 0.6470959782600403}]}, "Id": "c0e1aed9-2bdc-40f2-b611-8f968a1288c5"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "TRANSPORTATION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.3393649458885193, "Height": 0.013967436738312244, "Left": 0.06996220350265503, "Top": 0.6530441045761108}, "Polygon": [{"X": 0.06996229290962219, "Y": 0.6530441045761108}, {"X": 0.4093271493911743, "Y": 0.6530536413192749}, {"X": 0.4093270003795624, "Y": 0.667011559009552}, {"X": 0.06996220350265503, "Y": 0.6670019626617432}]}, "Id": "96da7bba-e3de-4c1e-abdd-5b69673da3c7"}, {"BlockType": "WORD", "Confidence": 99.7120132446289, "Text": "INVOICE#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1934967190027237, "Height": 0.014026295393705368, "Left": 0.07197312265634537, "Top": 0.677915632724762}, "Polygon": [{"X": 0.07197320461273193, "Y": 0.677915632724762}, {"X": 0.26546984910964966, "Y": 0.6779211163520813}, {"X": 0.2654697299003601, "Y": 0.6919419169425964}, {"X": 0.07197312265634537, "Y": 0.6919364333152771}]}, "Id": "df1af6fd-460b-4c66-bdaa-a0c604387170"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "44166", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1204879879951477, "Height": 0.013861657120287418, "Left": 0.2860279083251953, "Top": 0.6779040098190308}, "Polygon": [{"X": 0.2860279977321625, "Y": 0.6779040098190308}, {"X": 0.406515896320343, "Y": 0.677907407283783}, {"X": 0.4065157473087311, "Y": 0.6917656660079956}, {"X": 0.2860279083251953, "Y": 0.6917622685432434}]}, "Id": "f8ea9e1f-27b0-4f5a-9e40-3df993d64ec9"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "TripNumber", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2429015338420868, "Height": 0.01809234544634819, "Left": 0.032340798527002335, "Top": 0.745031476020813}, "Polygon": [{"X": 0.03234089910984039, "Y": 0.745031476020813}, {"X": 0.27524232864379883, "Y": 0.7450385093688965}, {"X": 0.2752421796321869, "Y": 0.7631238102912903}, {"X": 0.032340798527002335, "Y": 0.763116717338562}]}, "Id": "b3de6448-7e3a-49a1-802a-03816d0136d7"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "CLEVELAND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.21618646383285522, "Height": 0.013634724542498589, "Left": 0.7754725813865662, "Top": 0.7457865476608276}, "Polygon": [{"X": 0.7754727602005005, "Y": 0.7457865476608276}, {"X": 0.9916590452194214, "Y": 0.7457928657531738}, {"X": 0.9916588664054871, "Y": 0.7594212889671326}, {"X": 0.7754725813865662, "Y": 0.7594149708747864}]}, "Id": "efe97672-6122-4ca5-a067-2a8f1103401c"}, {"BlockType": "WORD", "Confidence": 57.75928497314453, "Text": "Hub0dometer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.26556143164634705, "Height": 0.014417048543691635, "Left": 0.03357602283358574, "Top": 0.7699764966964722}, "Polygon": [{"X": 0.03357610106468201, "Y": 0.7699764966964722}, {"X": 0.2991374433040619, "Y": 0.7699843049049377}, {"X": 0.29913732409477234, "Y": 0.7843935489654541}, {"X": 0.03357602283358574, "Y": 0.7843857407569885}]}, "Id": "4a727dd1-4452-4349-9c1a-c77b1cf9eacc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "32000", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11763736605644226, "Height": 0.013297347351908684, "Left": 0.8748686909675598, "Top": 0.7709243893623352}, "Polygon": [{"X": 0.8748688697814941, "Y": 0.7709243893623352}, {"X": 0.9925060272216797, "Y": 0.7709278464317322}, {"X": 0.9925058484077454, "Y": 0.7842217087745667}, {"X": 0.8748686909675598, "Y": 0.7842182517051697}]}, "Id": "261f140b-e619-40b1-b011-798c23861deb"}, {"BlockType": "WORD", "Confidence": 98.80580139160156, "Text": "VehicleID", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.21793395280838013, "Height": 0.01474472600966692, "Left": 0.032365307211875916, "Top": 0.794425904750824}, "Polygon": [{"X": 0.03236538916826248, "Y": 0.794425904750824}, {"X": 0.25029924511909485, "Y": 0.7944323420524597}, {"X": 0.2502991259098053, "Y": 0.809170663356781}, {"X": 0.032365307211875916, "Y": 0.8091641664505005}]}, "Id": "dee4c9a0-515c-4183-9ecf-72fa0f0b11af"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "6063", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09416894614696503, "Height": 0.013708856888115406, "Left": 0.8972878456115723, "Top": 0.795677661895752}, "Polygon": [{"X": 0.8972880244255066, "Y": 0.795677661895752}, {"X": 0.9914568066596985, "Y": 0.7956804633140564}, {"X": 0.9914565682411194, "Y": 0.8093864917755127}, {"X": 0.8972878456115723, "Y": 0.8093836903572083}]}, "Id": "dbf006b8-2c83-42d6-b6fb-192ac2b75a08"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "AMERILUX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19543160498142242, "Height": 0.013423096388578415, "Left": 0.8001055121421814, "Top": 0.8206210732460022}, "Polygon": [{"X": 0.8001056909561157, "Y": 0.8206210732460022}, {"X": 0.9955371022224426, "Y": 0.8206268548965454}, {"X": 0.9955368638038635, "Y": 0.8340441584587097}, {"X": 0.8001055121421814, "Y": 0.8340383172035217}]}, "Id": "2f95dc5f-f9ad-4fd0-9162-a2ab510eade2"}, {"BlockType": "WORD", "Confidence": 84.24964141845703, "Text": "TruckingCompanyName", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.4615255296230316, "Height": 0.01760481484234333, "Left": 0.03254159167408943, "Top": 0.8294249176979065}, "Polygon": [{"X": 0.03254168853163719, "Y": 0.8294249176979065}, {"X": 0.49406713247299194, "Y": 0.8294386863708496}, {"X": 0.49406692385673523, "Y": 0.8470296859741211}, {"X": 0.03254159167408943, "Y": 0.8470157980918884}]}, "Id": "d4e42058-3645-4728-a65d-741478187faa"}, {"BlockType": "WORD", "Confidence": 71.1318130493164, "Text": "TRANSPORTATION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.3385436534881592, "Height": 0.013585027307271957, "Left": 0.6540789604187012, "Top": 0.840741753578186}, "Polygon": [{"X": 0.6540791392326355, "Y": 0.840741753578186}, {"X": 0.9926226139068604, "Y": 0.8407519459724426}, {"X": 0.9926223754882812, "Y": 0.8543267846107483}, {"X": 0.6540789604187012, "Y": 0.8543165326118469}]}, "Id": "f0712683-dabf-4f7a-8ae0-cc2a8ed01a13"}, {"BlockType": "WORD", "Confidence": 99.27275848388672, "Text": "Pos:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0873478576540947, "Height": 0.013666235841810703, "Left": 0.026448041200637817, "Top": 0.8643727898597717}, "Polygon": [{"X": 0.026448117569088936, "Y": 0.8643727898597717}, {"X": 0.11379589885473251, "Y": 0.8643754720687866}, {"X": 0.11379580944776535, "Y": 0.8780390620231628}, {"X": 0.026448041200637817, "Y": 0.878036379814148}]}, "Id": "0515f158-fcf0-4f1e-91be-12ec75837573"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "#99", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07286417484283447, "Height": 0.013259653933346272, "Left": 0.1445065438747406, "Top": 0.8647053241729736}, "Polygon": [{"X": 0.14450663328170776, "Y": 0.8647053241729736}, {"X": 0.21737073361873627, "Y": 0.8647075891494751}, {"X": 0.2173706293106079, "Y": 0.8779650330543518}, {"X": 0.1445065438747406, "Y": 0.8779627680778503}]}, "Id": "9ea3c57b-500a-4966-aa94-8c65567273f5"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11296411603689194, "Height": 0.013553371652960777, "Left": 0.02509930543601513, "Top": 0.8847086429595947}, "Polygon": [{"X": 0.0250993799418211, "Y": 0.8847086429595947}, {"X": 0.1380634307861328, "Y": 0.8847121000289917}, {"X": 0.13806332647800446, "Y": 0.8982620239257812}, {"X": 0.02509930543601513, "Y": 0.8982585072517395}]}, "Id": "516f80b9-d4c7-4d70-8d94-6ef5e402a547"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "02/19/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.24083253741264343, "Height": 0.01591351442039013, "Left": 0.16873759031295776, "Top": 0.8843094110488892}, "Polygon": [{"X": 0.16873769462108612, "Y": 0.8843094110488892}, {"X": 0.4095701277256012, "Y": 0.8843167424201965}, {"X": 0.40956997871398926, "Y": 0.900222897529602}, {"X": 0.16873759031295776, "Y": 0.9002155065536499}]}, "Id": "a6c6a546-5c3f-4109-b7a9-5041740044c3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.120469830930233, "Height": 0.014296513982117176, "Left": 0.03238503262400627, "Top": 0.9253677725791931}, "Polygon": [{"X": 0.03238511458039284, "Y": 0.9253677725791931}, {"X": 0.15285485982894897, "Y": 0.9253714680671692}, {"X": 0.1528547704219818, "Y": 0.9396643042564392}, {"X": 0.03238503262400627, "Y": 0.9396605491638184}]}, "Id": "ced824af-f23a-4d53-b1f0-69161b3d211f"}, {"BlockType": "WORD", "Confidence": 98.80421447753906, "Text": "Sale:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11303164064884186, "Height": 0.014680208638310432, "Left": 0.17901530861854553, "Top": 0.9250051975250244}, "Polygon": [{"X": 0.1790154129266739, "Y": 0.9250051975250244}, {"X": 0.2920469641685486, "Y": 0.9250087141990662}, {"X": 0.29204681515693665, "Y": 0.9396854043006897}, {"X": 0.17901530861854553, "Y": 0.939681887626648}]}, "Id": "515b62f1-bca1-4a06-8baf-384cd57a1259"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "$392.89", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16717977821826935, "Height": 0.01643204875290394, "Left": 0.8248246908187866, "Top": 0.9252015352249146}, "Polygon": [{"X": 0.8248249292373657, "Y": 0.9252015352249146}, {"X": 0.9920045137405396, "Y": 0.9252066612243652}, {"X": 0.9920042157173157, "Y": 0.9416335821151733}, {"X": 0.8248246908187866, "Y": 0.9416283369064331}]}, "Id": "2f7ad4f7-5cf4-449d-a002-2e4feba5bf74"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Thank", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12302687764167786, "Height": 0.014477711170911789, "Left": 0.02347641810774803, "Top": 0.965557336807251}, "Polygon": [{"X": 0.02347649820148945, "Y": 0.965557336807251}, {"X": 0.1465032994747162, "Y": 0.9655612111091614}, {"X": 0.14650319516658783, "Y": 0.9800350666046143}, {"X": 0.02347641810774803, "Y": 0.9800311923027039}]}, "Id": "37c2a135-3342-4fa6-8a6f-f8fe42441f78"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "you", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07165098935365677, "Height": 0.014456904493272305, "Left": 0.17026261985301971, "Top": 0.9690039157867432}, "Polygon": [{"X": 0.17026272416114807, "Y": 0.9690039157867432}, {"X": 0.2419136017560959, "Y": 0.9690061807632446}, {"X": 0.24191349744796753, "Y": 0.9834607839584351}, {"X": 0.17026261985301971, "Y": 0.9834585189819336}]}, "Id": "b96f7786-7331-4f71-baa8-133c153d2e51"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07121920585632324, "Height": 0.014500398188829422, "Left": 0.2685699760913849, "Top": 0.9653629064559937}, "Polygon": [{"X": 0.26857009530067444, "Y": 0.9653629064559937}, {"X": 0.33978918194770813, "Y": 0.9653651118278503}, {"X": 0.3397890627384186, "Y": 0.9798632860183716}, {"X": 0.2685699760913849, "Y": 0.9798610210418701}]}, "Id": "f6c17956-9353-48b7-b1fb-83ca7e96e822"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "shopping", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1916341483592987, "Height": 0.01844141073524952, "Left": 0.3649239242076874, "Top": 0.9655724167823792}, "Polygon": [{"X": 0.3649241030216217, "Y": 0.9655724167823792}, {"X": 0.5565580725669861, "Y": 0.9655784368515015}, {"X": 0.5565578937530518, "Y": 0.9840138554573059}, {"X": 0.3649239242076874, "Y": 0.9840077757835388}]}, "Id": "26d3606e-4c63-4dd8-864d-54cbc7702be8"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "at", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04678300395607948, "Height": 0.013561444357037544, "Left": 0.5814979076385498, "Top": 0.9667906761169434}, "Polygon": [{"X": 0.5814980268478394, "Y": 0.9667906761169434}, {"X": 0.6282808780670166, "Y": 0.9667921662330627}, {"X": 0.628280758857727, "Y": 0.9803521037101746}, {"X": 0.5814979076385498, "Y": 0.9803506731987}]}, "Id": "5475db14-ce1c-4f13-a19b-751ae588397e"}, {"BlockType": "WORD", "Confidence": 99.80309295654297, "Text": "Love's", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1423451006412506, "Height": 0.013798468746244907, "Left": 0.654693067073822, "Top": 0.9664228558540344}, "Polygon": [{"X": 0.6546931862831116, "Y": 0.9664228558540344}, {"X": 0.7970381379127502, "Y": 0.9664273262023926}, {"X": 0.7970379590988159, "Y": 0.9802213311195374}, {"X": 0.654693067073822, "Y": 0.9802168011665344}]}, "Id": "4d25cdd8-c015-4c30-811e-4d34d97e655e"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "6659c30f-6ec1-4414-b5ca-cdc1f8d0c3df", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "6659c30f-6ec1-4414-b5ca-cdc1f8d0c3df", "content-type": "application/x-amz-json-1.1", "content-length": "63136", "date": "Thu, 18 Sep 2025 08:51:02 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "05bbea7c-2bfe-4254-9326-01517d7c70b3", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 08:51:05 GMT", "content-type": "application/json", "content-length": "976", "connection": "keep-alive", "x-amzn-requestid": "05bbea7c-2bfe-4254-9326-01517d7c70b3"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a page content. Let's analyze: It appears to be a fuel receipt from Love's Gas Station, showing pump details, gallons, total, etc. Keywords: \"Fuel\", \"Gallons\", \"Price / Gal\", \"Sales Tax\", \"Received\", \"Auth No\". This matches \"fuel_receipt\". The document also includes \"TRANSPORTATION INVOICE# 44166\" which might be a transport invoice line? But the content largely is a fuel receipt. We should classify as \"fuel_receipt\".\n\nCheck for any multi-page: Only one page. So output: [{\"page_no\":1,\"doc_type\":\"fuel_receipt\"}] using function call."}}}, {"toolUse": {"toolUseId": "tooluse_TlPNz0mBQ6S4qPDerGi7nA", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2329, "outputTokens": 175, "totalTokens": 2504}, "metrics": {"latencyMs": 886}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/757947e1_9_fuel_receipt_1.png", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nLoves\nSTORE 456\n26530 Baker Rd\nPerrysburg, OH 43551\n**************\n02/19/2025 Tkt #99350427\nType: SALE (MOBILE COPY)\nQty Name\nPrice\nTotal\nTRKDS\n392.89\nPump:\n24\nGallons:\n103.691\nPrice / Gal:\n3.789\nSubtotal\n392.89\nSales Tax\n0.00\nTotal\n392.89\nReceived:\nFLEETONE\n392.89\n0180 WAVED\nAuth No: 118916\nCompany: AMERILUX\nTRANSPORTATION\nINVOICE# 44166\nTripNumber\nCLEVELAND\nHub0dometer\n32000\nVehicleID\n6063\nAMERILUX\nTruckingCompanyName\nTRANSPORTATION\nPos: #99\nDate: 02/19/2025\nTotal Sale:\n$392.89\nThank you for shopping at <PERSON>'s\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}