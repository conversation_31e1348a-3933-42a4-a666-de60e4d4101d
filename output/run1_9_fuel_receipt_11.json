{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 5.784079704085343e-08, "Y": 0.0}, {"X": 1.0, "Y": 6.393236873236674e-08}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "292f11d0-e873-4ffa-a6fa-4c520975513c", "Relationships": [{"Type": "CHILD", "Ids": ["794df16c-8c72-4bc3-ab98-56ed2f8499a0", "6c6e9be7-b600-4eeb-a5f0-31c6dcbfacd1", "816fbf2a-0776-4081-8846-27cda913ec8e", "febbcb28-53eb-4d38-af1f-f358ed0df518", "ce66751b-68e4-4dc1-865a-607a1cc30009", "6dbabd18-ec83-418d-ab1c-54284c33178c", "614c70ed-17a0-4129-bad2-735e7d9bf5da", "63b78798-6479-4a5b-9acd-fa78aae3f503", "500e60b4-fe6d-4849-98d2-7896577e908b", "e29f9696-a938-4b8a-978e-2061dae1c135", "6096e74d-4987-4553-9a30-4227d2b4e627", "b40b6679-2684-426f-9e04-7d15b068e88c", "fec1ba47-400d-48d2-a1c8-e748cd23680d", "15818c48-c899-46ed-97dd-ffdc7477ea27", "53f16fe2-b13f-4433-9069-3aaed621c8ad", "118c059c-0ab0-462b-959e-b773886660ca", "4256b6b3-ebc1-4039-a889-bf71f1be5c60", "be522b78-0d88-42e9-a62e-81920711577d", "b4edbcfd-80de-4bc8-af3d-529c5d51b340", "e80d5cc3-8630-4513-9666-0fa8fe1b8ee5", "6dc6b84b-cd8e-4ca9-ae28-29dd01469fc6", "c05c7446-4466-4416-ad74-45891de61bed", "c1f1d030-df4d-454d-b05d-34f84282b7af", "3d2e3924-b41e-493e-a38f-a25af128037f", "b495f47e-ef1a-40c8-9874-ebf21d975e2d", "c68ba78f-b91c-4bd4-82f2-c694fd3381ec", "fbab5cfb-ab4f-4efd-be44-7868e698c23d", "7802d009-0986-4f0a-930a-49b7244b3fc6", "b4ad5208-4b12-44cc-a4b3-742da6483bb9", "b5d0a570-c20c-4bd0-9938-f2be4cd5615c", "48ff500a-45f7-4c7b-9548-02721ede10f8", "779c8ccc-2a3b-4abe-a3c2-0e9be69a3e20", "5da3b1c8-b30c-4acb-a43b-30878ab46cdc", "599a2440-e8b0-4b4f-9248-6ae5bcc43a96", "a1309950-8bb0-4723-8d03-864f7e8c7157", "fa7fe8cd-83ae-4c08-9444-ebac859460ac", "dea7f1f3-58c5-4d22-9fb9-dda556cee23c", "7b954834-529f-40e4-b89d-4111e68a6cb1", "f4b84f0d-fb0e-49ab-a088-1c4e0c0d58a6"]}]}, {"BlockType": "LINE", "Confidence": 51.820194244384766, "Text": "Loves.", "Geometry": {"BoundingBox": {"Width": 0.49028944969177246, "Height": 0.06023992598056793, "Left": 0.3811255395412445, "Top": 0.020498966798186302}, "Polygon": [{"X": 0.3811255991458893, "Y": 0.020498966798186302}, {"X": 0.8714141845703125, "Y": 0.02053953893482685}, {"X": 0.*********0353394, "Y": 0.08073889464139938}, {"X": 0.3811255395412445, "Y": 0.08069930970668793}]}, "Id": "794df16c-8c72-4bc3-ab98-56ed2f8499a0", "Relationships": [{"Type": "CHILD", "Ids": ["f2a562fb-c688-4bf2-b09d-d964f50c7bce"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Store #878", "Geometry": {"BoundingBox": {"Width": 0.17154346406459808, "Height": 0.01425723172724247, "Left": 0.4229850172996521, "Top": 0.09585750102996826}, "Polygon": [{"X": 0.4229850172996521, "Y": 0.09585750102996826}, {"X": 0.5945283770561218, "Y": 0.09587126225233078}, {"X": 0.5945284962654114, "Y": 0.11011473089456558}, {"X": 0.4229850172996521, "Y": 0.11010105162858963}]}, "Id": "6c6e9be7-b600-4eeb-a5f0-31c6dcbfacd1", "Relationships": [{"Type": "CHILD", "Ids": ["929215f1-d807-41f7-94d4-f5f5fd30ee38", "88a9c6b7-bf8c-494e-b84e-f2c0bbb09ae6"]}]}, {"BlockType": "LINE", "Confidence": 99.80069732666016, "Text": "7900 Adobe Road", "Geometry": {"BoundingBox": {"Width": 0.25730910897254944, "Height": 0.014717216603457928, "Left": 0.37171629071235657, "Top": 0.11410948634147644}, "Polygon": [{"X": 0.37171629071235657, "Y": 0.11410948634147644}, {"X": 0.6290252804756165, "Y": 0.11412996798753738}, {"X": 0.629025399684906, "Y": 0.12882670760154724}, {"X": 0.37171629071235657, "Y": 0.12880635261535645}]}, "Id": "816fbf2a-0776-4081-8846-27cda913ec8e", "Relationships": [{"Type": "CHILD", "Ids": ["19ad2527-502b-4cf2-8016-4614ff5abf4b", "4f9de1bc-6693-47d4-acb8-4ec67619169a", "16b8fb9d-907e-4131-be7e-f0522a582a16"]}]}, {"BlockType": "LINE", "Confidence": 99.88394165039062, "Text": "Alamo, MI 49009", "Geometry": {"BoundingBox": {"Width": 0.2574242949485779, "Height": 0.01638977974653244, "Left": 0.37165775895118713, "Top": 0.13229776918888092}, "Polygon": [{"X": 0.3716577887535095, "Y": 0.13229776918888092}, {"X": 0.6290819644927979, "Y": 0.13231810927391052}, {"X": 0.6290820837020874, "Y": 0.14868755638599396}, {"X": 0.37165775895118713, "Y": 0.1486673504114151}]}, "Id": "febbcb28-53eb-4d38-af1f-f358ed0df518", "Relationships": [{"Type": "CHILD", "Ids": ["822903e7-ca0d-4e5a-abc4-8296343d39ea", "bd68bd55-f990-4b0b-96f9-66b3d984b57e", "23a05616-c1aa-4a24-8411-6e3eb8382606"]}]}, {"BlockType": "LINE", "Confidence": 99.33454132080078, "Text": "(269)903-0234", "Geometry": {"BoundingBox": {"Width": 0.2204139232635498, "Height": 0.014820795506238937, "Left": 0.3920165002346039, "Top": 0.15107470750808716}, "Polygon": [{"X": 0.3920165002346039, "Y": 0.15107470750808716}, {"X": 0.6124303340911865, "Y": 0.15109197795391083}, {"X": 0.6124303936958313, "Y": 0.1658955067396164}, {"X": 0.3920165002346039, "Y": 0.16587834060192108}]}, "Id": "ce66751b-68e4-4dc1-865a-607a1cc30009", "Relationships": [{"Type": "CHILD", "Ids": ["20032ebc-dc3c-4eea-b90b-b097402412b8"]}]}, {"BlockType": "LINE", "Confidence": 99.94061279296875, "Text": "02/18/2025 Tkt #1247187", "Geometry": {"BoundingBox": {"Width": 0.38997316360473633, "Height": 0.015880590304732323, "Left": 0.10236267745494843, "Top": 0.18702295422554016}, "Polygon": [{"X": 0.10236281901597977, "Y": 0.18702295422554016}, {"X": 0.49233579635620117, "Y": 0.18705303966999054}, {"X": 0.49233582615852356, "Y": 0.20290355384349823}, {"X": 0.10236267745494843, "Y": 0.20287367701530457}]}, "Id": "6dbabd18-ec83-418d-ab1c-54284c33178c", "Relationships": [{"Type": "CHILD", "Ids": ["e3f9c9cf-8e19-468c-8b8e-44afa35b1f96", "66280a31-0ab6-4c1d-9966-4e1fffe8e43a", "82cce227-20ac-4ddc-a3d9-116c5beff348"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Type: SALE", "Geometry": {"BoundingBox": {"Width": 0.16908413171768188, "Height": 0.016350803896784782, "Left": 0.10122762620449066, "Top": 0.22403831779956818}, "Polygon": [{"X": 0.101227767765522, "Y": 0.22403831779956818}, {"X": 0.27031177282333374, "Y": 0.22405114769935608}, {"X": 0.2703116834163666, "Y": 0.24038910865783691}, {"X": 0.10122762620449066, "Y": 0.24037636816501617}]}, "Id": "614c70ed-17a0-4129-bad2-735e7d9bf5da", "Relationships": [{"Type": "CHILD", "Ids": ["3de6c353-371a-4812-9e5f-919c664df104", "fe6c2ac0-f317-434b-83af-f0012c44f6c0"]}]}, {"BlockType": "LINE", "Confidence": 98.31612396240234, "Text": "(ORIGINAL)", "Geometry": {"BoundingBox": {"Width": 0.1661902368068695, "Height": 0.015194191597402096, "Left": 0.39319106936454773, "Top": 0.22606484591960907}, "Polygon": [{"X": 0.39319106936454773, "Y": 0.22606484591960907}, {"X": 0.5593812465667725, "Y": 0.22607745230197906}, {"X": 0.5593813061714172, "Y": 0.24125903844833374}, {"X": 0.39319106936454773, "Y": 0.2412465214729309}]}, "Id": "63b78798-6479-4a5b-9acd-fa78aae3f503", "Relationships": [{"Type": "CHILD", "Ids": ["e39eb9be-5821-45b1-b5e0-6f5c7016b312"]}]}, {"BlockType": "LINE", "Confidence": 99.97518920898438, "Text": "Qty Name", "Geometry": {"BoundingBox": {"Width": 0.13598565757274628, "Height": 0.01564953476190567, "Left": 0.1015956923365593, "Top": 0.2613508403301239}, "Polygon": [{"X": 0.10159582644701004, "Y": 0.2613508403301239}, {"X": 0.23758134245872498, "Y": 0.2613610029220581}, {"X": 0.237581267952919, "Y": 0.27700039744377136}, {"X": 0.1015956923365593, "Y": 0.27699029445648193}]}, "Id": "500e60b4-fe6d-4849-98d2-7896577e908b", "Relationships": [{"Type": "CHILD", "Ids": ["a5507fc1-8ba9-4c80-ab75-2a971253f1ba", "7958d615-3450-4134-b487-eee21b45f2e9"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Price", "Geometry": {"BoundingBox": {"Width": 0.08646196126937866, "Height": 0.014408393763005733, "Left": 0.5451186895370483, "Top": 0.2647949159145355}, "Polygon": [{"X": 0.5451186895370483, "Y": 0.2647949159145355}, {"X": 0.6315805912017822, "Y": 0.26480135321617126}, {"X": 0.631580650806427, "Y": 0.279203325510025}, {"X": 0.5451187491416931, "Y": 0.27919691801071167}]}, "Id": "e29f9696-a938-4b8a-978e-2061dae1c135", "Relationships": [{"Type": "CHILD", "Ids": ["409ebbf0-352e-4d95-90ce-c43a51a49d62"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.08282741159200668, "Height": 0.014719282276928425, "Left": 0.7000827193260193, "Top": 0.2657468318939209}, "Polygon": [{"X": 0.7000827193260193, "Y": 0.2657468318939209}, {"X": 0.7829099893569946, "Y": 0.26575300097465515}, {"X": 0.782910168170929, "Y": 0.28046610951423645}, {"X": 0.7000828385353088, "Y": 0.2804599702358246}]}, "Id": "6096e74d-4987-4553-9a30-4227d2b4e627", "Relationships": [{"Type": "CHILD", "Ids": ["5d7d3e1d-7ff9-443e-8236-014fc05db659"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DIESEL", "Geometry": {"BoundingBox": {"Width": 0.10193616151809692, "Height": 0.014629894867539406, "Left": 0.16993656754493713, "Top": 0.29895445704460144}, "Polygon": [{"X": 0.1699366569519043, "Y": 0.29895445704460144}, {"X": 0.27187272906303406, "Y": 0.29896193742752075}, {"X": 0.2718726694583893, "Y": 0.3135843276977539}, {"X": 0.16993656754493713, "Y": 0.31357690691947937}]}, "Id": "b40b6679-2684-426f-9e04-7d15b068e88c", "Relationships": [{"Type": "CHILD", "Ids": ["dc147cc2-8b16-4707-b32c-ad17f2b3ff61"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "275.34", "Geometry": {"BoundingBox": {"Width": 0.10460227727890015, "Height": 0.014786704443395138, "Left": 0.6828750371932983, "Top": 0.3033471405506134}, "Polygon": [{"X": 0.6828750371932983, "Y": 0.3033471405506134}, {"X": 0.7874771952629089, "Y": 0.30335479974746704}, {"X": 0.7874773144721985, "Y": 0.3181338310241699}, {"X": 0.6828751564025879, "Y": 0.31812623143196106}]}, "Id": "fec1ba47-400d-48d2-a1c8-e748cd23680d", "Relationships": [{"Type": "CHILD", "Ids": ["643e5c3a-3b14-4e2a-b0de-f872c4825f73"]}]}, {"BlockType": "LINE", "Confidence": 99.55257415771484, "Text": "Pump:", "Geometry": {"BoundingBox": {"Width": 0.08048025518655777, "Height": 0.017153674736618996, "Left": 0.1895015686750412, "Top": 0.3362998366355896}, "Polygon": [{"X": 0.18950167298316956, "Y": 0.3362998366355896}, {"X": 0.26998183131217957, "Y": 0.3363056480884552}, {"X": 0.2699817717075348, "Y": 0.35345351696014404}, {"X": 0.1895015686750412, "Y": 0.35344773530960083}]}, "Id": "15818c48-c899-46ed-97dd-ffdc7477ea27", "Relationships": [{"Type": "CHILD", "Ids": ["78b08572-5ad4-4dac-bcf5-849a5d2a5d35"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "22", "Geometry": {"BoundingBox": {"Width": 0.03484457731246948, "Height": 0.013604267500340939, "Left": 0.5468358993530273, "Top": 0.34003570675849915}, "Polygon": [{"X": 0.5468358993530273, "Y": 0.34003570675849915}, {"X": 0.581680417060852, "Y": 0.3400382101535797}, {"X": 0.5816804766654968, "Y": 0.35363999009132385}, {"X": 0.5468359589576721, "Y": 0.3536374866962433}]}, "Id": "53f16fe2-b13f-4433-9069-3aaed621c8ad", "Relationships": [{"Type": "CHILD", "Ids": ["3c6c37dd-9b0a-452e-961d-b3f3ae3c7970"]}]}, {"BlockType": "LINE", "Confidence": 99.28392028808594, "Text": "Gallons:", "Geometry": {"BoundingBox": {"Width": 0.1312919706106186, "Height": 0.01502924133092165, "Left": 0.19010253250598907, "Top": 0.3549482822418213}, "Polygon": [{"X": 0.19010262191295624, "Y": 0.3549482822418213}, {"X": 0.32139450311660767, "Y": 0.3549576699733734}, {"X": 0.3213944733142853, "Y": 0.36997750401496887}, {"X": 0.19010253250598907, "Y": 0.3699682056903839}]}, "Id": "118c059c-0ab0-462b-959e-b773886660ca", "Relationships": [{"Type": "CHILD", "Ids": ["66f59758-da94-467d-917e-db03f4327d76"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "76.719", "Geometry": {"BoundingBox": {"Width": 0.10397351533174515, "Height": 0.01504726242274046, "Left": 0.5469093918800354, "Top": 0.3580980598926544}, "Polygon": [{"X": 0.5469093918800354, "Y": 0.3580980598926544}, {"X": 0.6508827805519104, "Y": 0.35810548067092896}, {"X": 0.6508828997612, "Y": 0.37314531207084656}, {"X": 0.5469094514846802, "Y": 0.3731379508972168}]}, "Id": "4256b6b3-ebc1-4039-a889-bf71f1be5c60", "Relationships": [{"Type": "CHILD", "Ids": ["40e68e4e-cdc5-4099-8b8d-01d8f978840f"]}]}, {"BlockType": "LINE", "Confidence": 99.4407730102539, "Text": "Price / Gal:", "Geometry": {"BoundingBox": {"Width": 0.1986866295337677, "Height": 0.01590246893465519, "Left": 0.19102951884269714, "Top": 0.37341877818107605}, "Polygon": [{"X": 0.1910296231508255, "Y": 0.37341877818107605}, {"X": 0.38971614837646484, "Y": 0.37343287467956543}, {"X": 0.38971614837646484, "Y": 0.3893212676048279}, {"X": 0.19102951884269714, "Y": 0.38930729031562805}]}, "Id": "be522b78-0d88-42e9-a62e-81920711577d", "Relationships": [{"Type": "CHILD", "Ids": ["3b042415-1ff8-41ec-b3dc-2457d8141bed", "e7918970-310d-4c41-849d-882d88908a92", "a21314e0-1687-40e9-93f6-fd03b91f10f6"]}]}, {"BlockType": "LINE", "Confidence": 99.71121215820312, "Text": "3.589", "Geometry": {"BoundingBox": {"Width": 0.08605967462062836, "Height": 0.014553891494870186, "Left": 0.5651865005493164, "Top": 0.3771696090698242}, "Polygon": [{"X": 0.5651865005493164, "Y": 0.3771696090698242}, {"X": 0.6512460112571716, "Y": 0.3771757185459137}, {"X": 0.6512461304664612, "Y": 0.39172351360321045}, {"X": 0.5651865601539612, "Y": 0.39171746373176575}]}, "Id": "b4edbcfd-80de-4bc8-af3d-529c5d51b340", "Relationships": [{"Type": "CHILD", "Ids": ["2f3f5f9d-5686-4e5e-93d0-73f783dc6fe9"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "Subtotal", "Geometry": {"BoundingBox": {"Width": 0.1317180097103119, "Height": 0.015163938514888287, "Left": 0.107841856777668, "Top": 0.4096653461456299}, "Polygon": [{"X": 0.10784199088811874, "Y": 0.4096653461456299}, {"X": 0.23955987393856049, "Y": 0.4096745252609253}, {"X": 0.23955979943275452, "Y": 0.42482930421829224}, {"X": 0.107841856777668, "Y": 0.4248201847076416}]}, "Id": "e80d5cc3-8630-4513-9666-0fa8fe1b8ee5", "Relationships": [{"Type": "CHILD", "Ids": ["55403495-7201-4caa-857b-16eb45ac76e7"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "275.34", "Geometry": {"BoundingBox": {"Width": 0.1040220856666565, "Height": 0.015680143609642982, "Left": 0.6855462789535522, "Top": 0.41555356979370117}, "Polygon": [{"X": 0.6855462789535522, "Y": 0.41555356979370117}, {"X": 0.7895681858062744, "Y": 0.4155608117580414}, {"X": 0.7895683646202087, "Y": 0.4312337040901184}, {"X": 0.6855463981628418, "Y": 0.43122655153274536}]}, "Id": "6dc6b84b-cd8e-4ca9-ae28-29dd01469fc6", "Relationships": [{"Type": "CHILD", "Ids": ["603aa6d2-a1af-406c-b8e2-30bbf1a3f09e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Sales Tax", "Geometry": {"BoundingBox": {"Width": 0.1532975286245346, "Height": 0.014982376247644424, "Left": 0.10798884928226471, "Top": 0.42805537581443787}, "Polygon": [{"X": 0.10798897594213486, "Y": 0.42805537581443787}, {"X": 0.2612863779067993, "Y": 0.4280659556388855}, {"X": 0.26128631830215454, "Y": 0.443037748336792}, {"X": 0.10798884928226471, "Y": 0.44302722811698914}]}, "Id": "c05c7446-4466-4416-ad74-45891de61bed", "Relationships": [{"Type": "CHILD", "Ids": ["23bebbd5-7e8a-4ea0-a414-e9cc106111c5", "9f3821d2-d4aa-4ead-98b6-8f85c50eedaa"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "0.00", "Geometry": {"BoundingBox": {"Width": 0.0698743462562561, "Height": 0.014984441921114922, "Left": 0.7199559211730957, "Top": 0.43451201915740967}, "Polygon": [{"X": 0.7199559211730957, "Y": 0.43451201915740967}, {"X": 0.7898300886154175, "Y": 0.4345168173313141}, {"X": 0.7898302674293518, "Y": 0.44949644804000854}, {"X": 0.71995609998703, "Y": 0.4494916796684265}]}, "Id": "c1f1d030-df4d-454d-b05d-34f84282b7af", "Relationships": [{"Type": "CHILD", "Ids": ["1ed91f43-e276-4e8e-8035-ea02b5af6955"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.08097734302282333, "Height": 0.014394180849194527, "Left": 0.10833615064620972, "Top": 0.44626858830451965}, "Polygon": [{"X": 0.10833627730607986, "Y": 0.44626858830451965}, {"X": 0.18931350111961365, "Y": 0.44627413153648376}, {"X": 0.18931341171264648, "Y": 0.46066275238990784}, {"X": 0.10833615064620972, "Y": 0.4606572687625885}]}, "Id": "3d2e3924-b41e-493e-a38f-a25af128037f", "Relationships": [{"Type": "CHILD", "Ids": ["e8a4add5-0158-4096-b29b-a0a4601d8a55"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "275.34", "Geometry": {"BoundingBox": {"Width": 0.10385239869356155, "Height": 0.015680035576224327, "Left": 0.6856388449668884, "Top": 0.4525975286960602}, "Polygon": [{"X": 0.6856388449668884, "Y": 0.4525975286960602}, {"X": 0.7894910573959351, "Y": 0.45260462164878845}, {"X": 0.7894912362098694, "Y": 0.46827757358551025}, {"X": 0.685638964176178, "Y": 0.46827054023742676}]}, "Id": "b495f47e-ef1a-40c8-9874-ebf21d975e2d", "Relationships": [{"Type": "CHILD", "Ids": ["9ba00a5d-4054-44cf-a659-dfb15828ff39"]}]}, {"BlockType": "LINE", "Confidence": 99.71600341796875, "Text": "Received:", "Geometry": {"BoundingBox": {"Width": 0.14861400425434113, "Height": 0.014920156449079514, "Left": 0.10910642147064209, "Top": 0.48336201906204224}, "Polygon": [{"X": 0.10910654813051224, "Y": 0.48336201906204224}, {"X": 0.2577204406261444, "Y": 0.4833720028400421}, {"X": 0.25772035121917725, "Y": 0.49828216433525085}, {"X": 0.10910642147064209, "Y": 0.49827224016189575}]}, "Id": "c68ba78f-b91c-4bd4-82f2-c694fd3381ec", "Relationships": [{"Type": "CHILD", "Ids": ["610aac3a-8922-4d81-b05a-7a215fbaa7ca"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "FLEETONE", "Geometry": {"BoundingBox": {"Width": 0.13503512740135193, "Height": 0.01493186503648758, "Left": 0.14425444602966309, "Top": 0.5021043419837952}, "Polygon": [{"X": 0.14425456523895264, "Y": 0.5021043419837952}, {"X": 0.279289573431015, "Y": 0.5021133422851562}, {"X": 0.27928951382637024, "Y": 0.5170361995697021}, {"X": 0.14425444602966309, "Y": 0.5170272588729858}]}, "Id": "fbab5cfb-ab4f-4efd-be44-7868e698c23d", "Relationships": [{"Type": "CHILD", "Ids": ["d90798df-65f1-4ff7-b1ab-d38fd83eae2b"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "275.34", "Geometry": {"BoundingBox": {"Width": 0.1039033755660057, "Height": 0.015935976058244705, "Left": 0.686782956123352, "Top": 0.5086930990219116}, "Polygon": [{"X": 0.686782956123352, "Y": 0.5086930990219116}, {"X": 0.7906861305236816, "Y": 0.5087000131607056}, {"X": 0.790686309337616, "Y": 0.5246291160583496}, {"X": 0.6867830753326416, "Y": 0.5246222615242004}]}, "Id": "7802d009-0986-4f0a-930a-49b7244b3fc6", "Relationships": [{"Type": "CHILD", "Ids": ["d738c7fb-edf3-4287-9b64-8bfe29ee8154"]}]}, {"BlockType": "LINE", "Confidence": 99.0380859375, "Text": "0180 SWIPED", "Geometry": {"BoundingBox": {"Width": 0.20365554094314575, "Height": 0.015699386596679688, "Left": 0.39950284361839294, "Top": 0.5233446359634399}, "Polygon": [{"X": 0.39950284361839294, "Y": 0.5233446359634399}, {"X": 0.6031582951545715, "Y": 0.5233580470085144}, {"X": 0.6031583547592163, "Y": 0.5390440225601196}, {"X": 0.39950284361839294, "Y": 0.5390307307243347}]}, "Id": "b4ad5208-4b12-44cc-a4b3-742da6483bb9", "Relationships": [{"Type": "CHILD", "Ids": ["9c7524e1-94f4-4aa7-b6ad-d4857d0dbbab", "da7b63c8-559f-4149-b58d-08dbffc22372"]}]}, {"BlockType": "LINE", "Confidence": 99.9571533203125, "Text": "Auth No: 116932", "Geometry": {"BoundingBox": {"Width": 0.25553271174430847, "Height": 0.016415990889072418, "Left": 0.17815275490283966, "Top": 0.5390648245811462}, "Polygon": [{"X": 0.17815285921096802, "Y": 0.5390648245811462}, {"X": 0.43368545174598694, "Y": 0.5390815138816833}, {"X": 0.43368545174598694, "Y": 0.5554808378219604}, {"X": 0.17815275490283966, "Y": 0.5554642677307129}]}, "Id": "b5d0a570-c20c-4bd0-9938-f2be4cd5615c", "Relationships": [{"Type": "CHILD", "Ids": ["a7ad42c6-1ccb-4fa9-af97-05ecd9870efc", "f84cc2c8-7654-46f7-b668-480b00e9efba", "9ad5289d-c920-480d-9597-30d8a548112b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "INVOICE# 14022", "Geometry": {"BoundingBox": {"Width": 0.23612883687019348, "Height": 0.015777889639139175, "Left": 0.14676323533058167, "Top": 0.5573989748954773}, "Polygon": [{"X": 0.14676335453987122, "Y": 0.5573989748954773}, {"X": 0.38289207220077515, "Y": 0.5574142336845398}, {"X": 0.38289207220077515, "Y": 0.5731768608093262}, {"X": 0.14676323533058167, "Y": 0.5731616616249084}]}, "Id": "48ff500a-45f7-4c7b-9548-02721ede10f8", "Relationships": [{"Type": "CHILD", "Ids": ["e03a64ab-0c83-445b-aca1-8ececb99125c", "d18352a2-4574-46f8-8b1a-83ac9e0cb5e8"]}]}, {"BlockType": "LINE", "Confidence": 99.61276245117188, "Text": "Signature:", "Geometry": {"BoundingBox": {"Width": 0.1679600477218628, "Height": 0.017386339604854584, "Left": 0.10678879916667938, "Top": 0.8216462135314941}, "Polygon": [{"X": 0.10678894817829132, "Y": 0.8216462135314941}, {"X": 0.274748831987381, "Y": 0.8216555714607239}, {"X": 0.2747487723827362, "Y": 0.8390325307846069}, {"X": 0.10678879916667938, "Y": 0.8390232920646667}]}, "Id": "779c8ccc-2a3b-4abe-a3c2-0e9be69a3e20", "Relationships": [{"Type": "CHILD", "Ids": ["c117d301-1660-4202-b8e2-e9c4ca11ac26"]}]}, {"BlockType": "LINE", "Confidence": 99.765625, "Text": "TripNumber", "Geometry": {"BoundingBox": {"Width": 0.1721016764640808, "Height": 0.01781240664422512, "Left": 0.10609334707260132, "Top": 0.8825020790100098}, "Polygon": [{"X": 0.10609350353479385, "Y": 0.8825020790100098}, {"X": 0.27819502353668213, "Y": 0.88251131772995}, {"X": 0.27819496393203735, "Y": 0.900314450263977}, {"X": 0.10609334707260132, "Y": 0.9003053307533264}]}, "Id": "5da3b1c8-b30c-4acb-a43b-30878ab46cdc", "Relationships": [{"Type": "CHILD", "Ids": ["d7c50cdb-92e3-4299-884d-6bc460f8ab24"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "6063", "Geometry": {"BoundingBox": {"Width": 0.06932178884744644, "Height": 0.015749726444482803, "Left": 0.7314403057098389, "Top": 0.883460521697998}, "Polygon": [{"X": 0.7314403057098389, "Y": 0.883460521697998}, {"X": 0.8007619380950928, "Y": 0.8834642767906189}, {"X": 0.8007621169090271, "Y": 0.8992102742195129}, {"X": 0.7314404845237732, "Y": 0.8992065787315369}]}, "Id": "599a2440-e8b0-4b4f-9248-6ae5bcc43a96", "Relationships": [{"Type": "CHILD", "Ids": ["4e403971-257f-403a-8529-75e05e244aca"]}]}, {"BlockType": "LINE", "Confidence": 99.4541244506836, "Text": "VehicleID", "Geometry": {"BoundingBox": {"Width": 0.1604834347963333, "Height": 0.016407852992415428, "Left": 0.10125917941331863, "Top": 0.9034998416900635}, "Polygon": [{"X": 0.10125932097434998, "Y": 0.9034998416900635}, {"X": 0.26174262166023254, "Y": 0.9035083651542664}, {"X": 0.2617425322532654, "Y": 0.9199076890945435}, {"X": 0.10125917941331863, "Y": 0.9198992848396301}]}, "Id": "a1309950-8bb0-4723-8d03-864f7e8c7157", "Relationships": [{"Type": "CHILD", "Ids": ["3f8dbc60-7749-4502-83bd-8bd4640c0c2f"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "6063", "Geometry": {"BoundingBox": {"Width": 0.0695929154753685, "Height": 0.016113631427288055, "Left": 0.7331275939941406, "Top": 0.9042515158653259}, "Polygon": [{"X": 0.7331275939941406, "Y": 0.9042515158653259}, {"X": 0.802720308303833, "Y": 0.904255211353302}, {"X": 0.8027205467224121, "Y": 0.9203651547431946}, {"X": 0.733127772808075, "Y": 0.9203614592552185}]}, "Id": "fa7fe8cd-83ae-4c08-9444-ebac859460ac", "Relationships": [{"Type": "CHILD", "Ids": ["c0084586-40be-4d25-87e0-ecd3b6efef5d"]}]}, {"BlockType": "LINE", "Confidence": 65.5261459350586, "Text": "HubOdometer", "Geometry": {"BoundingBox": {"Width": 0.1941739320755005, "Height": 0.016948474571108818, "Left": 0.10093144327402115, "Top": 0.9246818423271179}, "Polygon": [{"X": 0.10093159228563309, "Y": 0.9246818423271179}, {"X": 0.29510536789894104, "Y": 0.9246920347213745}, {"X": 0.29510530829429626, "Y": 0.9416303038597107}, {"X": 0.10093144327402115, "Y": 0.9416202902793884}]}, "Id": "dea7f1f3-58c5-4d22-9fb9-dda556cee23c", "Relationships": [{"Type": "CHILD", "Ids": ["a7d8ab51-cfec-4c12-a5c7-e70f2a91c492"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "21400", "Geometry": {"BoundingBox": {"Width": 0.08848832547664642, "Height": 0.016809213906526566, "Left": 0.7172682881355286, "Top": 0.9254225492477417}, "Polygon": [{"X": 0.7172682881355286, "Y": 0.9254225492477417}, {"X": 0.8057563900947571, "Y": 0.9254271984100342}, {"X": 0.8057566285133362, "Y": 0.9422317743301392}, {"X": 0.7172684073448181, "Y": 0.9422271847724915}]}, "Id": "7b954834-529f-40e4-b89d-4111e68a6cb1", "Relationships": [{"Type": "CHILD", "Ids": ["f8e03249-cc8f-4eca-bc61-c3537e786595"]}]}, {"BlockType": "LINE", "Confidence": 99.0479507446289, "Text": "TruckingCompanyName RILUX TRANSPORTATION", "Geometry": {"BoundingBox": {"Width": 0.7087636590003967, "Height": 0.019562946632504463, "Left": 0.10143045336008072, "Top": 0.9459205865859985}, "Polygon": [{"X": 0.10143062472343445, "Y": 0.9459205865859985}, {"X": 0.8101938962936401, "Y": 0.9459571838378906}, {"X": 0.8101941347122192, "Y": 0.965483546257019}, {"X": 0.10143045336008072, "Y": 0.9654474258422852}]}, "Id": "f4b84f0d-fb0e-49ab-a088-1c4e0c0d58a6", "Relationships": [{"Type": "CHILD", "Ids": ["a8bafab5-5476-430b-b036-cf691d6ba010", "b31d17a1-8937-4a42-8d07-b51f105b1c0d", "d82859ca-14db-4a32-93ed-911f421044cd"]}]}, {"BlockType": "WORD", "Confidence": 51.820194244384766, "Text": "Loves.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.49028944969177246, "Height": 0.06023992598056793, "Left": 0.3811255395412445, "Top": 0.020498966798186302}, "Polygon": [{"X": 0.3811255991458893, "Y": 0.020498966798186302}, {"X": 0.8714141845703125, "Y": 0.02053953893482685}, {"X": 0.*********0353394, "Y": 0.08073889464139938}, {"X": 0.3811255395412445, "Y": 0.08069930970668793}]}, "Id": "f2a562fb-c688-4bf2-b09d-d964f50c7bce"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Store", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08535871654748917, "Height": 0.013850255869328976, "Left": 0.4229850172996521, "Top": 0.09585750102996826}, "Polygon": [{"X": 0.4229850172996521, "Y": 0.09585750102996826}, {"X": 0.5083436965942383, "Y": 0.09586434811353683}, {"X": 0.5083436965942383, "Y": 0.10970775783061981}, {"X": 0.4229850172996521, "Y": 0.10970094799995422}]}, "Id": "929215f1-d807-41f7-94d4-f5f5fd30ee38"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "#878", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06853354722261429, "Height": 0.013949417509138584, "Left": 0.5259949564933777, "Top": 0.09616531431674957}, "Polygon": [{"X": 0.5259949564933777, "Y": 0.09616531431674957}, {"X": 0.5945283770561218, "Y": 0.0961708128452301}, {"X": 0.5945284962654114, "Y": 0.11011473089456558}, {"X": 0.5259950160980225, "Y": 0.11010926961898804}]}, "Id": "88a9c6b7-bf8c-494e-b84e-f2c0bbb09ae6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "7900", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06834106892347336, "Height": 0.013972406275570393, "Left": 0.37171629071235657, "Top": 0.11410948634147644}, "Polygon": [{"X": 0.37171629071235657, "Y": 0.11410948634147644}, {"X": 0.44005733728408813, "Y": 0.1141149252653122}, {"X": 0.44005733728408813, "Y": 0.12808188796043396}, {"X": 0.37171629071235657, "Y": 0.1280764788389206}]}, "Id": "19ad2527-502b-4cf2-8016-4614ff5abf4b"}, {"BlockType": "WORD", "Confidence": 99.40210723876953, "Text": "Adobe", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08565154671669006, "Height": 0.013716679997742176, "Left": 0.4574090838432312, "Top": 0.11469941586256027}, "Polygon": [{"X": 0.4574090838432312, "Y": 0.11469941586256027}, {"X": 0.5430606007575989, "Y": 0.11470623314380646}, {"X": 0.5430606007575989, "Y": 0.12841609120368958}, {"X": 0.4574091136455536, "Y": 0.12840931117534637}]}, "Id": "4f9de1bc-6693-47d4-acb8-4ec67619169a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Road", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06898421794176102, "Height": 0.013919379562139511, "Left": 0.5600411891937256, "Top": 0.11490732431411743}, "Polygon": [{"X": 0.5600411891937256, "Y": 0.11490732431411743}, {"X": 0.6290252804756165, "Y": 0.11491281539201736}, {"X": 0.629025399684906, "Y": 0.12882670760154724}, {"X": 0.5600412487983704, "Y": 0.1288212388753891}]}, "Id": "16b8fb9d-907e-4131-be7e-f0522a582a16"}, {"BlockType": "WORD", "Confidence": 99.67135620117188, "Text": "Alamo,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09776882082223892, "Height": 0.01637725532054901, "Left": 0.37165775895118713, "Top": 0.13229776918888092}, "Polygon": [{"X": 0.3716577887535095, "Y": 0.13229776918888092}, {"X": 0.46942657232284546, "Y": 0.13230548799037933}, {"X": 0.46942660212516785, "Y": 0.14867502450942993}, {"X": 0.37165775895118713, "Y": 0.1486673504114151}]}, "Id": "822903e7-ca0d-4e5a-abc4-8296343d39ea"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "MI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032599806785583496, "Height": 0.014237714931368828, "Left": 0.49112462997436523, "Top": 0.13270755112171173}, "Polygon": [{"X": 0.49112462997436523, "Y": 0.13270755112171173}, {"X": 0.523724377155304, "Y": 0.13271011412143707}, {"X": 0.5237244367599487, "Y": 0.1469452679157257}, {"X": 0.4911246597766876, "Y": 0.14694270491600037}]}, "Id": "bd68bd55-f990-4b0b-96f9-66b3d984b57e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "49009", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08605949580669403, "Height": 0.014030166901648045, "Left": 0.5430225729942322, "Top": 0.13303308188915253}, "Polygon": [{"X": 0.5430225729942322, "Y": 0.13303308188915253}, {"X": 0.6290819644927979, "Y": 0.13303987681865692}, {"X": 0.6290820240974426, "Y": 0.1470632404088974}, {"X": 0.543022632598877, "Y": 0.1470564901828766}]}, "Id": "23a05616-c1aa-4a24-8411-6e3eb8382606"}, {"BlockType": "WORD", "Confidence": 99.33454132080078, "Text": "(269)903-0234", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2204139232635498, "Height": 0.014820795506238937, "Left": 0.3920165002346039, "Top": 0.15107470750808716}, "Polygon": [{"X": 0.3920165002346039, "Y": 0.15107470750808716}, {"X": 0.6124303340911865, "Y": 0.15109197795391083}, {"X": 0.6124303936958313, "Y": 0.1658955067396164}, {"X": 0.3920165002346039, "Y": 0.16587834060192108}]}, "Id": "20032ebc-dc3c-4eea-b90b-b097402412b8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "02/18/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16938167810440063, "Height": 0.014462060295045376, "Left": 0.10236269235610962, "Top": 0.18702295422554016}, "Polygon": [{"X": 0.10236281901597977, "Y": 0.18702295422554016}, {"X": 0.27174437046051025, "Y": 0.18703602254390717}, {"X": 0.2717443108558655, "Y": 0.2014850229024887}, {"X": 0.10236269235610962, "Y": 0.20147202908992767}]}, "Id": "e3f9c9cf-8e19-468c-8b8e-44afa35b1f96"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Tkt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05123654752969742, "Height": 0.014401317574083805, "Left": 0.28784459829330444, "Top": 0.18771490454673767}, "Polygon": [{"X": 0.2878446578979492, "Y": 0.18771490454673767}, {"X": 0.33908116817474365, "Y": 0.18771885335445404}, {"X": 0.33908113837242126, "Y": 0.2021162211894989}, {"X": 0.28784459829330444, "Y": 0.20211228728294373}]}, "Id": "66280a31-0ab6-4c1d-9966-4e1fffe8e43a"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "#1247187", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1364755481481552, "Height": 0.014451817609369755, "Left": 0.35586029291152954, "Top": 0.18845173716545105}, "Polygon": [{"X": 0.35586029291152954, "Y": 0.18845173716545105}, {"X": 0.49233579635620117, "Y": 0.1884622573852539}, {"X": 0.49233582615852356, "Y": 0.20290355384349823}, {"X": 0.35586029291152954, "Y": 0.20289309322834015}]}, "Id": "82cce227-20ac-4ddc-a3d9-116c5beff348"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Type:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07936343550682068, "Height": 0.016344042494893074, "Left": 0.10122762620449066, "Top": 0.22403831779956818}, "Polygon": [{"X": 0.101227767765522, "Y": 0.22403831779956818}, {"X": 0.18059106171131134, "Y": 0.2240443378686905}, {"X": 0.18059095740318298, "Y": 0.2403823584318161}, {"X": 0.10122762620449066, "Y": 0.24037636816501617}]}, "Id": "3de6c353-371a-4812-9e5f-919c664df104"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SALE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06730260699987411, "Height": 0.013902727514505386, "Left": 0.20300915837287903, "Top": 0.22491002082824707}, "Polygon": [{"X": 0.203009232878685, "Y": 0.22491002082824707}, {"X": 0.27031174302101135, "Y": 0.22491513192653656}, {"X": 0.27031171321868896, "Y": 0.23881274461746216}, {"X": 0.20300915837287903, "Y": 0.23880767822265625}]}, "Id": "fe6c2ac0-f317-434b-83af-f0012c44f6c0"}, {"BlockType": "WORD", "Confidence": 98.31612396240234, "Text": "(ORIGINAL)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1661902368068695, "Height": 0.015194191597402096, "Left": 0.39319106936454773, "Top": 0.22606484591960907}, "Polygon": [{"X": 0.39319106936454773, "Y": 0.22606484591960907}, {"X": 0.5593812465667725, "Y": 0.22607745230197906}, {"X": 0.5593813061714172, "Y": 0.24125903844833374}, {"X": 0.39319106936454773, "Y": 0.2412465214729309}]}, "Id": "e39eb9be-5821-45b1-b5e0-6f5c7016b312"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Qty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0507124625146389, "Height": 0.01564321294426918, "Left": 0.1015956923365593, "Top": 0.2613508403301239}, "Polygon": [{"X": 0.10159582644701004, "Y": 0.2613508403301239}, {"X": 0.1523081511259079, "Y": 0.26135462522506714}, {"X": 0.15230803191661835, "Y": 0.27699407935142517}, {"X": 0.1015956923365593, "Y": 0.27699029445648193}]}, "Id": "a5507fc1-8ba9-4c80-ab75-2a971253f1ba"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Name", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06797534227371216, "Height": 0.014333085156977177, "Left": 0.16960600018501282, "Top": 0.2616651952266693}, "Polygon": [{"X": 0.16960608959197998, "Y": 0.2616651952266693}, {"X": 0.23758134245872498, "Y": 0.2616702616214752}, {"X": 0.2375812828540802, "Y": 0.2759982943534851}, {"X": 0.16960600018501282, "Y": 0.2759932279586792}]}, "Id": "7958d615-3450-4134-b487-eee21b45f2e9"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08646196126937866, "Height": 0.014408393763005733, "Left": 0.5451186895370483, "Top": 0.2647949159145355}, "Polygon": [{"X": 0.5451186895370483, "Y": 0.2647949159145355}, {"X": 0.6315805912017822, "Y": 0.26480135321617126}, {"X": 0.631580650806427, "Y": 0.279203325510025}, {"X": 0.5451187491416931, "Y": 0.27919691801071167}]}, "Id": "409ebbf0-352e-4d95-90ce-c43a51a49d62"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08282741159200668, "Height": 0.014719282276928425, "Left": 0.7000827193260193, "Top": 0.2657468318939209}, "Polygon": [{"X": 0.7000827193260193, "Y": 0.2657468318939209}, {"X": 0.7829099893569946, "Y": 0.26575300097465515}, {"X": 0.782910168170929, "Y": 0.28046610951423645}, {"X": 0.7000828385353088, "Y": 0.2804599702358246}]}, "Id": "5d7d3e1d-7ff9-443e-8236-014fc05db659"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DIESEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10193616151809692, "Height": 0.014629894867539406, "Left": 0.16993656754493713, "Top": 0.29895445704460144}, "Polygon": [{"X": 0.1699366569519043, "Y": 0.29895445704460144}, {"X": 0.27187272906303406, "Y": 0.29896193742752075}, {"X": 0.2718726694583893, "Y": 0.3135843276977539}, {"X": 0.16993656754493713, "Y": 0.31357690691947937}]}, "Id": "dc147cc2-8b16-4707-b32c-ad17f2b3ff61"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "275.34", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10460227727890015, "Height": 0.014786704443395138, "Left": 0.6828750371932983, "Top": 0.3033471405506134}, "Polygon": [{"X": 0.6828750371932983, "Y": 0.3033471405506134}, {"X": 0.7874771952629089, "Y": 0.30335479974746704}, {"X": 0.7874773144721985, "Y": 0.3181338310241699}, {"X": 0.6828751564025879, "Y": 0.31812623143196106}]}, "Id": "643e5c3a-3b14-4e2a-b0de-f872c4825f73"}, {"BlockType": "WORD", "Confidence": 99.55257415771484, "Text": "Pump:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08048025518655777, "Height": 0.017153674736618996, "Left": 0.1895015686750412, "Top": 0.3362998366355896}, "Polygon": [{"X": 0.18950167298316956, "Y": 0.3362998366355896}, {"X": 0.26998183131217957, "Y": 0.3363056480884552}, {"X": 0.2699817717075348, "Y": 0.35345351696014404}, {"X": 0.1895015686750412, "Y": 0.35344773530960083}]}, "Id": "78b08572-5ad4-4dac-bcf5-849a5d2a5d35"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03484457731246948, "Height": 0.013604267500340939, "Left": 0.5468358993530273, "Top": 0.34003570675849915}, "Polygon": [{"X": 0.5468358993530273, "Y": 0.34003570675849915}, {"X": 0.581680417060852, "Y": 0.3400382101535797}, {"X": 0.5816804766654968, "Y": 0.35363999009132385}, {"X": 0.5468359589576721, "Y": 0.3536374866962433}]}, "Id": "3c6c37dd-9b0a-452e-961d-b3f3ae3c7970"}, {"BlockType": "WORD", "Confidence": 99.28392028808594, "Text": "Gallons:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1312919706106186, "Height": 0.01502924133092165, "Left": 0.19010253250598907, "Top": 0.3549482822418213}, "Polygon": [{"X": 0.19010262191295624, "Y": 0.3549482822418213}, {"X": 0.32139450311660767, "Y": 0.3549576699733734}, {"X": 0.3213944733142853, "Y": 0.36997750401496887}, {"X": 0.19010253250598907, "Y": 0.3699682056903839}]}, "Id": "66f59758-da94-467d-917e-db03f4327d76"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "76.719", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10397351533174515, "Height": 0.01504726242274046, "Left": 0.5469093918800354, "Top": 0.3580980598926544}, "Polygon": [{"X": 0.5469093918800354, "Y": 0.3580980598926544}, {"X": 0.6508827805519104, "Y": 0.35810548067092896}, {"X": 0.6508828997612, "Y": 0.37314531207084656}, {"X": 0.5469094514846802, "Y": 0.3731379508972168}]}, "Id": "40e68e4e-cdc5-4099-8b8d-01d8f978840f"}, {"BlockType": "WORD", "Confidence": 99.72576141357422, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08452419191598892, "Height": 0.014588574878871441, "Left": 0.19102953374385834, "Top": 0.37341877818107605}, "Polygon": [{"X": 0.1910296231508255, "Y": 0.37341877818107605}, {"X": 0.27555373311042786, "Y": 0.373424768447876}, {"X": 0.2755536735057831, "Y": 0.38800737261772156}, {"X": 0.19102953374385834, "Y": 0.388001412153244}]}, "Id": "3b042415-1ff8-41ec-b3dc-2457d8141bed"}, {"BlockType": "WORD", "Confidence": 99.36124420166016, "Text": "/", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01586545817553997, "Height": 0.013689413666725159, "Left": 0.2931782603263855, "Top": 0.3743875026702881}, "Polygon": [{"X": 0.2931783199310303, "Y": 0.3743875026702881}, {"X": 0.3090437352657318, "Y": 0.3743886351585388}, {"X": 0.30904367566108704, "Y": 0.38807693123817444}, {"X": 0.2931782603263855, "Y": 0.3880757987499237}]}, "Id": "e7918970-310d-4c41-849d-882d88908a92"}, {"BlockType": "WORD", "Confidence": 99.23529052734375, "Text": "Gal:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06297680735588074, "Height": 0.01467137411236763, "Left": 0.3267393410205841, "Top": 0.37464988231658936}, "Polygon": [{"X": 0.3267393708229065, "Y": 0.37464988231658936}, {"X": 0.38971614837646484, "Y": 0.3746543526649475}, {"X": 0.38971614837646484, "Y": 0.3893212676048279}, {"X": 0.3267393410205841, "Y": 0.3893168270587921}]}, "Id": "a21314e0-1687-40e9-93f6-fd03b91f10f6"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "3.589", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08605967462062836, "Height": 0.014553891494870186, "Left": 0.5651865005493164, "Top": 0.3771696090698242}, "Polygon": [{"X": 0.5651865005493164, "Y": 0.3771696090698242}, {"X": 0.6512460112571716, "Y": 0.3771757185459137}, {"X": 0.6512461304664612, "Y": 0.39172351360321045}, {"X": 0.5651865601539612, "Y": 0.39171746373176575}]}, "Id": "2f3f5f9d-5686-4e5e-93d0-73f783dc6fe9"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Subtotal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1317180097103119, "Height": 0.015163938514888287, "Left": 0.107841856777668, "Top": 0.4096653461456299}, "Polygon": [{"X": 0.10784199088811874, "Y": 0.4096653461456299}, {"X": 0.23955987393856049, "Y": 0.4096745252609253}, {"X": 0.23955979943275452, "Y": 0.42482930421829224}, {"X": 0.107841856777668, "Y": 0.4248201847076416}]}, "Id": "55403495-7201-4caa-857b-16eb45ac76e7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "275.34", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1040220856666565, "Height": 0.015680143609642982, "Left": 0.6855462789535522, "Top": 0.41555356979370117}, "Polygon": [{"X": 0.6855462789535522, "Y": 0.41555356979370117}, {"X": 0.7895681858062744, "Y": 0.4155608117580414}, {"X": 0.7895683646202087, "Y": 0.4312337040901184}, {"X": 0.6855463981628418, "Y": 0.43122655153274536}]}, "Id": "603aa6d2-a1af-406c-b8e2-30bbf1a3f09e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Sales", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08494287729263306, "Height": 0.01430200133472681, "Left": 0.10798884928226471, "Top": 0.42805537581443787}, "Polygon": [{"X": 0.10798897594213486, "Y": 0.42805537581443787}, {"X": 0.19293172657489777, "Y": 0.42806121706962585}, {"X": 0.1929316371679306, "Y": 0.4423573613166809}, {"X": 0.10798884928226471, "Y": 0.4423515498638153}]}, "Id": "23bebbd5-7e8a-4ea0-a414-e9cc106111c5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05066678673028946, "Height": 0.014149637892842293, "Left": 0.21061958372592926, "Top": 0.42888811230659485}, "Polygon": [{"X": 0.21061965823173523, "Y": 0.42888811230659485}, {"X": 0.2612863779067993, "Y": 0.4288915991783142}, {"X": 0.26128631830215454, "Y": 0.443037748336792}, {"X": 0.21061958372592926, "Y": 0.44303426146507263}]}, "Id": "9f3821d2-d4aa-4ead-98b6-8f85c50eedaa"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "0.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0698743462562561, "Height": 0.014984441921114922, "Left": 0.7199559211730957, "Top": 0.43451201915740967}, "Polygon": [{"X": 0.7199559211730957, "Y": 0.43451201915740967}, {"X": 0.7898300886154175, "Y": 0.4345168173313141}, {"X": 0.7898302674293518, "Y": 0.44949644804000854}, {"X": 0.71995609998703, "Y": 0.4494916796684265}]}, "Id": "1ed91f43-e276-4e8e-8035-ea02b5af6955"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08097734302282333, "Height": 0.014394180849194527, "Left": 0.10833615064620972, "Top": 0.44626858830451965}, "Polygon": [{"X": 0.10833627730607986, "Y": 0.44626858830451965}, {"X": 0.18931350111961365, "Y": 0.44627413153648376}, {"X": 0.18931341171264648, "Y": 0.46066275238990784}, {"X": 0.10833615064620972, "Y": 0.4606572687625885}]}, "Id": "e8a4add5-0158-4096-b29b-a0a4601d8a55"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "275.34", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10385239869356155, "Height": 0.015680035576224327, "Left": 0.6856388449668884, "Top": 0.4525975286960602}, "Polygon": [{"X": 0.6856388449668884, "Y": 0.4525975286960602}, {"X": 0.7894910573959351, "Y": 0.45260462164878845}, {"X": 0.7894912362098694, "Y": 0.46827757358551025}, {"X": 0.685638964176178, "Y": 0.46827054023742676}]}, "Id": "9ba00a5d-4054-44cf-a659-dfb15828ff39"}, {"BlockType": "WORD", "Confidence": 99.71600341796875, "Text": "Received:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14861400425434113, "Height": 0.014920156449079514, "Left": 0.10910642147064209, "Top": 0.48336201906204224}, "Polygon": [{"X": 0.10910654813051224, "Y": 0.48336201906204224}, {"X": 0.2577204406261444, "Y": 0.4833720028400421}, {"X": 0.25772035121917725, "Y": 0.49828216433525085}, {"X": 0.10910642147064209, "Y": 0.49827224016189575}]}, "Id": "610aac3a-8922-4d81-b05a-7a215fbaa7ca"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "FLEETONE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13503512740135193, "Height": 0.01493186503648758, "Left": 0.14425444602966309, "Top": 0.5021043419837952}, "Polygon": [{"X": 0.14425456523895264, "Y": 0.5021043419837952}, {"X": 0.279289573431015, "Y": 0.5021133422851562}, {"X": 0.27928951382637024, "Y": 0.5170361995697021}, {"X": 0.14425444602966309, "Y": 0.5170272588729858}]}, "Id": "d90798df-65f1-4ff7-b1ab-d38fd83eae2b"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "275.34", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1039033755660057, "Height": 0.015935976058244705, "Left": 0.686782956123352, "Top": 0.5086930990219116}, "Polygon": [{"X": 0.686782956123352, "Y": 0.5086930990219116}, {"X": 0.7906861305236816, "Y": 0.5087000131607056}, {"X": 0.790686309337616, "Y": 0.5246291160583496}, {"X": 0.6867830753326416, "Y": 0.5246222615242004}]}, "Id": "d738c7fb-edf3-4287-9b64-8bfe29ee8154"}, {"BlockType": "WORD", "Confidence": 98.076171875, "Text": "0180", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06751014292240143, "Height": 0.013702590949833393, "Left": 0.39950284361839294, "Top": 0.5233446359634399}, "Polygon": [{"X": 0.39950284361839294, "Y": 0.5233446359634399}, {"X": 0.4670129418373108, "Y": 0.5233490467071533}, {"X": 0.4670129716396332, "Y": 0.5370472073554993}, {"X": 0.39950284361839294, "Y": 0.5370427966117859}]}, "Id": "9c7524e1-94f4-4aa7-b6ad-d4857d0dbbab"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SWIPED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1023295521736145, "Height": 0.014563599601387978, "Left": 0.5008288025856018, "Top": 0.524480402469635}, "Polygon": [{"X": 0.5008288025856018, "Y": 0.524480402469635}, {"X": 0.6031582951545715, "Y": 0.5244871377944946}, {"X": 0.6031583547592163, "Y": 0.5390440225601196}, {"X": 0.5008288621902466, "Y": 0.5390373468399048}]}, "Id": "da7b63c8-559f-4149-b58d-08dbffc22372"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0686224177479744, "Height": 0.014950529672205448, "Left": 0.17815275490283966, "Top": 0.5390648245811462}, "Polygon": [{"X": 0.17815285921096802, "Y": 0.5390648245811462}, {"X": 0.24677518010139465, "Y": 0.5390692949295044}, {"X": 0.24677510559558868, "Y": 0.5540153384208679}, {"X": 0.17815275490283966, "Y": 0.5540109276771545}]}, "Id": "a7ad42c6-1ccb-4fa9-af97-05ecd9870efc"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04595017433166504, "Height": 0.014001484960317612, "Left": 0.2630997598171234, "Top": 0.5401031970977783}, "Polygon": [{"X": 0.2630998194217682, "Y": 0.5401031970977783}, {"X": 0.30904993414878845, "Y": 0.5401061773300171}, {"X": 0.30904990434646606, "Y": 0.5541046857833862}, {"X": 0.2630997598171234, "Y": 0.5541017055511475}]}, "Id": "f84cc2c8-7654-46f7-b668-480b00e9efba"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "116932", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09915729612112045, "Height": 0.01482163555920124, "Left": 0.3345281481742859, "Top": 0.5406591892242432}, "Polygon": [{"X": 0.3345281779766083, "Y": 0.5406591892242432}, {"X": 0.43368545174598694, "Y": 0.5406656265258789}, {"X": 0.43368545174598694, "Y": 0.5554808378219604}, {"X": 0.3345281481742859, "Y": 0.5554744005203247}]}, "Id": "9ad5289d-c920-480d-9597-30d8a548112b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "INVOICE#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13406312465667725, "Height": 0.014903062954545021, "Left": 0.14676325023174286, "Top": 0.5573989748954773}, "Polygon": [{"X": 0.14676335453987122, "Y": 0.5573989748954773}, {"X": 0.2808263599872589, "Y": 0.5574076175689697}, {"X": 0.28082630038261414, "Y": 0.572301983833313}, {"X": 0.14676325023174286, "Y": 0.5722934007644653}]}, "Id": "e03a64ab-0c83-445b-aca1-8ececb99125c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "14022", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08262745290994644, "Height": 0.014331944286823273, "Left": 0.3002646267414093, "Top": 0.5588448643684387}, "Polygon": [{"X": 0.3002646863460541, "Y": 0.5588448643684387}, {"X": 0.38289207220077515, "Y": 0.5588502287864685}, {"X": 0.38289207220077515, "Y": 0.5731768608093262}, {"X": 0.3002646267414093, "Y": 0.5731715559959412}]}, "Id": "d18352a2-4574-46f8-8b1a-83ac9e0cb5e8"}, {"BlockType": "WORD", "Confidence": 99.61276245117188, "Text": "Signature:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1679600477218628, "Height": 0.017386339604854584, "Left": 0.10678879916667938, "Top": 0.8216462135314941}, "Polygon": [{"X": 0.10678894817829132, "Y": 0.8216462135314941}, {"X": 0.274748831987381, "Y": 0.8216555714607239}, {"X": 0.2747487723827362, "Y": 0.8390325307846069}, {"X": 0.10678879916667938, "Y": 0.8390232920646667}]}, "Id": "c117d301-1660-4202-b8e2-e9c4ca11ac26"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "TripNumber", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1721016764640808, "Height": 0.01781240664422512, "Left": 0.10609334707260132, "Top": 0.8825020790100098}, "Polygon": [{"X": 0.10609350353479385, "Y": 0.8825020790100098}, {"X": 0.27819502353668213, "Y": 0.88251131772995}, {"X": 0.27819496393203735, "Y": 0.900314450263977}, {"X": 0.10609334707260132, "Y": 0.9003053307533264}]}, "Id": "d7c50cdb-92e3-4299-884d-6bc460f8ab24"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "6063", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06932178884744644, "Height": 0.015749726444482803, "Left": 0.7314403057098389, "Top": 0.883460521697998}, "Polygon": [{"X": 0.7314403057098389, "Y": 0.883460521697998}, {"X": 0.8007619380950928, "Y": 0.8834642767906189}, {"X": 0.8007621169090271, "Y": 0.8992102742195129}, {"X": 0.7314404845237732, "Y": 0.8992065787315369}]}, "Id": "4e403971-257f-403a-8529-75e05e244aca"}, {"BlockType": "WORD", "Confidence": 99.4541244506836, "Text": "VehicleID", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1604834347963333, "Height": 0.016407852992415428, "Left": 0.10125917941331863, "Top": 0.9034998416900635}, "Polygon": [{"X": 0.10125932097434998, "Y": 0.9034998416900635}, {"X": 0.26174262166023254, "Y": 0.9035083651542664}, {"X": 0.2617425322532654, "Y": 0.9199076890945435}, {"X": 0.10125917941331863, "Y": 0.9198992848396301}]}, "Id": "3f8dbc60-7749-4502-83bd-8bd4640c0c2f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "6063", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0695929154753685, "Height": 0.016113631427288055, "Left": 0.7331275939941406, "Top": 0.9042515158653259}, "Polygon": [{"X": 0.7331275939941406, "Y": 0.9042515158653259}, {"X": 0.802720308303833, "Y": 0.904255211353302}, {"X": 0.8027205467224121, "Y": 0.9203651547431946}, {"X": 0.733127772808075, "Y": 0.9203614592552185}]}, "Id": "c0084586-40be-4d25-87e0-ecd3b6efef5d"}, {"BlockType": "WORD", "Confidence": 65.5261459350586, "Text": "HubOdometer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1941739320755005, "Height": 0.016948474571108818, "Left": 0.10093144327402115, "Top": 0.9246818423271179}, "Polygon": [{"X": 0.10093159228563309, "Y": 0.9246818423271179}, {"X": 0.29510536789894104, "Y": 0.9246920347213745}, {"X": 0.29510530829429626, "Y": 0.9416303038597107}, {"X": 0.10093144327402115, "Y": 0.9416202902793884}]}, "Id": "a7d8ab51-cfec-4c12-a5c7-e70f2a91c492"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "21400", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08848832547664642, "Height": 0.016809213906526566, "Left": 0.7172682881355286, "Top": 0.9254225492477417}, "Polygon": [{"X": 0.7172682881355286, "Y": 0.9254225492477417}, {"X": 0.8057563900947571, "Y": 0.9254271984100342}, {"X": 0.8057566285133362, "Y": 0.9422317743301392}, {"X": 0.7172684073448181, "Y": 0.9422271847724915}]}, "Id": "f8e03249-cc8f-4eca-bc61-c3537e786595"}, {"BlockType": "WORD", "Confidence": 97.17314910888672, "Text": "TruckingCompanyName", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.3372194766998291, "Height": 0.0188206285238266, "Left": 0.10143045336008072, "Top": 0.9466440081596375}, "Polygon": [{"X": 0.10143062472343445, "Y": 0.9466440081596375}, {"X": 0.4386499226093292, "Y": 0.9466614127159119}, {"X": 0.4386499226093292, "Y": 0.9654646515846252}, {"X": 0.10143045336008072, "Y": 0.9654474258422852}]}, "Id": "a8bafab5-5476-430b-b036-cf691d6ba010"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "RILUX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.089214026927948, "Height": 0.016920162364840508, "Left": 0.45502960681915283, "Top": 0.9459388852119446}, "Polygon": [{"X": 0.45502960681915283, "Y": 0.9459388852119446}, {"X": 0.544243574142456, "Y": 0.9459434747695923}, {"X": 0.5442436337471008, "Y": 0.962859034538269}, {"X": 0.4550296366214752, "Y": 0.9628544449806213}]}, "Id": "b31d17a1-8937-4a42-8d07-b51f105b1c0d"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "TRANSPORTATION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2490312159061432, "Height": 0.018175605684518814, "Left": 0.5611628890037537, "Top": 0.9462791085243225}, "Polygon": [{"X": 0.5611628890037537, "Y": 0.9462791085243225}, {"X": 0.8101938962936401, "Y": 0.946291983127594}, {"X": 0.8101941347122192, "Y": 0.964454710483551}, {"X": 0.5611630082130432, "Y": 0.9644420146942139}]}, "Id": "d82859ca-14db-4a32-93ed-911f421044cd"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "436edac4-1969-464b-b944-f42ad385c55d", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "436edac4-1969-464b-b944-f42ad385c55d", "content-type": "application/x-amz-json-1.1", "content-length": "50518", "date": "Thu, 18 Sep 2025 08:51:18 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "180b55dd-8146-4c5b-81d6-e0f2ab46d3ed", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 08:51:20 GMT", "content-type": "application/json", "content-length": "772", "connection": "keep-alive", "x-amzn-requestid": "180b55dd-8146-4c5b-81d6-e0f2ab46d3ed"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have page1 content. We need to determine doc type. Looks like a fuel receipt or fuel purchase. Keywords: \"Fuel\", \"Pump\", \"Gallons\", \"Price / Gal\", \"Total\", \"Received\". This matches fuel_receipt. Also \"INVOICE#\" but it's fuel receipt. The doc_type for fuel receipt is fuel_receipt. No continuation. So output: {page_no:1, doc_type: \"fuel_receipt\"}."}}}, {"toolUse": {"toolUseId": "tooluse_3zykb-AhSOy39qJ9becVYA", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2283, "outputTokens": 132, "totalTokens": 2415}, "metrics": {"latencyMs": 690}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/81cdb055_9_fuel_receipt_11.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nLoves.\nStore #878\n7900 Adobe Road\nAlamo, MI 49009\n(269)903-0234\n02/18/2025 Tkt #1247187\nType: SALE\n(ORIGINAL)\nQty Name\nPrice\nTotal\nDIESEL\n275.34\nPump:\n22\nGallons:\n76.719\nPrice / Gal:\n3.589\nSubtotal\n275.34\nSales Tax\n0.00\nTotal\n275.34\nReceived:\nFLEETONE\n275.34\n0180 SWIPED\nAuth No: 116932\nINVOICE# 14022\nSignature:\nTripNumber\n6063\nVehicleID\n6063\nHubOdometer\n21400\nTruckingCompanyName RILUX TRANSPORTATION\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}