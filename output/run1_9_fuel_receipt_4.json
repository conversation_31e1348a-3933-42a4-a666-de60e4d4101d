{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.35592392086982727, "Height": 0.8949424624443054, "Left": 0.2202940732240677, "Top": 0.0}, "Polygon": [{"X": 0.2331295609474182, "Y": 0.0}, {"X": 0.5762180089950562, "Y": 0.007515767123550177}, {"X": 0.5613404512405396, "Y": 0.8949424624443054}, {"X": 0.2202940732240677, "Y": 0.8876784443855286}]}, "Id": "9556764f-b97f-401a-b7b7-8052f464fb2a", "Relationships": [{"Type": "CHILD", "Ids": ["e4a5fd34-4fc0-4317-b83f-97f9c716d39b", "f779502d-5bf0-41d3-bb5d-23e9eeecfe01", "06c46102-e97a-4d0b-8559-ecab16611215", "6e629a4b-9f8d-4036-8071-4fac58d2b397", "3df7d720-5d24-47ff-9acf-582377b2a46f", "ce42b06b-f1e8-43bf-b00c-f40a40100544", "6d2d64c5-cde3-4bd2-a8a3-a354246ab53a", "4b918fc8-038a-43b4-a220-609ea676ef50", "99143616-f405-4ffc-8a29-3afd2a05b58c", "9d5392e9-be1d-429d-9b9e-9a132b9ecaee", "1b99a45f-08cf-46d8-aaae-baffbb3561d9", "b9b2ef03-a58c-4753-9a57-d87ce30b6094", "caf17ff8-a985-40ca-afcc-78f591c99518", "653a27cc-7896-41c7-bcd2-42e52e764465", "9fcda9c2-d2da-47ad-ab32-209c364808e3", "a80e9229-b9e2-4485-9188-6b0bec77853d", "2cd9d3b9-a01b-4d8a-b649-b8d71c0ff7ea", "904666ba-3e73-42ca-b6b7-cdeeca938609", "d6e7458f-1b0f-4892-8ffb-d6e894ea7c59", "cecc18b0-5eb8-482d-9aa3-f202902f489c", "ace80940-06aa-4687-958a-ec904839614d", "2539a610-66e5-45bb-846e-69f72cc389d1", "309424c4-107a-49a5-b0e0-ea1b7d3a1fce", "fc0eeb5a-1463-4a6c-a480-c551b34aee5f", "96494ad4-77ea-4eff-9200-6bd0cd89bfba", "dbdd8123-3098-47a9-ad23-a2f3a63ca68a", "e72bd5f9-42ea-4319-88da-9c1770cc29da", "414e5861-7d85-434d-b1e7-14380c2856f1", "a39df2a2-a99a-420f-92cf-dbaefd1c777f", "e581cd0f-2165-4346-9e21-d28c60588d48", "67715c0e-7a2f-487c-b26d-38fb9e6a8ae0", "7285acdb-573b-42b2-bb4e-465dff2d9e7d", "81a7146f-ab98-4191-97d2-f4c74d4453bb", "5f26ad8c-226d-49f4-8459-997d3174ddcc", "797ae65a-25f3-4bd6-a4d5-0be72399495a", "75de199c-1aa8-48e6-b782-2fe6b3baffa9", "aca54e8d-9240-47d6-a5c9-baf987013e7f", "42efd184-b6a4-47c9-973e-5f77e4b1ab78", "978c1566-8166-4b63-96d2-18ededa4ecfa", "c9ac11bd-30ae-4dff-9f89-9ed8de9a7571", "d5bce295-b1ab-490a-b09a-34ca5967c43d"]}]}, {"BlockType": "LINE", "Confidence": 99.90480041503906, "Text": "KWIK TRIP #1130", "Geometry": {"BoundingBox": {"Width": 0.16803796589374542, "Height": 0.01754576899111271, "Left": 0.30682897567749023, "Top": 0.1802370846271515}, "Polygon": [{"X": 0.307015597820282, "Y": 0.1802370846271515}, {"X": 0.47486695647239685, "Y": 0.1853097379207611}, {"X": 0.4746663272380829, "Y": 0.19778285920619965}, {"X": 0.30682897567749023, "Y": 0.19273702800273895}]}, "Id": "e4a5fd34-4fc0-4317-b83f-97f9c716d39b", "Relationships": [{"Type": "CHILD", "Ids": ["ec20435d-8ec0-4e57-b48e-bfb8b616f1a6", "be8a5a24-a392-483a-98a2-257fa8f135dc", "83d03272-e23f-4de3-a5f0-c3d20faaf656"]}]}, {"BlockType": "LINE", "Confidence": 99.924072265625, "Text": "9255 RIDGEVIEW RD", "Geometry": {"BoundingBox": {"Width": 0.19078737497329712, "Height": 0.01896020583808422, "Left": 0.2947388291358948, "Top": 0.19559623301029205}, "Polygon": [{"X": 0.29493585228919983, "Y": 0.19559623301029205}, {"X": 0.4855262041091919, "Y": 0.2013183832168579}, {"X": 0.48531225323677063, "Y": 0.21455644071102142}, {"X": 0.2947388291358948, "Y": 0.20886659622192383}]}, "Id": "f779502d-5bf0-41d3-bb5d-23e9eeecfe01", "Relationships": [{"Type": "CHILD", "Ids": ["621f9935-8924-4916-bba9-5627a976ebe0", "73ebe17a-7697-4702-9797-8473d990cbf0", "c6881d03-8f9b-4fe2-bfaa-0ce02976a708"]}]}, {"BlockType": "LINE", "Confidence": 99.38257598876953, "Text": "MT HOREB, WI", "Geometry": {"BoundingBox": {"Width": 0.13604922592639923, "Height": 0.018206780776381493, "Left": 0.3273572325706482, "Top": 0.21376948058605194}, "Polygon": [{"X": 0.3275710344314575, "Y": 0.21376948058605194}, {"X": 0.4634064733982086, "Y": 0.21781833469867706}, {"X": 0.4631797969341278, "Y": 0.23197625577449799}, {"X": 0.3273572325706482, "Y": 0.2279520481824875}]}, "Id": "06c46102-e97a-4d0b-8559-ecab16611215", "Relationships": [{"Type": "CHILD", "Ids": ["ff53a715-8107-4b8d-8cea-8a1577128402", "6bf3603e-91c0-4139-bdfe-c2e255f1f37f", "f49bf505-bfbf-4be6-a691-015ea3fb8f44"]}]}, {"BlockType": "LINE", "Confidence": 99.83567810058594, "Text": "Invoice# 0096563", "Geometry": {"BoundingBox": {"Width": 0.18979550898075104, "Height": 0.01800541579723358, "Left": 0.2838318645954132, "Top": 0.24509412050247192}, "Polygon": [{"X": 0.2840159833431244, "Y": 0.24509412050247192}, {"X": 0.47362735867500305, "Y": 0.25066807866096497}, {"X": 0.4734273850917816, "Y": 0.2630995512008667}, {"X": 0.2838318645954132, "Y": 0.25755575299263}]}, "Id": "6e629a4b-9f8d-4036-8071-4fac58d2b397", "Relationships": [{"Type": "CHILD", "Ids": ["b7473e5c-70ff-4c6d-89a6-716ad9e043c2", "c7d0ab0e-5787-41a3-b42e-18ba1919bca7"]}]}, {"BlockType": "LINE", "Confidence": 98.75963592529297, "Text": "Trans # 0900-6563", "Geometry": {"BoundingBox": {"Width": 0.21518297493457794, "Height": 0.019525179639458656, "Left": 0.28079289197921753, "Top": 0.2607874274253845}, "Polygon": [{"X": 0.2809889316558838, "Y": 0.2607874274253845}, {"X": 0.4959758520126343, "Y": 0.2670646905899048}, {"X": 0.4957607090473175, "Y": 0.2803126275539398}, {"X": 0.28079289197921753, "Y": 0.2740718424320221}]}, "Id": "3df7d720-5d24-47ff-9acf-582377b2a46f", "Relationships": [{"Type": "CHILD", "Ids": ["1bc436a3-c2b8-4a5e-8bc6-2189646f54c4", "deb7f665-8a82-46e9-b05c-c4f93d394a48", "5de17131-4e02-41e0-97ba-ab141af26ac0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.04482599347829819, "Height": 0.012393925338983536, "Left": 0.2813645303249359, "Top": 0.2795299291610718}, "Polygon": [{"X": 0.2815284729003906, "Y": 0.2795299291610718}, {"X": 0.3261905014514923, "Y": 0.2808234691619873}, {"X": 0.3260232210159302, "Y": 0.291923850774765}, {"X": 0.2813645303249359, "Y": 0.2906366288661957}]}, "Id": "ce42b06b-f1e8-43bf-b00c-f40a40100544", "Relationships": [{"Type": "CHILD", "Ids": ["67c7cf7c-96a8-4f6d-90cd-16d5e25938e4"]}]}, {"BlockType": "LINE", "Confidence": 99.85271453857422, "Text": "08/14/2025", "Geometry": {"BoundingBox": {"Width": 0.11169291287660599, "Height": 0.017333820462226868, "Left": 0.395081490278244, "Top": 0.2806254029273987}, "Polygon": [{"X": 0.39530104398727417, "Y": 0.2806254029273987}, {"X": 0.5067744255065918, "Y": 0.2838570773601532}, {"X": 0.506544291973114, "Y": 0.29795923829078674}, {"X": 0.395081490278244, "Y": 0.2947477102279663}]}, "Id": "6d2d64c5-cde3-4bd2-a8a3-a354246ab53a", "Relationships": [{"Type": "CHILD", "Ids": ["142fa133-87f9-4f09-8ea6-16c83b7a36bc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.04581568390130997, "Height": 0.013045527040958405, "Left": 0.2801567316055298, "Top": 0.2955985963344574}, "Polygon": [{"X": 0.28032994270324707, "Y": 0.2955985963344574}, {"X": 0.32597240805625916, "Y": 0.2969112992286682}, {"X": 0.32579556107521057, "Y": 0.3086441159248352}, {"X": 0.2801567316055298, "Y": 0.30733826756477356}]}, "Id": "4b918fc8-038a-43b4-a220-609ea676ef50", "Relationships": [{"Type": "CHILD", "Ids": ["b2859659-274f-4bbe-b6a9-e525e159a5f8"]}]}, {"BlockType": "LINE", "Confidence": 99.38884735107422, "Text": "12:53 PM", "Geometry": {"BoundingBox": {"Width": 0.08860776573419571, "Height": 0.014770619571208954, "Left": 0.3951602578163147, "Top": 0.2980412542819977}, "Polygon": [{"X": 0.3953505754470825, "Y": 0.2980412542819977}, {"X": 0.4837680160999298, "Y": 0.3005851209163666}, {"X": 0.4835704267024994, "Y": 0.31281188130378723}, {"X": 0.3951602578163147, "Y": 0.3102818429470062}]}, "Id": "99143616-f405-4ffc-8a29-3afd2a05b58c", "Relationships": [{"Type": "CHILD", "Ids": ["fff1c335-7d86-4c75-849d-789b150d68fc", "0f03cc4b-7b57-434a-bed6-2c603a25b471"]}]}, {"BlockType": "LINE", "Confidence": 99.94033813476562, "Text": "Pump Quantity Price", "Geometry": {"BoundingBox": {"Width": 0.2252100557088852, "Height": 0.022623715922236443, "Left": 0.2804444432258606, "Top": 0.3274646997451782}, "Polygon": [{"X": 0.2806849181652069, "Y": 0.3274646997451782}, {"X": 0.505654513835907, "Y": 0.33384478092193604}, {"X": 0.5053894519805908, "Y": 0.3500884175300598}, {"X": 0.2804444432258606, "Y": 0.3437551259994507}]}, "Id": "9d5392e9-be1d-429d-9b9e-9a132b9ecaee", "Relationships": [{"Type": "CHILD", "Ids": ["077aa76a-b0a5-408f-b882-2ff503cbb46f", "2efa57e1-f7c7-466f-a338-8dd4ecd3cd3f", "2ae46c3d-2979-4479-b8cf-2f7f4496c5ee"]}]}, {"BlockType": "LINE", "Confidence": 99.79199981689453, "Text": "02 133.011 3.599", "Geometry": {"BoundingBox": {"Width": 0.21385200321674347, "Height": 0.01894504949450493, "Left": 0.2917088568210602, "Top": 0.34472495317459106}, "Polygon": [{"X": 0.2919013202190399, "Y": 0.34472495317459106}, {"X": 0.5055608749389648, "Y": 0.3507387638092041}, {"X": 0.50534987449646, "Y": 0.3636700212955475}, {"X": 0.2917088568210602, "Y": 0.35769158601760864}]}, "Id": "1b99a45f-08cf-46d8-aaae-baffbb3561d9", "Relationships": [{"Type": "CHILD", "Ids": ["0b7391d5-8cd8-4c13-b9d4-d4367551824d", "e7f6e8cd-e9e8-4c9e-9db6-20b6fbc86a57", "820e0505-d208-45be-b1fd-5ca7cf943846"]}]}, {"BlockType": "LINE", "Confidence": 99.88042449951172, "Text": "Product", "Geometry": {"BoundingBox": {"Width": 0.07920148968696594, "Height": 0.013335750438272953, "Left": 0.27967944741249084, "Top": 0.3796563744544983}, "Polygon": [{"X": 0.27984413504600525, "Y": 0.3796563744544983}, {"X": 0.3588809370994568, "Y": 0.38184595108032227}, {"X": 0.35871031880378723, "Y": 0.39299213886260986}, {"X": 0.27967944741249084, "Y": 0.39081382751464844}]}, "Id": "b9b2ef03-a58c-4753-9a57-d87ce30b6094", "Relationships": [{"Type": "CHILD", "Ids": ["5d104e46-5429-473b-9397-09c79c8e330a"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.06599244475364685, "Height": 0.013088247738778591, "Left": 0.4383161962032318, "Top": 0.38222095370292664}, "Polygon": [{"X": 0.4384949505329132, "Y": 0.38222095370292664}, {"X": 0.5043086409568787, "Y": 0.38404572010040283}, {"X": 0.5041249394416809, "Y": 0.395309180021286}, {"X": 0.4383161962032318, "Y": 0.3934939205646515}]}, "Id": "caf17ff8-a985-40ca-afcc-78f591c99518", "Relationships": [{"Type": "CHILD", "Ids": ["0e9e86c3-72b9-42ed-a2b5-29b5caafa7b7"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "DSL2", "Geometry": {"BoundingBox": {"Width": 0.044782817363739014, "Height": 0.01185215637087822, "Left": 0.27961981296539307, "Top": 0.3966348469257355}, "Polygon": [{"X": 0.27977675199508667, "Y": 0.3966348469257355}, {"X": 0.3244026303291321, "Y": 0.3978615999221802}, {"X": 0.324242502450943, "Y": 0.4084869921207428}, {"X": 0.27961981296539307, "Y": 0.4072663187980652}]}, "Id": "653a27cc-7896-41c7-bcd2-42e52e764465", "Relationships": [{"Type": "CHILD", "Ids": ["73924318-99b2-4e98-8abb-bbdcce228b01"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "478.71", "Geometry": {"BoundingBox": {"Width": 0.06295692920684814, "Height": 0.013400412164628506, "Left": 0.43833038210868835, "Top": 0.39837875962257385}, "Polygon": [{"X": 0.43851566314697266, "Y": 0.39837875962257385}, {"X": 0.5012873411178589, "Y": 0.40010640025138855}, {"X": 0.5010971426963806, "Y": 0.41177916526794434}, {"X": 0.43833038210868835, "Y": 0.4100608825683594}]}, "Id": "9fcda9c2-d2da-47ad-ab32-209c364808e3", "Relationships": [{"Type": "CHILD", "Ids": ["cc0c1d1a-fbc2-4493-8336-9ca4e02144fb"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.05786651000380516, "Height": 0.012360181659460068, "Left": 0.27800869941711426, "Top": 0.42971271276474}, "Polygon": [{"X": 0.2781681418418884, "Y": 0.42971271276474}, {"X": 0.3358752131462097, "Y": 0.43127503991127014}, {"X": 0.3357115685939789, "Y": 0.44207289814949036}, {"X": 0.27800869941711426, "Y": 0.4405185580253601}]}, "Id": "a80e9229-b9e2-4485-9188-6b0bec77853d", "Relationships": [{"Type": "CHILD", "Ids": ["5059d571-6e55-4b4f-b3d9-7fd6a562d205"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "478.71", "Geometry": {"BoundingBox": {"Width": 0.06308291852474213, "Height": 0.013173331506550312, "Left": 0.42661911249160767, "Top": 0.43137645721435547}, "Polygon": [{"X": 0.4268002510070801, "Y": 0.43137645721435547}, {"X": 0.489702045917511, "Y": 0.4330812692642212}, {"X": 0.48951607942581177, "Y": 0.4445497989654541}, {"X": 0.42661911249160767, "Y": 0.4428541958332062}]}, "Id": "2cd9d3b9-a01b-4d8a-b649-b8d71c0ff7ea", "Relationships": [{"Type": "CHILD", "Ids": ["e11118a1-98c4-48f4-a7d4-49e490278eaa"]}]}, {"BlockType": "LINE", "Confidence": 99.90506744384766, "Text": "NBS/KWIK TRIP FLEET", "Geometry": {"BoundingBox": {"Width": 0.21297141909599304, "Height": 0.020689761266112328, "Left": 0.2785523533821106, "Top": 0.*****************}, "Polygon": [{"X": 0.27877455949783325, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.2785523533821106, "Y": 0.****************}]}, "Id": "904666ba-3e73-42ca-b6b7-cdeeca938609", "Relationships": [{"Type": "CHILD", "Ids": ["05af7076-dc83-4a8e-bf4b-2259e37a7be4", "cc17e7fb-64a4-42a0-889e-f68e77e19cf1", "5e4de30d-0f11-440f-b768-192a13601b2d"]}]}, {"BlockType": "LINE", "Confidence": 99.87554168701172, "Text": "CARD# ICR", "Geometry": {"BoundingBox": {"Width": 0.10180341452360153, "Height": 0.014511027373373508, "Left": 0.2782739996910095, "Top": 0.****************}, "Polygon": [{"X": 0.27844876050949097, "Y": 0.****************}, {"X": 0.38007742166519165, "Y": 0.*****************}, {"X": 0.3798946142196655, "Y": 0.***************}, {"X": 0.2782739996910095, "Y": 0.****************}]}, "Id": "d6e7458f-1b0f-4892-8ffb-d6e894ea7c59", "Relationships": [{"Type": "CHILD", "Ids": ["852209f6-fea4-4628-97ab-f79344f7dbc9", "7ad659f6-f159-460a-b6e5-cc3834597b03"]}]}, {"BlockType": "LINE", "Confidence": 15.152114868164062, "Text": "XXXXXXXXXXXXXX1716", "Geometry": {"BoundingBox": {"Width": 0.2029736042022705, "Height": 0.01910027116537094, "Left": 0.2770345211029053, "Top": 0.*****************}, "Polygon": [{"X": 0.27723824977874756, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.2770345211029053, "Y": 0.506790280342102}]}, "Id": "cecc18b0-5eb8-482d-9aa3-f202902f489c", "Relationships": [{"Type": "CHILD", "Ids": ["ae3309d9-8b01-4c45-947e-ed2c7a04f7fd"]}]}, {"BlockType": "LINE", "Confidence": 99.8425521850586, "Text": "AUTH #", "Geometry": {"BoundingBox": {"Width": 0.06865070015192032, "Height": 0.013498947955667973, "Left": 0.2774169147014618, "Top": 0.5118042826652527}, "Polygon": [{"X": 0.2775900065898895, "Y": 0.5118042826652527}, {"X": 0.3460676372051239, "Y": 0.513587474822998}, {"X": 0.3458891808986664, "Y": 0.5253032445907593}, {"X": 0.2774169147014618, "Y": 0.5235302448272705}]}, "Id": "ace80940-06aa-4687-958a-ec904839614d", "Relationships": [{"Type": "CHILD", "Ids": ["900b2e5e-bd18-475c-9bf6-2352bf2f245a", "0de7c495-d96e-4bf2-9250-8b71998e913f"]}]}, {"BlockType": "LINE", "Confidence": 99.61276245117188, "Text": "P20276", "Geometry": {"BoundingBox": {"Width": 0.06553672254085541, "Height": 0.01314892340451479, "Left": 0.4360447824001312, "Top": 0.5133455395698547}, "Polygon": [{"X": 0.43622639775276184, "Y": 0.5133455395698547}, {"X": 0.5015814900398254, "Y": 0.5150495767593384}, {"X": 0.5013948678970337, "Y": 0.5264944434165955}, {"X": 0.4360447824001312, "Y": 0.5248000025749207}]}, "Id": "2539a610-66e5-45bb-846e-69f72cc389d1", "Relationships": [{"Type": "CHILD", "Ids": ["67def819-23be-4e8f-97ee-84c3942dc57c"]}]}, {"BlockType": "LINE", "Confidence": 99.67623901367188, "Text": "RESPONSE: APPROVED", "Geometry": {"BoundingBox": {"Width": 0.20120878517627716, "Height": 0.018951082602143288, "Left": 0.2776530385017395, "Top": 0.5259419679641724}, "Polygon": [{"X": 0.27785658836364746, "Y": 0.5259419679641724}, {"X": 0.47886183857917786, "Y": 0.5311405658721924}, {"X": 0.4786396920681, "Y": 0.5448930263519287}, {"X": 0.2776530385017395, "Y": 0.5397297739982605}]}, "Id": "309424c4-107a-49a5-b0e0-ea1b7d3a1fce", "Relationships": [{"Type": "CHILD", "Ids": ["3105a1ae-8cc2-45c4-be97-31bb1225eb52", "6158e740-7891-4e38-b328-02a0ce54bcfb"]}]}, {"BlockType": "LINE", "Confidence": 99.82455444335938, "Text": "AUTH REF#: P20276", "Geometry": {"BoundingBox": {"Width": 0.19054166972637177, "Height": 0.01886558346450329, "Left": 0.2882300615310669, "Top": 0.5425525307655334}, "Polygon": [{"X": 0.28843802213668823, "Y": 0.5425525307655334}, {"X": 0.47877174615859985, "Y": 0.5474360585212708}, {"X": 0.4785458743572235, "Y": 0.5614181160926819}, {"X": 0.2882300615310669, "Y": 0.5565686821937561}]}, "Id": "fc0eeb5a-1463-4a6c-a480-c551b34aee5f", "Relationships": [{"Type": "CHILD", "Ids": ["f35554f9-5a17-4b88-9f46-71c0c7ee790c", "f11e4286-7ad1-4c07-9b0b-468479fbcb5a", "83ef3c40-deba-4305-b062-afaf5c520618"]}]}, {"BlockType": "LINE", "Confidence": 99.68431091308594, "Text": "KWIK REWARDS/LOYALTY", "Geometry": {"BoundingBox": {"Width": 0.2244812697172165, "Height": 0.020969493314623833, "Left": 0.2756343483924866, "Top": 0.5741077661514282}, "Polygon": [{"X": 0.2758607864379883, "Y": 0.5741077661514282}, {"X": 0.5001156330108643, "Y": 0.5797717571258545}, {"X": 0.****************, "Y": 0.5950772762298584}, {"X": 0.2756343483924866, "Y": 0.5894572138786316}]}, "Id": "96494ad4-77ea-4eff-9200-6bd0cd89bfba", "Relationships": [{"Type": "CHILD", "Ids": ["a3ce2f4b-efcb-4dd0-9a8a-7cc80cd008e0", "ff4de137-5a55-4302-8c3d-35f0e8009bd9"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "CARD# ICR", "Geometry": {"BoundingBox": {"Width": 0.10158850252628326, "Height": 0.015067911706864834, "Left": 0.27529942989349365, "Top": 0.5936724543571472}, "Polygon": [{"X": 0.275484561920166, "Y": 0.5936724543571472}, {"X": 0.3768879473209381, "Y": 0.5962086319923401}, {"X": 0.3766942620277405, "Y": 0.6087403297424316}, {"X": 0.27529942989349365, "Y": 0.6062204241752625}]}, "Id": "dbdd8123-3098-47a9-ad23-a2f3a63ca68a", "Relationships": [{"Type": "CHILD", "Ids": ["f9dc96cf-0314-441d-8ec1-3f63e505bc8f", "691fc040-ee8a-4d13-b21f-77cfdc50c614"]}]}, {"BlockType": "LINE", "Confidence": 87.18849182128906, "Text": "XXXXXXXXXXXX6391", "Geometry": {"BoundingBox": {"Width": 0.17831552028656006, "Height": 0.019157525151968002, "Left": 0.2745996117591858, "Top": 0.6085521578788757}, "Polygon": [{"X": 0.27481746673583984, "Y": 0.6085521578788757}, {"X": 0.*****************, "Y": 0.6129730939865112}, {"X": 0.****************, "Y": 0.627709686756134}, {"X": 0.2745996117591858, "Y": 0.6233221888542175}]}, "Id": "e72bd5f9-42ea-4319-88da-9c1770cc29da", "Relationships": [{"Type": "CHILD", "Ids": ["8d7240b6-5216-4197-be9a-da5d8ad560a5"]}]}, {"BlockType": "LINE", "Confidence": 99.8876953125, "Text": "AUTH #", "Geometry": {"BoundingBox": {"Width": 0.06826059520244598, "Height": 0.013773374259471893, "Left": 0.27405181527137756, "Top": 0.6276068091392517}, "Polygon": [{"X": 0.27423039078712463, "Y": 0.6276068091392517}, {"X": 0.34231239557266235, "Y": 0.6292805075645447}, {"X": 0.3421282470226288, "Y": 0.6413801908493042}, {"X": 0.27405181527137756, "Y": 0.6397169828414917}]}, "Id": "414e5861-7d85-434d-b1e7-14380c2856f1", "Relationships": [{"Type": "CHILD", "Ids": ["cba80504-35b7-455b-8dbb-67cfd2daa136", "e87764d3-8e07-44f5-b185-9a784445bef9"]}]}, {"BlockType": "LINE", "Confidence": 95.095458984375, "Text": "U:4395515", "Geometry": {"BoundingBox": {"Width": 0.09981165081262589, "Height": 0.014054574072360992, "Left": 0.3991587162017822, "Top": 0.6280974745750427}, "Polygon": [{"X": 0.39934009313583374, "Y": 0.6280974745750427}, {"X": 0.4989703893661499, "Y": 0.630549967288971}, {"X": 0.4987812340259552, "Y": 0.6421520113945007}, {"X": 0.3991587162017822, "Y": 0.6397143006324768}]}, "Id": "a39df2a2-a99a-420f-92cf-dbaefd1c777f", "Relationships": [{"Type": "CHILD", "Ids": ["a18b6a41-8ca4-40f2-ba3d-272492aa6021"]}]}, {"BlockType": "LINE", "Confidence": 98.92777252197266, "Text": "RESPONSE:", "Geometry": {"BoundingBox": {"Width": 0.09812457859516144, "Height": 0.014504678547382355, "Left": 0.2738610506057739, "Top": 0.644151508808136}, "Polygon": [{"X": 0.27403998374938965, "Y": 0.644151508808136}, {"X": 0.37198564410209656, "Y": 0.646539032459259}, {"X": 0.37179872393608093, "Y": 0.6586562395095825}, {"X": 0.2738610506057739, "Y": 0.6562838554382324}]}, "Id": "e581cd0f-2165-4346-9e21-d28c60588d48", "Relationships": [{"Type": "CHILD", "Ids": ["fd1063fb-4184-47c3-a404-7c85ec12e483"]}]}, {"BlockType": "LINE", "Confidence": 98.08184814453125, "Text": "APPROVED, Total", "Geometry": {"BoundingBox": {"Width": 0.16840240359306335, "Height": 0.018529748544096947, "Left": 0.2842485010623932, "Top": 0.6594187021255493}, "Polygon": [{"X": 0.2844633162021637, "Y": 0.6594187021255493}, {"X": 0.45265093445777893, "Y": 0.6634865999221802}, {"X": 0.4524197280406952, "Y": 0.6779484152793884}, {"X": 0.2842485010623932, "Y": 0.6739115715026855}]}, "Id": "67715c0e-7a2f-487c-b26d-38fb9e6a8ae0", "Relationships": [{"Type": "CHILD", "Ids": ["13a37702-f45f-4a80-a395-deafc602767f", "62ebac15-e3ab-4da7-a726-e1d5c6d3a833"]}]}, {"BlockType": "LINE", "Confidence": 99.71446990966797, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.20179536938667297, "Height": 0.0182784553617239, "Left": 0.28439876437187195, "Top": 0.6757219433784485}, "Polygon": [{"X": 0.28459858894348145, "Y": 0.6757219433784485}, {"X": 0.4861941635608673, "Y": 0.6805565357208252}, {"X": 0.48597609996795654, "Y": 0.6940003633499146}, {"X": 0.28439876437187195, "Y": 0.6892004013061523}]}, "Id": "7285acdb-573b-42b2-bb4e-465dff2d9e7d", "Relationships": [{"Type": "CHILD", "Ids": ["161704ff-7d6a-4658-87ab-32ce5a090480", "60b6f23a-c493-4a3a-aadc-58ec605f2658", "153081fe-b272-49f0-b26e-3ae8a3e677d1"]}]}, {"BlockType": "LINE", "Confidence": 99.59732055664062, "Text": "Points: $7.23", "Geometry": {"BoundingBox": {"Width": 0.14649030566215515, "Height": 0.016564566642045975, "Left": 0.28357619047164917, "Top": 0.6929957866668701}, "Polygon": [{"X": 0.28377053141593933, "Y": 0.6929957866668701}, {"X": 0.****************, "Y": 0.6964724063873291}, {"X": 0.****************, "Y": 0.7095603346824646}, {"X": 0.28357619047164917, "Y": 0.7061081528663635}]}, "Id": "81a7146f-ab98-4191-97d2-f4c74d4453bb", "Relationships": [{"Type": "CHILD", "Ids": ["a744e933-c771-4333-ac92-caa8c9b3086c", "d48af346-c4a2-4766-b6aa-36025e55f519"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Shower credit", "Geometry": {"BoundingBox": {"Width": 0.146262988448143, "Height": 0.016330746933817863, "Left": 0.28325483202934265, "Top": 0.7100061774253845}, "Polygon": [{"X": 0.28344622254371643, "Y": 0.7100061774253845}, {"X": 0.*****************, "Y": 0.7134461998939514}, {"X": 0.****************, "Y": 0.7263369560241699}, {"X": 0.28325483202934265, "Y": 0.7229209542274475}]}, "Id": "5f26ad8c-226d-49f4-8459-997d3174ddcc", "Relationships": [{"Type": "CHILD", "Ids": ["f1bbd189-a16f-4b3b-9e10-942b3313e37f", "1caf4446-4a6c-40af-be35-fdc86b8a4a5a"]}]}, {"BlockType": "LINE", "Confidence": 99.1206283569336, "Text": "earned! Expires in", "Geometry": {"BoundingBox": {"Width": 0.2028564214706421, "Height": 0.02076311968266964, "Left": 0.2827306091785431, "Top": 0.7255867719650269}, "Polygon": [{"X": 0.28296878933906555, "Y": 0.7255867719650269}, {"X": 0.****************, "Y": 0.7303187847137451}, {"X": 0.*****************, "Y": 0.7463499307632446}, {"X": 0.2827306091785431, "Y": 0.74165940284729}]}, "Id": "797ae65a-25f3-4bd6-a4d5-0be72399495a", "Relationships": [{"Type": "CHILD", "Ids": ["3dedea1b-0f79-4a54-8700-4bab0ecc3c01", "542a0f91-f9e2-47c0-9f25-b41ab7bd0df1", "baaf54d2-c405-4d8f-a90a-6edfd78ced44"]}]}, {"BlockType": "LINE", "Confidence": 99.5582504272461, "Text": "14 days.", "Geometry": {"BoundingBox": {"Width": 0.08551566302776337, "Height": 0.016161518171429634, "Left": 0.2831372022628784, "Top": 0.7448059916496277}, "Polygon": [{"X": 0.2833477854728699, "Y": 0.7448059916496277}, {"X": 0.368652880191803, "Y": 0.7467775940895081}, {"X": 0.3684341609477997, "Y": 0.760967493057251}, {"X": 0.2831372022628784, "Y": 0.7590112686157227}]}, "Id": "75de199c-1aa8-48e6-b782-2fe6b3baffa9", "Relationships": [{"Type": "CHILD", "Ids": ["f4825bab-7f45-49fc-b1bc-e6db4a89f578", "2cd4739b-1b1f-444b-899a-5f5ba66a6648"]}]}, {"BlockType": "LINE", "Confidence": 99.9867172241211, "Text": "TRUCK NUMBER 02929", "Geometry": {"BoundingBox": {"Width": 0.22592490911483765, "Height": 0.017693933099508286, "Left": 0.2698817253112793, "Top": 0.7769306898117065}, "Polygon": [{"X": 0.270067423582077, "Y": 0.7769306898117065}, {"X": 0.49580660462379456, "Y": 0.7820560336112976}, {"X": 0.4956017732620239, "Y": 0.7946246266365051}, {"X": 0.2698817253112793, "Y": 0.7895354628562927}]}, "Id": "aca54e8d-9240-47d6-a5c9-baf987013e7f", "Relationships": [{"Type": "CHILD", "Ids": ["b2533dbc-66bd-4274-bea0-98004cf645c7", "79e57eb6-73a4-42a9-9c66-c1cfbd00065d", "9290d450-d58d-414e-8b4f-58f735375692"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "MERCHANT NUMBER", "Geometry": {"BoundingBox": {"Width": 0.16980108618736267, "Height": 0.015460457652807236, "Left": 0.26939019560813904, "Top": 0.7939839959144592}, "Polygon": [{"X": 0.26956212520599365, "Y": 0.7939839959144592}, {"X": 0.4391912817955017, "Y": 0.7977989912033081}, {"X": 0.4390060305595398, "Y": 0.8094444274902344}, {"X": 0.26939019560813904, "Y": 0.8056546449661255}]}, "Id": "42efd184-b6a4-47c9-973e-5f77e4b1ab78", "Relationships": [{"Type": "CHILD", "Ids": ["64a13ade-69af-4872-93ce-17ad9f24f0c8", "f0204e35-210f-46f1-a0c6-fba8f69fb783"]}]}, {"BlockType": "LINE", "Confidence": 78.8741683959961, "Text": "*******1306", "Geometry": {"BoundingBox": {"Width": 0.12348933517932892, "Height": 0.013591546565294266, "Left": 0.2923128008842468, "Top": 0.8106266856193542}, "Polygon": [{"X": 0.29247453808784485, "Y": 0.8106266856193542}, {"X": 0.41580212116241455, "Y": 0.8133753538131714}, {"X": 0.41563138365745544, "Y": 0.824218213558197}, {"X": 0.2923128008842468, "Y": 0.8214865922927856}]}, "Id": "978c1566-8166-4b63-96d2-18ededa4ecfa", "Relationships": [{"Type": "CHILD", "Ids": ["da42829a-73dc-449f-808d-d4b873ae8ffe"]}]}, {"BlockType": "LINE", "Confidence": 99.98697662353516, "Text": "ALL TAXES INCLUDED", "Geometry": {"BoundingBox": {"Width": 0.20142850279808044, "Height": 0.016250256448984146, "Left": 0.28177565336227417, "Top": 0.8256976008415222}, "Polygon": [{"X": 0.28195104002952576, "Y": 0.8256976008415222}, {"X": 0.4832041561603546, "Y": 0.8301442861557007}, {"X": 0.48301273584365845, "Y": 0.8419478535652161}, {"X": 0.28177565336227417, "Y": 0.8375315070152283}]}, "Id": "c9ac11bd-30ae-4dff-9f89-9ed8de9a7571", "Relationships": [{"Type": "CHILD", "Ids": ["a29d8485-cb11-46ec-bd64-ae4ebaa8336b", "ab45da44-5759-4ae4-81e0-96e99450c258", "6e3bc614-f2bf-4a57-8512-6d2a353ec451"]}]}, {"BlockType": "LINE", "Confidence": 99.99349212646484, "Text": "IN FUEL PRICE", "Geometry": {"BoundingBox": {"Width": 0.14257313311100006, "Height": 0.014173103496432304, "Left": 0.30631160736083984, "Top": 0.8419910669326782}, "Polygon": [{"X": 0.3064776659011841, "Y": 0.8419910669326782}, {"X": 0.4488847553730011, "Y": 0.8451093435287476}, {"X": 0.44870808720588684, "Y": 0.8561642169952393}, {"X": 0.30631160736083984, "Y": 0.8530660271644592}]}, "Id": "d5bce295-b1ab-490a-b09a-34ca5967c43d", "Relationships": [{"Type": "CHILD", "Ids": ["9ca9c0db-4e6b-4b26-a0f2-5764c34e83c9", "53a98156-d759-4a57-95f9-83da1a1650bd", "404df474-bbd1-4664-8105-710dc049d0f2"]}]}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044690974056720734, "Height": 0.012769077904522419, "Left": 0.30683526396751404, "Top": 0.1808854043483734}, "Polygon": [{"X": 0.307005912065506, "Y": 0.1808854043483734}, {"X": 0.35152626037597656, "Y": 0.18223050236701965}, {"X": 0.35135218501091003, "Y": 0.19365449249744415}, {"X": 0.30683526396751404, "Y": 0.19231590628623962}]}, "Id": "ec20435d-8ec0-4e57-b48e-bfb8b616f1a6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04478249326348305, "Height": 0.012081943452358246, "Left": 0.36293426156044006, "Top": 0.1836831122636795}, "Polygon": [{"X": 0.36309874057769775, "Y": 0.1836831122636795}, {"X": 0.4077167510986328, "Y": 0.18503053486347198}, {"X": 0.40754902362823486, "Y": 0.19576504826545715}, {"X": 0.36293426156044006, "Y": 0.19442376494407654}]}, "Id": "be8a5a24-a392-483a-98a2-257fa8f135dc"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "#1130", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055754125118255615, "Height": 0.013790382072329521, "Left": 0.41911283135414124, "Top": 0.1836305409669876}, "Polygon": [{"X": 0.41930314898490906, "Y": 0.1836305409669876}, {"X": 0.47486695647239685, "Y": 0.1853097379207611}, {"X": 0.4746721386909485, "Y": 0.19742092490196228}, {"X": 0.41911283135414124, "Y": 0.19575034081935883}]}, "Id": "83d03272-e23f-4de3-a5f0-c3d20faaf656"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "9255", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04507420212030411, "Height": 0.012263944372534752, "Left": 0.2947511374950409, "Top": 0.19711603224277496}, "Polygon": [{"X": 0.29491329193115234, "Y": 0.19711603224277496}, {"X": 0.3398253321647644, "Y": 0.1984635889530182}, {"X": 0.3396598696708679, "Y": 0.20937997102737427}, {"X": 0.2947511374950409, "Y": 0.20803868770599365}]}, "Id": "621f9935-8924-4916-bba9-5627a976ebe0"}, {"BlockType": "WORD", "Confidence": 99.83159637451172, "Text": "RIDGEVIEW", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10032014548778534, "Height": 0.015000300481915474, "Left": 0.3521500825881958, "Top": 0.1985698789358139}, "Polygon": [{"X": 0.3523331582546234, "Y": 0.1985698789358139}, {"X": 0.45247021317481995, "Y": 0.20157475769519806}, {"X": 0.45227909088134766, "Y": 0.21357017755508423}, {"X": 0.3521500825881958, "Y": 0.21058069169521332}]}, "Id": "73ebe17a-7697-4702-9797-8473d990cbf0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "RD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021639835089445114, "Height": 0.011896620504558086, "Left": 0.4638863801956177, "Top": 0.20067410171031952}, "Polygon": [{"X": 0.46406659483909607, "Y": 0.20067410171031952}, {"X": 0.4855262041091919, "Y": 0.2013183832168579}, {"X": 0.4853443503379822, "Y": 0.21257072687149048}, {"X": 0.4638863801956177, "Y": 0.2119295299053192}]}, "Id": "c6881d03-8f9b-4fe2-bfaa-0ce02976a708"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023096270859241486, "Height": 0.01152867916971445, "Left": 0.3273884952068329, "Top": 0.21503031253814697}, "Polygon": [{"X": 0.32755202054977417, "Y": 0.21503031253814697}, {"X": 0.3504847586154938, "Y": 0.2157135009765625}, {"X": 0.3503195643424988, "Y": 0.22655898332595825}, {"X": 0.3273884952068329, "Y": 0.2258789837360382}]}, "Id": "ff53a715-8107-4b8d-8cea-8a1577128402"}, {"BlockType": "WORD", "Confidence": 98.29659271240234, "Text": "HOREB,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06375300884246826, "Height": 0.014752506278455257, "Left": 0.3628372550010681, "Top": 0.21613365411758423}, "Polygon": [{"X": 0.3630343973636627, "Y": 0.21613365411758423}, {"X": 0.4265902638435364, "Y": 0.2180270254611969}, {"X": 0.42638763785362244, "Y": 0.23088616132736206}, {"X": 0.3628372550010681, "Y": 0.22900326550006866}]}, "Id": "6bf3603e-91c0-4139-bdfe-c2e255f1f37f"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023180069401860237, "Height": 0.011267789639532566, "Left": 0.44022640585899353, "Top": 0.21713240444660187}, "Polygon": [{"X": 0.44039419293403625, "Y": 0.21713240444660187}, {"X": 0.4634064733982086, "Y": 0.21781833469867706}, {"X": 0.46323704719543457, "Y": 0.22840020060539246}, {"X": 0.44022640585899353, "Y": 0.22771738469600677}]}, "Id": "f49bf505-bfbf-4be6-a691-015ea3fb8f44"}, {"BlockType": "WORD", "Confidence": 99.7510757446289, "Text": "Invoice#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08960670977830887, "Height": 0.014199591241776943, "Left": 0.2838318645954132, "Top": 0.24597106873989105}, "Polygon": [{"X": 0.28400301933288574, "Y": 0.24597106873989105}, {"X": 0.3734385669231415, "Y": 0.24859918653964996}, {"X": 0.3732604384422302, "Y": 0.26017066836357117}, {"X": 0.2838318645954132, "Y": 0.25755575299263}]}, "Id": "b7473e5c-70ff-4c6d-89a6-716ad9e043c2"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "0096563", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07770223915576935, "Height": 0.013934211805462837, "Left": 0.3959251344203949, "Top": 0.24838922917842865}, "Polygon": [{"X": 0.3961065411567688, "Y": 0.24838922917842865}, {"X": 0.47362735867500305, "Y": 0.25066807866096497}, {"X": 0.47343987226486206, "Y": 0.26232343912124634}, {"X": 0.3959251344203949, "Y": 0.26005613803863525}]}, "Id": "c7d0ab0e-5787-41a3-b42e-18ba1919bca7"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Trans", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05738121271133423, "Height": 0.013096063397824764, "Left": 0.28079289197921753, "Top": 0.26263660192489624}, "Polygon": [{"X": 0.28096163272857666, "Y": 0.26263660192489624}, {"X": 0.33817410469055176, "Y": 0.2643057703971863}, {"X": 0.33800095319747925, "Y": 0.2757326662540436}, {"X": 0.28079289197921753, "Y": 0.2740718424320221}]}, "Id": "1bc436a3-c2b8-4a5e-8bc6-2189646f54c4"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011923393234610558, "Height": 0.010909676551818848, "Left": 0.3608875870704651, "Top": 0.26543596386909485}, "Polygon": [{"X": 0.3610493838787079, "Y": 0.26543596386909485}, {"X": 0.3728109896183014, "Y": 0.265779048204422}, {"X": 0.37264835834503174, "Y": 0.2763456404209137}, {"X": 0.3608875870704651, "Y": 0.2760041654109955}]}, "Id": "deb7f665-8a82-46e9-b05c-c4f93d394a48"}, {"BlockType": "WORD", "Confidence": 96.41561889648438, "Text": "0900-6563", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10063974559307098, "Height": 0.015516486950218678, "Left": 0.3953361213207245, "Top": 0.26413190364837646}, "Polygon": [{"X": 0.3955320119857788, "Y": 0.26413190364837646}, {"X": 0.4959758520126343, "Y": 0.2670646905899048}, {"X": 0.49577149748802185, "Y": 0.27964839339256287}, {"X": 0.3953361213207245, "Y": 0.27673178911209106}]}, "Id": "5de17131-4e02-41e0-97ba-ab141af26ac0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04482599347829819, "Height": 0.012393925338983536, "Left": 0.2813645303249359, "Top": 0.2795299291610718}, "Polygon": [{"X": 0.2815284729003906, "Y": 0.2795299291610718}, {"X": 0.3261905014514923, "Y": 0.2808234691619873}, {"X": 0.3260232210159302, "Y": 0.291923850774765}, {"X": 0.2813645303249359, "Y": 0.2906366288661957}]}, "Id": "67c7cf7c-96a8-4f6d-90cd-16d5e25938e4"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "08/14/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11169291287660599, "Height": 0.017333820462226868, "Left": 0.395081490278244, "Top": 0.2806254029273987}, "Polygon": [{"X": 0.39530104398727417, "Y": 0.2806254029273987}, {"X": 0.5067744255065918, "Y": 0.2838570773601532}, {"X": 0.506544291973114, "Y": 0.29795923829078674}, {"X": 0.395081490278244, "Y": 0.2947477102279663}]}, "Id": "142fa133-87f9-4f09-8ea6-16c83b7a36bc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04581568390130997, "Height": 0.013045527040958405, "Left": 0.2801567316055298, "Top": 0.2955985963344574}, "Polygon": [{"X": 0.28032994270324707, "Y": 0.2955985963344574}, {"X": 0.32597240805625916, "Y": 0.2969112992286682}, {"X": 0.32579556107521057, "Y": 0.3086441159248352}, {"X": 0.2801567316055298, "Y": 0.30733826756477356}]}, "Id": "b2859659-274f-4bbe-b6a9-e525e159a5f8"}, {"BlockType": "WORD", "Confidence": 99.02184295654297, "Text": "12:53", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05553343892097473, "Height": 0.013118498027324677, "Left": 0.3951602578163147, "Top": 0.29874730110168457}, "Polygon": [{"X": 0.39533960819244385, "Y": 0.29874730110168457}, {"X": 0.45069369673728943, "Y": 0.30033940076828003}, {"X": 0.45051005482673645, "Y": 0.31186577677726746}, {"X": 0.3951602578163147, "Y": 0.3102818429470062}]}, "Id": "fff1c335-7d86-4c75-849d-789b150d68fc"}, {"BlockType": "WORD", "Confidence": 99.755859375, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021349187940359116, "Height": 0.011839879676699638, "Left": 0.4624188244342804, "Top": 0.29997608065605164}, "Polygon": [{"X": 0.4625987112522125, "Y": 0.29997608065605164}, {"X": 0.4837680160999298, "Y": 0.3005851209163666}, {"X": 0.48358651995658875, "Y": 0.31181594729423523}, {"X": 0.4624188244342804, "Y": 0.31120991706848145}]}, "Id": "0f03cc4b-7b57-434a-bed6-2c603a25b471"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Pump", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044993627816438675, "Height": 0.01515751052647829, "Left": 0.2804444432258606, "Top": 0.3298585116863251}, "Polygon": [{"X": 0.2806495726108551, "Y": 0.3298585116863251}, {"X": 0.3254380524158478, "Y": 0.3311273455619812}, {"X": 0.325228750705719, "Y": 0.3450160324573517}, {"X": 0.2804444432258606, "Y": 0.3437551259994507}]}, "Id": "077aa76a-b0a5-408f-b882-2ff503cbb46f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08944222331047058, "Height": 0.016704432666301727, "Left": 0.3379461169242859, "Top": 0.3301079571247101}, "Polygon": [{"X": 0.3381612300872803, "Y": 0.3301079571247101}, {"X": 0.42738834023475647, "Y": 0.3326372504234314}, {"X": 0.4271647334098816, "Y": 0.34681236743927}, {"X": 0.3379461169242859, "Y": 0.34429922699928284}]}, "Id": "2efa57e1-f7c7-466f-a338-8dd4ecd3cd3f"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055335912853479385, "Height": 0.013129731640219688, "Left": 0.4503185749053955, "Top": 0.332280695438385}, "Polygon": [{"X": 0.45050299167633057, "Y": 0.332280695438385}, {"X": 0.505654513835907, "Y": 0.33384478092193604}, {"X": 0.5054658055305481, "Y": 0.34541043639183044}, {"X": 0.4503185749053955, "Y": 0.34385451674461365}]}, "Id": "2ae46c3d-2979-4479-b8cf-2f7f4496c5ee"}, {"BlockType": "WORD", "Confidence": 99.83159637451172, "Text": "02", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022227933630347252, "Height": 0.011453503742814064, "Left": 0.2917088568210602, "Top": 0.3468555510044098}, "Polygon": [{"X": 0.2918696999549866, "Y": 0.3468555510044098}, {"X": 0.3139367997646332, "Y": 0.347476065158844}, {"X": 0.31377437710762024, "Y": 0.3583090603351593}, {"X": 0.2917088568210602, "Y": 0.35769158601760864}]}, "Id": "0b7391d5-8cd8-4c13-b9d4-d4367551824d"}, {"BlockType": "WORD", "Confidence": 99.63309478759766, "Text": "133.011", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07537224143743515, "Height": 0.013673411682248116, "Left": 0.34901225566864014, "Top": 0.3476758599281311}, "Polygon": [{"X": 0.34918853640556335, "Y": 0.3476758599281311}, {"X": 0.4243845045566559, "Y": 0.34979110956192017}, {"X": 0.4242023825645447, "Y": 0.36134928464889526}, {"X": 0.34901225566864014, "Y": 0.3592451512813568}]}, "Id": "e7f6e8cd-e9e8-4c9e-9db6-20b6fbc86a57"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "3.599", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05569396913051605, "Height": 0.013055620715022087, "Left": 0.4498668909072876, "Top": 0.3491763174533844}, "Polygon": [{"X": 0.45005014538764954, "Y": 0.3491763174533844}, {"X": 0.5055608749389648, "Y": 0.3507387638092041}, {"X": 0.5053733587265015, "Y": 0.36223194003105164}, {"X": 0.4498668909072876, "Y": 0.36067768931388855}]}, "Id": "820e0505-d208-45be-b1fd-5ca7cf943846"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Product", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07920148968696594, "Height": 0.013335750438272953, "Left": 0.27967944741249084, "Top": 0.3796563744544983}, "Polygon": [{"X": 0.27984413504600525, "Y": 0.3796563744544983}, {"X": 0.3588809370994568, "Y": 0.38184595108032227}, {"X": 0.35871031880378723, "Y": 0.39299213886260986}, {"X": 0.27967944741249084, "Y": 0.39081382751464844}]}, "Id": "5d104e46-5429-473b-9397-09c79c8e330a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06599244475364685, "Height": 0.013088247738778591, "Left": 0.4383161962032318, "Top": 0.38222095370292664}, "Polygon": [{"X": 0.4384949505329132, "Y": 0.38222095370292664}, {"X": 0.5043086409568787, "Y": 0.38404572010040283}, {"X": 0.5041249394416809, "Y": 0.395309180021286}, {"X": 0.4383161962032318, "Y": 0.3934939205646515}]}, "Id": "0e9e86c3-72b9-42ed-a2b5-29b5caafa7b7"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "DSL2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044782817363739014, "Height": 0.01185215637087822, "Left": 0.27961981296539307, "Top": 0.3966348469257355}, "Polygon": [{"X": 0.27977675199508667, "Y": 0.3966348469257355}, {"X": 0.3244026303291321, "Y": 0.3978615999221802}, {"X": 0.324242502450943, "Y": 0.4084869921207428}, {"X": 0.27961981296539307, "Y": 0.4072663187980652}]}, "Id": "73924318-99b2-4e98-8abb-bbdcce228b01"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "478.71", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06295692920684814, "Height": 0.013400412164628506, "Left": 0.43833038210868835, "Top": 0.39837875962257385}, "Polygon": [{"X": 0.43851566314697266, "Y": 0.39837875962257385}, {"X": 0.5012873411178589, "Y": 0.40010640025138855}, {"X": 0.5010971426963806, "Y": 0.41177916526794434}, {"X": 0.43833038210868835, "Y": 0.4100608825683594}]}, "Id": "cc0c1d1a-fbc2-4493-8336-9ca4e02144fb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05786651000380516, "Height": 0.012360181659460068, "Left": 0.27800869941711426, "Top": 0.42971271276474}, "Polygon": [{"X": 0.2781681418418884, "Y": 0.42971271276474}, {"X": 0.3358752131462097, "Y": 0.43127503991127014}, {"X": 0.3357115685939789, "Y": 0.44207289814949036}, {"X": 0.27800869941711426, "Y": 0.4405185580253601}]}, "Id": "5059d571-6e55-4b4f-b3d9-7fd6a562d205"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "478.71", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06308291852474213, "Height": 0.013173331506550312, "Left": 0.42661911249160767, "Top": 0.43137645721435547}, "Polygon": [{"X": 0.4268002510070801, "Y": 0.43137645721435547}, {"X": 0.489702045917511, "Y": 0.4330812692642212}, {"X": 0.48951607942581177, "Y": 0.4445497989654541}, {"X": 0.42661911249160767, "Y": 0.4428541958332062}]}, "Id": "e11118a1-98c4-48f4-a7d4-49e490278eaa"}, {"BlockType": "WORD", "Confidence": 99.71520233154297, "Text": "NBS/KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09110381454229355, "Height": 0.015783553943037987, "Left": 0.2785523533821106, "Top": 0.46197208762168884}, "Polygon": [{"X": 0.2787497937679291, "Y": 0.46197208762168884}, {"X": 0.36965617537498474, "Y": 0.4643963575363159}, {"X": 0.36945056915283203, "Y": 0.47775566577911377}, {"X": 0.2785523533821106, "Y": 0.****************}]}, "Id": "05af7076-dc83-4a8e-bf4b-2259e37a7be4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04484197869896889, "Height": 0.012129193171858788, "Left": 0.3801209032535553, "Top": 0.46421995759010315}, "Polygon": [{"X": 0.380290150642395, "Y": 0.46421995759010315}, {"X": 0.4249628782272339, "Y": 0.46541154384613037}, {"X": 0.4247903823852539, "Y": 0.4763491451740265}, {"X": 0.3801209032535553, "Y": 0.4751638174057007}]}, "Id": "cc17e7fb-64a4-42a0-889e-f68e77e19cf1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FLEET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054844412952661514, "Height": 0.012901091016829014, "Left": 0.****************, "Top": 0.****************}, "Polygon": [{"X": 0.****************, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.****************}, {"X": 0.****************, "Y": 0.*****************}]}, "Id": "5e4de30d-0f11-440f-b768-192a13601b2d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CARD#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057511650025844574, "Height": 0.012593521736562252, "Left": 0.2782739996910095, "Top": 0.*****************}, "Polygon": [{"X": 0.27843764424324036, "Y": 0.*****************}, {"X": 0.3357856571674347, "Y": 0.****************}, {"X": 0.3356177508831024, "Y": 0.****************}, {"X": 0.2782739996910095, "Y": 0.****************}]}, "Id": "852209f6-fea4-4628-97ab-f79344f7dbc9"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "ICR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031170999631285667, "Height": 0.011728459969162941, "Left": 0.34890642762184143, "Top": 0.*****************}, "Polygon": [{"X": 0.3490728437900543, "Y": 0.*****************}, {"X": 0.38007742166519165, "Y": 0.*****************}, {"X": 0.3799087405204773, "Y": 0.*****************}, {"X": 0.34890642762184143, "Y": 0.****************}]}, "Id": "7ad659f6-f159-460a-b6e5-cc3834597b03"}, {"BlockType": "WORD", "Confidence": 15.152114868164062, "Text": "XXXXXXXXXXXXXX1716", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2029736042022705, "Height": 0.01910027116537094, "Left": 0.2770345211029053, "Top": 0.*****************}, "Polygon": [{"X": 0.27723824977874756, "Y": 0.*****************}, {"X": 0.****************, "Y": 0.*****************}, {"X": 0.*****************, "Y": 0.****************}, {"X": 0.2770345211029053, "Y": 0.506790280342102}]}, "Id": "ae3309d9-8b01-4c45-947e-ed2c7a04f7fd"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045264095067977905, "Height": 0.012605622410774231, "Left": 0.2774169147014618, "Top": 0.5120922327041626}, "Polygon": [{"X": 0.2775857448577881, "Y": 0.5120922327041626}, {"X": 0.3226810097694397, "Y": 0.513266384601593}, {"X": 0.3225087523460388, "Y": 0.5246978402137756}, {"X": 0.2774169147014618, "Y": 0.5235302448272705}]}, "Id": "900b2e5e-bd18-475c-9bf6-2352bf2f245a"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012110444717109203, "Height": 0.01078086532652378, "Left": 0.3339571952819824, "Top": 0.5132762789726257}, "Polygon": [{"X": 0.3341158330440521, "Y": 0.5132762789726257}, {"X": 0.3460676372051239, "Y": 0.513587474822998}, {"X": 0.34590813517570496, "Y": 0.5240570902824402}, {"X": 0.3339571952819824, "Y": 0.5237475037574768}]}, "Id": "0de7c495-d96e-4bf2-9250-8b71998e913f"}, {"BlockType": "WORD", "Confidence": 99.61276245117188, "Text": "P20276", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06553672254085541, "Height": 0.01314892340451479, "Left": 0.4360447824001312, "Top": 0.5133455395698547}, "Polygon": [{"X": 0.43622639775276184, "Y": 0.5133455395698547}, {"X": 0.5015814900398254, "Y": 0.5150495767593384}, {"X": 0.5013948678970337, "Y": 0.5264944434165955}, {"X": 0.4360447824001312, "Y": 0.5248000025749207}]}, "Id": "67def819-23be-4e8f-97ee-84c3942dc57c"}, {"BlockType": "WORD", "Confidence": 99.35247802734375, "Text": "RESPONSE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09809166193008423, "Height": 0.014428773894906044, "Left": 0.2776530385017395, "Top": 0.5278162360191345}, "Polygon": [{"X": 0.2778289318084717, "Y": 0.5278162360191345}, {"X": 0.3757447302341461, "Y": 0.5303463339805603}, {"X": 0.375560998916626, "Y": 0.5422449707984924}, {"X": 0.2776530385017395, "Y": 0.5397297739982605}]}, "Id": "3105a1ae-8cc2-45c4-be97-31bb1225eb52"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08826638013124466, "Height": 0.013835202902555466, "Left": 0.3905954360961914, "Top": 0.5288623571395874}, "Polygon": [{"X": 0.39077529311180115, "Y": 0.5288623571395874}, {"X": 0.47886183857917786, "Y": 0.5311405658721924}, {"X": 0.47867515683174133, "Y": 0.5426976084709167}, {"X": 0.3905954360961914, "Y": 0.5404323935508728}]}, "Id": "6158e740-7891-4e38-b328-02a0ce54bcfb"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04500817507505417, "Height": 0.012757636606693268, "Left": 0.2882300615310669, "Top": 0.5449534058570862}, "Polygon": [{"X": 0.28840240836143494, "Y": 0.5449534058570862}, {"X": 0.33323824405670166, "Y": 0.5461024045944214}, {"X": 0.33306241035461426, "Y": 0.5577110648155212}, {"X": 0.2882300615310669, "Y": 0.5565686821937561}]}, "Id": "f35554f9-5a17-4b88-9f46-71c0c7ee790c"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "REF#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05209978297352791, "Height": 0.012354681268334389, "Left": 0.34565943479537964, "Top": 0.5454901456832886}, "Polygon": [{"X": 0.34582749009132385, "Y": 0.5454901456832886}, {"X": 0.39775922894477844, "Y": 0.5468215942382812}, {"X": 0.3975873291492462, "Y": 0.5578448176383972}, {"X": 0.34565943479537964, "Y": 0.5565206408500671}]}, "Id": "f11e4286-7ad1-4c07-9b0b-468479fbcb5a"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "P20276", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06614235788583755, "Height": 0.013036654330790043, "Left": 0.4126293659210205, "Top": 0.5457435846328735}, "Polygon": [{"X": 0.4128076136112213, "Y": 0.5457435846328735}, {"X": 0.47877174615859985, "Y": 0.5474360585212708}, {"X": 0.4785884916782379, "Y": 0.5587802529335022}, {"X": 0.4126293659210205, "Y": 0.557097315788269}]}, "Id": "83ef3c40-deba-4305-b062-afaf5c520618"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04507599025964737, "Height": 0.012900995090603828, "Left": 0.2756343483924866, "Top": 0.5776815414428711}, "Polygon": [{"X": 0.27580806612968445, "Y": 0.5776815414428711}, {"X": 0.32071033120155334, "Y": 0.5788136124610901}, {"X": 0.32053303718566895, "Y": 0.590582549571991}, {"X": 0.2756343483924866, "Y": 0.5894572138786316}]}, "Id": "a3ce2f4b-efcb-4dd0-9a8a-7cc80cd008e0"}, {"BlockType": "WORD", "Confidence": 99.41824340820312, "Text": "REWARDS/LOYALTY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16767118871212006, "Height": 0.019046420231461525, "Left": 0.332444429397583, "Top": 0.5755425691604614}, "Polygon": [{"X": 0.33266934752464294, "Y": 0.5755425691604614}, {"X": 0.5001156330108643, "Y": 0.5797717571258545}, {"X": 0.****************, "Y": 0.5945889949798584}, {"X": 0.332444429397583, "Y": 0.5903915762901306}]}, "Id": "ff4de137-5a55-4302-8c3d-35f0e8009bd9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CARD#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05702127516269684, "Height": 0.013188662938773632, "Left": 0.27529942989349365, "Top": 0.5944444537162781}, "Polygon": [{"X": 0.2754731774330139, "Y": 0.5944444537162781}, {"X": 0.3323207199573517, "Y": 0.5958656668663025}, {"X": 0.3321424722671509, "Y": 0.607633113861084}, {"X": 0.27529942989349365, "Y": 0.6062204241752625}]}, "Id": "f9dc96cf-0314-441d-8ec1-3f63e505bc8f"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "ICR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03141691908240318, "Height": 0.011623227037489414, "Left": 0.34547102451324463, "Top": 0.5954269766807556}, "Polygon": [{"X": 0.3456362783908844, "Y": 0.5954269766807556}, {"X": 0.3768879473209381, "Y": 0.5962086319923401}, {"X": 0.3767203986644745, "Y": 0.6070502400398254}, {"X": 0.34547102451324463, "Y": 0.6062729358673096}]}, "Id": "691fc040-ee8a-4d13-b21f-77cfdc50c614"}, {"BlockType": "WORD", "Confidence": 87.18849182128906, "Text": "XXXXXXXXXXXX6391", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17831552028656006, "Height": 0.019157525151968002, "Left": 0.2745996117591858, "Top": 0.6085521578788757}, "Polygon": [{"X": 0.27481746673583984, "Y": 0.6085521578788757}, {"X": 0.*****************, "Y": 0.6129730939865112}, {"X": 0.****************, "Y": 0.627709686756134}, {"X": 0.2745996117591858, "Y": 0.6233221888542175}]}, "Id": "8d7240b6-5216-4197-be9a-da5d8ad560a5"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04509676620364189, "Height": 0.012732376344501972, "Left": 0.27405181527137756, "Top": 0.6280820965766907}, "Polygon": [{"X": 0.2742233872413635, "Y": 0.6280820965766907}, {"X": 0.31914857029914856, "Y": 0.6291862726211548}, {"X": 0.31897345185279846, "Y": 0.640814483165741}, {"X": 0.27405181527137756, "Y": 0.6397169828414917}]}, "Id": "cba80504-35b7-455b-8dbb-67cfd2daa136"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011874893680214882, "Height": 0.011433578096330166, "Left": 0.3304375112056732, "Top": 0.6289927363395691}, "Polygon": [{"X": 0.33060625195503235, "Y": 0.6289927363395691}, {"X": 0.34231239557266235, "Y": 0.6292805075645447}, {"X": 0.3421427607536316, "Y": 0.6404263377189636}, {"X": 0.3304375112056732, "Y": 0.640140175819397}]}, "Id": "e87764d3-8e07-44f5-b185-9a784445bef9"}, {"BlockType": "WORD", "Confidence": 95.095458984375, "Text": "U:4395515", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09981165081262589, "Height": 0.014054574072360992, "Left": 0.3991587162017822, "Top": 0.6280974745750427}, "Polygon": [{"X": 0.39934009313583374, "Y": 0.6280974745750427}, {"X": 0.4989703893661499, "Y": 0.630549967288971}, {"X": 0.4987812340259552, "Y": 0.6421520113945007}, {"X": 0.3991587162017822, "Y": 0.6397143006324768}]}, "Id": "a18b6a41-8ca4-40f2-ba3d-272492aa6021"}, {"BlockType": "WORD", "Confidence": 98.92777252197266, "Text": "RESPONSE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09812457859516144, "Height": 0.014504678547382355, "Left": 0.2738610506057739, "Top": 0.644151508808136}, "Polygon": [{"X": 0.27403998374938965, "Y": 0.644151508808136}, {"X": 0.37198564410209656, "Y": 0.646539032459259}, {"X": 0.37179872393608093, "Y": 0.6586562395095825}, {"X": 0.2738610506057739, "Y": 0.6562838554382324}]}, "Id": "fd1063fb-4184-47c3-a404-7c85ec12e483"}, {"BlockType": "WORD", "Confidence": 96.16370391845703, "Text": "APPROVED,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09904354810714722, "Height": 0.014955931343138218, "Left": 0.2842485010623932, "Top": 0.6613284349441528}, "Polygon": [{"X": 0.2844350039958954, "Y": 0.6613284349441528}, {"X": 0.3832920491695404, "Y": 0.6637170910835266}, {"X": 0.38309720158576965, "Y": 0.6762843728065491}, {"X": 0.2842485010623932, "Y": 0.6739115715026855}]}, "Id": "13a37702-f45f-4a80-a395-deafc602767f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055691689252853394, "Height": 0.012488821521401405, "Left": 0.39695924520492554, "Top": 0.6621437668800354}, "Polygon": [{"X": 0.3971332609653473, "Y": 0.6621437668800354}, {"X": 0.45265093445777893, "Y": 0.6634865999221802}, {"X": 0.4524727463722229, "Y": 0.6746326088905334}, {"X": 0.39695924520492554, "Y": 0.6732977032661438}]}, "Id": "62ebac15-e3ab-4da7-a726-e1d5c6d3a833"}, {"BlockType": "WORD", "Confidence": 99.19303894042969, "Text": "K<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042929768562316895, "Height": 0.012320997193455696, "Left": 0.28439876437187195, "Top": 0.677897572517395}, "Polygon": [{"X": 0.28456634283065796, "Y": 0.677897572517395}, {"X": 0.32732856273651123, "Y": 0.6789219379425049}, {"X": 0.32715773582458496, "Y": 0.6902185678482056}, {"X": 0.28439876437187195, "Y": 0.6892004013061523}]}, "Id": "161704ff-7d6a-4658-87ab-32ce5a090480"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Rewards", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07785393297672272, "Height": 0.013192630372941494, "Left": 0.34098318219184875, "Top": 0.6783376336097717}, "Polygon": [{"X": 0.3411557376384735, "Y": 0.6783376336097717}, {"X": 0.4188371002674103, "Y": 0.680199384689331}, {"X": 0.418658584356308, "Y": 0.6915302872657776}, {"X": 0.34098318219184875, "Y": 0.6896798014640808}]}, "Id": "60b6f23a-c493-4a3a-aadc-58ec605f2658"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Fleet", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05501727759838104, "Height": 0.012505261227488518, "Left": 0.4311768710613251, "Top": 0.6792413592338562}, "Polygon": [{"X": 0.43135425448417664, "Y": 0.6792413592338562}, {"X": 0.4861941635608673, "Y": 0.6805565357208252}, {"X": 0.48601263761520386, "Y": 0.6917466521263123}, {"X": 0.4311768710613251, "Y": 0.6904393434524536}]}, "Id": "153081fe-b272-49f0-b26e-3ae8a3e677d1"}, {"BlockType": "WORD", "Confidence": 99.27275848388672, "Text": "Points:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07567606121301651, "Height": 0.013679133728146553, "Left": 0.28357619047164917, "Top": 0.6942106485366821}, "Polygon": [{"X": 0.28375253081321716, "Y": 0.6942106485366821}, {"X": 0.35925227403640747, "Y": 0.6960036754608154}, {"X": 0.3590698838233948, "Y": 0.7078897953033447}, {"X": 0.28357619047164917, "Y": 0.7061081528663635}]}, "Id": "a744e933-c771-4333-ac92-caa8c9b3086c"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "$7.23", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05580831319093704, "Height": 0.014316871762275696, "Left": 0.3742581903934479, "Top": 0.6951509118080139}, "Polygon": [{"X": 0.37445908784866333, "Y": 0.6951509118080139}, {"X": 0.****************, "Y": 0.6964724063873291}, {"X": 0.4298607409000397, "Y": 0.7094678282737732}, {"X": 0.3742581903934479, "Y": 0.7081555724143982}]}, "Id": "d48af346-c4a2-4766-b6aa-36025e55f519"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Shower", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06632430106401443, "Height": 0.012198829092085361, "Left": 0.28325483202934265, "Top": 0.7122694849967957}, "Polygon": [{"X": 0.2834126949310303, "Y": 0.7122694849967957}, {"X": 0.3495791256427765, "Y": 0.7138258218765259}, {"X": 0.3494165241718292, "Y": 0.7244682908058167}, {"X": 0.28325483202934265, "Y": 0.7229209542274475}]}, "Id": "f1bbd189-a16f-4b3b-9e10-942b3313e37f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "credit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06743276864290237, "Height": 0.013445576652884483, "Left": 0.3620850443840027, "Top": 0.7118624448776245}, "Polygon": [{"X": 0.36226746439933777, "Y": 0.7118624448776245}, {"X": 0.*****************, "Y": 0.7134461998939514}, {"X": 0.****************, "Y": 0.7253080010414124}, {"X": 0.3620850443840027, "Y": 0.723734438419342}]}, "Id": "1caf4446-4a6c-40af-be35-fdc86b8a4a5a"}, {"BlockType": "WORD", "Confidence": 97.63851165771484, "Text": "earned!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07584226131439209, "Height": 0.013457164168357849, "Left": 0.2827603816986084, "Top": 0.7279475927352905}, "Polygon": [{"X": 0.2829338014125824, "Y": 0.7279475927352905}, {"X": 0.3586026430130005, "Y": 0.7297124862670898}, {"X": 0.35842326283454895, "Y": 0.7414047122001648}, {"X": 0.2827603816986084, "Y": 0.739651083946228}]}, "Id": "3dedea1b-0f79-4a54-8700-4bab0ecc3c01"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Expires", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07803527265787125, "Height": 0.016908185556530952, "Left": 0.37297239899635315, "Top": 0.7286415696144104}, "Polygon": [{"X": 0.3732057213783264, "Y": 0.7286415696144104}, {"X": 0.4510076940059662, "Y": 0.7304576635360718}, {"X": 0.4507664740085602, "Y": 0.7455497980117798}, {"X": 0.37297239899635315, "Y": 0.743748664855957}]}, "Id": "542a0f91-f9e2-47c0-9f25-b41ab7bd0df1"}, {"BlockType": "WORD", "Confidence": 99.73313903808594, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02300458401441574, "Height": 0.012611176818609238, "Left": 0.46258246898651123, "Top": 0.7297860383987427}, "Polygon": [{"X": 0.4627765417098999, "Y": 0.7297860383987427}, {"X": 0.****************, "Y": 0.7303187847137451}, {"X": 0.48539111018180847, "Y": 0.7423972487449646}, {"X": 0.46258246898651123, "Y": 0.7418680191040039}]}, "Id": "baaf54d2-c405-4d8f-a90a-6edfd78ced44"}, {"BlockType": "WORD", "Confidence": 99.79332733154297, "Text": "14", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020817730575799942, "Height": 0.011372921988368034, "Left": 0.2831727862358093, "Top": 0.7457131743431091}, "Polygon": [{"X": 0.28333431482315063, "Y": 0.7457131743431091}, {"X": 0.30399051308631897, "Y": 0.7461903691291809}, {"X": 0.3038274347782135, "Y": 0.7570860981941223}, {"X": 0.2831727862358093, "Y": 0.7566117644309998}]}, "Id": "f4825bab-7f45-49fc-b1bc-e6db4a89f578"}, {"BlockType": "WORD", "Confidence": 99.32318115234375, "Text": "days.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052198756486177444, "Height": 0.015391404740512371, "Left": 0.31645411252975464, "Top": 0.7455760836601257}, "Polygon": [{"X": 0.3166678547859192, "Y": 0.7455760836601257}, {"X": 0.368652880191803, "Y": 0.7467775940895081}, {"X": 0.3684341609477997, "Y": 0.760967493057251}, {"X": 0.31645411252975464, "Y": 0.7597754001617432}]}, "Id": "2cd4739b-1b1f-444b-899a-5f5ba66a6648"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRUCK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05644802376627922, "Height": 0.012562410905957222, "Left": 0.2698817253112793, "Top": 0.7782419323921204}, "Polygon": [{"X": 0.2700481116771698, "Y": 0.7782419323921204}, {"X": 0.3263297379016876, "Y": 0.7795188426971436}, {"X": 0.3261590600013733, "Y": 0.7908043265342712}, {"X": 0.2698817253112793, "Y": 0.7895354628562927}]}, "Id": "b2533dbc-66bd-4274-bea0-98004cf645c7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "NUMBER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06746797263622284, "Height": 0.01239491906017065, "Left": 0.3379962146282196, "Top": 0.7794482111930847}, "Polygon": [{"X": 0.3381616175174713, "Y": 0.7794482111930847}, {"X": 0.40546417236328125, "Y": 0.7809755206108093}, {"X": 0.40529385209083557, "Y": 0.7918431758880615}, {"X": 0.3379962146282196, "Y": 0.7903252243995667}]}, "Id": "79e57eb6-73a4-42a9-9c66-c1cfbd00065d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "02929", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05546736344695091, "Height": 0.011918717995285988, "Left": 0.44033923745155334, "Top": 0.7808005213737488}, "Polygon": [{"X": 0.4405090808868408, "Y": 0.7808005213737488}, {"X": 0.49580660462379456, "Y": 0.7820560336112976}, {"X": 0.4956328272819519, "Y": 0.7927192449569702}, {"X": 0.44033923745155334, "Y": 0.7914712429046631}]}, "Id": "9290d450-d58d-414e-8b4f-58f735375692"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "MERCHANT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08996830135583878, "Height": 0.012955130077898502, "Left": 0.26939019560813904, "Top": 0.7947059869766235}, "Polygon": [{"X": 0.26955148577690125, "Y": 0.7947059869766235}, {"X": 0.3593584895133972, "Y": 0.7967249155044556}, {"X": 0.35919055342674255, "Y": 0.8076611161231995}, {"X": 0.26939019560813904, "Y": 0.8056546449661255}]}, "Id": "64a13ade-69af-4872-93ce-17ad9f24f0c8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "NUMBER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06703711301088333, "Height": 0.01243484765291214, "Left": 0.372154176235199, "Top": 0.7962951064109802}, "Polygon": [{"X": 0.3723231256008148, "Y": 0.7962951064109802}, {"X": 0.4391912817955017, "Y": 0.7977989912033081}, {"X": 0.4390173852443695, "Y": 0.8087299466133118}, {"X": 0.372154176235199, "Y": 0.8072354197502136}]}, "Id": "f0204e35-210f-46f1-a0c6-fba8f69fb783"}, {"BlockType": "WORD", "Confidence": 78.8741683959961, "Text": "*******1306", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12348933517932892, "Height": 0.013591546565294266, "Left": 0.2923128008842468, "Top": 0.8106266856193542}, "Polygon": [{"X": 0.29247453808784485, "Y": 0.8106266856193542}, {"X": 0.41580212116241455, "Y": 0.8133753538131714}, {"X": 0.41563138365745544, "Y": 0.824218213558197}, {"X": 0.2923128008842468, "Y": 0.8214865922927856}]}, "Id": "da42829a-73dc-449f-808d-d4b873ae8ffe"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "ALL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032680027186870575, "Height": 0.011150932870805264, "Left": 0.28177565336227417, "Top": 0.827094316482544}, "Polygon": [{"X": 0.2819303274154663, "Y": 0.827094316482544}, {"X": 0.31445568799972534, "Y": 0.8278123736381531}, {"X": 0.31429868936538696, "Y": 0.8382452130317688}, {"X": 0.28177565336227417, "Y": 0.8375315070152283}]}, "Id": "a29d8485-cb11-46ec-bd64-ae4ebaa8336b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TAXES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056345708668231964, "Height": 0.011967227794229984, "Left": 0.32599303126335144, "Top": 0.8275033235549927}, "Polygon": [{"X": 0.326155424118042, "Y": 0.8275033235549927}, {"X": 0.3823387622833252, "Y": 0.828744113445282}, {"X": 0.3821723163127899, "Y": 0.8394705653190613}, {"X": 0.32599303126335144, "Y": 0.838237464427948}]}, "Id": "ab45da44-5759-4ae4-81e0-96e99450c258"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "INCLUDED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.086939238011837, "Height": 0.012618708424270153, "Left": 0.396264910697937, "Top": 0.8282270431518555}, "Polygon": [{"X": 0.39643219113349915, "Y": 0.8282270431518555}, {"X": 0.4832041561603546, "Y": 0.8301442861557007}, {"X": 0.48303061723709106, "Y": 0.8408457636833191}, {"X": 0.396264910697937, "Y": 0.8389403820037842}]}, "Id": "6e3bc614-f2bf-4a57-8512-6d2a353ec451"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "IN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019695166498422623, "Height": 0.010442627593874931, "Left": 0.30631160736083984, "Top": 0.8430485725402832}, "Polygon": [{"X": 0.30646181106567383, "Y": 0.8430485725402832}, {"X": 0.32600677013397217, "Y": 0.8434762954711914}, {"X": 0.3258552849292755, "Y": 0.8534912467002869}, {"X": 0.30631160736083984, "Y": 0.8530660271644592}]}, "Id": "9ca9c0db-4e6b-4b26-a0f2-5764c34e83c9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FUEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04395810142159462, "Height": 0.011390573345124722, "Left": 0.33823031187057495, "Top": 0.8433101177215576}, "Polygon": [{"X": 0.338389128446579, "Y": 0.8433101177215576}, {"X": 0.3821884095668793, "Y": 0.8442688584327698}, {"X": 0.3820265531539917, "Y": 0.8547006845474243}, {"X": 0.33823031187057495, "Y": 0.8537477850914001}]}, "Id": "53a98156-d759-4a57-95f9-83da1a1650bd"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "PRICE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05424901843070984, "Height": 0.011789748445153236, "Left": 0.39463573694229126, "Top": 0.8439251184463501}, "Polygon": [{"X": 0.3948013484477997, "Y": 0.8439251184463501}, {"X": 0.4488847553730011, "Y": 0.8451093435287476}, {"X": 0.4487152695655823, "Y": 0.8557148575782776}, {"X": 0.39463573694229126, "Y": 0.8545379042625427}]}, "Id": "404df474-bbd1-4664-8105-710dc049d0f2"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "8dd35f65-a335-4c5a-ad65-8ca396a46201", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "8dd35f65-a335-4c5a-ad65-8ca396a46201", "content-type": "application/x-amz-json-1.1", "content-length": "63761", "date": "Thu, 18 Sep 2025 08:51:14 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "a4a2b5da-5956-476e-bad3-e6a4682a243e", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 08:51:16 GMT", "content-type": "application/json", "content-length": "1195", "connection": "keep-alive", "x-amzn-requestid": "a4a2b5da-5956-476e-bad3-e6a4682a243e"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "The user provided a single page content. We need to classify the document type. It looks like a fuel receipt from Kwik Trip. Keywords: \"Invoice# 0096563\". \"Pump Quantity Price\". \"Product Amount\". \"Kwik Trip\". This seems like a fuel receipt. There's no explicit header. The classification \"fuel_receipt\" seems appropriate. Check definitions: fuel receipt: receipt for fuel purchase. Keywords: Fuel, Diesel, Pump, Gallons, etc. This page shows fuel purchase: pump quantity, price. So classify as fuel_receipt. There's no continuation. So output one page object.\n\nWe need to output via the function call. The function requires array of documents with page_no and doc_type. We have page_no 1. So call function with documents array containing {page_no:1, doc_type:\"fuel_receipt\"}."}}}, {"toolUse": {"toolUseId": "tooluse_dVFJIwliSsu72cVzLJSOrg", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2330, "outputTokens": 214, "totalTokens": 2544}, "metrics": {"latencyMs": 989}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/970b9b3f_9_fuel_receipt_4.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nKWIK TRIP #1130\n9255 RIDGEVIEW RD\nMT HOREB, WI\nInvoice# 0096563\nTrans # 0900-6563\nDate\n08/14/2025\nTime\n12:53 PM\nPump Quantity Price\n02 133.011 3.599\nProduct\nAmount\nDSL2\n478.71\nTOTAL\n478.71\nNBS/KWIK TRIP FLEET\nCARD# ICR\nXXXXXXXXXXXXXX1716\nAUTH #\nP20276\nRESPONSE: APPROVED\nAUTH REF#: P20276\nKWIK REWARDS/LOYALTY\nCARD# ICR\nXXXXXXXXXXXX6391\nAUTH #\nU:4395515\nRESPONSE:\nAPPROVED, Total\nKwik Rewards Fleet\nPoints: $7.23\nShower credit\nearned! Expires in\n14 days.\nTRUCK NUMBER 02929\nMERCHANT NUMBER\n*******1306\nALL TAXES INCLUDED\nIN FUEL PRICE\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}