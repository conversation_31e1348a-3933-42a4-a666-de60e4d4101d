{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.94500732421875, "Height": 0.9750085473060608, "Left": 0.006367251742631197, "Top": 0.0}, "Polygon": [{"X": 0.9416237473487854, "Y": 0.0}, {"X": 0.9513745903968811, "Y": 0.9750085473060608}, {"X": 0.0863228365778923, "Y": 0.9384060502052307}, {"X": 0.006367251742631197, "Y": 0.0}]}, "Id": "e536408c-dbbf-4f76-a8da-e312745f3b80", "Relationships": [{"Type": "CHILD", "Ids": ["528b3844-155d-4aa1-958d-205be2c818ba", "fe890246-ac6b-488a-9fcb-3d0a5bf3b19e", "f0423847-7185-4d11-97c6-3fd819ddf2dd", "dd715875-3469-429a-9d9c-d161d0c5a326", "3f08d450-6281-43c9-a921-864bc487d60e", "577d427c-b9f6-423a-b075-9a5cf9eef8ca", "15498ddf-d3ce-4791-a852-db595609c33b", "1741fa57-4d0a-49ae-9633-751b79337bff", "74132b85-408a-42bf-8bae-2d7047bd1f44", "9bbe78d5-f50a-4809-90ef-ab073bae8d51", "5e8c57da-34cd-492b-b174-5aa564d38964", "a2f50ea1-3d23-4f4f-9ebd-153d31c50ffd", "b6db1f81-cb91-4f8d-ab6f-239e681ca62f", "35593502-46ab-4da6-b5f4-a8392f731fd1", "842d11d5-1e27-486e-9e72-5db8de55391e", "3b7c8b85-af65-419c-a83b-f37efddb2d0c", "5e544dd1-9ba3-4cb6-9ffb-40e836a32a6a", "ebe4ad63-980d-465d-a1b8-4e6bbad8d64d", "fa2b8f51-6187-4b70-8d46-2b167653fd0b", "80705f32-0cd3-4172-964f-1c220d338017", "6be05cff-5665-4510-bb8a-6fec1c5d8443", "0b68b952-c3f6-485c-a4f7-435c55654ca4", "fe25642c-59b7-4d5d-871f-f15a9201cf6e", "e87f6b36-0c43-4bb8-8a14-694180cb5d28", "a9f1e95d-94f2-43ee-b7d4-e506fe9f02b5", "e23f0aa8-11d4-4f83-bbdf-df2ccf098834", "8918b175-d67e-46c6-92a3-9e6f513577a1", "4ce80e5e-9986-48dd-85b6-ae41db3d6914", "6637f1d6-c1ce-4514-9e34-cee5ba4ad282", "2fd974c7-5214-47af-9e4c-0ca1c2fd4aca", "44181267-1e69-416f-b055-8fe52af040c4", "d5ae5b9b-e4f3-4218-8bea-8d751da8d21a", "431ca536-250d-42f3-8f4b-001d353ebfd4", "841d7973-5d04-4061-9917-476901c8f5ed", "c8eca73e-4aaa-4dc1-86b0-16329b5dc551", "e83525d2-07bd-4f8d-acbf-187baec28d25", "d0b6db94-640e-452c-bf6b-9e286bdc4f86", "c752b126-0a24-4b2d-af24-b4b627ad14e5", "596ab12a-19f5-463d-a203-ecf113424031", "3b71d7fd-8675-47c3-81b3-7403aa36e0f2", "8205b6c7-0453-454d-83e5-79cda37b8185"]}]}, {"BlockType": "LINE", "Confidence": 81.62238311767578, "Text": "******* (DUP) ICATE RECEIPT) *******", "Geometry": {"BoundingBox": {"Width": 0.03635147958993912, "Height": 0.5879443883895874, "Left": 0.8585858345031738, "Top": 0.18042559921741486}, "Polygon": [{"X": 0.8863243460655212, "Y": 0.18062880635261536}, {"X": 0.8949373364448547, "Y": 0.7683699727058411}, {"X": 0.8685175776481628, "Y": 0.7674905061721802}, {"X": 0.8585858345031738, "Y": 0.18042559921741486}]}, "Id": "528b3844-155d-4aa1-958d-205be2c818ba", "Relationships": [{"Type": "CHILD", "Ids": ["5ef24f25-5fc2-454d-8f32-c65be14a5e0d", "69937ca6-2792-426d-800d-a72725d3b0b0", "d5c6d878-beb9-4618-a3a4-33015d6c3726", "f4ae483c-5fa3-423a-93e6-1424cf7cf44f", "82c23669-9505-4f36-b322-d605acf360d9"]}]}, {"BlockType": "LINE", "Confidence": 92.84906005859375, "Text": "WIK TRIP 1281 /106 N. ROYAL AVENUE /BELGIUM. WI 53004/(262) 988-7024", "Geometry": {"BoundingBox": {"Width": 0.044332120567560196, "Height": 0.825235903263092, "Left": 0.824474573135376, "Top": 0.0004490015562623739}, "Polygon": [{"X": 0.8547962307929993, "Y": 0.0004490015562623739}, {"X": 0.8688066601753235, "Y": 0.8256849050521851}, {"X": 0.8404771685600281, "Y": 0.8246689438819885}, {"X": 0.824474573135376, "Y": 0.0004683601437136531}]}, "Id": "fe890246-ac6b-488a-9fcb-3d0a5bf3b19e", "Relationships": [{"Type": "CHILD", "Ids": ["493ef833-8823-445e-9975-5ed8b65eb98f", "5d2116b5-0184-4042-90d6-f35fd17c046a", "80bf2607-adc2-4144-9f3f-4cfe9158ff3c", "ebe1c48a-79ac-4b77-85fe-ae3738627fc7", "f5885c99-04cd-45a1-9dc7-72fb6ac7cdeb", "8ffcbf29-428b-4361-b405-4bf834d2009b", "edd8f3b8-4109-4198-aa5f-5919fcdd9c27", "10da40ea-7111-44f9-a509-c43e85a9b79a", "9f9e6185-8993-4807-b79c-3badd9f1ad01", "a07da95b-8b11-4067-a9a3-f8cd75c4eac4", "5d2959b9-a435-4eaa-b71c-2cfc6163265f"]}]}, {"BlockType": "LINE", "Confidence": 98.90756225585938, "Text": "umber: 0900-9604 08/04/2025 Station: 9", "Geometry": {"BoundingBox": {"Width": 0.0325562059879303, "Height": 0.4897702634334564, "Left": 0.8018145561218262, "Top": 0.0014421410160139203}, "Polygon": [{"X": 0.8248764872550964, "Y": 0.0014421410160139203}, {"X": 0.8343707919120789, "Y": 0.4912124276161194}, {"X": 0.8122069835662842, "Y": 0.4907447397708893}, {"X": 0.8018145561218262, "Y": 0.0014558692928403616}]}, "Id": "f0423847-7185-4d11-97c6-3fd819ddf2dd", "Relationships": [{"Type": "CHILD", "Ids": ["8b2ee4c8-0523-41fb-b86e-01a558e1a80e", "7e88bf05-db1c-4b42-8ca4-fedea91dc4bd", "98f51cbb-dd0c-4848-98b0-cd6bc30084d1", "45cc48b8-2993-4f66-b24f-faa5c0536ec5", "b1455375-150d-4195-a4c0-03aee4231cdf"]}]}, {"BlockType": "LINE", "Confidence": 99.4541244506836, "Text": "esponse: APPROVED AUTH REF#: P54220", "Geometry": {"BoundingBox": {"Width": 0.03300108388066292, "Height": 0.4289412796497345, "Left": 0.7437043786048889, "Top": 0.0013437950983643532}, "Polygon": [{"X": 0.7663722038269043, "Y": 0.0013437950983643532}, {"X": 0.7767054438591003, "Y": 0.43028509616851807}, {"X": 0.7548087239265442, "Y": 0.42988112568855286}, {"X": 0.7437043786048889, "Y": 0.001357422792352736}]}, "Id": "dd715875-3469-429a-9d9c-d161d0c5a326", "Relationships": [{"Type": "CHILD", "Ids": ["d037b556-18b3-4bef-bd41-a376c0b37191", "d4d9ef35-4b25-407f-8f8f-5cf08fd5a21e", "d14bd79b-92af-4aee-939e-32b6e79b23d5", "916569d5-7084-4691-af63-956b364f9861", "f03b83e6-e34c-418c-a2eb-e85f019439cf"]}]}, {"BlockType": "LINE", "Confidence": 96.95042419433594, "Text": "OYALTY Response: APPROVED. Total Kwik Rewards Fleet Points: $27.07 Shower credi", "Geometry": {"BoundingBox": {"Width": 0.06192180886864662, "Height": 0.9373138546943665, "Left": 0.6749889850616455, "Top": 0.002655978547409177}, "Polygon": [{"X": 0.7100849747657776, "Y": 0.002655978547409177}, {"X": 0.7369108200073242, "Y": 0.9399698376655579}, {"X": 0.7044181823730469, "Y": 0.9386325478553772}, {"X": 0.6749889850616455, "Y": 0.002675079507753253}]}, "Id": "3f08d450-6281-43c9-a921-864bc487d60e", "Relationships": [{"Type": "CHILD", "Ids": ["b2c72073-25b6-4dbf-ba4b-0899d8f1a7ed", "d2eef9ea-eaf6-4908-9b99-478bc8cb2e71", "29c91b3a-495f-438c-a2c9-6d80a3b85af2", "1692e512-d741-4ec3-874c-b01515d3571e", "2d43fc27-03d2-4593-b3b9-e74f90034232", "35a0ab30-d8c3-425f-a8d5-71c8112a528b", "03ca8834-b278-44e7-8d56-739c3a8e0c2d", "4a86b473-2594-4246-974f-aed8cf8efba0", "8281896e-f8b3-4fdf-9585-871f9a4d1c31", "03299dd4-2857-42f0-b332-a5c3bcee483c", "ec426091-e699-4b67-ac6d-fe8a4f565681"]}]}, {"BlockType": "LINE", "Confidence": 92.55906677246094, "Text": "earned! Expires in 14 davs", "Geometry": {"BoundingBox": {"Width": 0.03178298845887184, "Height": 0.3170047998428345, "Left": 0.6594950556755066, "Top": 0.01617884263396263}, "Polygon": [{"X": 0.6814649701118469, "Y": 0.016180122271180153}, {"X": 0.6912780404090881, "Y": 0.3331836462020874}, {"X": 0.6698589324951172, "Y": 0.3328796923160553}, {"X": 0.6594950556755066, "Y": 0.01617884263396263}]}, "Id": "577d427c-b9f6-423a-b075-9a5cf9eef8ca", "Relationships": [{"Type": "CHILD", "Ids": ["19245b5e-c361-4ebf-b3e2-56990bc35723", "bb05fbd7-8b3e-48d4-8687-73d4c1a6ee71", "b1af8e71-07b1-44c7-94d3-bbae1d6f632b", "f001849e-1d5b-4992-b031-2eb3246efdbc", "10ab341c-852d-4823-8e95-3c29404a2b0b"]}]}, {"BlockType": "LINE", "Confidence": 32.001556396484375, "Text": "clun,", "Geometry": {"BoundingBox": {"Width": 0.018062399700284004, "Height": 0.040958043187856674, "Left": 0.6078760623931885, "Top": 0.002941589104011655}, "Polygon": [{"X": 0.6244842410087585, "Y": 0.002941589104011655}, {"X": 0.6259384751319885, "Y": 0.04389963299036026}, {"X": 0.6093839406967163, "Y": 0.04387814924120903}, {"X": 0.6078760623931885, "Y": 0.002950450638309121}]}, "Id": "15498ddf-d3ce-4791-a852-db595609c33b", "Relationships": [{"Type": "CHILD", "Ids": ["f9d8d24f-4d7b-4b1b-a568-f99a69b63833"]}]}, {"BlockType": "LINE", "Confidence": 99.76243591308594, "Text": "Products", "Geometry": {"BoundingBox": {"Width": 0.02343807928264141, "Height": 0.0987871065735817, "Left": 0.6114155054092407, "Top": 0.23621799051761627}, "Polygon": [{"X": 0.6313414573669434, "Y": 0.2364153116941452}, {"X": 0.6348535418510437, "Y": 0.33500510454177856}, {"X": 0.6150853633880615, "Y": 0.33472225069999695}, {"X": 0.6114155054092407, "Y": 0.23621799051761627}]}, "Id": "1741fa57-4d0a-49ae-9633-751b79337bff", "Relationships": [{"Type": "CHILD", "Ids": ["152f8d1f-3e5c-4130-9417-3f46f5989fda"]}]}, {"BlockType": "LINE", "Confidence": 99.77379608154297, "Text": "Reefer", "Geometry": {"BoundingBox": {"Width": 0.021255087107419968, "Height": 0.07099785655736923, "Left": 0.6146841645240784, "Top": 0.3721995949745178}, "Polygon": [{"X": 0.6334044337272644, "Y": 0.37249884009361267}, {"X": 0.6359392404556274, "Y": 0.44319745898246765}, {"X": 0.6173264980316162, "Y": 0.44284117221832275}, {"X": 0.6146841645240784, "Y": 0.3721995949745178}]}, "Id": "74132b85-408a-42bf-8bae-2d7047bd1f44", "Relationships": [{"Type": "CHILD", "Ids": ["e3385947-77ba-4d04-b7a6-15d3d47e979d"]}]}, {"BlockType": "LINE", "Confidence": 94.94339752197266, "Text": "Quantity", "Geometry": {"BoundingBox": {"Width": 0.023336876183748245, "Height": 0.09521082788705826, "Left": 0.6151654720306396, "Top": 0.48175248503685}, "Polygon": [{"X": 0.6350857019424438, "Y": 0.4821685254573822}, {"X": 0.6385023593902588, "Y": 0.5769633054733276}, {"X": 0.6187369227409363, "Y": 0.5764668583869934}, {"X": 0.6151654720306396, "Y": 0.48175248503685}]}, "Id": "9bbe78d5-f50a-4809-90ef-ab073bae8d51", "Relationships": [{"Type": "CHILD", "Ids": ["e985f998-6e8d-4cb7-bd04-639796529c1b"]}]}, {"BlockType": "LINE", "Confidence": 99.90154266357422, "Text": "Unit Cost", "Geometry": {"BoundingBox": {"Width": 0.022985942661762238, "Height": 0.10511425137519836, "Left": 0.6184707283973694, "Top": 0.6012048721313477}, "Polygon": [{"X": 0.6376709938049316, "Y": 0.6017083525657654}, {"X": 0.641456663608551, "Y": 0.7063190937042236}, {"X": 0.6224226951599121, "Y": 0.7057310938835144}, {"X": 0.6184707283973694, "Y": 0.6012048721313477}]}, "Id": "5e8c57da-34cd-492b-b174-5aa564d38964", "Relationships": [{"Type": "CHILD", "Ids": ["fe14174d-7ded-4695-8235-5496a6b843a0", "54eee6c1-0ef9-4098-8899-91b879270453"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.019862957298755646, "Height": 0.05325392633676529, "Left": 0.6229977607727051, "Top": 0.7753885984420776}, "Polygon": [{"X": 0.6409419178962708, "Y": 0.7759988307952881}, {"X": 0.6428607106208801, "Y": 0.8286424875259399}, {"X": 0.6249959468841553, "Y": 0.8279930353164673}, {"X": 0.6229977607727051, "Y": 0.7753885984420776}]}, "Id": "a2f50ea1-3d23-4f4f-9ebd-153d31c50ffd", "Relationships": [{"Type": "CHILD", "Ids": ["a8596436-36df-4b82-9fdd-c98397c6703e"]}]}, {"BlockType": "LINE", "Confidence": 46.687461853027344, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.017601842060685158, "Height": 0.00892448890954256, "Left": 0.5825130939483643, "Top": 0.02184722013771534}, "Polygon": [{"X": 0.5997800230979919, "Y": 0.021852614358067513}, {"X": 0.6001149415969849, "Y": 0.030771709978580475}, {"X": 0.5828601121902466, "Y": 0.030759431421756744}, {"X": 0.5825130939483643, "Y": 0.02184722013771534}]}, "Id": "b6db1f81-cb91-4f8d-ab6f-239e681ca62f", "Relationships": [{"Type": "CHILD", "Ids": ["814d1ba9-a29e-455f-80dc-f556030ded18"]}]}, {"BlockType": "LINE", "Confidence": 98.896484375, "Text": "Diesel 2", "Geometry": {"BoundingBox": {"Width": 0.02356801927089691, "Height": 0.09826973080635071, "Left": 0.5838730931282043, "Top": 0.2376871556043625}, "Polygon": [{"X": 0.603725016117096, "Y": 0.23788529634475708}, {"X": 0.6074411273002625, "Y": 0.335956871509552}, {"X": 0.5877453684806824, "Y": 0.33567389845848083}, {"X": 0.5838730931282043, "Y": 0.2376871556043625}]}, "Id": "35593502-46ab-4da6-b5f4-a8392f731fd1", "Relationships": [{"Type": "CHILD", "Ids": ["a6956ad1-956d-40bd-a3c4-4d3b84f48241", "a4df247c-c4c1-4398-9318-8fd710d59cf1"]}]}, {"BlockType": "LINE", "Confidence": 99.75426483154297, "Text": "N", "Geometry": {"BoundingBox": {"Width": 0.01746189221739769, "Height": 0.011608586646616459, "Left": 0.5889094471931458, "Top": 0.3975532054901123}, "Polygon": [{"X": 0.6059389114379883, "Y": 0.397845059633255}, {"X": 0.6063713431358337, "Y": 0.409161776304245}, {"X": 0.5893575549125671, "Y": 0.40886157751083374}, {"X": 0.5889094471931458, "Y": 0.3975532054901123}]}, "Id": "842d11d5-1e27-486e-9e72-5db8de55391e", "Relationships": [{"Type": "CHILD", "Ids": ["eb73d05c-e6fc-4dcb-90db-bf0dd8ba9bbe"]}]}, {"BlockType": "LINE", "Confidence": 99.45731353759766, "Text": "68.665", "Geometry": {"BoundingBox": {"Width": 0.021002618595957756, "Height": 0.07104797661304474, "Left": 0.5902341604232788, "Top": 0.5065358281135559}, "Polygon": [{"X": 0.6085280179977417, "Y": 0.5069385766983032}, {"X": 0.6112367510795593, "Y": 0.5775837898254395}, {"X": 0.5930489897727966, "Y": 0.5771259069442749}, {"X": 0.5902341604232788, "Y": 0.5065358281135559}]}, "Id": "3b7c8b85-af65-419c-a83b-f37efddb2d0c", "Relationships": [{"Type": "CHILD", "Ids": ["d753ec93-1306-4b3c-9e95-0ab845800ee3"]}]}, {"BlockType": "LINE", "Confidence": 99.45651245117188, "Text": "3.499", "Geometry": {"BoundingBox": {"Width": 0.019920630380511284, "Height": 0.056672967970371246, "Left": 0.593967080116272, "Top": 0.6490057110786438}, "Polygon": [{"X": 0.6117231845855713, "Y": 0.649509847164154}, {"X": 0.6138877272605896, "Y": 0.7056786417961121}, {"X": 0.5962144732475281, "Y": 0.7051324844360352}, {"X": 0.593967080116272, "Y": 0.6490057110786438}]}, "Id": "5e544dd1-9ba3-4cb6-9ffb-40e836a32a6a", "Relationships": [{"Type": "CHILD", "Ids": ["b15a5e3e-55d0-4c7a-a82c-d29d5260fe78"]}]}, {"BlockType": "LINE", "Confidence": 99.76164245605469, "Text": "240.26", "Geometry": {"BoundingBox": {"Width": 0.02063179761171341, "Height": 0.06571657210588455, "Left": 0.5963854193687439, "Top": 0.762620210647583}, "Polygon": [{"X": 0.6144991517066956, "Y": 0.7632266283035278}, {"X": 0.6170172095298767, "Y": 0.8283368349075317}, {"X": 0.5990024209022522, "Y": 0.8276813626289368}, {"X": 0.5963854193687439, "Y": 0.762620210647583}]}, "Id": "ebe4ad63-980d-465d-a1b8-4e6bbad8d64d", "Relationships": [{"Type": "CHILD", "Ids": ["7fbe2b72-d1b6-45de-80d6-d4045d589609"]}]}, {"BlockType": "LINE", "Confidence": 72.95843505859375, "Text": "# 1", "Geometry": {"BoundingBox": {"Width": 0.01800047792494297, "Height": 0.031134406104683876, "Left": 0.5547758340835571, "Top": 0.0003406285832170397}, "Polygon": [{"X": 0.571538507938385, "Y": 0.0003406285832170397}, {"X": 0.5727763175964355, "Y": 0.03147503361105919}, {"X": 0.5560545921325684, "Y": 0.031462594866752625}, {"X": 0.5547758340835571, "Y": 0.0003515478456392884}]}, "Id": "fa2b8f51-6187-4b70-8d46-2b167653fd0b", "Relationships": [{"Type": "CHILD", "Ids": ["8f1bb451-ed7b-43bb-80df-db4a90e4f4a8", "73f760f8-fec7-44d5-9f22-37d5aaef7fdf"]}]}, {"BlockType": "LINE", "Confidence": 90.00248718261719, "Text": "DFF Bulk", "Geometry": {"BoundingBox": {"Width": 0.023080673068761826, "Height": 0.09641758352518082, "Left": 0.5572395920753479, "Top": 0.23867946863174438}, "Polygon": [{"X": 0.576458752155304, "Y": 0.23887237906455994}, {"X": 0.5803202390670776, "Y": 0.3350970447063446}, {"X": 0.561249315738678, "Y": 0.3348234295845032}, {"X": 0.5572395920753479, "Y": 0.23867946863174438}]}, "Id": "80705f32-0cd3-4172-964f-1c220d338017", "Relationships": [{"Type": "CHILD", "Ids": ["a1f27eb9-9d1b-4172-a2eb-0676cae83256", "696a4ce7-c1ca-4331-8cd8-e4e3f0bd8b08"]}]}, {"BlockType": "LINE", "Confidence": 99.41744995117188, "Text": "N", "Geometry": {"BoundingBox": {"Width": 0.017327461391687393, "Height": 0.011672141961753368, "Left": 0.5626154541969299, "Top": 0.39822718501091003}, "Polygon": [{"X": 0.579482913017273, "Y": 0.3985171318054199}, {"X": 0.5799428820610046, "Y": 0.40989935398101807}, {"X": 0.563090980052948, "Y": 0.409601092338562}, {"X": 0.5626154541969299, "Y": 0.39822718501091003}]}, "Id": "6be05cff-5665-4510-bb8a-6fec1c5d8443", "Relationships": [{"Type": "CHILD", "Ids": ["70100615-f0e3-4f4e-99d1-5a40f794317f"]}]}, {"BlockType": "LINE", "Confidence": 81.69862365722656, "Text": "11.491", "Geometry": {"BoundingBox": {"Width": 0.02103036269545555, "Height": 0.06829206645488739, "Left": 0.563568115234375, "Top": 0.5078546404838562}, "Polygon": [{"X": 0.5818431973457336, "Y": 0.508258581161499}, {"X": 0.5845984816551208, "Y": 0.5761467218399048}, {"X": 0.5664250254631042, "Y": 0.5756897926330566}, {"X": 0.563568115234375, "Y": 0.5078546404838562}]}, "Id": "0b68b952-c3f6-485c-a4f7-435c55654ca4", "Relationships": [{"Type": "CHILD", "Ids": ["f0751092-b2a1-4965-a0a3-16ede40796e8"]}]}, {"BlockType": "LINE", "Confidence": 99.72257995605469, "Text": "3.599", "Geometry": {"BoundingBox": {"Width": 0.02024536021053791, "Height": 0.05624109134078026, "Left": 0.5672898292541504, "Top": 0.648983359336853}, "Polygon": [{"X": 0.5852624773979187, "Y": 0.6494942903518677}, {"X": 0.5875352025032043, "Y": 0.7052244544029236}, {"X": 0.5696457028388977, "Y": 0.704671323299408}, {"X": 0.5672898292541504, "Y": 0.648983359336853}]}, "Id": "fe25642c-59b7-4d5d-871f-f15a9201cf6e", "Relationships": [{"Type": "CHILD", "Ids": ["e7eaf048-217a-4e92-a4eb-cc8ad7c97726"]}]}, {"BlockType": "LINE", "Confidence": 99.61196899414062, "Text": "41.36", "Geometry": {"BoundingBox": {"Width": 0.019633103162050247, "Height": 0.05323975160717964, "Left": 0.571301281452179, "Top": 0.7732113003730774}, "Polygon": [{"X": 0.5887804627418518, "Y": 0.7738053798675537}, {"X": 0.5909343957901001, "Y": 0.8264510631561279}, {"X": 0.5735324621200562, "Y": 0.8258185982704163}, {"X": 0.571301281452179, "Y": 0.7732113003730774}]}, "Id": "e87f6b36-0c43-4bb8-8a14-694180cb5d28", "Relationships": [{"Type": "CHILD", "Ids": ["2aff1c1e-708a-4165-af9e-0cf82ffc3a0a"]}]}, {"BlockType": "LINE", "Confidence": 97.47359466552734, "Text": "subtotal =", "Geometry": {"BoundingBox": {"Width": 0.024104729294776917, "Height": 0.11930298805236816, "Left": 0.5016434788703918, "Top": 0.14529067277908325}, "Polygon": [{"X": 0.5204582810401917, "Y": 0.1454010009765625}, {"X": 0.5257481932640076, "Y": 0.2645936608314514}, {"X": 0.5071113109588623, "Y": 0.2643846273422241}, {"X": 0.5016434788703918, "Y": 0.14529067277908325}]}, "Id": "a9f1e95d-94f2-43ee-b7d4-e506fe9f02b5", "Relationships": [{"Type": "CHILD", "Ids": ["976754e7-2510-44ae-b02d-59953c3c19ef", "b34a11de-bd81-45df-b1e6-b104a1cc4c4a"]}]}, {"BlockType": "LINE", "Confidence": 99.67534637451172, "Text": "281.62", "Geometry": {"BoundingBox": {"Width": 0.02206922322511673, "Height": 0.07267369329929352, "Left": 0.5050190091133118, "Top": 0.3141566514968872}, "Polygon": [{"X": 0.5238495469093323, "Y": 0.3144099712371826}, {"X": 0.5270882248878479, "Y": 0.38683032989501953}, {"X": 0.5083673596382141, "Y": 0.38651761412620544}, {"X": 0.5050190091133118, "Y": 0.3141566514968872}]}, "Id": "e23f0aa8-11d4-4f83-bbdf-df2ccf098834", "Relationships": [{"Type": "CHILD", "Ids": ["58720440-0b96-4403-80b7-4a759a3c1dec"]}]}, {"BlockType": "LINE", "Confidence": 79.00955200195312, "Text": "sales tax :::::", "Geometry": {"BoundingBox": {"Width": 0.026240121573209763, "Height": 0.13092011213302612, "Left": 0.44682636857032776, "Top": 0.13552656769752502}, "Polygon": [{"X": 0.466693639755249, "Y": 0.13563460111618042}, {"X": 0.4730664789676666, "Y": 0.26644667983055115}, {"X": 0.45340466499328613, "Y": 0.2662239968776703}, {"X": 0.44682636857032776, "Y": 0.13552656769752502}]}, "Id": "8918b175-d67e-46c6-92a3-9e6f513577a1", "Relationships": [{"Type": "CHILD", "Ids": ["fbe0f058-c9b6-4a12-9cbf-c9184f589e6e", "e3a1c067-fdbf-47b7-a984-694f2827d658", "be168634-60e4-4554-93b2-1edac20f1356"]}]}, {"BlockType": "LINE", "Confidence": 99.43219757080078, "Text": "0.00", "Geometry": {"BoundingBox": {"Width": 0.019331173971295357, "Height": 0.047916196286678314, "Left": 0.45374178886413574, "Top": 0.33979088068008423}, "Polygon": [{"X": 0.4707273542881012, "Y": 0.34003955125808716}, {"X": 0.47307294607162476, "Y": 0.38770708441734314}, {"X": 0.45615246891975403, "Y": 0.3874230682849884}, {"X": 0.45374178886413574, "Y": 0.33979088068008423}]}, "Id": "4ce80e5e-9986-48dd-85b6-ae41db3d6914", "Relationships": [{"Type": "CHILD", "Ids": ["e9946d32-20d1-4c14-8ff9-6bd1130ec545"]}]}, {"BlockType": "LINE", "Confidence": 98.7316665649414, "Text": "TOTAL =", "Geometry": {"BoundingBox": {"Width": 0.023473400622606277, "Height": 0.08406826853752136, "Left": 0.42246225476264954, "Top": 0.18331825733184814}, "Polygon": [{"X": 0.44165974855422974, "Y": 0.1834641695022583}, {"X": 0.4459356665611267, "Y": 0.2673865258693695}, {"X": 0.42686590552330017, "Y": 0.2671694755554199}, {"X": 0.42246225476264954, "Y": 0.18331825733184814}]}, "Id": "6637f1d6-c1ce-4514-9e34-cee5ba4ad282", "Relationships": [{"Type": "CHILD", "Ids": ["3ef5dd2e-8bb4-40ce-993b-0fd9cc63bf33", "8e8fab52-2436-4e70-ac55-4b3e9dd18a94"]}]}, {"BlockType": "LINE", "Confidence": 99.23568725585938, "Text": "281.62 Signature:", "Geometry": {"BoundingBox": {"Width": 0.03213358297944069, "Height": 0.21270263195037842, "Left": 0.4227204918861389, "Top": 0.31630781292915344}, "Polygon": [{"X": 0.44395267963409424, "Y": 0.3165965676307678}, {"X": 0.4548540711402893, "Y": 0.5290104150772095}, {"X": 0.4339834153652191, "Y": 0.5285269618034363}, {"X": 0.4227204918861389, "Y": 0.31630781292915344}]}, "Id": "2fd974c7-5214-47af-9e4c-0ca1c2fd4aca", "Relationships": [{"Type": "CHILD", "Ids": ["58ff8f9a-f372-43ff-8d83-f6b7e92c8e5a", "6e992651-8cfe-4d68-910b-51620d3fcd4d"]}]}, {"BlockType": "LINE", "Confidence": 96.9239273071289, "Text": "I amount", "Geometry": {"BoundingBox": {"Width": 0.032101914286613464, "Height": 0.2923918664455414, "Left": 0.*****************, "Top": 0.5920058488845825}, "Polygon": [{"X": 0.****************, "Y": 0.5924316644668579}, {"X": 0.****************, "Y": 0.8843976855278015}, {"X": 0.****************, "Y": 0.8837724328041077}, {"X": 0.*****************, "Y": 0.5920058488845825}]}, "Id": "44181267-1e69-416f-b055-8fe52af040c4", "Relationships": [{"Type": "CHILD", "Ids": ["d8c8c486-8ec6-447d-aeab-7fb726f28884", "8d46298c-e70d-4193-8956-27b0a4842901"]}]}, {"BlockType": "LINE", "Confidence": 98.**************, "Text": "according to card issuer agreement.", "Geometry": {"BoundingBox": {"Width": 0.041756294667720795, "Height": 0.3792074918746948, "Left": 0.37864285707473755, "Top": 0.****************}, "Polygon": [{"X": 0.39916449785232544, "Y": 0.****************}, {"X": 0.*****************, "Y": 0.9228702187538147}, {"X": 0.****************, "Y": 0.9220553636550903}, {"X": 0.37864285707473755, "Y": 0.****************}]}, "Id": "d5ae5b9b-e4f3-4218-8bea-8d751da8d21a", "Relationships": [{"Type": "CHILD", "Ids": ["2d532412-428c-4ce5-9728-2b03713e0d38", "4c8c77c8-8101-4a17-9f10-2cb692eec7f1", "4361b5ac-20f3-412a-8303-4a94486a249a", "55f5a609-b1ba-465f-a855-d482517a46fb", "81ac6aaa-5693-4fc4-be37-60a624ace652"]}]}, {"BlockType": "LINE", "Confidence": 97.60330200195312, "Text": "Salesperson ID: AUTO/SMARTFUEL", "Geometry": {"BoundingBox": {"Width": 0.05150863155722618, "Height": 0.359058678150177, "Left": 0.30300211906433105, "Top": 0.055988725274801254}, "Polygon": [{"X": 0.3332640826702118, "Y": 0.05604517087340355}, {"X": 0.35451075434684753, "Y": 0.41504737734794617}, {"X": 0.3250969350337982, "Y": 0.4145146608352661}, {"X": 0.30300211906433105, "Y": 0.055988725274801254}]}, "Id": "431ca536-250d-42f3-8f4b-001d353ebfd4", "Relationships": [{"Type": "CHILD", "Ids": ["3f828638-5994-497b-b6c9-81388a4d99fc", "8b23b6a3-790f-4f17-862a-fa6f71c2a5f1", "a5ef79e3-f42e-40ce-b931-acb3fd4c3ac9"]}]}, {"BlockType": "LINE", "Confidence": 98.67652893066406, "Text": "Invoice #: 0099604", "Geometry": {"BoundingBox": {"Width": 0.029843058437108994, "Height": 0.19634844362735748, "Left": 0.33300232887268066, "Top": 0.5910825133323669}, "Polygon": [{"X": 0.3510223627090454, "Y": 0.5915529727935791}, {"X": 0.36284539103507996, "Y": 0.787431001663208}, {"X": 0.3451136350631714, "Y": 0.7868112325668335}, {"X": 0.33300232887268066, "Y": 0.5910825133323669}]}, "Id": "841d7973-5d04-4061-9917-476901c8f5ed", "Relationships": [{"Type": "CHILD", "Ids": ["5783f6bd-67ee-4b46-8980-d9a5d02b1a75", "69186047-f59e-468e-82cf-ea49dbf62176", "741652cf-8052-4545-9b7f-03942678032b"]}]}, {"BlockType": "LINE", "Confidence": 99.67567443847656, "Text": "Truck Number: 3482", "Geometry": {"BoundingBox": {"Width": 0.039654411375522614, "Height": 0.2132185399532318, "Left": 0.2818622887134552, "Top": 0.0817880854010582}, "Polygon": [{"X": 0.3084481358528137, "Y": 0.08186886459589005}, {"X": 0.3215166926383972, "Y": 0.2950066030025482}, {"X": 0.29537349939346313, "Y": 0.29467466473579407}, {"X": 0.2818622887134552, "Y": 0.0817880854010582}]}, "Id": "c8eca73e-4aaa-4dc1-86b0-16329b5dc551", "Relationships": [{"Type": "CHILD", "Ids": ["68b50960-b98c-4e74-bb72-bbd6a97b21d4", "ff084028-77e5-437c-ba45-1bd92174c2fe", "857c031f-2989-40cc-b289-0f724cdaff43"]}]}, {"BlockType": "LINE", "Confidence": 98.73192596435547, "Text": "Merchant Number: *******2817", "Geometry": {"BoundingBox": {"Width": 0.03889840468764305, "Height": 0.3074772357940674, "Left": 0.30256131291389465, "Top": 0.****************}, "Polygon": [{"X": 0.3222975730895996, "Y": 0.****************}, {"X": 0.***************, "Y": 0.8279900550842285}, {"X": 0.32221487164497375, "Y": 0.8272813558578491}, {"X": 0.30256131291389465, "Y": 0.****************}]}, "Id": "e83525d2-07bd-4f8d-acbf-187baec28d25", "Relationships": [{"Type": "CHILD", "Ids": ["fb9f07f3-d2c8-4ee1-b218-f5632a5b5bce", "7e98f455-b62f-4206-ac9d-2e8b617e33be", "8c7d4d97-a6a0-49cc-af15-ead42e8903af"]}]}, {"BlockType": "LINE", "Confidence": 96.62199401855469, "Text": "Card Number: 1773 -ICR-", "Geometry": {"BoundingBox": {"Width": 0.060906972736120224, "Height": 0.****************, "Left": 0.2494162768125534, "Top": 0.094520702958107}, "Polygon": [{"X": 0.28251534700393677, "Y": 0.0946405902504921}, {"X": 0.31032323837280273, "Y": 0.****************}, {"X": 0.27835705876350403, "Y": 0.****************}, {"X": 0.2494162768125534, "Y": 0.094520702958107}]}, "Id": "d0b6db94-640e-452c-bf6b-9e286bdc4f86", "Relationships": [{"Type": "CHILD", "Ids": ["ab9fa390-929c-47af-9209-f5a31ab56411", "bf62e4ac-ca31-4f08-a491-4ccea39bbd61", "cc92a002-f28e-46bd-a56d-c3853d174524", "51087329-3e35-4621-bafd-84ec89ea5d75"]}]}, {"BlockType": "LINE", "Confidence": 99.57818603515625, "Text": "Loyalty Card#:", "Geometry": {"BoundingBox": {"Width": 0.03483564779162407, "Height": 0.16349801421165466, "Left": 0.23168563842773438, "Top": 0.07095947116613388}, "Polygon": [{"X": 0.25581321120262146, "Y": 0.07102105766534805}, {"X": 0.26652130484580994, "Y": 0.23445747792720795}, {"X": 0.2427006959915161, "Y": 0.23421978950500488}, {"X": 0.23168563842773438, "Y": 0.07095947116613388}]}, "Id": "c752b126-0a24-4b2d-af24-b4b627ad14e5", "Relationships": [{"Type": "CHILD", "Ids": ["b69dc739-c537-4087-884e-2c910a463b69", "97851e93-6804-46f5-9a42-d1cd81bc5aab"]}]}, {"BlockType": "LINE", "Confidence": 94.87862396240234, "Text": "8997 -ICR-", "Geometry": {"BoundingBox": {"Width": 0.027465030550956726, "Height": 0.11752402037382126, "Left": 0.2441142499446869, "Top": 0.39248695969581604}, "Polygon": [{"X": 0.26377373933792114, "Y": 0.39282462000846863}, {"X": 0.2715792655944824, "Y": 0.****************}, {"X": 0.2521040141582489, "Y": 0.5095728635787964}, {"X": 0.2441142499446869, "Y": 0.39248695969581604}]}, "Id": "596ab12a-19f5-463d-a203-ecf113424031", "Relationships": [{"Type": "CHILD", "Ids": ["34730c3f-4a6a-44c7-98cd-ff6050bc2edb", "523fc47b-3962-4595-a323-6d187ab3f9f9"]}]}, {"BlockType": "LINE", "Confidence": 98.7177734375, "Text": "Billing Company: NBS / KWIK TRIP FLEET", "Geometry": {"BoundingBox": {"Width": 0.06432776898145676, "Height": 0.456226110458374, "Left": 0.16598215699195862, "Top": 0.006706136744469404}, "Polygon": [{"X": 0.19846850633621216, "Y": 0.006706136744469404}, {"X": 0.23030993342399597, "Y": 0.4629322290420532}, {"X": 0.19896894693374634, "Y": 0.4622930884361267}, {"X": 0.16598215699195862, "Y": 0.006718232296407223}]}, "Id": "3b71d7fd-8675-47c3-81b3-7403aa36e0f2", "Relationships": [{"Type": "CHILD", "Ids": ["64f46cde-3eb5-454b-94d3-3a49c3e707b2", "77ec33a3-b42c-4ebb-9fd7-ccfc489ef8bd", "37a88e92-4b28-4d4f-a545-51327eaaccc4", "381a54dd-e201-44c5-a264-4218575ffb9d", "2571bc7a-dc9c-4d59-9efc-8cb427abe980", "04f81b0d-eabf-4906-8698-1ffc944e46f6", "e5a2d7b6-5551-48b9-8970-6f1b1c25bfcc"]}]}, {"BlockType": "LINE", "Confidence": 85.78917694091797, "Text": "THANK YOU! ----------------- THANK YOU!", "Geometry": {"BoundingBox": {"Width": 0.05130639672279358, "Height": 0.3228159546852112, "Left": 0.11862792074680328, "Top": 0.008069106377661228}, "Polygon": [{"X": 0.14603963494300842, "Y": 0.008069106377661228}, {"X": 0.16993431746959686, "Y": 0.33088505268096924}, {"X": 0.14320462942123413, "Y": 0.330499529838562}, {"X": 0.11862792074680328, "Y": 0.008077629841864109}]}, "Id": "8205b6c7-0453-454d-83e5-79cda37b8185", "Relationships": [{"Type": "CHILD", "Ids": ["e8454f64-1768-4caa-883d-3ce13c35b02d", "7f429e6c-d96a-4887-bc23-ab1fc0de22bb", "b9a7d5c4-880b-4faa-958e-4d974c6f5cd0", "c515b89f-0611-4178-af37-e2492241631c", "1645a6ba-cde3-4f10-b694-f9b34e92d98f"]}]}, {"BlockType": "WORD", "Confidence": 78.7244644165039, "Text": "*******", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021633092314004898, "Height": 0.1585499793291092, "Left": 0.867012619972229, "Top": 0.18048733472824097}, "Polygon": [{"X": 0.8863243460655212, "Y": 0.18062880635261536}, {"X": 0.8886457085609436, "Y": 0.33903729915618896}, {"X": 0.8695814609527588, "Y": 0.3387642204761505}, {"X": 0.867012619972229, "Y": 0.18048733472824097}]}, "Id": "5ef24f25-5fc2-454d-8f32-c65be14a5e0d"}, {"BlockType": "WORD", "Confidence": 73.46321105957031, "Text": "(DUP)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02147936262190342, "Height": 0.05180279165506363, "Left": 0.8681244254112244, "Top": 0.3693450391292572}, "Polygon": [{"X": 0.8888483643531799, "Y": 0.36966997385025024}, {"X": 0.8896037936210632, "Y": 0.4211478531360626}, {"X": 0.8689675331115723, "Y": 0.4207773506641388}, {"X": 0.8681244254112244, "Y": 0.3693450391292572}]}, "Id": "69937ca6-2792-426d-800d-a72725d3b0b0"}, {"BlockType": "WORD", "Confidence": 87.21281433105469, "Text": "ICATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020919399335980415, "Height": 0.058514565229415894, "Left": 0.8678183555603027, "Top": 0.43094438314437866}, "Polygon": [{"X": 0.8878753781318665, "Y": 0.43131351470947266}, {"X": 0.8887377381324768, "Y": 0.48945894837379456}, {"X": 0.8687770366668701, "Y": 0.4890403151512146}, {"X": 0.8678183555603027, "Y": 0.43094438314437866}]}, "Id": "d5c6d878-beb9-4618-a3a4-33015d6c3726"}, {"BlockType": "WORD", "Confidence": 96.89971160888672, "Text": "RECEIPT)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021651608869433403, "Height": 0.0942503958940506, "Left": 0.8663619756698608, "Top": 0.5035107135772705}, "Polygon": [{"X": 0.8866037726402283, "Y": 0.5039482116699219}, {"X": 0.8880136013031006, "Y": 0.5977610945701599}, {"X": 0.8679295778274536, "Y": 0.5972437262535095}, {"X": 0.8663619756698608, "Y": 0.5035107135772705}]}, "Id": "f4ae483c-5fa3-423a-93e6-1424cf7cf44f"}, {"BlockType": "WORD", "Confidence": 71.81169891357422, "Text": "*******", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023297810927033424, "Height": 0.15864042937755585, "Left": 0.8658453822135925, "Top": 0.6095366477966309}, "Polygon": [{"X": 0.8867480158805847, "Y": 0.6100865006446838}, {"X": 0.889143168926239, "Y": 0.7681770920753479}, {"X": 0.8685175776481628, "Y": 0.7674905061721802}, {"X": 0.8658453822135925, "Y": 0.6095366477966309}]}, "Id": "82c23669-9505-4f36-b322-d605acf360d9"}, {"BlockType": "WORD", "Confidence": 99.4190444946289, "Text": "WIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01955997757613659, "Height": 0.03535359725356102, "Left": 0.830720841884613, "Top": 0.0004522768722381443}, "Polygon": [{"X": 0.8496660590171814, "Y": 0.0004522768722381443}, {"X": 0.8502808213233948, "Y": 0.035805873572826385}, {"X": 0.8313889503479004, "Y": 0.03578836843371391}, {"X": 0.830720841884613, "Y": 0.00046437224955298007}]}, "Id": "493ef833-8823-445e-9975-5ed8b65eb98f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01919037103652954, "Height": 0.04655241593718529, "Left": 0.832680344581604, "Top": 0.04841091111302376}, "Polygon": [{"X": 0.8510637283325195, "Y": 0.04843822494149208}, {"X": 0.8518707156181335, "Y": 0.09496332705020905}, {"X": 0.8335556387901306, "Y": 0.09489840269088745}, {"X": 0.832680344581604, "Y": 0.04841091111302376}]}, "Id": "5d2116b5-0184-4042-90d6-f35fd17c046a"}, {"BlockType": "WORD", "Confidence": 99.287109375, "Text": "1281", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020059118047356606, "Height": 0.04479077830910683, "Left": 0.8346170783042908, "Top": 0.10945508629083633}, "Polygon": [{"X": 0.8539071679115295, "Y": 0.10953590273857117}, {"X": 0.8546761870384216, "Y": 0.15424586832523346}, {"X": 0.8354553580284119, "Y": 0.154127299785614}, {"X": 0.8346170783042908, "Y": 0.10945508629083633}]}, "Id": "80bf2607-adc2-4144-9f3f-4cfe9158ff3c"}, {"BlockType": "WORD", "Confidence": 89.65899658203125, "Text": "/106", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019332880154252052, "Height": 0.046586185693740845, "Left": 0.8377177119255066, "Top": 0.1691650003194809}, "Polygon": [{"X": 0.8562564849853516, "Y": 0.169291689991951}, {"X": 0.8570505976676941, "Y": 0.21575118601322174}, {"X": 0.8385813236236572, "Y": 0.21558700501918793}, {"X": 0.8377177119255066, "Y": 0.1691650003194809}]}, "Id": "ebe1c48a-79ac-4b77-85fe-ae3738627fc7"}, {"BlockType": "WORD", "Confidence": 99.17669677734375, "Text": "N.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018655726686120033, "Height": 0.022303201258182526, "Left": 0.8397799730300903, "Top": 0.22803696990013123}, "Polygon": [{"X": 0.8580588698387146, "Y": 0.22820954024791718}, {"X": 0.8584356904029846, "Y": 0.25034016370773315}, {"X": 0.8401895761489868, "Y": 0.25015005469322205}, {"X": 0.8397799730300903, "Y": 0.22803696990013123}]}, "Id": "f5885c99-04cd-45a1-9dc7-72fb6ac7cdeb"}, {"BlockType": "WORD", "Confidence": 64.03758239746094, "Text": "ROYAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020432449877262115, "Height": 0.05556977912783623, "Left": 0.8398091197013855, "Top": 0.26561886072158813}, "Polygon": [{"X": 0.8593018054962158, "Y": 0.26583531498908997}, {"X": 0.8602415919303894, "Y": 0.32118862867355347}, {"X": 0.8408367037773132, "Y": 0.32092562317848206}, {"X": 0.8398091197013855, "Y": 0.26561886072158813}]}, "Id": "8ffcbf29-428b-4361-b405-4bf834d2009b"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "AVENUE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02143034338951111, "Height": 0.07491736859083176, "Left": 0.8400183320045471, "Top": 0.34150853753089905}, "Polygon": [{"X": 0.8601791858673096, "Y": 0.34180018305778503}, {"X": 0.8614487051963806, "Y": 0.4164259135723114}, {"X": 0.8414109945297241, "Y": 0.416069895029068}, {"X": 0.8400183320045471, "Y": 0.34150853753089905}]}, "Id": "edd8f3b8-4109-4198-aa5f-5919fcdd9c27"}, {"BlockType": "WORD", "Confidence": 82.45355987548828, "Text": "/BELGIUM.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02430144138634205, "Height": 0.10585309565067291, "Left": 0.8373246788978577, "Top": 0.4318004548549652}, "Polygon": [{"X": 0.8598157167434692, "Y": 0.43221578001976013}, {"X": 0.8616260886192322, "Y": 0.5376535654067993}, {"X": 0.8393306136131287, "Y": 0.5371378660202026}, {"X": 0.8373246788978577, "Y": 0.4318004548549652}]}, "Id": "10da40ea-7111-44f9-a509-c43e85a9b79a"}, {"BlockType": "WORD", "Confidence": 99.75347137451172, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020127439871430397, "Height": 0.022716237232089043, "Left": 0.8390659689903259, "Top": 0.5523882508277893}, "Polygon": [{"X": 0.8588057160377502, "Y": 0.5528581738471985}, {"X": 0.8591934442520142, "Y": 0.5751044750213623}, {"X": 0.8394902944564819, "Y": 0.5746160745620728}, {"X": 0.8390659689903259, "Y": 0.5523882508277893}]}, "Id": "9f9e6185-8993-4807-b79c-3badd9f1ad01"}, {"BlockType": "WORD", "Confidence": 94.47444915771484, "Text": "53004/(262)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023823857307434082, "Height": 0.13080258667469025, "Left": 0.8372964859008789, "Top": 0.5888983011245728}, "Polygon": [{"X": 0.8588435053825378, "Y": 0.5894461274147034}, {"X": 0.861120343208313, "Y": 0.7197008728981018}, {"X": 0.8398078680038452, "Y": 0.7190362811088562}, {"X": 0.8372964859008789, "Y": 0.5888983011245728}]}, "Id": "a07da95b-8b11-4067-a9a3-f8cd75c4eac4"}, {"BlockType": "WORD", "Confidence": 93.27567291259766, "Text": "988-7024", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022223178297281265, "Height": 0.09172642230987549, "Left": 0.8387104272842407, "Top": 0.7336761355400085}, "Polygon": [{"X": 0.8593257665634155, "Y": 0.7343324422836304}, {"X": 0.8609336018562317, "Y": 0.825402557849884}, {"X": 0.8404771685600281, "Y": 0.8246689438819885}, {"X": 0.8387104272842407, "Y": 0.7336761355400085}]}, "Id": "5d2959b9-a435-4eaa-b71c-2cfc6163265f"}, {"BlockType": "WORD", "Confidence": 97.49202728271484, "Text": "umber:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019163750112056732, "Height": 0.0679067075252533, "Left": 0.8023715615272522, "Top": 0.00144492881372571}, "Polygon": [{"X": 0.820193350315094, "Y": 0.00144492881372571}, {"X": 0.8215352892875671, "Y": 0.06935163587331772}, {"X": 0.8038097023963928, "Y": 0.06930883973836899}, {"X": 0.8023715615272522, "Y": 0.0014555377420037985}]}, "Id": "8b2ee4c8-0523-41fb-b86e-01a558e1a80e"}, {"BlockType": "WORD", "Confidence": 99.17091369628906, "Text": "0900-9604", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021462753415107727, "Height": 0.1071469858288765, "Left": 0.8058833479881287, "Top": 0.08460360765457153}, "Polygon": [{"X": 0.8252595067024231, "Y": 0.08466353267431259}, {"X": 0.8273460865020752, "Y": 0.19175060093402863}, {"X": 0.808135986328125, "Y": 0.191600039601326}, {"X": 0.8058833479881287, "Y": 0.08460360765457153}]}, "Id": "7e88bf05-db1c-4b42-8ca4-fedea91dc4bd"}, {"BlockType": "WORD", "Confidence": 99.77220153808594, "Text": "08/04/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021994030103087425, "Height": 0.12524518370628357, "Left": 0.8094894289970398, "Top": 0.21702170372009277}, "Polygon": [{"X": 0.8290589451789856, "Y": 0.21719713509082794}, {"X": 0.8314834237098694, "Y": 0.34226688742637634}, {"X": 0.8121119737625122, "Y": 0.34198591113090515}, {"X": 0.8094894289970398, "Y": 0.21702170372009277}]}, "Id": "98f51cbb-dd0c-4848-98b0-cd6bc30084d1"}, {"BlockType": "WORD", "Confidence": 99.07425689697266, "Text": "Station:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023503338918089867, "Height": 0.0974230021238327, "Left": 0.8095867037773132, "Top": 0.36737823486328125}, "Polygon": [{"X": 0.8312017917633057, "Y": 0.36771613359451294}, {"X": 0.83309006690979, "Y": 0.46480122208595276}, {"X": 0.8116468787193298, "Y": 0.4643738269805908}, {"X": 0.8095867037773132, "Y": 0.36737823486328125}]}, "Id": "45cc48b8-2993-4f66-b24f-faa5c0536ec5"}, {"BlockType": "WORD", "Confidence": 99.0284194946289, "Text": "9", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01794663444161415, "Height": 0.010994943790137768, "Left": 0.812256932258606, "Top": 0.48012953996658325}, "Polygon": [{"X": 0.8299937844276428, "Y": 0.4804954528808594}, {"X": 0.8302035331726074, "Y": 0.4911244809627533}, {"X": 0.8124822378158569, "Y": 0.4907505512237549}, {"X": 0.812256932258606, "Y": 0.48012953996658325}]}, "Id": "b1455375-150d-4195-a4c0-03aee4231cdf"}, {"BlockType": "WORD", "Confidence": 98.62025451660156, "Text": "esponse:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020066197961568832, "Height": 0.09314574301242828, "Left": 0.7437043786048889, "Top": 0.0013467301614582539}, "Polygon": [{"X": 0.7614901065826416, "Y": 0.0013467301614582539}, {"X": 0.7637705206871033, "Y": 0.09449247270822525}, {"X": 0.7461161613464355, "Y": 0.09443000704050064}, {"X": 0.7437043786048889, "Y": 0.001357422792352736}]}, "Id": "d037b556-18b3-4bef-bd41-a376c0b37191"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02088618464767933, "Height": 0.0961172878742218, "Left": 0.7503231763839722, "Top": 0.11015431582927704}, "Polygon": [{"X": 0.7688949704170227, "Y": 0.11023299396038055}, {"X": 0.7712093591690063, "Y": 0.20627160370349884}, {"X": 0.7527801990509033, "Y": 0.20611491799354553}, {"X": 0.7503231763839722, "Y": 0.11015431582927704}]}, "Id": "d4d9ef35-4b25-407f-8f8f-5cf08fd5a21e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019921498373150826, "Height": 0.04949048534035683, "Left": 0.7528969049453735, "Top": 0.21944095194339752}, "Polygon": [{"X": 0.7716302871704102, "Y": 0.21961131691932678}, {"X": 0.772818386554718, "Y": 0.26893141865730286}, {"X": 0.7541596293449402, "Y": 0.2687208652496338}, {"X": 0.7528969049453735, "Y": 0.21944095194339752}]}, "Id": "d14bd79b-92af-4aee-939e-32b6e79b23d5"}, {"BlockType": "WORD", "Confidence": 98.93753814697266, "Text": "REF#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020817192271351814, "Height": 0.060048360377550125, "Left": 0.7533392906188965, "Top": 0.28078725934028625}, "Polygon": [{"X": 0.7727134823799133, "Y": 0.2810163199901581}, {"X": 0.7741565108299255, "Y": 0.3408356308937073}, {"X": 0.7548763155937195, "Y": 0.3405565023422241}, {"X": 0.7533392906188965, "Y": 0.28078725934028625}]}, "Id": "916569d5-7084-4691-af63-956b364f9861"}, {"BlockType": "WORD", "Confidence": 99.75186920166016, "Text": "P54220", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02188378944993019, "Height": 0.07439157366752625, "Left": 0.7534351348876953, "Top": 0.3558679223060608}, "Polygon": [{"X": 0.773525595664978, "Y": 0.35617250204086304}, {"X": 0.7753189206123352, "Y": 0.43025949597358704}, {"X": 0.7553499937057495, "Y": 0.42989110946655273}, {"X": 0.7534351348876953, "Y": 0.3558679223060608}]}, "Id": "f03b83e6-e34c-418c-a2eb-e85f019439cf"}, {"BlockType": "WORD", "Confidence": 88.5241928100586, "Text": "OYALTY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02097863145172596, "Height": 0.07297239452600479, "Left": 0.6911947727203369, "Top": 0.002655978547409177}, "Polygon": [{"X": 0.7100849747657776, "Y": 0.002655978547409177}, {"X": 0.7121734023094177, "Y": 0.0756283700466156}, {"X": 0.693392276763916, "Y": 0.07557754963636398}, {"X": 0.6911947727203369, "Y": 0.002666259417310357}]}, "Id": "b2c72073-25b6-4dbf-ba4b-0899d8f1a7ed"}, {"BlockType": "WORD", "Confidence": 98.73505401611328, "Text": "Response:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024648597463965416, "Height": 0.10524328052997589, "Left": 0.6901426315307617, "Top": 0.08804312348365784}, "Polygon": [{"X": 0.7117747664451599, "Y": 0.0881136879324913}, {"X": 0.7147912383079529, "Y": 0.19328640401363373}, {"X": 0.6933403015136719, "Y": 0.1931159645318985}, {"X": 0.6901426315307617, "Y": 0.08804312348365784}]}, "Id": "d2eef9ea-eaf6-4908-9b99-478bc8cb2e71"}, {"BlockType": "WORD", "Confidence": 82.27300262451172, "Text": "APPROVED.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02374223805963993, "Height": 0.10877235978841782, "Left": 0.6944873332977295, "Top": 0.20889787375926971}, "Polygon": [{"X": 0.7151138186454773, "Y": 0.20907625555992126}, {"X": 0.7182295322418213, "Y": 0.31767022609710693}, {"X": 0.6977832913398743, "Y": 0.31739455461502075}, {"X": 0.6944873332977295, "Y": 0.20889787375926971}]}, "Id": "29c91b3a-495f-438c-a2c9-6d80a3b85af2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02095354162156582, "Height": 0.05760863795876503, "Left": 0.6974808573722839, "Top": 0.33422157168388367}, "Polygon": [{"X": 0.7167802453041077, "Y": 0.33449625968933105}, {"X": 0.7184344530105591, "Y": 0.3918302059173584}, {"X": 0.6992250084877014, "Y": 0.3915078043937683}, {"X": 0.6974808573722839, "Y": 0.33422157168388367}]}, "Id": "1692e512-d741-4ec3-874c-b01515d3571e"}, {"BlockType": "WORD", "Confidence": 98.99613189697266, "Text": "K<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01984839327633381, "Height": 0.04628913849592209, "Left": 0.6986397504806519, "Top": 0.4081491529941559}, "Polygon": [{"X": 0.7171552777290344, "Y": 0.4084736704826355}, {"X": 0.7184881567955017, "Y": 0.4544382691383362}, {"X": 0.7000422477722168, "Y": 0.45407724380493164}, {"X": 0.6986397504806519, "Y": 0.4081491529941559}]}, "Id": "2d43fc27-03d2-4593-b3b9-e74f90034232"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Rewards", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021856335923075676, "Height": 0.08465787023305893, "Left": 0.6980543732643127, "Top": 0.46819204092025757}, "Polygon": [{"X": 0.7174569964408875, "Y": 0.4685840606689453}, {"X": 0.7199106812477112, "Y": 0.5528499484062195}, {"X": 0.7006424069404602, "Y": 0.5523884296417236}, {"X": 0.6980543732643127, "Y": 0.46819204092025757}]}, "Id": "35a0ab30-d8c3-425f-a8d5-71c8112a528b"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "Fleet", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020852429792284966, "Height": 0.060256656259298325, "Left": 0.7000134587287903, "Top": 0.565251886844635}, "Polygon": [{"X": 0.7191190719604492, "Y": 0.5657204985618591}, {"X": 0.7208658456802368, "Y": 0.6255085468292236}, {"X": 0.7018548846244812, "Y": 0.6249917149543762}, {"X": 0.7000134587287903, "Y": 0.565251886844635}]}, "Id": "03ca8834-b278-44e7-8d56-739c3a8e0c2d"}, {"BlockType": "WORD", "Confidence": 99.44276428222656, "Text": "Points:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02132757380604744, "Height": 0.08018984645605087, "Left": 0.701133668422699, "Top": 0.6371689438819885}, "Polygon": [{"X": 0.7201263308525085, "Y": 0.6376956105232239}, {"X": 0.7224612236022949, "Y": 0.7173587679862976}, {"X": 0.7035946846008301, "Y": 0.716768741607666}, {"X": 0.701133668422699, "Y": 0.6371689438819885}]}, "Id": "4a86b473-2594-4246-974f-aed8cf8efba0"}, {"BlockType": "WORD", "Confidence": 99.5362319946289, "Text": "$27.07", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021359413862228394, "Height": 0.06822596490383148, "Left": 0.70224928855896, "Top": 0.7320565581321716}, "Polygon": [{"X": 0.721619725227356, "Y": 0.7326755523681641}, {"X": 0.723608672618866, "Y": 0.8002824783325195}, {"X": 0.7043483257293701, "Y": 0.7996090650558472}, {"X": 0.70224928855896, "Y": 0.7320565581321716}]}, "Id": "8281896e-f8b3-4fdf-9585-871f9a4d1c31"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "Shower", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02094680443406105, "Height": 0.06544720381498337, "Left": 0.7028957009315491, "Top": 0.811572253704071}, "Polygon": [{"X": 0.7219255566596985, "Y": 0.8122478127479553}, {"X": 0.7238425016403198, "Y": 0.877019464969635}, {"X": 0.7049168944358826, "Y": 0.8762931227684021}, {"X": 0.7028957009315491, "Y": 0.811572253704071}]}, "Id": "03299dd4-2857-42f0-b332-a5c3bcee483c"}, {"BlockType": "WORD", "Confidence": 99.23369598388672, "Text": "credi", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01917099580168724, "Height": 0.050780393183231354, "Left": 0.7028442621231079, "Top": 0.8885763883590698}, "Polygon": [{"X": 0.7205168008804321, "Y": 0.8892644047737122}, {"X": 0.7220152616500854, "Y": 0.939356803894043}, {"X": 0.7044181823730469, "Y": 0.9386325478553772}, {"X": 0.7028442621231079, "Y": 0.8885763883590698}]}, "Id": "ec426091-e699-4b67-ac6d-fe8a4f565681"}, {"BlockType": "WORD", "Confidence": 98.08833312988281, "Text": "earned!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020922450348734856, "Height": 0.08146081864833832, "Left": 0.6629332900047302, "Top": 0.0161790419369936}, "Polygon": [{"X": 0.6813332438468933, "Y": 0.016180114820599556}, {"X": 0.6838557124137878, "Y": 0.09763985872268677}, {"X": 0.665574312210083, "Y": 0.09757238626480103}, {"X": 0.6629332900047302, "Y": 0.0161790419369936}]}, "Id": "19245b5e-c361-4ebf-b3e2-56990bc35723"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Expires", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02439073845744133, "Height": 0.08374135941267014, "Left": 0.6626665592193604, "Top": 0.1130949929356575}, "Polygon": [{"X": 0.6844680309295654, "Y": 0.11319057643413544}, {"X": 0.6870573163032532, "Y": 0.19683635234832764}, {"X": 0.6654012203216553, "Y": 0.19666063785552979}, {"X": 0.6626665592193604, "Y": 0.1130949929356575}]}, "Id": "bb05fbd7-8b3e-48d4-8687-73d4c1a6ee71"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019509920850396156, "Height": 0.021461568772792816, "Left": 0.668074905872345, "Top": 0.2118859738111496}, "Polygon": [{"X": 0.6869245767593384, "Y": 0.21205168962478638}, {"X": 0.6875848174095154, "Y": 0.23334753513336182}, {"X": 0.6687674522399902, "Y": 0.2331642359495163}, {"X": 0.668074905872345, "Y": 0.2118859738111496}]}, "Id": "b1af8e71-07b1-44c7-94d3-bbae1d6f632b"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "14", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018808990716934204, "Height": 0.02235996536910534, "Left": 0.668978214263916, "Top": 0.24923521280288696}, "Polygon": [{"X": 0.68709796667099, "Y": 0.24942471086978912}, {"X": 0.6877871751785278, "Y": 0.27159518003463745}, {"X": 0.6696997284889221, "Y": 0.27138814330101013}, {"X": 0.668978214263916, "Y": 0.24923521280288696}]}, "Id": "f001849e-1d5b-4992-b031-2eb3246efdbc"}, {"BlockType": "WORD", "Confidence": 64.814453125, "Text": "davs", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01990719884634018, "Height": 0.048546403646469116, "Left": 0.6686863303184509, "Top": 0.2845991551876068}, "Polygon": [{"X": 0.6870868802070618, "Y": 0.2848206162452698}, {"X": 0.6885935664176941, "Y": 0.3331455588340759}, {"X": 0.6702648997306824, "Y": 0.3328854739665985}, {"X": 0.6686863303184509, "Y": 0.2845991551876068}]}, "Id": "10ab341c-852d-4823-8e95-3c29404a2b0b"}, {"BlockType": "WORD", "Confidence": 32.001556396484375, "Text": "clun,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018062399700284004, "Height": 0.040958043187856674, "Left": 0.6078760623931885, "Top": 0.002941589104011655}, "Polygon": [{"X": 0.6093839406967163, "Y": 0.04387814924120903}, {"X": 0.6078760623931885, "Y": 0.002950450638309121}, {"X": 0.6244842410087585, "Y": 0.002941589104011655}, {"X": 0.6259384751319885, "Y": 0.04389963299036026}]}, "Id": "f9d8d24f-4d7b-4b1b-a568-f99a69b63833"}, {"BlockType": "WORD", "Confidence": 99.76243591308594, "Text": "Products", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02343807928264141, "Height": 0.0987871065735817, "Left": 0.6114155054092407, "Top": 0.23621799051761627}, "Polygon": [{"X": 0.6313414573669434, "Y": 0.2364153116941452}, {"X": 0.6348535418510437, "Y": 0.33500510454177856}, {"X": 0.6150853633880615, "Y": 0.33472225069999695}, {"X": 0.6114155054092407, "Y": 0.23621799051761627}]}, "Id": "152f8d1f-3e5c-4130-9417-3f46f5989fda"}, {"BlockType": "WORD", "Confidence": 99.77379608154297, "Text": "Reefer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021255087107419968, "Height": 0.07099785655736923, "Left": 0.6146841645240784, "Top": 0.3721995949745178}, "Polygon": [{"X": 0.6334044337272644, "Y": 0.37249884009361267}, {"X": 0.6359392404556274, "Y": 0.44319745898246765}, {"X": 0.6173264980316162, "Y": 0.44284117221832275}, {"X": 0.6146841645240784, "Y": 0.3721995949745178}]}, "Id": "e3385947-77ba-4d04-b7a6-15d3d47e979d"}, {"BlockType": "WORD", "Confidence": 94.94339752197266, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023336876183748245, "Height": 0.09521082788705826, "Left": 0.6151654720306396, "Top": 0.48175248503685}, "Polygon": [{"X": 0.6350857019424438, "Y": 0.4821685254573822}, {"X": 0.6385023593902588, "Y": 0.5769633054733276}, {"X": 0.6187369227409363, "Y": 0.5764668583869934}, {"X": 0.6151654720306396, "Y": 0.48175248503685}]}, "Id": "e985f998-6e8d-4cb7-bd04-639796529c1b"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "Unit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02029222995042801, "Height": 0.047517433762550354, "Left": 0.619080662727356, "Top": 0.6012208461761475}, "Polygon": [{"X": 0.6376709938049316, "Y": 0.6017083525657654}, {"X": 0.6393728852272034, "Y": 0.6487382650375366}, {"X": 0.6208550333976746, "Y": 0.648213803768158}, {"X": 0.619080662727356, "Y": 0.6012208461761475}]}, "Id": "fe14174d-7ded-4695-8235-5496a6b843a0"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019426090642809868, "Height": 0.04574159160256386, "Left": 0.620714008808136, "Top": 0.6605368256568909}, "Polygon": [{"X": 0.6384983062744141, "Y": 0.6610503792762756}, {"X": 0.6401400566101074, "Y": 0.7062784433364868}, {"X": 0.6224226951599121, "Y": 0.7057310938835144}, {"X": 0.620714008808136, "Y": 0.6605368256568909}]}, "Id": "54eee6c1-0ef9-4098-8899-91b879270453"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019862957298755646, "Height": 0.05325392633676529, "Left": 0.6229977607727051, "Top": 0.7753885984420776}, "Polygon": [{"X": 0.6409419178962708, "Y": 0.7759988307952881}, {"X": 0.6428607106208801, "Y": 0.8286424875259399}, {"X": 0.6249959468841553, "Y": 0.8279930353164673}, {"X": 0.6229977607727051, "Y": 0.7753885984420776}]}, "Id": "a8596436-36df-4b82-9fdd-c98397c6703e"}, {"BlockType": "WORD", "Confidence": 46.687461853027344, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017601842060685158, "Height": 0.00892448890954256, "Left": 0.5825130939483643, "Top": 0.02184722013771534}, "Polygon": [{"X": 0.5825130939483643, "Y": 0.02184722013771534}, {"X": 0.5997800230979919, "Y": 0.021852614358067513}, {"X": 0.6001149415969849, "Y": 0.030771709978580475}, {"X": 0.5828601121902466, "Y": 0.030759431421756744}]}, "Id": "814d1ba9-a29e-455f-80dc-f556030ded18"}, {"BlockType": "WORD", "Confidence": 99.75186920166016, "Text": "Diesel", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021651996299624443, "Height": 0.07122344523668289, "Left": 0.5847645998001099, "Top": 0.23769605159759521}, "Polygon": [{"X": 0.603725016117096, "Y": 0.23788529634475708}, {"X": 0.606416642665863, "Y": 0.3089194893836975}, {"X": 0.5875642895698547, "Y": 0.30867141485214233}, {"X": 0.5847645998001099, "Y": 0.23769605159759521}]}, "Id": "a6956ad1-956d-40bd-a3c4-4d3b84f48241"}, {"BlockType": "WORD", "Confidence": 98.04109191894531, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01819339580833912, "Height": 0.011781562119722366, "Left": 0.5872898697853088, "Top": 0.32414719462394714}, "Polygon": [{"X": 0.6050443053245544, "Y": 0.3243931233882904}, {"X": 0.6054832339286804, "Y": 0.335928738117218}, {"X": 0.5877453684806824, "Y": 0.33567389845848083}, {"X": 0.5872898697853088, "Y": 0.32414719462394714}]}, "Id": "a4df247c-c4c1-4398-9318-8fd710d59cf1"}, {"BlockType": "WORD", "Confidence": 99.75426483154297, "Text": "N", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01746189221739769, "Height": 0.011608586646616459, "Left": 0.5889094471931458, "Top": 0.3975532054901123}, "Polygon": [{"X": 0.6059389114379883, "Y": 0.397845059633255}, {"X": 0.6063713431358337, "Y": 0.409161776304245}, {"X": 0.5893575549125671, "Y": 0.40886157751083374}, {"X": 0.5889094471931458, "Y": 0.3975532054901123}]}, "Id": "eb73d05c-e6fc-4dcb-90db-bf0dd8ba9bbe"}, {"BlockType": "WORD", "Confidence": 99.45731353759766, "Text": "68.665", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021002618595957756, "Height": 0.07104797661304474, "Left": 0.5902341604232788, "Top": 0.5065358281135559}, "Polygon": [{"X": 0.6085280179977417, "Y": 0.5069385766983032}, {"X": 0.6112367510795593, "Y": 0.5775837898254395}, {"X": 0.5930489897727966, "Y": 0.5771259069442749}, {"X": 0.5902341604232788, "Y": 0.5065358281135559}]}, "Id": "d753ec93-1306-4b3c-9e95-0ab845800ee3"}, {"BlockType": "WORD", "Confidence": 99.45651245117188, "Text": "3.499", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019920630380511284, "Height": 0.056672967970371246, "Left": 0.593967080116272, "Top": 0.6490057110786438}, "Polygon": [{"X": 0.6117231845855713, "Y": 0.649509847164154}, {"X": 0.6138877272605896, "Y": 0.7056786417961121}, {"X": 0.5962144732475281, "Y": 0.7051324844360352}, {"X": 0.593967080116272, "Y": 0.6490057110786438}]}, "Id": "b15a5e3e-55d0-4c7a-a82c-d29d5260fe78"}, {"BlockType": "WORD", "Confidence": 99.76164245605469, "Text": "240.26", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02063179761171341, "Height": 0.06571657210588455, "Left": 0.5963854193687439, "Top": 0.762620210647583}, "Polygon": [{"X": 0.6144991517066956, "Y": 0.7632266283035278}, {"X": 0.6170172095298767, "Y": 0.8283368349075317}, {"X": 0.5990024209022522, "Y": 0.8276813626289368}, {"X": 0.5963854193687439, "Y": 0.762620210647583}]}, "Id": "7fbe2b72-d1b6-45de-80d6-d4045d589609"}, {"BlockType": "WORD", "Confidence": 47.41340637207031, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015426521189510822, "Height": 0.007121990900486708, "Left": 0.5563949346542358, "Top": 0.0003406285832170397}, "Polygon": [{"X": 0.571538507938385, "Y": 0.0003406285832170397}, {"X": 0.5718214511871338, "Y": 0.007457588333636522}, {"X": 0.5566863417625427, "Y": 0.007462619338184595}, {"X": 0.5563949346542358, "Y": 0.00035049315192736685}]}, "Id": "8f1bb451-ed7b-43bb-80df-db4a90e4f4a8"}, {"BlockType": "WORD", "Confidence": 98.50347137451172, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01674777641892433, "Height": 0.008886399678885937, "Left": 0.5556898713111877, "Top": 0.02258838340640068}, "Polygon": [{"X": 0.5720843076705933, "Y": 0.02259405516088009}, {"X": 0.5724376440048218, "Y": 0.03147478401660919}, {"X": 0.5560545921325684, "Y": 0.031462594866752625}, {"X": 0.5556898713111877, "Y": 0.02258838340640068}]}, "Id": "73f760f8-fec7-44d5-9f22-37d5aaef7fdf"}, {"BlockType": "WORD", "Confidence": 80.24095153808594, "Text": "DFF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018410002812743187, "Height": 0.035881299525499344, "Left": 0.5594817996025085, "Top": 0.2387019693851471}, "Polygon": [{"X": 0.576458752155304, "Y": 0.23887237906455994}, {"X": 0.5778918266296387, "Y": 0.27458328008651733}, {"X": 0.56096351146698, "Y": 0.27438628673553467}, {"X": 0.5594817996025085, "Y": 0.2387019693851471}]}, "Id": "a1f27eb9-9d1b-4172-a2eb-0676cae83256"}, {"BlockType": "WORD", "Confidence": 99.76403045654297, "Text": "Bulk", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02022913657128811, "Height": 0.04725790396332741, "Left": 0.5592893362045288, "Top": 0.28782764077186584}, "Polygon": [{"X": 0.5776278972625732, "Y": 0.28805211186408997}, {"X": 0.5795184373855591, "Y": 0.33508554100990295}, {"X": 0.561249315738678, "Y": 0.3348234295845032}, {"X": 0.5592893362045288, "Y": 0.28782764077186584}]}, "Id": "696a4ce7-c1ca-4331-8cd8-e4e3f0bd8b08"}, {"BlockType": "WORD", "Confidence": 99.41744995117188, "Text": "N", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017327461391687393, "Height": 0.011672141961753368, "Left": 0.5626154541969299, "Top": 0.39822718501091003}, "Polygon": [{"X": 0.579482913017273, "Y": 0.3985171318054199}, {"X": 0.5799428820610046, "Y": 0.40989935398101807}, {"X": 0.563090980052948, "Y": 0.409601092338562}, {"X": 0.5626154541969299, "Y": 0.39822718501091003}]}, "Id": "70100615-f0e3-4f4e-99d1-5a40f794317f"}, {"BlockType": "WORD", "Confidence": 81.69862365722656, "Text": "11.491", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02103036269545555, "Height": 0.06829206645488739, "Left": 0.563568115234375, "Top": 0.5078546404838562}, "Polygon": [{"X": 0.5818431973457336, "Y": 0.508258581161499}, {"X": 0.5845984816551208, "Y": 0.5761467218399048}, {"X": 0.5664250254631042, "Y": 0.5756897926330566}, {"X": 0.563568115234375, "Y": 0.5078546404838562}]}, "Id": "f0751092-b2a1-4965-a0a3-16ede40796e8"}, {"BlockType": "WORD", "Confidence": 99.72257995605469, "Text": "3.599", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02024536021053791, "Height": 0.05624109134078026, "Left": 0.5672898292541504, "Top": 0.648983359336853}, "Polygon": [{"X": 0.5852624773979187, "Y": 0.6494942903518677}, {"X": 0.5875352025032043, "Y": 0.7052244544029236}, {"X": 0.5696457028388977, "Y": 0.704671323299408}, {"X": 0.5672898292541504, "Y": 0.648983359336853}]}, "Id": "e7eaf048-217a-4e92-a4eb-cc8ad7c97726"}, {"BlockType": "WORD", "Confidence": 99.61196899414062, "Text": "41.36", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019633103162050247, "Height": 0.05323975160717964, "Left": 0.571301281452179, "Top": 0.7732113003730774}, "Polygon": [{"X": 0.5887804627418518, "Y": 0.7738053798675537}, {"X": 0.5909343957901001, "Y": 0.8264510631561279}, {"X": 0.5735324621200562, "Y": 0.8258185982704163}, {"X": 0.571301281452179, "Y": 0.7732113003730774}]}, "Id": "2aff1c1e-708a-4165-af9e-0cf82ffc3a0a"}, {"BlockType": "WORD", "Confidence": 99.72576141357422, "Text": "subtotal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022910840809345245, "Height": 0.09240242093801498, "Left": 0.5016434788703918, "Top": 0.14529067277908325}, "Polygon": [{"X": 0.5204582810401917, "Y": 0.1454010009765625}, {"X": 0.5245543122291565, "Y": 0.23769310116767883}, {"X": 0.5058773159980774, "Y": 0.2375061810016632}, {"X": 0.5016434788703918, "Y": 0.14529067277908325}]}, "Id": "976754e7-2510-44ae-b02d-59953c3c19ef"}, {"BlockType": "WORD", "Confidence": 95.22142028808594, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009459078311920166, "Height": 0.012322607450187206, "Left": 0.5097094774246216, "Top": 0.252197265625}, "Polygon": [{"X": 0.5186192989349365, "Y": 0.25229230523109436}, {"X": 0.5191685557365417, "Y": 0.2645198702812195}, {"X": 0.5102674961090088, "Y": 0.2644200325012207}, {"X": 0.5097094774246216, "Y": 0.252197265625}]}, "Id": "b34a11de-bd81-45df-b1e6-b104a1cc4c4a"}, {"BlockType": "WORD", "Confidence": 99.67534637451172, "Text": "281.62", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02206922322511673, "Height": 0.07267369329929352, "Left": 0.5050190091133118, "Top": 0.3141566514968872}, "Polygon": [{"X": 0.5238495469093323, "Y": 0.3144099712371826}, {"X": 0.5270882248878479, "Y": 0.38683032989501953}, {"X": 0.5083673596382141, "Y": 0.38651761412620544}, {"X": 0.5050190091133118, "Y": 0.3141566514968872}]}, "Id": "58720440-0b96-4403-80b7-4a759a3c1dec"}, {"BlockType": "WORD", "Confidence": 99.31341552734375, "Text": "sales", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020746994763612747, "Height": 0.058408793061971664, "Left": 0.4487874209880829, "Top": 0.13553722202777863}, "Polygon": [{"X": 0.466693639755249, "Y": 0.13563460111618042}, {"X": 0.46953442692756653, "Y": 0.1939460188150406}, {"X": 0.4517107605934143, "Y": 0.19380231201648712}, {"X": 0.4487874209880829, "Y": 0.13553722202777863}]}, "Id": "fbe0f058-c9b6-4a12-9cbf-c9184f589e6e"}, {"BlockType": "WORD", "Confidence": 99.24724578857422, "Text": "tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019581330940127373, "Height": 0.03363323584198952, "Left": 0.45044636726379395, "Top": 0.20744892954826355}, "Polygon": [{"X": 0.4683918058872223, "Y": 0.20760466158390045}, {"X": 0.47002771496772766, "Y": 0.24108216166496277}, {"X": 0.4521300196647644, "Y": 0.24089987576007843}, {"X": 0.45044636726379395, "Y": 0.20744892954826355}]}, "Id": "e3a1c067-fdbf-47b7-a984-694f2827d658"}, {"BlockType": "WORD", "Confidence": 38.46799087524414, "Text": ":::::", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009179012849926949, "Height": 0.012224104255437851, "Left": 0.45625656843185425, "Top": 0.2541361451148987}, "Polygon": [{"X": 0.4648369550704956, "Y": 0.2542286515235901}, {"X": 0.46543559432029724, "Y": 0.26636025309562683}, {"X": 0.45686349272727966, "Y": 0.2662631571292877}, {"X": 0.45625656843185425, "Y": 0.2541361451148987}]}, "Id": "be168634-60e4-4554-93b2-1edac20f1356"}, {"BlockType": "WORD", "Confidence": 99.43219757080078, "Text": "0.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019331173971295357, "Height": 0.047916196286678314, "Left": 0.45374178886413574, "Top": 0.33979088068008423}, "Polygon": [{"X": 0.4707273542881012, "Y": 0.34003955125808716}, {"X": 0.47307294607162476, "Y": 0.38770708441734314}, {"X": 0.45615246891975403, "Y": 0.3874230682849884}, {"X": 0.45374178886413574, "Y": 0.33979088068008423}]}, "Id": "e9946d32-20d1-4c14-8ff9-6bd1130ec545"}, {"BlockType": "WORD", "Confidence": 99.81365966796875, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022190596908330917, "Height": 0.05889104679226875, "Left": 0.42246225476264954, "Top": 0.18331825733184814}, "Polygon": [{"X": 0.44165974855422974, "Y": 0.1834641695022583}, {"X": 0.44465285539627075, "Y": 0.2422093003988266}, {"X": 0.42554476857185364, "Y": 0.24201349914073944}, {"X": 0.42246225476264954, "Y": 0.18331825733184814}]}, "Id": "3ef5dd2e-8bb4-40ce-993b-0fd9cc63bf33"}, {"BlockType": "WORD", "Confidence": 97.64967346191406, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00906875729560852, "Height": 0.012443911284208298, "Left": 0.4294821321964264, "Top": 0.2548585534095764}, "Polygon": [{"X": 0.43791401386260986, "Y": 0.2549498379230499}, {"X": 0.4385508894920349, "Y": 0.2673024833202362}, {"X": 0.4301273226737976, "Y": 0.267206609249115}, {"X": 0.4294821321964264, "Y": 0.2548585534095764}]}, "Id": "8e8fab52-2436-4e70-ac55-4b3e9dd18a94"}, {"BlockType": "WORD", "Confidence": 99.79252624511719, "Text": "281.62", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022577591240406036, "Height": 0.07225572317838669, "Left": 0.4250701665878296, "Top": 0.31633976101875305}, "Polygon": [{"X": 0.44395267963409424, "Y": 0.3165965676307678}, {"X": 0.4476477801799774, "Y": 0.38859549164772034}, {"X": 0.4288742244243622, "Y": 0.3882792592048645}, {"X": 0.4250701665878296, "Y": 0.31633976101875305}]}, "Id": "58ff8f9a-f372-43ff-8d83-f6b7e92c8e5a"}, {"BlockType": "WORD", "Confidence": 98.67884826660156, "Text": "Signature:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024869099259376526, "Height": 0.11650849878787994, "Left": 0.4278230667114258, "Top": 0.4124518632888794}, "Polygon": [{"X": 0.4467090964317322, "Y": 0.41279056668281555}, {"X": 0.4526921510696411, "Y": 0.5289603471755981}, {"X": 0.4339834153652191, "Y": 0.5285269618034363}, {"X": 0.4278230667114258, "Y": 0.4124518632888794}]}, "Id": "6e992651-8cfe-4d68-910b-51620d3fcd4d"}, {"BlockType": "WORD", "Confidence": 93.9365463256836, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015586036257445812, "Height": 0.007691489532589912, "Left": 0.41089174151420593, "Top": 0.5920203328132629}, "Polygon": [{"X": 0.42608362436294556, "Y": 0.5924161076545715}, {"X": 0.4264777600765228, "Y": 0.5997117757797241}, {"X": 0.41129496693611145, "Y": 0.5993112325668335}, {"X": 0.41089174151420593, "Y": 0.5920203328132629}]}, "Id": "d8c8c486-8ec6-447d-aeab-7fb726f28884"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019382551312446594, "Height": 0.06260659545660019, "Left": 0.4230556786060333, "Top": 0.8217911124229431}, "Polygon": [{"X": 0.4390905499458313, "Y": 0.8223748803138733}, {"X": 0.****************, "Y": 0.8843976855278015}, {"X": 0.****************, "Y": 0.8837724328041077}, {"X": 0.4230556786060333, "Y": 0.8217911124229431}]}, "Id": "8d46298c-e70d-4193-8956-27b0a4842901"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "according", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026228558272123337, "Height": 0.10288657248020172, "Left": 0.37864285707473755, "Top": 0.****************}, "Polygon": [{"X": 0.3991297483444214, "Y": 0.5441524982452393}, {"X": 0.4048714339733124, "Y": 0.6465492844581604}, {"X": 0.38455554842948914, "Y": 0.645969808101654}, {"X": 0.37864285707473755, "Y": 0.****************}]}, "Id": "2d532412-428c-4ce5-9728-2b03713e0d38"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01772359013557434, "Height": 0.021463587880134583, "Left": 0.388185054063797, "Top": 0.****************}, "Polygon": [{"X": 0.*****************, "Y": 0.6602438688278198}, {"X": 0.*****************, "Y": 0.6812252402305603}, {"X": 0.3893917500972748, "Y": 0.6807282567024231}, {"X": 0.388185054063797, "Y": 0.****************}]}, "Id": "4c8c77c8-8101-4a17-9f10-2cb692eec7f1"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "card", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01910265162587166, "Height": 0.04185800999403, "Left": 0.3901558518409729, "Top": 0.692663311958313}, "Polygon": [{"X": 0.****************, "Y": 0.693177342414856}, {"X": 0.*****************, "Y": 0.7345213294029236}, {"X": 0.3925333321094513, "Y": 0.7339779138565063}, {"X": 0.3901558518409729, "Y": 0.692663311958313}]}, "Id": "4361b5ac-20f3-412a-8303-4a94486a249a"}, {"BlockType": "WORD", "Confidence": 94.75546264648438, "Text": "issuer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017579853534698486, "Height": 0.06418056041002274, "Left": 0.3940710723400116, "Top": 0.7467579245567322}, "Polygon": [{"X": 0.*****************, "Y": 0.7472206354141235}, {"X": 0.****************, "Y": 0.8109384775161743}, {"X": 0.3977312445640564, "Y": 0.8104382753372192}, {"X": 0.3940710723400116, "Y": 0.7467579245567322}]}, "Id": "55f5a609-b1ba-465f-a855-d482517a46fb"}, {"BlockType": "WORD", "Confidence": 97.46113586425781, "Text": "agreement.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02393232472240925, "Height": 0.10083041340112686, "Left": 0.39646685123443604, "Top": 0.822039783000946}, "Polygon": [{"X": 0.4147830605506897, "Y": 0.8227076530456543}, {"X": 0.*****************, "Y": 0.9228702187538147}, {"X": 0.4022361934185028, "Y": 0.9221259951591492}, {"X": 0.39646685123443604, "Y": 0.822039783000946}]}, "Id": "81ac6aaa-5693-4fc4-be37-60a624ace652"}, {"BlockType": "WORD", "Confidence": 99.69407653808594, "Text": "Salesperson", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030540766194462776, "Height": 0.12969808280467987, "Left": 0.31039664149284363, "Top": 0.05600252002477646}, "Polygon": [{"X": 0.3332640826702118, "Y": 0.05604517087340355}, {"X": 0.34093740582466125, "Y": 0.18570061028003693}, {"X": 0.31830137968063354, "Y": 0.18552549183368683}, {"X": 0.31039664149284363, "Y": 0.05600252002477646}]}, "Id": "3f828638-5994-497b-b6c9-81388a4d99fc"}, {"BlockType": "WORD", "Confidence": 99.12368774414062, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01941947266459465, "Height": 0.031134387478232384, "Left": 0.3180861175060272, "Top": 0.2003125101327896}, "Polygon": [{"X": 0.33565613627433777, "Y": 0.20046022534370422}, {"X": 0.33750560879707336, "Y": 0.23144690692424774}, {"X": 0.31997859477996826, "Y": 0.23127496242523193}, {"X": 0.3180861175060272, "Y": 0.2003125101327896}]}, "Id": "8b23b6a3-790f-4f17-862a-fa6f71c2a5f1"}, {"BlockType": "WORD", "Confidence": 93.99214935302734, "Text": "AUTO/SMARTFUEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03232920169830322, "Height": 0.16818960011005402, "Left": 0.31475648283958435, "Top": 0.24672330915927887}, "Polygon": [{"X": 0.33704203367233276, "Y": 0.24695758521556854}, {"X": 0.3470856845378876, "Y": 0.4149129092693329}, {"X": 0.3250969350337982, "Y": 0.4145146608352661}, {"X": 0.31475648283958435, "Y": 0.24672330915927887}]}, "Id": "a5ef79e3-f42e-40ce-b931-acb3fd4c3ac9"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02155814878642559, "Height": 0.07866278290748596, "Left": 0.33310598134994507, "Top": 0.5910852551460266}, "Polygon": [{"X": 0.34993547201156616, "Y": 0.5915245413780212}, {"X": 0.354664146900177, "Y": 0.6697480082511902}, {"X": 0.3379421532154083, "Y": 0.6692523956298828}, {"X": 0.33310598134994507, "Y": 0.5910852551460266}]}, "Id": "5783f6bd-67ee-4b46-8980-d9a5d02b1a75"}, {"BlockType": "WORD", "Confidence": 96.25737762451172, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017310479655861855, "Height": 0.01963004656136036, "Left": 0.3385235071182251, "Top": 0.6803092360496521}, "Polygon": [{"X": 0.35467568039894104, "Y": 0.6807960271835327}, {"X": 0.3558339774608612, "Y": 0.6999392509460449}, {"X": 0.33970722556114197, "Y": 0.699439287185669}, {"X": 0.3385235071182251, "Y": 0.6803092360496521}]}, "Id": "69186047-f59e-468e-82cf-ea49dbf62176"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "0099604", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02177695743739605, "Height": 0.07408397644758224, "Left": 0.34106841683387756, "Top": 0.7133470177650452}, "Polygon": [{"X": 0.35840684175491333, "Y": 0.7138954401016235}, {"X": 0.36284539103507996, "Y": 0.787431001663208}, {"X": 0.34561219811439514, "Y": 0.7868286371231079}, {"X": 0.34106841683387756, "Y": 0.7133470177650452}]}, "Id": "741652cf-8052-4545-9b7f-03942678032b"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02275308407843113, "Height": 0.05720054358243942, "Left": 0.28919872641563416, "Top": 0.0818103775382042}, "Polygon": [{"X": 0.3084481358528137, "Y": 0.08186886459589005}, {"X": 0.31195181608200073, "Y": 0.13901092112064362}, {"X": 0.2927882969379425, "Y": 0.13890303671360016}, {"X": 0.28919872641563416, "Y": 0.0818103775382042}]}, "Id": "68b50960-b98c-4e74-bb72-bbd6a97b21d4"}, {"BlockType": "WORD", "Confidence": 99.16454315185547, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025445783510804176, "Height": 0.0809284970164299, "Left": 0.2885058522224426, "Top": 0.15165354311466217}, "Polygon": [{"X": 0.3089725375175476, "Y": 0.1517806202173233}, {"X": 0.31395164132118225, "Y": 0.23258204758167267}, {"X": 0.29361486434936523, "Y": 0.23238128423690796}, {"X": 0.2885058522224426, "Y": 0.15165354311466217}]}, "Id": "ff084028-77e5-437c-ba45-1bd92174c2fe"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "3482", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021387359127402306, "Height": 0.04721919447183609, "Left": 0.29239147901535034, "Top": 0.2476891726255417}, "Polygon": [{"X": 0.3108656108379364, "Y": 0.2478843778371811}, {"X": 0.3137788474559784, "Y": 0.2949083745479584}, {"X": 0.29537349939346313, "Y": 0.29467466473579407}, {"X": 0.29239147901535034, "Y": 0.2476891726255417}]}, "Id": "857c031f-2989-40cc-b289-0f724cdaff43"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "Merchant", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024290038272738457, "Height": 0.09320449829101562, "Left": 0.3029012680053711, "Top": 0.5205206274986267}, "Polygon": [{"X": 0.3213936388492584, "Y": 0.5209447741508484}, {"X": 0.3271913230419159, "Y": 0.6137251257896423}, {"X": 0.30883800983428955, "Y": 0.6132270693778992}, {"X": 0.3029012680053711, "Y": 0.5205206274986267}]}, "Id": "fb9f07f3-d2c8-4ee1-b218-f5632a5b5bce"}, {"BlockType": "WORD", "Confidence": 99.41266632080078, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022275982424616814, "Height": 0.0765322595834732, "Left": 0.3091725707054138, "Top": 0.6237068176269531}, "Polygon": [{"X": 0.3266889750957489, "Y": 0.6241905093193054}, {"X": 0.3314485549926758, "Y": 0.7002391219139099}, {"X": 0.3140411078929901, "Y": 0.6996985673904419}, {"X": 0.3091725707054138, "Y": 0.6237068176269531}]}, "Id": "7e98f455-b62f-4206-ac9d-2e8b617e33be"}, {"BlockType": "WORD", "Confidence": 96.91007995605469, "Text": "*******2817", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025135817006230354, "Height": 0.11355902254581451, "Left": 0.3163239061832428, "Top": 0.7144309878349304}, "Polygon": [{"X": 0.3344080150127411, "Y": 0.715004563331604}, {"X": 0.***************, "Y": 0.8279900550842285}, {"X": 0.32354405522346497, "Y": 0.8273302912712097}, {"X": 0.3163239061832428, "Y": 0.7144309878349304}]}, "Id": "8c7d4d97-a6a0-49cc-af15-ead42e8903af"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Card", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021792545914649963, "Height": 0.04669163376092911, "Left": 0.26368245482444763, "Top": 0.09457238018512726}, "Polygon": [{"X": 0.28251534700393677, "Y": 0.0946405902504921}, {"X": 0.2854749858379364, "Y": 0.14126400649547577}, {"X": 0.26671066880226135, "Y": 0.14115633070468903}, {"X": 0.26368245482444763, "Y": 0.09457238018512726}]}, "Id": "ab9fa390-929c-47af-9209-f5a31ab56411"}, {"BlockType": "WORD", "Confidence": 99.07505798339844, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026110665872693062, "Height": 0.08001638203859329, "Left": 0.2620573043823242, "Top": 0.1532817780971527}, "Polygon": [{"X": 0.28307631611824036, "Y": 0.15341399610042572}, {"X": 0.2881679832935333, "Y": 0.2332981526851654}, {"X": 0.2672807276248932, "Y": 0.23309104144573212}, {"X": 0.2620573043823242, "Y": 0.1532817780971527}]}, "Id": "bf62e4ac-ca31-4f08-a491-4ccea39bbd61"}, {"BlockType": "WORD", "Confidence": 92.55779266357422, "Text": "1773", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021098773926496506, "Height": 0.044921860098838806, "Left": 0.27184268832206726, "Top": 0.41705724596977234}, "Polygon": [{"X": 0.29006293416023254, "Y": 0.4173901379108429}, {"X": 0.29294145107269287, "Y": 0.46197909116744995}, {"X": 0.2747863531112671, "Y": 0.46161067485809326}, {"X": 0.27184268832206726, "Y": 0.41705724596977234}]}, "Id": "cc92a002-f28e-46bd-a56d-c3853d174524"}, {"BlockType": "WORD", "Confidence": 94.92426300048828, "Text": "-ICR-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021679777652025223, "Height": 0.05788636952638626, "Left": 0.2745549976825714, "Top": 0.4744816720485687}, "Polygon": [{"X": 0.29251599311828613, "Y": 0.4748566448688507}, {"X": 0.29623478651046753, "Y": 0.5323680639266968}, {"X": 0.27835705876350403, "Y": 0.****************}, {"X": 0.2745549976825714, "Y": 0.4744816720485687}]}, "Id": "51087329-3e35-4621-bafd-84ec89ea5d75"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "Loyalty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02745874412357807, "Height": 0.082524873316288, "Left": 0.23375767469406128, "Top": 0.0709647610783577}, "Polygon": [{"X": 0.25581321120262146, "Y": 0.07102105766534805}, {"X": 0.2612164318561554, "Y": 0.1534896343946457}, {"X": 0.23930245637893677, "Y": 0.1533515602350235}, {"X": 0.23375767469406128, "Y": 0.0709647610783577}]}, "Id": "b69dc739-c537-4087-884e-2c910a463b69"}, {"BlockType": "WORD", "Confidence": 99.32398223876953, "Text": "Card#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02444060891866684, "Height": 0.06815086305141449, "Left": 0.23811598122119904, "Top": 0.1662670522928238}, "Polygon": [{"X": 0.258078396320343, "Y": 0.16640456020832062}, {"X": 0.2625565826892853, "Y": 0.23441791534423828}, {"X": 0.2427006959915161, "Y": 0.23421978950500488}, {"X": 0.23811598122119904, "Y": 0.1662670522928238}]}, "Id": "97851e93-6804-46f5-9a42-d1cd81bc5aab"}, {"BlockType": "WORD", "Confidence": 93.51821899414062, "Text": "8997", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019757378846406937, "Height": 0.046258606016635895, "Left": 0.24707844853401184, "Top": 0.3925378620624542}, "Polygon": [{"X": 0.26377373933792114, "Y": 0.39282462000846863}, {"X": 0.2668358087539673, "Y": 0.****************}, {"X": 0.25020191073417664, "Y": 0.****************}, {"X": 0.24707844853401184, "Y": 0.3925378620624542}]}, "Id": "34730c3f-4a6a-44c7-98cd-ff6050bc2edb"}, {"BlockType": "WORD", "Confidence": 96.2390365600586, "Text": "-ICR-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021562885493040085, "Height": 0.05832891911268234, "Left": 0.2481507658958435, "Top": 0.4516400694847107}, "Polygon": [{"X": 0.26584282517433167, "Y": 0.4519914984703064}, {"X": 0.2697136700153351, "Y": 0.5099689960479736}, {"X": 0.2521040141582489, "Y": 0.5095728635787964}, {"X": 0.2481507658958435, "Y": 0.4516400694847107}]}, "Id": "523fc47b-3962-4595-a323-6d187ab3f9f9"}, {"BlockType": "WORD", "Confidence": 99.69407653808594, "Text": "Billing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0256807878613472, "Height": 0.08871935307979584, "Left": 0.17859457433223724, "Top": 0.00670628109946847}, "Polygon": [{"X": 0.19808059930801392, "Y": 0.00670628109946847}, {"X": 0.20427536964416504, "Y": 0.09542562812566757}, {"X": 0.18492285907268524, "Y": 0.0953545942902565}, {"X": 0.17859457433223724, "Y": 0.006713536102324724}]}, "Id": "64f46cde-3eb5-454b-94d3-3a49c3e707b2"}, {"BlockType": "WORD", "Confidence": 97.341552734375, "Text": "Company:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029426582157611847, "Height": 0.09153775125741959, "Left": 0.18259118497371674, "Top": 0.10930318385362625}, "Polygon": [{"X": 0.20563599467277527, "Y": 0.10940244048833847}, {"X": 0.21201777458190918, "Y": 0.20084093511104584}, {"X": 0.18913708627223969, "Y": 0.20064710080623627}, {"X": 0.18259118497371674, "Y": 0.10930318385362625}]}, "Id": "77ec33a3-b42c-4ebb-9fd7-ccfc489ef8bd"}, {"BlockType": "WORD", "Confidence": 98.28125, "Text": "NBS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019482601433992386, "Height": 0.03506742790341377, "Left": 0.19075557589530945, "Top": 0.2155689299106598}, "Polygon": [{"X": 0.20778654515743256, "Y": 0.21572478115558624}, {"X": 0.21023818850517273, "Y": 0.25063636898994446}, {"X": 0.19325394928455353, "Y": 0.250453919172287}, {"X": 0.19075557589530945, "Y": 0.2155689299106598}]}, "Id": "37a88e92-4b28-4d4f-a545-51327eaaccc4"}, {"BlockType": "WORD", "Confidence": 95.89544677734375, "Text": "/", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016603292897343636, "Height": 0.008487766608595848, "Left": 0.1931273341178894, "Top": 0.2644193172454834}, "Polygon": [{"X": 0.20914596319198608, "Y": 0.26460155844688416}, {"X": 0.2097306251525879, "Y": 0.2729070782661438}, {"X": 0.19372250139713287, "Y": 0.2727189064025879}, {"X": 0.1931273341178894, "Y": 0.2644193172454834}]}, "Id": "381a54dd-e201-44c5-a264-4218575ffb9d"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021982520818710327, "Height": 0.046111512929201126, "Left": 0.1903633326292038, "Top": 0.2870548963546753}, "Polygon": [{"X": 0.2091100513935089, "Y": 0.28728756308555603}, {"X": 0.21234585344791412, "Y": 0.3331664204597473}, {"X": 0.19366711378097534, "Y": 0.33289557695388794}, {"X": 0.1903633326292038, "Y": 0.2870548963546753}]}, "Id": "2571bc7a-dc9c-4d59-9efc-8cb427abe980"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020603971555829048, "Height": 0.04641557112336159, "Left": 0.19297407567501068, "Top": 0.34556251764297485}, "Polygon": [{"X": 0.2103116661310196, "Y": 0.34582391381263733}, {"X": 0.21357804536819458, "Y": 0.39197808504104614}, {"X": 0.19630403816699982, "Y": 0.39168137311935425}, {"X": 0.19297407567501068, "Y": 0.34556251764297485}]}, "Id": "04f81b0d-eabf-4906-8698-1ffc944e46f6"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "FLEET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0222774688154459, "Height": 0.05841067060828209, "Left": 0.19476629793643951, "Top": 0.40425100922584534}, "Polygon": [{"X": 0.21292532980442047, "Y": 0.40457338094711304}, {"X": 0.21704375743865967, "Y": 0.4626616835594177}, {"X": 0.19896894693374634, "Y": 0.4622930884361267}, {"X": 0.19476629793643951, "Y": 0.40425100922584534}]}, "Id": "e5a2d7b6-5551-48b9-8970-6f1b1c25bfcc"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "THANK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024870209395885468, "Height": 0.064132459461689, "Left": 0.12374500185251236, "Top": 0.008069785311818123}, "Polygon": [{"X": 0.14385689795017242, "Y": 0.008069785311818123}, {"X": 0.14861521124839783, "Y": 0.07220224291086197}, {"X": 0.12860266864299774, "Y": 0.07214982807636261}, {"X": 0.12374500185251236, "Y": 0.008076039142906666}]}, "Id": "e8454f64-1768-4caa-883d-3ce13c35b02d"}, {"BlockType": "WORD", "Confidence": 99.23529052734375, "Text": "YOU!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021000094711780548, "Height": 0.04498935118317604, "Left": 0.13414119184017181, "Top": 0.08604254573583603}, "Polygon": [{"X": 0.15181544423103333, "Y": 0.08610004931688309}, {"X": 0.15514127910137177, "Y": 0.13103190064430237}, {"X": 0.13752859830856323, "Y": 0.13093845546245575}, {"X": 0.13414119184017181, "Y": 0.08604254573583603}]}, "Id": "7f429e6c-d96a-4887-bc23-ab1fc0de22bb"}, {"BlockType": "WORD", "Confidence": 30.394012451171875, "Text": "-----------------", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008529109880328178, "Height": 0.0342605784535408, "Left": 0.14438630640506744, "Top": 0.15981604158878326}, "Polygon": [{"X": 0.15036313235759735, "Y": 0.15985563397407532}, {"X": 0.15291541814804077, "Y": 0.19407662749290466}, {"X": 0.14695455133914948, "Y": 0.194027841091156}, {"X": 0.14438630640506744, "Y": 0.15981604158878326}]}, "Id": "b9a7d5c4-880b-4faa-958e-4d974c6f5cd0"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "THANK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023365048691630363, "Height": 0.05761363357305527, "Left": 0.13843660056591034, "Top": 0.21823272109031677}, "Polygon": [{"X": 0.15753108263015747, "Y": 0.2184101939201355}, {"X": 0.16180165112018585, "Y": 0.27584636211395264}, {"X": 0.14279314875602722, "Y": 0.27561986446380615}, {"X": 0.13843660056591034, "Y": 0.21823272109031677}]}, "Id": "c515b89f-0611-4178-af37-e2492241631c"}, {"BlockType": "WORD", "Confidence": 99.47285461425781, "Text": "YOU!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021541258320212364, "Height": 0.04285453259944916, "Left": 0.13995814323425293, "Top": 0.2879088819026947}, "Polygon": [{"X": 0.15831461548805237, "Y": 0.28813794255256653}, {"X": 0.16149941086769104, "Y": 0.33076339960098267}, {"X": 0.14320462942123413, "Y": 0.330499529838562}, {"X": 0.13995814323425293, "Y": 0.2879088819026947}]}, "Id": "1645a6ba-cde3-4f10-b694-f9b34e92d98f"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "b9bb2057-1119-4999-86d3-fad66e7a5b85", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "b9bb2057-1119-4999-86d3-fad66e7a5b85", "content-type": "application/x-amz-json-1.1", "content-length": "81725", "date": "Thu, 18 Sep 2025 08:51:21 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "5a8b13d4-d86a-4e86-83a5-85576e1e5649", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 08:51:24 GMT", "content-type": "application/json", "content-length": "1660", "connection": "keep-alive", "x-amzn-requestid": "5a8b13d4-d86a-4e86-83a5-85576e1e5649"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We are given a single page. We need to classify the document type. Let's examine content:\n\nIt has \"WIK TRIP\" maybe fuel receipt for a fuel purchase. The page contains \"Diesel\", \"Quantity\", \"Unit Cost\", \"Total\", \"Invoice #0099604\". It also says \"Billing Company: NBS / KWIK TRIP FLEET\". It says \"I amount according to card issuer agreement.\" This looks like a fuel receipt. The keywords from the enum: fuel_receipt: \"Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\". Indeed it has Diesel. So classify as fuel_receipt.\n\nBut we also need to check if the page might be an invoice. It also says \"Invoice #\". But the presence of fuel specific items suggests fuel receipt. The difference between \"fuel receipt\" and \"invoice\" is ambiguous. But the presence of \"Diesel\" and \"Quantity\" and \"Unit Cost\" suggests a fuel receipt. The classification tool expects we return an array of objects: each object includes page_no and doc_type. For each page. There is only page1. So we return:\n\n[ { page_no: 1, doc_type: \"fuel_receipt\" } ]\n\nWe must call the function with the array accordingly.\n\nWe must ensure the JSON matches the function signature: documents: [ { page_no: number, doc_type: \"fuel_receipt\" } ].\n\nSo we call."}}}, {"toolUse": {"toolUseId": "tooluse_lBApB_uOSMSpxRuOD_uYLg", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2396, "outputTokens": 337, "totalTokens": 2733}, "metrics": {"latencyMs": 1388}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/6db80e85_9_fuel_receipt_6.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n******* (DUP) ICATE RECEIPT) *******\nWIK TRIP 1281 /106 N. ROYAL AVENUE /BELGIUM. WI 53004/(262) 988-7024\number: 0900-9604 08/04/2025 Station: 9\nesponse: APPROVED AUTH REF#: P54220\nOYALTY Response: APPROVED. Total Kwik Rewards Fleet Points: $27.07 Shower credi\nearned! Expires in 14 davs\nclun,\nProducts\nReefer\nQuantity\nUnit Cost\nTotal\n1\nDiesel 2\nN\n68.665\n3.499\n240.26\n# 1\nDFF Bulk\nN\n11.491\n3.599\n41.36\nsubtotal =\n281.62\nsales tax :::::\n0.00\nTOTAL =\n281.62 Signature:\nI amount\naccording to card issuer agreement.\nSalesperson ID: AUTO/SMARTFUEL\nInvoice #: 0099604\nTruck Number: 3482\nMerchant Number: *******2817\nCard Number: 1773 -ICR-\nLoyalty Card#:\n8997 -ICR-\nBilling Company: NBS / KWIK TRIP FLEET\nTHANK YOU! ----------------- THANK YOU!\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}