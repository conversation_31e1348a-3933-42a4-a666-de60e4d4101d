{"classification_result": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9250969290733337, "Height": 0.8516643643379211, "Left": 0.07490307837724686, "Top": 0.09973122179508209}, "Polygon": [{"X": 0.07490307837724686, "Y": 0.11155091971158981}, {"X": 0.9983123540878296, "Y": 0.09973122179508209}, {"X": 1.0, "Y": 0.951395571231842}, {"X": 0.12637802958488464, "Y": 0.9447089433670044}]}, "Id": "d2ea1a99-75fb-4698-8abe-1a889318171b", "Relationships": [{"Type": "CHILD", "Ids": ["297fc5ff-e48d-43a2-aaee-4f48bea5e84d", "ece9f51c-19cc-4996-8190-008c351db244", "a6a538a0-5fef-4cac-bb53-89164df1ca76", "8a926219-9f3a-4f3c-8589-8c3a69ebbdaf", "4b982460-fa42-45d2-a02d-dcd4cd1db2fe", "f8978449-ae26-492f-ab94-4419ad4f0a90", "4a15dd2e-165e-4d49-8c95-76291cf99b7a", "688b25bf-5589-4781-bbd2-ae5e2ca21315", "d23f6ce5-cf27-48ec-8bc1-487d3bafe613", "d790511c-534c-4668-9fc0-f9ff92562c58", "12ce4fae-9091-412d-a3c5-b99337e47435", "b0db5ea8-dd96-4708-a830-396f8883cb8e", "9bd93eaf-d751-474d-9f95-ae0a1304e10c", "6b66ddb4-0c79-474f-837e-48b4bcaa6ce5", "74a773c3-c01b-4b3f-b39e-3382aabc6d03", "9bef0f14-09e4-4328-b392-eed84fe2ca88", "f0aee71e-ef2b-45e0-8c26-51aefecbd04d", "bbb7eae4-0b86-4363-93ad-f6330018bba7", "0f545d7d-c990-4761-b74a-649f2e05261c", "6b6b0ed9-f898-4335-b4b7-6378ec80c058", "c872ba1b-4e5b-45b9-9ce5-d0d90be98bad", "f5b506d6-1717-44f7-8d7e-f40bad1fe833", "b1ed4f77-18cd-4823-95dd-f0634f1126fe", "b75d57c0-2b76-45e3-a14f-a2b2f1d9e779", "3cd1b844-013e-43bc-bf7b-0044da5a72a9", "7481e1f0-fd1d-41c9-8f73-93d6999c2abc", "90bec5cc-44e4-4d61-9a04-47ca2477067a", "a87df70f-9a5a-4d78-a8b9-77082738edf1", "6f46d2d9-f871-4050-9b3a-861d4a4ea72c", "6ca1ee5a-3e98-4ddb-9b59-b3c783d0b99a", "bab43585-ed24-4e8a-a822-beb806428d28", "5dbecea1-6e8a-4601-85a5-8e7bd90015bf", "07d11bae-c8b9-44c7-acb6-19185530a61d"]}]}, {"BlockType": "LINE", "Confidence": 98.81141662597656, "Text": "KWIK TRIP 139 /7002 SAUK TRAIL RD. /SHEBOYGAN, WI 53081/(920) 457-1028", "Geometry": {"BoundingBox": {"Width": 0.7264955639839172, "Height": 0.04277355596423149, "Left": 0.15205377340316772, "Top": 0.24547070264816284}, "Polygon": [{"X": 0.15205377340316772, "Y": 0.2522498369216919}, {"X": 0.8778321743011475, "Y": 0.24547070264816284}, {"X": 0.878549337387085, "Y": 0.2821149528026581}, {"X": 0.15414667129516602, "Y": 0.28824424743652344}]}, "Id": "297fc5ff-e48d-43a2-aaee-4f48bea5e84d", "Relationships": [{"Type": "CHILD", "Ids": ["0ee58a0a-7a8d-4996-ad46-9b4521a8880c", "383a6858-e8e0-4a32-9fe8-049ab5247e91", "0b8f93c5-56b7-4239-b3dd-2c2e49517a03", "6771651a-a68e-4ae7-b466-53620c75cc50", "97a19d7a-6202-479f-ac72-ec566d45c693", "19fa466d-f25e-4ca2-92c2-1d4ef2d36cf2", "02d671ea-32f6-4ab6-99c8-41ddf6d3a279", "d4994454-90b2-4a77-bb1f-1bddc2b70556", "e81d8d2a-77d3-4d64-bbc3-00b6b0f72706", "1b2f8799-7e24-4645-a33a-4d0a876ee2b7", "626146a0-1a66-4a9d-b021-a49c7dec790a"]}]}, {"BlockType": "LINE", "Confidence": 99.24577331542969, "Text": "Number: 0900-1223 08/13/2025 Station: 9", "Geometry": {"BoundingBox": {"Width": 0.44433343410491943, "Height": 0.03432294726371765, "Left": 0.15405353903770447, "Top": 0.2753171920776367}, "Polygon": [{"X": 0.15405353903770447, "Y": 0.279166042804718}, {"X": 0.5973232388496399, "Y": 0.2753171920776367}, {"X": 0.5983870029449463, "Y": 0.3061268925666809}, {"X": 0.15582475066184998, "Y": 0.30964013934135437}]}, "Id": "ece9f51c-19cc-4996-8190-008c351db244", "Relationships": [{"Type": "CHILD", "Ids": ["0784520f-cdaf-4e22-a95e-db9300e23987", "63e901e9-f934-4bf3-8aa3-178b03544427", "05a1fda4-a388-43d1-84d2-d9dc7dc977aa", "e6c930a8-c96a-4b3e-8703-452bebab5576", "375680fe-0728-411d-a809-fb8ec3a15ce7"]}]}, {"BlockType": "LINE", "Confidence": 99.44113159179688, "Text": "Response: APPROVED AUTH REF#: P19403", "Geometry": {"BoundingBox": {"Width": 0.3842911124229431, "Height": 0.033620018512010574, "Left": 0.1587354987859726, "Top": 0.32750675082206726}, "Polygon": [{"X": 0.1587354987859726, "Y": 0.3303542137145996}, {"X": 0.5418591499328613, "Y": 0.32750675082206726}, {"X": 0.5430266261100769, "Y": 0.3585713803768158}, {"X": 0.1605212539434433, "Y": 0.36112678050994873}]}, "Id": "a6a538a0-5fef-4cac-bb53-89164df1ca76", "Relationships": [{"Type": "CHILD", "Ids": ["55b3d5fc-b4ed-4651-8f93-7ae45f63cb06", "541b8f19-012b-4631-a910-045976f7b8a4", "8e0c4d16-174f-417a-abcb-7ba5654629ee", "14480c72-5dfe-482f-8c01-07c93b7494d9", "0dbaf98a-1e80-486a-bd93-e653f44805c4"]}]}, {"BlockType": "LINE", "Confidence": 99.59530639648438, "Text": "LOYALTY Response: APPROVED, You earned 2.06 Fleet points! Drink Credit Earned! E", "Geometry": {"BoundingBox": {"Width": 0.8024576306343079, "Height": 0.03308068960905075, "Left": 0.16156907379627228, "Top": 0.3748467266559601}, "Polygon": [{"X": 0.16156907379627228, "Y": 0.3798382878303528}, {"X": 0.9635936617851257, "Y": 0.3748467266559601}, {"X": 0.9640267491340637, "Y": 0.****************}, {"X": 0.16319915652275085, "Y": 0.*****************}]}, "Id": "8a926219-9f3a-4f3c-8589-8c3a69ebbdaf", "Relationships": [{"Type": "CHILD", "Ids": ["56fd1410-b054-41c2-8dd5-6e6aa600030d", "11d087b8-00d0-4266-9fb7-de45e1078d66", "564377d6-c23d-4875-bcc6-ee3ff7f00ea2", "950c0feb-ecc1-436e-8b6a-1f7cbcc16d4b", "3b26626c-de2c-47f8-8dfd-2ba6684a28ee", "fa278b20-107a-4695-99ff-7e59d384ba0d", "862dacd4-84fe-47d1-9e67-9846bf3e41cf", "bfeb0506-4e35-4af8-b265-0875e704f3e5", "df28f706-e419-4a05-987a-628b957f8488", "1a197ab9-9cdf-48e8-9fc6-3a0273590c1c", "290962f3-dc17-4565-8686-e76ad824a744", "f2a373b9-bcd4-408b-b534-a32516690375"]}]}, {"BlockType": "LINE", "Confidence": 98.8603744506836, "Text": "xpires in 24 Hours. Shower C", "Geometry": {"BoundingBox": {"Width": 0.2871510982513428, "Height": 0.026034878566861153, "Left": 0.16454820334911346, "Top": 0.4074113667011261}, "Polygon": [{"X": 0.16454820334911346, "Y": 0.4089882969856262}, {"X": 0.45064932107925415, "Y": 0.4074113667011261}, {"X": 0.45169928669929504, "Y": 0.4320420026779175}, {"X": 0.16596588492393494, "Y": 0.4334462583065033}]}, "Id": "4b982460-fa42-45d2-a02d-dcd4cd1db2fe", "Relationships": [{"Type": "CHILD", "Ids": ["83f51cb1-f3f9-4198-94e9-0faafe9807a6", "bcb54313-d41c-4d52-9cab-efe7fb0d9f74", "28e050cd-eab2-48e7-b0db-de047ac4f5cb", "e7b3922a-1e6b-4887-9c82-28bc010338f6", "ac1e1740-004e-40cb-babf-c0f09cce0deb", "64f99701-e882-4fc0-994c-9219ba261a30"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "Pump", "Geometry": {"BoundingBox": {"Width": 0.03554574400186539, "Height": 0.019807077944278717, "Left": 0.16674378514289856, "Top": 0.4619748890399933}, "Polygon": [{"X": 0.16674378514289856, "Y": 0.46212002635002136}, {"X": 0.20118437707424164, "Y": 0.4619748890399933}, {"X": 0.20228952169418335, "Y": 0.48165348172187805}, {"X": 0.16788439452648163, "Y": 0.4817819595336914}]}, "Id": "f8978449-ae26-492f-ab94-4419ad4f0a90", "Relationships": [{"Type": "CHILD", "Ids": ["b208ee50-c349-416d-870d-8f99ce066a8f"]}]}, {"BlockType": "LINE", "Confidence": 99.77220153808594, "Text": "Products", "Geometry": {"BoundingBox": {"Width": 0.08966726809740067, "Height": 0.01877777650952339, "Left": 0.3609897494316101, "Top": 0.4576737880706787}, "Polygon": [{"X": 0.3609897494316101, "Y": 0.45805537700653076}, {"X": 0.44986820220947266, "Y": 0.4576737880706787}, {"X": 0.4506570100784302, "Y": 0.4761100113391876}, {"X": 0.36186471581459045, "Y": 0.4764515459537506}]}, "Id": "4a15dd2e-165e-4d49-8c95-76291cf99b7a", "Relationships": [{"Type": "CHILD", "Ids": ["38d1b8c0-8942-4148-86a5-eabc8fc12a4f"]}]}, {"BlockType": "LINE", "Confidence": 99.775390625, "Text": "Reefer", "Geometry": {"BoundingBox": {"Width": 0.0680665522813797, "Height": 0.017681226134300232, "Left": 0.48687344789505005, "Top": 0.45507505536079407}, "Polygon": [{"X": 0.48687344789505005, "Y": 0.4553680419921875}, {"X": 0.5542925596237183, "Y": 0.45507505536079407}, {"X": 0.5549399852752686, "Y": 0.472491979598999}, {"X": 0.4875827431678772, "Y": 0.4727562963962555}]}, "Id": "688b25bf-5589-4781-bbd2-ae5e2ca21315", "Relationships": [{"Type": "CHILD", "Ids": ["5852cf5b-f824-4dc6-8256-5b22baef98d2"]}]}, {"BlockType": "LINE", "Confidence": 99.912109375, "Text": "Quantity", "Geometry": {"BoundingBox": {"Width": 0.08215805143117905, "Height": 0.02107558399438858, "Left": 0.5896950960159302, "Top": 0.45281440019607544}, "Polygon": [{"X": 0.5896950960159302, "Y": 0.45317208766937256}, {"X": 0.6712119579315186, "Y": 0.45281440019607544}, {"X": 0.6718531250953674, "Y": 0.4735734760761261}, {"X": 0.5904256701469421, "Y": 0.4738899767398834}]}, "Id": "d23f6ce5-cf27-48ec-8bc1-487d3bafe613", "Relationships": [{"Type": "CHILD", "Ids": ["51867d2d-98da-4206-a316-8f35958a515c"]}]}, {"BlockType": "LINE", "Confidence": 99.92636108398438, "Text": "Unit Cost", "Geometry": {"BoundingBox": {"Width": 0.08953322470188141, "Height": 0.017101535573601723, "Left": 0.692863941192627, "Top": 0.4518778324127197}, "Polygon": [{"X": 0.692863941192627, "Y": 0.4522697925567627}, {"X": 0.7819796204566956, "Y": 0.4518778324127197}, {"X": 0.7823972105979919, "Y": 0.4686236083507538}, {"X": 0.6933606266975403, "Y": 0.4689793586730957}]}, "Id": "d790511c-534c-4668-9fc0-f9ff92562c58", "Relationships": [{"Type": "CHILD", "Ids": ["ae9281f5-6d6a-48ca-a7e2-f58dc799e8b8", "36d279ac-8238-40c0-9cee-97f46026191a"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.043630387634038925, "Height": 0.01600932516157627, "Left": 0.8390679955482483, "Top": 0.45104140043258667}, "Polygon": [{"X": 0.8390679955482483, "Y": 0.451232373714447}, {"X": 0.8823889493942261, "Y": 0.45104140043258667}, {"X": 0.8826984167098999, "Y": 0.46687638759613037}, {"X": 0.8394139409065247, "Y": 0.4670507311820984}]}, "Id": "12ce4fae-9091-412d-a3c5-b99337e47435", "Relationships": [{"Type": "CHILD", "Ids": ["a0a0eee7-15d5-41ae-a5ad-d89a74d7f934"]}]}, {"BlockType": "LINE", "Confidence": 85.09247589111328, "Text": "# 1", "Geometry": {"BoundingBox": {"Width": 0.02496218867599964, "Height": 0.016770021989941597, "Left": 0.16844572126865387, "Top": 0.48467394709587097}, "Polygon": [{"X": 0.16844572126865387, "Y": 0.48476189374923706}, {"X": 0.1924615055322647, "Y": 0.48467394709587097}, {"X": 0.19340790808200836, "Y": 0.5013659000396729}, {"X": 0.16941311955451965, "Y": 0.5014439821243286}]}, "Id": "b0db5ea8-dd96-4708-a830-396f8883cb8e", "Relationships": [{"Type": "CHILD", "Ids": ["9269c3be-26e8-4ed5-9027-399434addbbb"]}]}, {"BlockType": "LINE", "Confidence": 98.72887420654297, "Text": "Diesel 2", "Geometry": {"BoundingBox": {"Width": 0.09105007350444794, "Height": 0.019007572904229164, "Left": 0.3619249165058136, "Top": 0.48205772042274475}, "Polygon": [{"X": 0.3619249165058136, "Y": 0.4823918640613556}, {"X": 0.4521755278110504, "Y": 0.48205772042274475}, {"X": 0.45297500491142273, "Y": 0.5007724165916443}, {"X": 0.3628132939338684, "Y": 0.5010653138160706}]}, "Id": "9bd93eaf-d751-474d-9f95-ae0a1304e10c", "Relationships": [{"Type": "CHILD", "Ids": ["4d88b6dd-0d5d-40f5-a508-08a80fea8aee", "d8133791-f3c5-43d4-b1c9-42abab307c5f"]}]}, {"BlockType": "LINE", "Confidence": 99.87145233154297, "Text": "N", "Geometry": {"BoundingBox": {"Width": 0.011491024866700172, "Height": 0.0167912058532238, "Left": 0.5100135207176208, "Top": 0.48050186038017273}, "Polygon": [{"X": 0.5100135207176208, "Y": 0.4805423319339752}, {"X": 0.5208507776260376, "Y": 0.48050186038017273}, {"X": 0.5215045809745789, "Y": 0.4972570240497589}, {"X": 0.510676920413971, "Y": 0.49729305505752563}]}, "Id": "6b66ddb4-0c79-474f-837e-48b4bcaa6ce5", "Relationships": [{"Type": "CHILD", "Ids": ["50b24328-29b7-4d7f-847d-bb173b82547d"]}]}, {"BlockType": "LINE", "Confidence": 99.61595153808594, "Text": "103.875", "Geometry": {"BoundingBox": {"Width": 0.07105434685945511, "Height": 0.017517561092972755, "Left": 0.6030239462852478, "Top": 0.47747036814689636}, "Polygon": [{"X": 0.6030239462852478, "Y": 0.4777378439903259}, {"X": 0.6735460162162781, "Y": 0.47747036814689636}, {"X": 0.6740782856941223, "Y": 0.4947500228881836}, {"X": 0.603620707988739, "Y": 0.49498793482780457}]}, "Id": "74a773c3-c01b-4b3f-b39e-3382aabc6d03", "Relationships": [{"Type": "CHILD", "Ids": ["a7a90923-553c-40e8-9cb8-cbb497c5ad1b"]}]}, {"BlockType": "LINE", "Confidence": 99.73313903808594, "Text": "3.499", "Geometry": {"BoundingBox": {"Width": 0.0491974875330925, "Height": 0.01705878973007202, "Left": 0.7354100942611694, "Top": 0.4753817617893219}, "Polygon": [{"X": 0.7354100942611694, "Y": 0.4755687415599823}, {"X": 0.7841878533363342, "Y": 0.4753817617893219}, {"X": 0.7846075892448425, "Y": 0.4922735393047333}, {"X": 0.7358735799789429, "Y": 0.4924405515193939}]}, "Id": "9bef0f14-09e4-4328-b392-eed84fe2ca88", "Relationships": [{"Type": "CHILD", "Ids": ["79335d1a-1e42-4f32-8a2e-5614cb3406cc"]}]}, {"BlockType": "LINE", "Confidence": 99.60299682617188, "Text": "363.46", "Geometry": {"BoundingBox": {"Width": 0.053930822759866714, "Height": 0.016017938032746315, "Left": 0.8312375545501709, "Top": 0.474378377199173}, "Polygon": [{"X": 0.8312375545501709, "Y": 0.4745847284793854}, {"X": 0.8848606944084167, "Y": 0.474378377199173}, {"X": 0.8851683735847473, "Y": 0.49021050333976746}, {"X": 0.8315904140472412, "Y": 0.49039632081985474}]}, "Id": "f0aee71e-ef2b-45e0-8c26-51aefecbd04d", "Relationships": [{"Type": "CHILD", "Ids": ["a4cbb3cd-88e7-4fbf-be5a-1bb78c4d0e3b"]}]}, {"BlockType": "LINE", "Confidence": 98.64427185058594, "Text": "subtotal =", "Geometry": {"BoundingBox": {"Width": 0.10586842894554138, "Height": 0.016927938908338547, "Left": 0.28005534410476685, "Top": 0.5353136658668518}, "Polygon": [{"X": 0.28005534410476685, "Y": 0.5355674624443054}, {"X": 0.3851471543312073, "Y": 0.5353136658668518}, {"X": 0.3859237730503082, "Y": 0.552030622959137}, {"X": 0.280924528837204, "Y": 0.5522415637969971}]}, "Id": "bbb7eae4-0b86-4363-93ad-f6330018bba7", "Relationships": [{"Type": "CHILD", "Ids": ["ad7b0e9c-5315-437d-bf92-22735af2708d", "2cce38f9-a200-4745-bb3a-8ea64fe0f4b0"]}]}, {"BlockType": "LINE", "Confidence": 98.57720947265625, "Text": "363.46", "Geometry": {"BoundingBox": {"Width": 0.06809592992067337, "Height": 0.01801452226936817, "Left": 0.43044766783714294, "Top": 0.5326091647148132}, "Polygon": [{"X": 0.43044766783714294, "Y": 0.53277587890625}, {"X": 0.4978218674659729, "Y": 0.5326091647148132}, {"X": 0.4985435903072357, "Y": 0.5504863262176514}, {"X": 0.43123310804367065, "Y": 0.5506237149238586}]}, "Id": "0f545d7d-c990-4761-b74a-649f2e05261c", "Relationships": [{"Type": "CHILD", "Ids": ["a752d2a1-d9a3-43f0-ab89-fa48f815ee44"]}]}, {"BlockType": "LINE", "Confidence": 99.628173828125, "Text": "sales tax =", "Geometry": {"BoundingBox": {"Width": 0.11396884173154831, "Height": 0.017407331615686417, "Left": 0.27240124344825745, "Top": 0.5852007865905762}, "Polygon": [{"X": 0.27240124344825745, "Y": 0.5853369832038879}, {"X": 0.3855637311935425, "Y": 0.5852007865905762}, {"X": 0.38637009263038635, "Y": 0.6025195121765137}, {"X": 0.27331113815307617, "Y": 0.6026080846786499}]}, "Id": "6b6b0ed9-f898-4335-b4b7-6378ec80c058", "Relationships": [{"Type": "CHILD", "Ids": ["cad97931-570b-41f7-9701-9d576dcad7e5", "a1dcc4d0-d1e6-46b2-9d29-5b6496c14b6d", "909760e0-9de3-4f3c-84a8-fed56bcaa114"]}]}, {"BlockType": "LINE", "Confidence": 99.*********, "Text": "0.00", "Geometry": {"BoundingBox": {"Width": 0.04632358253002167, "Height": 0.01736222580075264, "Left": 0.45406076312065125, "Top": 0.5838415622711182}, "Polygon": [{"X": 0.45406076312065125, "Y": 0.5838978886604309}, {"X": 0.49968472123146057, "Y": 0.5838415622711182}, {"X": 0.5003843307495117, "Y": 0.6011667251586914}, {"X": 0.45480236411094666, "Y": 0.6012037992477417}]}, "Id": "c872ba1b-4e5b-45b9-9ce5-d0d90be98bad", "Relationships": [{"Type": "CHILD", "Ids": ["500aa039-f6ad-4f66-b5ea-3091fb32d6ba"]}]}, {"BlockType": "LINE", "Confidence": 99.64564514160156, "Text": "TOTAL =", "Geometry": {"BoundingBox": {"Width": 0.07556217908859253, "Height": 0.01768774725496769, "Left": 0.3112349808216095, "Top": 0.6099181771278381}, "Polygon": [{"X": 0.3112349808216095, "Y": 0.6099632978439331}, {"X": 0.3859735131263733, "Y": 0.6099181771278381}, {"X": 0.386797159910202, "Y": 0.6275928616523743}, {"X": 0.31212854385375977, "Y": 0.6276059150695801}]}, "Id": "f5b506d6-1717-44f7-8d7e-f40bad1fe833", "Relationships": [{"Type": "CHILD", "Ids": ["462cb787-68f4-4cfd-aab7-057d6bc85ac2", "d59774ef-42b0-4d90-8af6-30efadb4481b"]}]}, {"BlockType": "LINE", "Confidence": 99.06320190429688, "Text": "363.46 Signature:", "Geometry": {"BoundingBox": {"Width": 0.20189882814884186, "Height": 0.021975580602884293, "Left": 0.43180152773857117, "Top": 0.6074680685997009}, "Polygon": [{"X": 0.43180152773857117, "Y": 0.607600748538971}, {"X": 0.6329712867736816, "Y": 0.6074680685997009}, {"X": 0.6337003707885742, "Y": 0.6294175982475281}, {"X": 0.43276506662368774, "Y": 0.6294436454772949}]}, "Id": "b1ed4f77-18cd-4823-95dd-f0634f1126fe", "Relationships": [{"Type": "CHILD", "Ids": ["af3255b6-38d7-4855-b2ad-a50faa1f347c", "4d65b93a-6800-40e8-9e8c-da071e155541"]}]}, {"BlockType": "LINE", "Confidence": 95.33837890625, "Text": "<PERSON><PERSON> I agree to pay total amount", "Geometry": {"BoundingBox": {"Width": 0.2935501039028168, "Height": 0.09370014071464539, "Left": 0.6495888829231262, "Top": 0.5526717305183411}, "Polygon": [{"X": 0.6495888829231262, "Y": 0.5532461404800415}, {"X": 0.9415975213050842, "Y": 0.5526717305183411}, {"X": 0.9431390166282654, "Y": 0.6463718414306641}, {"X": 0.****************, "Y": 0.6462912559509277}]}, "Id": "b75d57c0-2b76-45e3-a14f-a2b2f1d9e779", "Relationships": [{"Type": "CHILD", "Ids": ["41ea137f-aa08-4185-bb8b-49faf9055693", "b761a57e-3b7e-48ab-87ab-1b738725bc2c", "b12188cf-5e7d-4533-9571-8e4167d56902", "e42c339e-46b3-4180-a1d6-62bcc7184023", "9567c237-43b1-4698-bae8-f65af9e979d0", "de136eda-b9c4-456b-a265-fa664d91d4e3", "9a892da6-4556-4ac9-8f41-3f1825408663"]}]}, {"BlockType": "LINE", "Confidence": 99.*************, "Text": "according to card issuer agreement.", "Geometry": {"BoundingBox": {"Width": 0.3260726034641266, "Height": 0.04125523194670677, "Left": 0.647072970867157, "Top": 0.6309276819229126}, "Polygon": [{"X": 0.647072970867157, "Y": 0.6309577226638794}, {"X": 0.9725332260131836, "Y": 0.6309276819229126}, {"X": 0.973145604133606, "Y": 0.6721829175949097}, {"X": 0.6484031081199646, "Y": 0.6718922257423401}]}, "Id": "3cd1b844-013e-43bc-bf7b-0044da5a72a9", "Relationships": [{"Type": "CHILD", "Ids": ["54bb437c-340b-4e04-96f6-3fda0ef757dd", "85faae9c-541a-482d-b161-fcd7da9d78f8", "d3802d42-4553-4c8c-92af-1b637f2f9b69", "a192649f-751c-43c6-b8d6-9569dd4676ad", "2e1956c6-cd8e-47b8-91d2-344d93eb714c"]}]}, {"BlockType": "LINE", "Confidence": 99.27130126953125, "Text": "Salesperson ID: AUTO/SMARTFUEL", "Geometry": {"BoundingBox": {"Width": 0.31233084201812744, "Height": 0.030049802735447884, "Left": 0.2129642814397812, "Top": 0.6957677602767944}, "Polygon": [{"X": 0.2129642814397812, "Y": 0.6957677602767944}, {"X": 0.5241323709487915, "Y": 0.6962303519248962}, {"X": 0.5252951383590698, "Y": 0.7258175611495972}, {"X": 0.2146156132221222, "Y": 0.7251332402229309}]}, "Id": "7481e1f0-fd1d-41c9-8f73-93d6999c2abc", "Relationships": [{"Type": "CHILD", "Ids": ["fa593aff-ac3a-4022-9139-88cce8e35c90", "466d01f7-612f-4075-85e9-7b816bce85a9", "7e4721df-cbec-4615-a6a5-690c7b84b231"]}]}, {"BlockType": "LINE", "Confidence": 98.92106628417969, "Text": "Invoice #: 0091223", "Geometry": {"BoundingBox": {"Width": 0.17759524285793304, "Height": 0.031095776706933975, "Left": 0.6921435594558716, "Top": 0.6863973736763}, "Polygon": [{"X": 0.6921435594558716, "Y": 0.6863973736763}, {"X": 0.8691054582595825, "Y": 0.6866174340248108}, {"X": 0.8697388172149658, "Y": 0.7174931168556213}, {"X": 0.6930702328681946, "Y": 0.7171425223350525}]}, "Id": "90bec5cc-44e4-4d61-9a04-47ca2477067a", "Relationships": [{"Type": "CHILD", "Ids": ["c56f2afe-0053-41fc-9f48-da43fd129ed9", "e4360de3-39be-431d-9026-c36a32bc06de", "00a9b08c-5840-4936-8b41-47c2587ef6ae"]}]}, {"BlockType": "LINE", "Confidence": 99.7396469116211, "Text": "Truck Number: 3482", "Geometry": {"BoundingBox": {"Width": 0.18084286153316498, "Height": 0.029282674193382263, "Left": 0.23135431110858917, "Top": 0.721206784248352}, "Polygon": [{"X": 0.23135431110858917, "Y": 0.721206784248352}, {"X": 0.41088125109672546, "Y": 0.7215848565101624}, {"X": 0.41219717264175415, "Y": 0.7504894733428955}, {"X": 0.23294608294963837, "Y": 0.7499862909317017}]}, "Id": "a87df70f-9a5a-4d78-a8b9-77082738edf1", "Relationships": [{"Type": "CHILD", "Ids": ["d6ac2213-2c6f-4f8a-bb27-50ddff85c85d", "1192dd81-da94-4ef9-962c-1c096bc92c76", "5af6d568-aa63-48a7-a840-40687cb59115"]}]}, {"BlockType": "LINE", "Confidence": 99.00749206542969, "Text": "Merchant Number: *******1398", "Geometry": {"BoundingBox": {"Width": 0.28144121170043945, "Height": 0.04227790981531143, "Left": 0.6261391043663025, "Top": 0.7061774134635925}, "Polygon": [{"X": 0.6261391043663025, "Y": 0.7061774134635925}, {"X": 0.9068077802658081, "Y": 0.7066610455513}, {"X": 0.9075803160667419, "Y": 0.7484553456306458}, {"X": 0.6275409460067749, "Y": 0.7476921677589417}]}, "Id": "6f46d2d9-f871-4050-9b3a-861d4a4ea72c", "Relationships": [{"Type": "CHILD", "Ids": ["54f77beb-8b5a-410a-b5bf-162c0293552f", "339a8c7c-cc57-40ff-83ab-7901a6cf20fc", "9a01d72a-02ef-40f3-9230-e6266cb6df5f"]}]}, {"BlockType": "LINE", "Confidence": 90.68344116210938, "Text": "Card Number: xxxxxxxxxxxxxx1773 -ICR-", "Geometry": {"BoundingBox": {"Width": 0.3985545039176941, "Height": 0.030425261706113815, "Left": 0.2411057949066162, "Top": 0.746216356754303}, "Polygon": [{"X": 0.2411057949066162, "Y": 0.746216356754303}, {"X": 0.638687252998352, "Y": 0.7472957372665405}, {"X": 0.6396602988243103, "Y": 0.7766416668891907}, {"X": 0.24270017445087433, "Y": 0.7752829194068909}]}, "Id": "6ca1ee5a-3e98-4ddb-9b59-b3c783d0b99a", "Relationships": [{"Type": "CHILD", "Ids": ["356d63f2-58c5-4d29-b4e3-3756fe5e4f6b", "c044adcd-afc6-4f7f-8388-b7c9d7f679fd", "43219bb3-0304-439c-9c34-7400fac2c048", "2965e3dd-9ceb-4916-ac13-35b673470d90"]}]}, {"BlockType": "LINE", "Confidence": 95.15022277832031, "Text": "Loyalty Card#: xxxxxxxxxxxx8997 -ICR-", "Geometry": {"BoundingBox": {"Width": 0.3934252858161926, "Height": 0.03549645468592644, "Left": 0.22445522248744965, "Top": 0.7656084895133972}, "Polygon": [{"X": 0.22445522248744965, "Y": 0.7656084895133972}, {"X": 0.6167026162147522, "Y": 0.766859233379364}, {"X": 0.6178805232048035, "Y": 0.8011049628257751}, {"X": 0.2263488918542862, "Y": 0.7995328307151794}]}, "Id": "bab43585-ed24-4e8a-a822-beb806428d28", "Relationships": [{"Type": "CHILD", "Ids": ["f783f579-0246-454d-87de-7e9b7713120b", "21a32567-773d-4a4e-8a97-298b2c90a844", "a9f3052b-ead7-474d-8694-8f82204a36a6", "7e0c3f7c-c4d8-4158-9325-19e63f118bbf"]}]}, {"BlockType": "LINE", "Confidence": 98.90852355957031, "Text": "Billing Company: NBS / KWIK TRIP FLEET", "Geometry": {"BoundingBox": {"Width": 0.3861217796802521, "Height": 0.05078795179724693, "Left": 0.18706189095973969, "Top": 0.8014000654220581}, "Polygon": [{"X": 0.18706189095973969, "Y": 0.8014000654220581}, {"X": 0.5713650584220886, "Y": 0.8029621243476868}, {"X": 0.5731836557388306, "Y": 0.852187991142273}, {"X": 0.18988963961601257, "Y": 0.8501741290092468}]}, "Id": "5dbecea1-6e8a-4601-85a5-8e7bd90015bf", "Relationships": [{"Type": "CHILD", "Ids": ["484f98b5-d580-4dda-81d1-4b0143cf3e31", "6fde9aac-7de6-430d-ad42-6389a3f0c009", "a5df40ca-3181-4de7-abde-761d4d8b79a6", "e9eb9d40-d113-43bd-b795-308d64b431e6", "88aa43c2-4557-4e18-9f72-87e183b03847", "8ce87358-ded1-406f-b5fe-5a412e47ba93", "af459524-fb2f-4166-9f2f-7628e6a6c107"]}]}, {"BlockType": "LINE", "Confidence": 91.74882507324219, "Text": "ALL TAXES INCLUDED IN FUEL PRICE ----------------- THANK YOU!", "Geometry": {"BoundingBox": {"Width": 0.5069026350975037, "Height": 0.05960991233587265, "Left": 0.18947510421276093, "Top": 0.844785749912262}, "Polygon": [{"X": 0.18947510421276093, "Y": 0.844785749912262}, {"X": 0.6946517825126648, "Y": 0.8473736643791199}, {"X": 0.6963777542114258, "Y": 0.9043956398963928}, {"X": 0.19274170696735382, "Y": 0.9011237025260925}]}, "Id": "07d11bae-c8b9-44c7-acb6-19185530a61d", "Relationships": [{"Type": "CHILD", "Ids": ["918da1a5-0a18-45fd-b66a-e3852f450887", "2c550151-092d-4f81-ab9f-081aee7b5b1d", "404d3d0a-606a-485b-b58e-26f51292dbfc", "d0bea79b-92ad-4665-ab07-5de5dc2d3a79", "ba1678c2-f9ab-41e5-9c47-3086ed73b8db", "fb776747-97f9-4948-82f0-fd3bddc2aa9d", "2a571c0a-5a49-4152-98bb-32df7c211340", "00b55a19-2d25-494a-a155-755ed5993cd9", "082da029-d091-41e0-95fc-4f59ec72fba6"]}]}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03830670192837715, "Height": 0.0198777224868536, "Left": 0.1528266966342926, "Top": 0.2652073800563812}, "Polygon": [{"X": 0.1528266966342926, "Y": 0.2655428647994995}, {"X": 0.19003477692604065, "Y": 0.2652073800563812}, {"X": 0.19113339483737946, "Y": 0.284767746925354}, {"X": 0.15396298468112946, "Y": 0.28508511185646057}]}, "Id": "0ee58a0a-7a8d-4996-ad46-9b4521a8880c"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03989313915371895, "Height": 0.0201865267008543, "Left": 0.1981399655342102, "Top": 0.26066434383392334}, "Polygon": [{"X": 0.1981399655342102, "Y": 0.261018306016922}, {"X": 0.23696793615818024, "Y": 0.26066434383392334}, {"X": 0.23803311586380005, "Y": 0.28051602840423584}, {"X": 0.19924508035182953, "Y": 0.2808508574962616}]}, "Id": "383a6858-e8e0-4a32-9fe8-049ab5247e91"}, {"BlockType": "WORD", "Confidence": 99.404296875, "Text": "139", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031207719817757607, "Height": 0.019505351781845093, "Left": 0.2475915402173996, "Top": 0.25710082054138184}, "Polygon": [{"X": 0.2475915402173996, "Y": 0.25737863779067993}, {"X": 0.2778087556362152, "Y": 0.25710082054138184}, {"X": 0.27879926562309265, "Y": 0.27634280920028687}, {"X": 0.2486121952533722, "Y": 0.27660617232322693}]}, "Id": "0b8f93c5-56b7-4239-b3dd-2c2e49517a03"}, {"BlockType": "WORD", "Confidence": 99.4361801147461, "Text": "/7002", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056029029190540314, "Height": 0.018834764137864113, "Left": 0.28993499279022217, "Top": 0.2545042335987091}, "Polygon": [{"X": 0.28993499279022217, "Y": 0.25501394271850586}, {"X": 0.34508517384529114, "Y": 0.2545042335987091}, {"X": 0.3459640145301819, "Y": 0.27285438776016235}, {"X": 0.2908663749694824, "Y": 0.27333900332450867}]}, "Id": "6771651a-a68e-4ae7-b466-53620c75cc50"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "SAUK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04613117501139641, "Height": 0.018432999029755592, "Left": 0.35710206627845764, "Top": 0.25275641679763794}, "Polygon": [{"X": 0.35710206627845764, "Y": 0.25317662954330444}, {"X": 0.4024246037006378, "Y": 0.25275641679763794}, {"X": 0.40323323011398315, "Y": 0.2707894444465637}, {"X": 0.35795319080352783, "Y": 0.271189421415329}]}, "Id": "97a19d7a-6202-479f-ac72-ec566d45c693"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TRAIL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05746884644031525, "Height": 0.0181343425065279, "Left": 0.41494014859199524, "Top": 0.25137439370155334}, "Polygon": [{"X": 0.41494014859199524, "Y": 0.2519014775753021}, {"X": 0.4716832935810089, "Y": 0.25137439370155334}, {"X": 0.4724090099334717, "Y": 0.26900631189346313}, {"X": 0.4157179296016693, "Y": 0.2695087194442749}]}, "Id": "19fa466d-f25e-4ca2-92c2-1d4ef2d36cf2"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "RD.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028827156871557236, "Height": 0.017753643915057182, "Left": 0.4845552146434784, "Top": 0.25046005845069885}, "Polygon": [{"X": 0.4845552146434784, "Y": 0.25072184205055237}, {"X": 0.5127001404762268, "Y": 0.25046005845069885}, {"X": 0.5133823752403259, "Y": 0.26796403527259827}, {"X": 0.4852631390094757, "Y": 0.26821368932724}]}, "Id": "02d671ea-32f6-4ab6-99c8-41ddf6d3a279"}, {"BlockType": "WORD", "Confidence": 99.23529052734375, "Text": "/SHEBOYGAN,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11667481809854507, "Height": 0.02589098922908306, "Left": 0.5308560132980347, "Top": 0.24762924015522003}, "Polygon": [{"X": 0.5308560132980347, "Y": 0.24871163070201874}, {"X": 0.6467384696006775, "Y": 0.24762924015522003}, {"X": 0.6475308537483215, "Y": 0.2725087106227875}, {"X": 0.5317989587783813, "Y": 0.27352023124694824}]}, "Id": "d4994454-90b2-4a77-bb1f-1bddc2b70556"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "WI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019455039873719215, "Height": 0.018166041001677513, "Left": 0.6609216332435608, "Top": 0.2524193525314331}, "Polygon": [{"X": 0.6609216332435608, "Y": 0.2525936961174011}, {"X": 0.6798349022865295, "Y": 0.2524193525314331}, {"X": 0.6803767085075378, "Y": 0.2704194486141205}, {"X": 0.6614812612533569, "Y": 0.27058541774749756}]}, "Id": "e81d8d2a-77d3-4d64-bbc3-00b6b0f72706"}, {"BlockType": "WORD", "Confidence": 92.40433502197266, "Text": "53081/(920)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10469674319028854, "Height": 0.0244735274463892, "Left": 0.6908394694328308, "Top": 0.2535606324672699}, "Polygon": [{"X": 0.6908394694328308, "Y": 0.2545149326324463}, {"X": 0.7949707508087158, "Y": 0.2535606324672699}, {"X": 0.7955362200737, "Y": 0.27713996171951294}, {"X": 0.6915337443351746, "Y": 0.27803415060043335}]}, "Id": "1b2f8799-7e24-4645-a33a-4d0a876ee2b7"}, {"BlockType": "WORD", "Confidence": 96.84012603759766, "Text": "457-1028", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07188337296247482, "Height": 0.02240133099257946, "Left": 0.8066659569740295, "Top": 0.26031753420829773}, "Polygon": [{"X": 0.8066659569740295, "Y": 0.26095953583717346}, {"X": 0.8781227469444275, "Y": 0.26031753420829773}, {"X": 0.878549337387085, "Y": 0.2821149528026581}, {"X": 0.8071744441986084, "Y": 0.28271886706352234}]}, "Id": "626146a0-1a66-4a9d-b021-a49c7dec790a"}, {"BlockType": "WORD", "Confidence": 99.75186920166016, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06275273114442825, "Height": 0.0190527755767107, "Left": 0.15474748611450195, "Top": 0.2905873656272888}, "Polygon": [{"X": 0.15474748611450195, "Y": 0.29110538959503174}, {"X": 0.21648234128952026, "Y": 0.2905873656272888}, {"X": 0.2175002098083496, "Y": 0.3091505467891693}, {"X": 0.15582475066184998, "Y": 0.30964013934135437}]}, "Id": "0784520f-cdaf-4e22-a95e-db9300e23987"}, {"BlockType": "WORD", "Confidence": 98.60192108154297, "Text": "0900-1223", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09530305117368698, "Height": 0.02183794230222702, "Left": 0.22828759253025055, "Top": 0.2813170552253723}, "Polygon": [{"X": 0.22828759253025055, "Y": 0.2821272909641266}, {"X": 0.32255449891090393, "Y": 0.2813170552253723}, {"X": 0.32359063625335693, "Y": 0.30239394307136536}, {"X": 0.22942687571048737, "Y": 0.30315500497817993}]}, "Id": "63e901e9-f934-4bf3-8aa3-178b03544427"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "08/13/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11641933768987656, "Height": 0.020048733800649643, "Left": 0.34588462114334106, "Top": 0.2778943181037903}, "Polygon": [{"X": 0.34588462114334106, "Y": 0.278894305229187}, {"X": 0.4615062475204468, "Y": 0.2778943181037903}, {"X": 0.4623039662837982, "Y": 0.2969975173473358}, {"X": 0.34679728746414185, "Y": 0.29794302582740784}]}, "Id": "05a1fda4-a388-43d1-84d2-d9dc7dc977aa"}, {"BlockType": "WORD", "Confidence": 98.6756591796875, "Text": "Station:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0879829078912735, "Height": 0.018643496558070183, "Left": 0.48473358154296875, "Top": 0.275536447763443}, "Polygon": [{"X": 0.48473358154296875, "Y": 0.2762947976589203}, {"X": 0.5720735788345337, "Y": 0.275536447763443}, {"X": 0.572716474533081, "Y": 0.29346010088920593}, {"X": 0.48545825481414795, "Y": 0.2941799461841583}]}, "Id": "e6c930a8-c96a-4b3e-8703-452bebab5576"}, {"BlockType": "WORD", "Confidence": 99.27754211425781, "Text": "9", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010368294082581997, "Height": 0.01678067073225975, "Left": 0.5875418782234192, "Top": 0.2756173312664032}, "Polygon": [{"X": 0.5875418782234192, "Y": 0.27570226788520813}, {"X": 0.5973336100578308, "Y": 0.2756173312664032}, {"X": 0.5979101657867432, "Y": 0.29231706261634827}, {"X": 0.5881270170211792, "Y": 0.29239797592163086}]}, "Id": "375680fe-0728-411d-a809-fb8ec3a15ce7"}, {"BlockType": "WORD", "Confidence": 99.76243591308594, "Text": "Response:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0795515850186348, "Height": 0.02011401206254959, "Left": 0.15938661992549896, "Top": 0.34101277589797974}, "Polygon": [{"X": 0.15938661992549896, "Y": 0.34157466888427734}, {"X": 0.23788350820541382, "Y": 0.34101277589797974}, {"X": 0.23893821239471436, "Y": 0.36060288548469543}, {"X": 0.1605212539434433, "Y": 0.36112678050994873}]}, "Id": "55b3d5fc-b4ed-4651-8f93-7ae45f63cb06"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "APPROVED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08688801527023315, "Height": 0.02089981734752655, "Left": 0.24958236515522003, "Top": 0.3326759934425354}, "Polygon": [{"X": 0.24958236515522003, "Y": 0.33330681920051575}, {"X": 0.3354831039905548, "Y": 0.3326759934425354}, {"X": 0.3364703953266144, "Y": 0.35298800468444824}, {"X": 0.2506605088710785, "Y": 0.35357579588890076}]}, "Id": "541b8f19-012b-4631-a910-045976f7b8a4"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "AUTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0466412715613842, "Height": 0.018459510058164597, "Left": 0.3471364676952362, "Top": 0.3311998248100281}, "Polygon": [{"X": 0.3471364676952362, "Y": 0.3315374553203583}, {"X": 0.392951637506485, "Y": 0.3311998248100281}, {"X": 0.3937777280807495, "Y": 0.3493421971797943}, {"X": 0.3480059504508972, "Y": 0.3496593236923218}]}, "Id": "8e0c4d16-174f-417a-abcb-7ba5654629ee"}, {"BlockType": "WORD", "Confidence": 97.69132995605469, "Text": "REF#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05412719026207924, "Height": 0.01840526983141899, "Left": 0.40535441040992737, "Top": 0.32949352264404297}, "Polygon": [{"X": 0.40535441040992737, "Y": 0.32988840341567993}, {"X": 0.458723783493042, "Y": 0.32949352264404297}, {"X": 0.4594815969467163, "Y": 0.3475275933742523}, {"X": 0.4061625301837921, "Y": 0.34789881110191345}]}, "Id": "14480c72-5dfe-482f-8c01-07c93b7494d9"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "P19403", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06938216090202332, "Height": 0.018611742183566093, "Left": 0.47315841913223267, "Top": 0.32750675082206726}, "Polygon": [{"X": 0.47315841913223267, "Y": 0.32801735401153564}, {"X": 0.5418591499328613, "Y": 0.32750675082206726}, {"X": 0.5425406098365784, "Y": 0.3456384837627411}, {"X": 0.4739050567150116, "Y": 0.3461185097694397}]}, "Id": "0dbaf98a-1e80-486a-bd93-e653f44805c4"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "LOYALTY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06404946744441986, "Height": 0.0185606237500906, "Left": 0.16205789148807526, "Top": 0.38788214325904846}, "Polygon": [{"X": 0.16205789148807526, "Y": 0.388261616230011}, {"X": 0.22511209547519684, "Y": 0.38788214325904846}, {"X": 0.22610735893249512, "Y": 0.4060916304588318}, {"X": 0.16311299800872803, "Y": 0.4064427614212036}]}, "Id": "56fd1410-b054-41c2-8dd5-6e6aa600030d"}, {"BlockType": "WORD", "Confidence": 99.44276428222656, "Text": "Response:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08882289379835129, "Height": 0.020954860374331474, "Left": 0.2347767949104309, "Top": 0.3865700364112854}, "Polygon": [{"X": 0.2347767949104309, "Y": 0.3871000409126282}, {"X": 0.322587788105011, "Y": 0.3865700364112854}, {"X": 0.3235996961593628, "Y": 0.4070391356945038}, {"X": 0.23588253557682037, "Y": 0.4075249135494232}]}, "Id": "11d087b8-00d0-4266-9fb7-de45e1078d66"}, {"BlockType": "WORD", "Confidence": 99.49238586425781, "Text": "APPROVED,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09795131534337997, "Height": 0.020897457376122475, "Left": 0.3367598354816437, "Top": 0.3815302550792694}, "Polygon": [{"X": 0.3367598354816437, "Y": 0.38212642073631287}, {"X": 0.4338265359401703, "Y": 0.3815302550792694}, {"X": 0.43471115827560425, "Y": 0.40188008546829224}, {"X": 0.33774781227111816, "Y": 0.4024277329444885}]}, "Id": "564377d6-c23d-4875-bcc6-ee3ff7f00ea2"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "You", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034748584032058716, "Height": 0.017048519104719162, "Left": 0.4506526291370392, "Top": 0.380118191242218}, "Polygon": [{"X": 0.4506526291370392, "Y": 0.38032829761505127}, {"X": 0.4847145676612854, "Y": 0.380118191242218}, {"X": 0.4854012131690979, "Y": 0.3969706892967224}, {"X": 0.45136940479278564, "Y": 0.3971666991710663}]}, "Id": "950c0feb-ecc1-436e-8b6a-1f7cbcc16d4b"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "earned", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06748142838478088, "Height": 0.018674097955226898, "Left": 0.49759694933891296, "Top": 0.3775862157344818}, "Polygon": [{"X": 0.49759694933891296, "Y": 0.3780016303062439}, {"X": 0.5644112825393677, "Y": 0.3775862157344818}, {"X": 0.5650783777236938, "Y": 0.3958747982978821}, {"X": 0.49832823872566223, "Y": 0.3962603211402893}]}, "Id": "3b26626c-de2c-47f8-8dfd-2ba6684a28ee"}, {"BlockType": "WORD", "Confidence": 99.0625, "Text": "2.06", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04236120730638504, "Height": 0.01800246722996235, "Left": 0.5770240426063538, "Top": 0.37699267268180847}, "Polygon": [{"X": 0.5770240426063538, "Y": 0.37725260853767395}, {"X": 0.6187891364097595, "Y": 0.37699267268180847}, {"X": 0.6193852424621582, "Y": 0.3947533667087555}, {"X": 0.5776592493057251, "Y": 0.39499515295028687}]}, "Id": "fa278b20-107a-4695-99ff-7e59d384ba0d"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Fleet", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04949772357940674, "Height": 0.017650337889790535, "Left": 0.6300654411315918, "Top": 0.37765371799468994}, "Polygon": [{"X": 0.6300654411315918, "Y": 0.377957284450531}, {"X": 0.6790362000465393, "Y": 0.37765371799468994}, {"X": 0.6795631647109985, "Y": 0.3950212597846985}, {"X": 0.6306372880935669, "Y": 0.3953040540218353}]}, "Id": "862dacd4-84fe-47d1-9e67-9846bf3e41cf"}, {"BlockType": "WORD", "Confidence": 99.41266632080078, "Text": "points!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0667300894856453, "Height": 0.02007148414850235, "Left": 0.6915141344070435, "Top": 0.37936729192733765}, "Polygon": [{"X": 0.6915141344070435, "Y": 0.3797742426395416}, {"X": 0.7577294111251831, "Y": 0.37936729192733765}, {"X": 0.7582442164421082, "Y": 0.3990636169910431}, {"X": 0.6920977234840393, "Y": 0.3994387984275818}]}, "Id": "bfeb0506-4e35-4af8-b265-0875e704f3e5"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Drink", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0482763908803463, "Height": 0.016147589311003685, "Left": 0.7700286507606506, "Top": 0.38018885254859924}, "Polygon": [{"X": 0.7700286507606506, "Y": 0.38048192858695984}, {"X": 0.8179412484169006, "Y": 0.38018885254859924}, {"X": 0.8183050155639648, "Y": 0.39606186747550964}, {"X": 0.7704325914382935, "Y": 0.3963364362716675}]}, "Id": "df28f706-e419-4a05-987a-628b957f8488"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Credit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05245257169008255, "Height": 0.017171110957860947, "Left": 0.8281650543212891, "Top": 0.3801395893096924}, "Polygon": [{"X": 0.8281650543212891, "Y": 0.3804579973220825}, {"X": 0.880287230014801, "Y": 0.3801395893096924}, {"X": 0.880617618560791, "Y": 0.3970136344432831}, {"X": 0.8285420536994934, "Y": 0.39731070399284363}]}, "Id": "1a197ab9-9cdf-48e8-9fc6-3a0273590c1c"}, {"BlockType": "WORD", "Confidence": 99.68192291259766, "Text": "Earned!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05727224797010422, "Height": 0.016974689438939095, "Left": 0.8900437951087952, "Top": 0.38202396035194397}, "Polygon": [{"X": 0.8900437951087952, "Y": 0.38236910104751587}, {"X": 0.9470494389533997, "Y": 0.38202396035194397}, {"X": 0.94731605052948, "Y": 0.3986765444278717}, {"X": 0.8903607726097107, "Y": 0.3989986479282379}]}, "Id": "290962f3-dc17-4565-8686-e76ad824a744"}, {"BlockType": "WORD", "Confidence": 98.46480560302734, "Text": "E", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008149237371981144, "Height": 0.015063883736729622, "Left": 0.9558118581771851, "Top": 0.38413336873054504}, "Polygon": [{"X": 0.9558118581771851, "Y": 0.3841809034347534}, {"X": 0.9637340307235718, "Y": 0.38413336873054504}, {"X": 0.963961124420166, "Y": 0.39915260672569275}, {"X": 0.9560452699661255, "Y": 0.3991972506046295}]}, "Id": "f2a373b9-bcd4-408b-b534-a32516690375"}, {"BlockType": "WORD", "Confidence": 99.6314926147461, "Text": "xpires", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05263783037662506, "Height": 0.019986296072602272, "Left": 0.1648235321044922, "Top": 0.4134599566459656}, "Polygon": [{"X": 0.1648235321044922, "Y": 0.41373810172080994}, {"X": 0.21637208759784698, "Y": 0.4134599566459656}, {"X": 0.21746136248111725, "Y": 0.433193176984787}, {"X": 0.16596588492393494, "Y": 0.4334462583065033}]}, "Id": "83f51cb1-f3f9-4198-94e9-0faafe9807a6"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016525371000170708, "Height": 0.016511300578713417, "Left": 0.2276383340358734, "Top": 0.4119725227355957}, "Polygon": [{"X": 0.2276383340358734, "Y": 0.41205745935440063}, {"X": 0.24328038096427917, "Y": 0.4119725227355957}, {"X": 0.24416370689868927, "Y": 0.4284052550792694}, {"X": 0.22853508591651917, "Y": 0.42848384380340576}]}, "Id": "bcb54313-d41c-4d52-9cab-efe7fb0d9f74"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "24", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02034575678408146, "Height": 0.017068199813365936, "Left": 0.25329872965812683, "Top": 0.41099482774734497}, "Polygon": [{"X": 0.25329872965812683, "Y": 0.4111008644104004}, {"X": 0.2727591395378113, "Y": 0.41099482774734497}, {"X": 0.27364450693130493, "Y": 0.4279651343822479}, {"X": 0.25420135259628296, "Y": 0.4280630350112915}]}, "Id": "28e050cd-eab2-48e7-b0db-de047ac4f5cb"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "Hours.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060651808977127075, "Height": 0.016810834407806396, "Left": 0.2835563123226166, "Top": 0.4100809395313263}, "Polygon": [{"X": 0.2835563123226166, "Y": 0.41040781140327454}, {"X": 0.3434094190597534, "Y": 0.4100809395313263}, {"X": 0.34420812129974365, "Y": 0.42658916115760803}, {"X": 0.28440672159194946, "Y": 0.4268917739391327}]}, "Id": "e7b3922a-1e6b-4887-9c82-28bc010338f6"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "Shower", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06812702119350433, "Height": 0.017382251098752022, "Left": 0.3605800271034241, "Top": 0.40753644704818726}, "Polygon": [{"X": 0.3605800271034241, "Y": 0.4079078137874603}, {"X": 0.42795997858047485, "Y": 0.40753644704818726}, {"X": 0.4287070333957672, "Y": 0.42457547783851624}, {"X": 0.36138731241226196, "Y": 0.42491868138313293}]}, "Id": "ac1e1740-004e-40cb-babf-c0f09cce0deb"}, {"BlockType": "WORD", "Confidence": 93.96524047851562, "Text": "C", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010903807356953621, "Height": 0.012858175672590733, "Left": 0.44040101766586304, "Top": 0.40998339653015137}, "Polygon": [{"X": 0.44040101766586304, "Y": 0.41003984212875366}, {"X": 0.4507589638233185, "Y": 0.40998339653015137}, {"X": 0.4513048231601715, "Y": 0.4227883815765381}, {"X": 0.4409538507461548, "Y": 0.4228415787220001}]}, "Id": "64f99701-e882-4fc0-994c-9219ba261a30"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Pump", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03554574400186539, "Height": 0.019807077944278717, "Left": 0.16674378514289856, "Top": 0.4619748890399933}, "Polygon": [{"X": 0.16674378514289856, "Y": 0.46212002635002136}, {"X": 0.20118437707424164, "Y": 0.4619748890399933}, {"X": 0.20228952169418335, "Y": 0.48165348172187805}, {"X": 0.16788439452648163, "Y": 0.4817819595336914}]}, "Id": "b208ee50-c349-416d-870d-8f99ce066a8f"}, {"BlockType": "WORD", "Confidence": 99.77220153808594, "Text": "Products", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08966726809740067, "Height": 0.01877777650952339, "Left": 0.3609897494316101, "Top": 0.4576737880706787}, "Polygon": [{"X": 0.3609897494316101, "Y": 0.45805537700653076}, {"X": 0.44986820220947266, "Y": 0.4576737880706787}, {"X": 0.4506570100784302, "Y": 0.4761100113391876}, {"X": 0.36186471581459045, "Y": 0.4764515459537506}]}, "Id": "38d1b8c0-8942-4148-86a5-eabc8fc12a4f"}, {"BlockType": "WORD", "Confidence": 99.775390625, "Text": "Reefer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0680665522813797, "Height": 0.017681226134300232, "Left": 0.48687344789505005, "Top": 0.45507505536079407}, "Polygon": [{"X": 0.48687344789505005, "Y": 0.4553680419921875}, {"X": 0.5542925596237183, "Y": 0.45507505536079407}, {"X": 0.5549399852752686, "Y": 0.472491979598999}, {"X": 0.4875827431678772, "Y": 0.4727562963962555}]}, "Id": "5852cf5b-f824-4dc6-8256-5b22baef98d2"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08215805143117905, "Height": 0.02107558399438858, "Left": 0.5896950960159302, "Top": 0.45281440019607544}, "Polygon": [{"X": 0.5896950960159302, "Y": 0.45317208766937256}, {"X": 0.6712119579315186, "Y": 0.45281440019607544}, {"X": 0.6718531250953674, "Y": 0.4735734760761261}, {"X": 0.5904256701469421, "Y": 0.4738899767398834}]}, "Id": "51867d2d-98da-4206-a316-8f35958a515c"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Unit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03981609642505646, "Height": 0.016701703891158104, "Left": 0.6928693652153015, "Top": 0.45227766036987305}, "Polygon": [{"X": 0.6928693652153015, "Y": 0.45245060324668884}, {"X": 0.732228696346283, "Y": 0.45227766036987305}, {"X": 0.7326854467391968, "Y": 0.4688222408294678}, {"X": 0.6933606266975403, "Y": 0.4689793586730957}]}, "Id": "ae9281f5-6d6a-48ca-a7e2-f58dc799e8b8"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Cost", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03812386468052864, "Height": 0.016208184882998466, "Left": 0.7442561388015747, "Top": 0.4518778324127197}, "Polygon": [{"X": 0.7442561388015747, "Y": 0.4520437717437744}, {"X": 0.7819796204566956, "Y": 0.4518778324127197}, {"X": 0.7823800444602966, "Y": 0.467934787273407}, {"X": 0.7446886897087097, "Y": 0.46808600425720215}]}, "Id": "36d279ac-8238-40c0-9cee-97f46026191a"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043630387634038925, "Height": 0.01600932516157627, "Left": 0.8390679955482483, "Top": 0.45104140043258667}, "Polygon": [{"X": 0.8390679955482483, "Y": 0.451232373714447}, {"X": 0.8823889493942261, "Y": 0.45104140043258667}, {"X": 0.8826984167098999, "Y": 0.46687638759613037}, {"X": 0.8394139409065247, "Y": 0.4670507311820984}]}, "Id": "a0a0eee7-15d5-41ae-a5ad-d89a74d7f934"}, {"BlockType": "WORD", "Confidence": 85.09247589111328, "Text": "# 1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02496218867599964, "Height": 0.016770021989941597, "Left": 0.16844572126865387, "Top": 0.48467394709587097}, "Polygon": [{"X": 0.16844572126865387, "Y": 0.48476189374923706}, {"X": 0.1924615055322647, "Y": 0.48467394709587097}, {"X": 0.19340790808200836, "Y": 0.5013659000396729}, {"X": 0.16941311955451965, "Y": 0.5014439821243286}]}, "Id": "9269c3be-26e8-4ed5-9027-399434addbbb"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "Diesel", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06613872200250626, "Height": 0.01821095496416092, "Left": 0.361958384513855, "Top": 0.48285433650016785}, "Polygon": [{"X": 0.361958384513855, "Y": 0.4830951392650604}, {"X": 0.42730408906936646, "Y": 0.48285433650016785}, {"X": 0.42809709906578064, "Y": 0.5008532404899597}, {"X": 0.3628132939338684, "Y": 0.5010653138160706}]}, "Id": "4d88b6dd-0d5d-40f5-a508-08a80fea8aee"}, {"BlockType": "WORD", "Confidence": 97.62535858154297, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01175614446401596, "Height": 0.0170934796333313, "Left": 0.441148042678833, "Top": 0.48205772042274475}, "Polygon": [{"X": 0.441148042678833, "Y": 0.4820985496044159}, {"X": 0.4521755278110504, "Y": 0.48205772042274475}, {"X": 0.45290419459342957, "Y": 0.499114*********}, {"X": 0.44188663363456726, "Y": 0.49915120005607605}]}, "Id": "d8133791-f3c5-43d4-b1c9-42abab307c5f"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "N", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011491024866700172, "Height": 0.0167912058532238, "Left": 0.5100135207176208, "Top": 0.48050186038017273}, "Polygon": [{"X": 0.5100135207176208, "Y": 0.4805423319339752}, {"X": 0.5208507776260376, "Y": 0.48050186038017273}, {"X": 0.5215045809745789, "Y": 0.4972570240497589}, {"X": 0.510676920413971, "Y": 0.49729305505752563}]}, "Id": "50b24328-29b7-4d7f-847d-bb173b82547d"}, {"BlockType": "WORD", "Confidence": 99.61595153808594, "Text": "103.875", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07105434685945511, "Height": 0.017517561092972755, "Left": 0.6030239462852478, "Top": 0.47747036814689636}, "Polygon": [{"X": 0.6030239462852478, "Y": 0.4777378439903259}, {"X": 0.6735460162162781, "Y": 0.47747036814689636}, {"X": 0.6740782856941223, "Y": 0.4947500228881836}, {"X": 0.603620707988739, "Y": 0.49498793482780457}]}, "Id": "a7a90923-553c-40e8-9cb8-cbb497c5ad1b"}, {"BlockType": "WORD", "Confidence": 99.73313903808594, "Text": "3.499", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0491974875330925, "Height": 0.01705878973007202, "Left": 0.7354100942611694, "Top": 0.4753817617893219}, "Polygon": [{"X": 0.7354100942611694, "Y": 0.4755687415599823}, {"X": 0.7841878533363342, "Y": 0.4753817617893219}, {"X": 0.7846075892448425, "Y": 0.4922735393047333}, {"X": 0.7358735799789429, "Y": 0.4924405515193939}]}, "Id": "79335d1a-1e42-4f32-8a2e-5614cb3406cc"}, {"BlockType": "WORD", "Confidence": 99.60299682617188, "Text": "363.46", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053930822759866714, "Height": 0.016017938032746315, "Left": 0.8312375545501709, "Top": 0.474378377199173}, "Polygon": [{"X": 0.8312375545501709, "Y": 0.4745847284793854}, {"X": 0.8848606944084167, "Y": 0.474378377199173}, {"X": 0.8851683735847473, "Y": 0.49021050333976746}, {"X": 0.8315904140472412, "Y": 0.49039632081985474}]}, "Id": "a4cbb3cd-88e7-4fbf-be5a-1bb78c4d0e3b"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "subtotal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08118629455566406, "Height": 0.01686827465891838, "Left": 0.28005534410476685, "Top": 0.535373330116272}, "Polygon": [{"X": 0.28005534410476685, "Y": 0.5355674624443054}, {"X": 0.36044320464134216, "Y": 0.535373330116272}, {"X": 0.3612416386604309, "Y": 0.5520802140235901}, {"X": 0.280924528837204, "Y": 0.5522415637969971}]}, "Id": "ad7b0e9c-5315-437d-bf92-22735af2708d"}, {"BlockType": "WORD", "Confidence": 97.37643432617188, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011363426223397255, "Height": 0.008117414079606533, "Left": 0.3743518888950348, "Top": 0.5394490957260132}, "Polygon": [{"X": 0.3743518888950348, "Y": 0.5394745469093323}, {"X": 0.38533926010131836, "Y": 0.5394490957260132}, {"X": 0.3857153058052063, "Y": 0.5475432872772217}, {"X": 0.3747326135635376, "Y": 0.5475665330886841}]}, "Id": "2cce38f9-a200-4745-bb3a-8ea64fe0f4b0"}, {"BlockType": "WORD", "Confidence": 98.57720947265625, "Text": "363.46", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06809592992067337, "Height": 0.01801452226936817, "Left": 0.43044766783714294, "Top": 0.5326091647148132}, "Polygon": [{"X": 0.43044766783714294, "Y": 0.53277587890625}, {"X": 0.4978218674659729, "Y": 0.5326091647148132}, {"X": 0.4985435903072357, "Y": 0.5504863262176514}, {"X": 0.43123310804367065, "Y": 0.5506237149238586}]}, "Id": "a752d2a1-d9a3-43f0-ab89-fa48f815ee44"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "sales", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04777642711997032, "Height": 0.01615472137928009, "Left": 0.27240124344825745, "Top": 0.5852804780006409}, "Polygon": [{"X": 0.27240124344825745, "Y": 0.5853369832038879}, {"X": 0.3193695545196533, "Y": 0.5852804780006409}, {"X": 0.32017767429351807, "Y": 0.6013970971107483}, {"X": 0.2732493281364441, "Y": 0.6014351844787598}]}, "Id": "cad97931-570b-41f7-9701-9d576dcad7e5"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "tax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03097311221063137, "Height": 0.016728999093174934, "Left": 0.3318595290184021, "Top": 0.5858325958251953}, "Polygon": [{"X": 0.3318595290184021, "Y": 0.5858684778213501}, {"X": 0.36203354597091675, "Y": 0.5858325958251953}, {"X": 0.362832635641098, "Y": 0.602537989616394}, {"X": 0.33268529176712036, "Y": 0.6025615930557251}]}, "Id": "a1dcc4d0-d1e6-46b2-9d29-5b6496c14b6d"}, {"BlockType": "WORD", "Confidence": 99.05213928222656, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01154934149235487, "Height": 0.008354442194104195, "Left": 0.37461549043655396, "Top": 0.589766263961792}, "Polygon": [{"X": 0.37461549043655396, "Y": 0.589778482913971}, {"X": 0.3857763111591339, "Y": 0.589766263961792}, {"X": 0.3861648142337799, "Y": 0.5981107950210571}, {"X": 0.3750089406967163, "Y": 0.5981207489967346}]}, "Id": "909760e0-9de3-4f3c-84a8-fed56bcaa114"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "0.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04632358253002167, "Height": 0.01736222580075264, "Left": 0.45406076312065125, "Top": 0.5838415622711182}, "Polygon": [{"X": 0.45406076312065125, "Y": 0.5838978886604309}, {"X": 0.49968472123146057, "Y": 0.5838415622711182}, {"X": 0.5003843307495117, "Y": 0.6011667251586914}, {"X": 0.45480236411094666, "Y": 0.6012037992477417}]}, "Id": "500aa039-f6ad-4f66-b5ea-3091fb32d6ba"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05335581675171852, "Height": 0.01767432503402233, "Left": 0.3112349808216095, "Top": 0.6099315881729126}, "Polygon": [{"X": 0.3112349808216095, "Y": 0.6099632978439331}, {"X": 0.36374631524086, "Y": 0.6099315881729126}, {"X": 0.3645907938480377, "Y": 0.6275967359542847}, {"X": 0.31212854385375977, "Y": 0.6276059150695801}]}, "Id": "462cb787-68f4-4cfd-aab7-057d6bc85ac2"}, {"BlockType": "WORD", "Confidence": 99.32058715820312, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011141611263155937, "Height": 0.007782547734677792, "Left": 0.37545132637023926, "Top": 0.615431010723114}, "Polygon": [{"X": 0.37545132637023926, "Y": 0.6154360771179199}, {"X": 0.3862304091453552, "Y": 0.615431010723114}, {"X": 0.38659292459487915, "Y": 0.6232105493545532}, {"X": 0.37581828236579895, "Y": 0.623213529586792}]}, "Id": "d59774ef-42b0-4d90-8af6-30efadb4481b"}, {"BlockType": "WORD", "Confidence": 99.52407836914062, "Text": "363.46", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06763007491827011, "Height": 0.01689782179892063, "Left": 0.431893914937973, "Top": 0.6096541881561279}, "Polygon": [{"X": 0.431893914937973, "Y": 0.6096949577331543}, {"X": 0.49884042143821716, "Y": 0.6096541881561279}, {"X": 0.49952399730682373, "Y": 0.6265386939048767}, {"X": 0.4326375126838684, "Y": 0.6265520453453064}]}, "Id": "af3255b6-38d7-4855-b2ad-a50faa1f347c"}, {"BlockType": "WORD", "Confidence": 98.6023178100586, "Text": "Signature:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11058777570724487, "Height": 0.021963756531476974, "Left": 0.5231125950813293, "Top": 0.6074680685997009}, "Polygon": [{"X": 0.5231125950813293, "Y": 0.6075405478477478}, {"X": 0.6329712867736816, "Y": 0.6074680685997009}, {"X": 0.6337003707885742, "Y": 0.6294175982475281}, {"X": 0.5239700078964233, "Y": 0.6294318437576294}]}, "Id": "4d65b93a-6800-40e8-9e8c-da071e155541"}, {"BlockType": "WORD", "Confidence": 73.44766235351562, "Text": "<PERSON><PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.2102731168270111, "Height": 0.0925038680434227, "Left": 0.6495888829231262, "Top": 0.5528362989425659}, "Polygon": [{"X": 0.6495888829231262, "Y": 0.5532461404800415}, {"X": 0.8579221963882446, "Y": 0.5528362989425659}, {"X": 0.8598620295524597, "Y": 0.6453402042388916}, {"X": 0.6525546312332153, "Y": 0.6452876925468445}]}, "Id": "41ea137f-aa08-4185-bb8b-49faf9055693"}, {"BlockType": "WORD", "Confidence": 98.79563903808594, "Text": "I", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00723130302503705, "Height": 0.01624104380607605, "Left": 0.6918386220932007, "Top": 0.6267730593681335}, "Polygon": [{"X": 0.6918386220932007, "Y": 0.6267743706703186}, {"X": 0.6985876560211182, "Y": 0.6267730593681335}, {"X": 0.6990699172019958, "Y": 0.6430140733718872}, {"X": 0.6923267245292664, "Y": 0.6430127620697021}]}, "Id": "b761a57e-3b7e-48ab-87ab-1b738725bc2c"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "agree", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05145075172185898, "Height": 0.019127817824482918, "Left": 0.7108200192451477, "Top": 0.6271938681602478}, "Polygon": [{"X": 0.7108200192451477, "Y": 0.6272031664848328}, {"X": 0.7617682814598083, "Y": 0.6271938681602478}, {"X": 0.7622707486152649, "Y": 0.6463216543197632}, {"X": 0.7113746404647827, "Y": 0.6463075280189514}]}, "Id": "b12188cf-5e7d-4533-9571-8e4167d56902"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017172571271657944, "Height": 0.015970144420862198, "Left": 0.7718445658683777, "Top": 0.622044563293457}, "Polygon": [{"X": 0.7718445658683777, "Y": 0.6220496892929077}, {"X": 0.7886209487915039, "Y": 0.622044563293457}, {"X": 0.7890170812606812, "Y": 0.6380147337913513}, {"X": 0.7722550630569458, "Y": 0.6380134224891663}]}, "Id": "e42c339e-46b3-4180-a1d6-62bcc7184023"}, {"BlockType": "WORD", "Confidence": 95.65349578857422, "Text": "pay", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026604842394590378, "Height": 0.018286556005477905, "Left": 0.7996382713317871, "Top": 0.6220688819885254}, "Polygon": [{"X": 0.7996382713317871, "Y": 0.6220768690109253}, {"X": 0.8258263468742371, "Y": 0.6220688819885254}, {"X": 0.8262431025505066, "Y": 0.6403554081916809}, {"X": 0.8000806570053101, "Y": 0.6403519511222839}]}, "Id": "9567c237-43b1-4698-bae8-f65af9e979d0"}, {"BlockType": "WORD", "Confidence": 99.80388641357422, "Text": "total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044812195003032684, "Height": 0.019529331475496292, "Left": 0.836371898651123, "Top": 0.6148192882537842}, "Polygon": [{"X": 0.836371898651123, "Y": 0.614840567111969}, {"X": 0.8807973861694336, "Y": 0.6148192882537842}, {"X": 0.8811841011047363, "Y": 0.6343481540679932}, {"X": 0.8368051052093506, "Y": 0.6343486309051514}]}, "Id": "de136eda-b9c4-456b-a265-fa664d91d4e3"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0519317202270031, "Height": 0.01985303685069084, "Left": 0.8909420371055603, "Top": 0.6104013323783875}, "Polygon": [{"X": 0.8909420371055603, "Y": 0.610431432723999}, {"X": 0.9425472617149353, "Y": 0.6104013323783875}, {"X": 0.9428737759590149, "Y": 0.6302487254142761}, {"X": 0.8913235664367676, "Y": 0.630254328250885}]}, "Id": "9a892da6-4556-4ac9-8f41-3f1825408663"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "according", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09587405622005463, "Height": 0.02310888096690178, "Left": 0.6476550102233887, "Top": 0.6488685011863708}, "Polygon": [{"X": 0.6476550102233887, "Y": 0.6488685011863708}, {"X": 0.7428985238075256, "Y": 0.6489008069038391}, {"X": 0.7435290217399597, "Y": 0.6719773411750793}, {"X": 0.6484031081199646, "Y": 0.6718922257423401}]}, "Id": "54bb437c-340b-4e04-96f6-3fda0ef757dd"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019390538334846497, "Height": 0.015829766169190407, "Left": 0.753178060054779, "Top": 0.6476133465766907}, "Polygon": [{"X": 0.753178060054779, "Y": 0.6476133465766907}, {"X": 0.7721614241600037, "Y": 0.6476192474365234}, {"X": 0.7725685834884644, "Y": 0.6634431481361389}, {"X": 0.7536013722419739, "Y": 0.6634300947189331}]}, "Id": "85faae9c-541a-482d-b161-fcd7da9d78f8"}, {"BlockType": "WORD", "Confidence": 99.02263641357422, "Text": "card", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03840429708361626, "Height": 0.017890602350234985, "Left": 0.7812252044677734, "Top": 0.6440171003341675}, "Polygon": [{"X": 0.7812252044677734, "Y": 0.6440171003341675}, {"X": 0.8192150592803955, "Y": 0.6440255641937256}, {"X": 0.8196294903755188, "Y": 0.6619077324867249}, {"X": 0.7816760540008545, "Y": 0.6618829965591431}]}, "Id": "d3802d42-4553-4c8c-92af-1b637f2f9b69"}, {"BlockType": "WORD", "Confidence": 98.79644012451172, "Text": "issuer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05383456125855446, "Height": 0.018311256542801857, "Left": 0.8300656080245972, "Top": 0.6404123902320862}, "Polygon": [{"X": 0.8300656080245972, "Y": 0.6404123902320862}, {"X": 0.8835399150848389, "Y": 0.640419602394104}, {"X": 0.8839001655578613, "Y": 0.****************}, {"X": 0.8304783701896667, "Y": 0.****************}]}, "Id": "a192649f-751c-43c6-b8d6-9569dd4676ad"}, {"BlockType": "WORD", "Confidence": 99.64205932617188, "Text": "agreement.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0803200975060463, "Height": 0.02604503557085991, "Left": 0.8925997018814087, "Top": 0.6309276819229126}, "Polygon": [{"X": 0.8925997018814087, "Y": 0.6309350728988647}, {"X": 0.9725332260131836, "Y": 0.6309276819229126}, {"X": 0.9729198217391968, "Y": 0.6569727063179016}, {"X": 0.8930982947349548, "Y": 0.6569303274154663}]}, "Id": "2e1956c6-cd8e-47b8-91d2-344d93eb714c"}, {"BlockType": "WORD", "Confidence": 99.5533676147461, "Text": "Salesperson", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10151645541191101, "Height": 0.029245072975754738, "Left": 0.2129642814397812, "Top": 0.6957677602767944}, "Polygon": [{"X": 0.2129642814397812, "Y": 0.6957677602767944}, {"X": 0.31300294399261475, "Y": 0.695916473865509}, {"X": 0.3144807517528534, "Y": 0.7250128388404846}, {"X": 0.21459650993347168, "Y": 0.7247936725616455}]}, "Id": "fa593aff-ac3a-4022-9139-88cce8e35c90"}, {"BlockType": "WORD", "Confidence": 99.12946319580078, "Text": "ID:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028362490236759186, "Height": 0.01770421303808689, "Left": 0.32533493638038635, "Top": 0.7075225114822388}, "Polygon": [{"X": 0.32533493638038635, "Y": 0.7075225114822388}, {"X": 0.3528384268283844, "Y": 0.7075711488723755}, {"X": 0.35369741916656494, "Y": 0.7252267599105835}, {"X": 0.326219767332077, "Y": 0.725166380405426}]}, "Id": "466d01f7-612f-4075-85e9-7b816bce85a9"}, {"BlockType": "WORD", "Confidence": 99.13105773925781, "Text": "AUTO/SMARTFUEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15810517966747284, "Height": 0.01819894090294838, "Left": 0.3671899735927582, "Top": 0.7076185941696167}, "Polygon": [{"X": 0.3671899735927582, "Y": 0.7076185941696167}, {"X": 0.5245908498764038, "Y": 0.7078970670700073}, {"X": 0.5252951383590698, "Y": 0.7258175611495972}, {"X": 0.36804458498954773, "Y": 0.7254711985588074}]}, "Id": "7e4721df-cbec-4615-a6a5-690c7b84b231"}, {"BlockType": "WORD", "Confidence": 99.81365966796875, "Text": "Invoice", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07312220335006714, "Height": 0.020197926089167595, "Left": 0.6924658417701721, "Top": 0.6970885396003723}, "Polygon": [{"X": 0.6924658417701721, "Y": 0.6970885396003723}, {"X": 0.7650619149208069, "Y": 0.6971974968910217}, {"X": 0.7655880451202393, "Y": 0.7172864675521851}, {"X": 0.6930702328681946, "Y": 0.7171425223350525}]}, "Id": "c56f2afe-0053-41fc-9f48-da43fd129ed9"}, {"BlockType": "WORD", "Confidence": 96.95930480957031, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017118031159043312, "Height": 0.017491085454821587, "Left": 0.7751505374908447, "Top": 0.6939499378204346}, "Polygon": [{"X": 0.7751505374908447, "Y": 0.6939499378204346}, {"X": 0.7918366193771362, "Y": 0.6939736604690552}, {"X": 0.7922685742378235, "Y": 0.7114410400390625}, {"X": 0.7755981683731079, "Y": 0.7114102840423584}]}, "Id": "e4360de3-39be-431d-9026-c36a32bc06de"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "0091223", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06577359139919281, "Height": 0.022486422210931778, "Left": 0.8037914633750916, "Top": 0.6865362524986267}, "Polygon": [{"X": 0.8037914633750916, "Y": 0.6865362524986267}, {"X": 0.8691054582595825, "Y": 0.6866174340248108}, {"X": 0.8695650696754456, "Y": 0.7090226411819458}, {"X": 0.8043298721313477, "Y": 0.7089064121246338}]}, "Id": "00a9b08c-5840-4936-8b41-47c2587ef6ae"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0459342896938324, "Height": 0.02184419147670269, "Left": 0.23135431110858917, "Top": 0.721206784248352}, "Polygon": [{"X": 0.23135431110858917, "Y": 0.721206784248352}, {"X": 0.2761387228965759, "Y": 0.7213010787963867}, {"X": 0.27728861570358276, "Y": 0.7430509924888611}, {"X": 0.23255598545074463, "Y": 0.7429330945014954}]}, "Id": "d6ac2213-2c6f-4f8a-bb27-50ddff85c85d"}, {"BlockType": "WORD", "Confidence": 99.33613586425781, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06900437921285629, "Height": 0.022798597812652588, "Left": 0.2852075695991516, "Top": 0.7275280952453613}, "Polygon": [{"X": 0.2852075695991516, "Y": 0.7275280952453613}, {"X": 0.35310932993888855, "Y": 0.7276813387870789}, {"X": 0.3542119562625885, "Y": 0.7503266930580139}, {"X": 0.28639206290245056, "Y": 0.7501363158226013}]}, "Id": "1192dd81-da94-4ef9-962c-1c096bc92c76"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "3482", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04460356384515762, "Height": 0.01700223609805107, "Left": 0.3675599992275238, "Top": 0.7327490448951721}, "Polygon": [{"X": 0.3675599992275238, "Y": 0.7327490448951721}, {"X": 0.41139426827430725, "Y": 0.732853353023529}, {"X": 0.4121635854244232, "Y": 0.7497513294219971}, {"X": 0.36836883425712585, "Y": 0.7496291399002075}]}, "Id": "5af6d568-aa63-48a7-a840-40687cb59115"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "Merchant", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08531639724969864, "Height": 0.02377857081592083, "Left": 0.6267457604408264, "Top": 0.724143922328949}, "Polygon": [{"X": 0.6267457604408264, "Y": 0.724143922328949}, {"X": 0.7113742828369141, "Y": 0.7243264317512512}, {"X": 0.7120621800422668, "Y": 0.7479224801063538}, {"X": 0.6275409460067749, "Y": 0.7476921677589417}]}, "Id": "54f77beb-8b5a-410a-b5bf-162c0293552f"}, {"BlockType": "WORD", "Confidence": 99.55496978759766, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07168173044919968, "Height": 0.018726397305727005, "Left": 0.7230977416038513, "Top": 0.7211973667144775}, "Polygon": [{"X": 0.7230977416038513, "Y": 0.7211973667144775}, {"X": 0.7943218946456909, "Y": 0.7213455438613892}, {"X": 0.7947794795036316, "Y": 0.7399237155914307}, {"X": 0.7236264944076538, "Y": 0.7397438883781433}]}, "Id": "339a8c7c-cc57-40ff-83ab-7901a6cf20fc"}, {"BlockType": "WORD", "Confidence": 97.54643249511719, "Text": "*******1398", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10170920193195343, "Height": 0.026690106838941574, "Left": 0.8055887222290039, "Top": 0.7064866423606873}, "Polygon": [{"X": 0.8055887222290039, "Y": 0.7064866423606873}, {"X": 0.9068077802658081, "Y": 0.7066610455513}, {"X": 0.9072979092597961, "Y": 0.7331767082214355}, {"X": 0.806223452091217, "Y": 0.732938289642334}]}, "Id": "9a01d72a-02ef-40f3-9230-e6266cb6df5f"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Card", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03696366399526596, "Height": 0.020019518211483955, "Left": 0.2411057949066162, "Top": 0.746216356754303}, "Polygon": [{"X": 0.2411057949066162, "Y": 0.746216356754303}, {"X": 0.27701571583747864, "Y": 0.7463138699531555}, {"X": 0.27806946635246277, "Y": 0.766235888004303}, {"X": 0.24219761788845062, "Y": 0.7661210894584656}]}, "Id": "356d63f2-58c5-4d29-b4e3-3756fe5e4f6b"}, {"BlockType": "WORD", "Confidence": 99.*************, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06867770105600357, "Height": 0.022696632891893387, "Left": 0.28622278571128845, "Top": 0.7517635226249695}, "Polygon": [{"X": 0.28622278571128845, "Y": 0.7517635226249695}, {"X": 0.3538041114807129, "Y": 0.7519559264183044}, {"X": 0.3549004793167114, "Y": 0.7744601368904114}, {"X": 0.2874002158641815, "Y": 0.7742310762405396}]}, "Id": "c044adcd-afc6-4f7f-8388-b7c9d7f679fd"}, {"BlockType": "WORD", "Confidence": 65.74138641357422, "Text": "xxxxxxxxxxxxxx1773", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.20462505519390106, "Height": 0.017536019906401634, "Left": 0.3677917718887329, "Top": 0.7588754296302795}, "Polygon": [{"X": 0.3677917718887329, "Y": 0.7588754296302795}, {"X": 0.5717935562133789, "Y": 0.7594903111457825}, {"X": 0.5724168419837952, "Y": 0.7764114737510681}, {"X": 0.368599534034729, "Y": 0.7757138609886169}]}, "Id": "43219bb3-0304-439c-9c34-7400fac2c048"}, {"BlockType": "WORD", "Confidence": 97.77582550048828, "Text": "-ICR-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05622268468141556, "Height": 0.018872780725359917, "Left": 0.5834147334098816, "Top": 0.7570782899856567}, "Polygon": [{"X": 0.5834147334098816, "Y": 0.7570782899856567}, {"X": 0.6390171051025391, "Y": 0.7572426199913025}, {"X": 0.6396374106407166, "Y": 0.775951087474823}, {"X": 0.5840909481048584, "Y": 0.7757619023323059}]}, "Id": "2965e3dd-9ceb-4916-ac13-35b673470d90"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "Loyalty", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06409601122140884, "Height": 0.028863420709967613, "Left": 0.22445522248744965, "Top": 0.7656084895133972}, "Polygon": [{"X": 0.22445522248744965, "Y": 0.7656084895133972}, {"X": 0.28704920411109924, "Y": 0.76580810546875}, {"X": 0.2885512411594391, "Y": 0.7944719195365906}, {"X": 0.22605283558368683, "Y": 0.7942290306091309}]}, "Id": "f783f579-0246-454d-87de-7e9b7713120b"}, {"BlockType": "WORD", "Confidence": 99.6827163696289, "Text": "Card#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05880158022046089, "Height": 0.02238260768353939, "Left": 0.2966969311237335, "Top": 0.7765152454376221}, "Polygon": [{"X": 0.2966969311237335, "Y": 0.7765152454376221}, {"X": 0.3544170558452606, "Y": 0.7767143249511719}, {"X": 0.3554985225200653, "Y": 0.7988978624343872}, {"X": 0.2978467643260956, "Y": 0.7986680269241333}]}, "Id": "21a32567-773d-4a4e-8a97-298b2c90a844"}, {"BlockType": "WORD", "Confidence": 84.21855163574219, "Text": "xxxxxxxxxxxx8997", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1798325926065445, "Height": 0.01757710427045822, "Left": 0.36866942048072815, "Top": 0.783189058303833}, "Polygon": [{"X": 0.36866942048072815, "Y": 0.783189058303833}, {"X": 0.****************, "Y": 0.7838349342346191}, {"X": 0.****************, "Y": 0.8007661700248718}, {"X": 0.36947840452194214, "Y": 0.8000476360321045}]}, "Id": "a9f3052b-ead7-474d-8694-8f82204a36a6"}, {"BlockType": "WORD", "Confidence": 96.80703735351562, "Text": "-ICR-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057696156203746796, "Height": 0.01732446998357773, "Left": 0.5601843595504761, "Top": 0.7837805151939392}, "Polygon": [{"X": 0.5601843595504761, "Y": 0.7837805151939392}, {"X": 0.6172916889190674, "Y": 0.7839862108230591}, {"X": 0.6178805232048035, "Y": 0.8011049628257751}, {"X": 0.5608257055282593, "Y": 0.8008759021759033}]}, "Id": "7e0c3f7c-c4d8-4158-9325-19e63f118bbf"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Billing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05931319296360016, "Height": 0.03139117732644081, "Left": 0.18706189095973969, "Top": 0.8014000654220581}, "Polygon": [{"X": 0.18706189095973969, "Y": 0.8014000654220581}, {"X": 0.2446669191122055, "Y": 0.8016341924667358}, {"X": 0.24637508392333984, "Y": 0.8327912092208862}, {"X": 0.1888657659292221, "Y": 0.8325138688087463}]}, "Id": "484f98b5-d580-4dda-81d1-4b0143cf3e31"}, {"BlockType": "WORD", "Confidence": 97.84837341308594, "Text": "Company:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07537521421909332, "Height": 0.030342450365424156, "Left": 0.2519053518772125, "Top": 0.8163847327232361}, "Polygon": [{"X": 0.2519053518772125, "Y": 0.8163847327232361}, {"X": 0.32576698064804077, "Y": 0.81671142578125}, {"X": 0.32728055119514465, "Y": 0.846727192401886}, {"X": 0.25353747606277466, "Y": 0.8463472127914429}]}, "Id": "6fde9aac-7de6-430d-ad42-6389a3f0c009"}, {"BlockType": "WORD", "Confidence": 99.5703125, "Text": "NBS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032829400151968, "Height": 0.018417399376630783, "Left": 0.33770400285720825, "Top": 0.8290317058563232}, "Polygon": [{"X": 0.33770400285720825, "Y": 0.8290317058563232}, {"X": 0.3696556091308594, "Y": 0.8291825652122498}, {"X": 0.37053340673446655, "Y": 0.8474490642547607}, {"X": 0.3386130928993225, "Y": 0.847284197807312}]}, "Id": "a5df40ca-3181-4de7-abde-761d4d8b79a6"}, {"BlockType": "WORD", "Confidence": 95.*********54688, "Text": "/", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009757322259247303, "Height": 0.01656789891421795, "Left": 0.38188663125038147, "Top": 0.8316590785980225}, "Polygon": [{"X": 0.38188663125038147, "Y": 0.8316590785980225}, {"X": 0.3908689618110657, "Y": 0.8317020535469055}, {"X": 0.3916439712047577, "Y": 0.8482270240783691}, {"X": 0.382669597864151, "Y": 0.8481805324554443}]}, "Id": "e9eb9d40-d113-43bd-b795-308d64b431e6"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "KWIK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04477133974432945, "Height": 0.017607683315873146, "Left": 0.4028354585170746, "Top": 0.8316909670829773}, "Polygon": [{"X": 0.4028354585170746, "Y": 0.8316909670829773}, {"X": 0.4468442499637604, "Y": 0.8319012522697449}, {"X": 0.44760677218437195, "Y": 0.8492986559867859}, {"X": 0.4036391079425812, "Y": 0.8490700125694275}]}, "Id": "88aa43c2-4557-4e18-9f72-87e183b03847"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "TRIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04509727656841278, "Height": 0.01852131448686123, "Left": 0.4592130482196808, "Top": 0.8329562544822693}, "Polygon": [{"X": 0.4592130482196808, "Y": 0.8329562544822693}, {"X": 0.5035647749900818, "Y": 0.8331692814826965}, {"X": 0.5043103098869324, "Y": 0.8514776229858398}, {"X": 0.4600023031234741, "Y": 0.8512451648712158}]}, "Id": "8ce87358-ded1-406f-b5fe-5a412e47ba93"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FLEET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057819921523332596, "Height": 0.019583620131015778, "Left": 0.5153637528419495, "Top": 0.8326044082641602}, "Polygon": [{"X": 0.5153637528419495, "Y": 0.8326044082641602}, {"X": 0.5724703073501587, "Y": 0.8328778147697449}, {"X": 0.5731836557388306, "Y": 0.852187991142273}, {"X": 0.5161365866661072, "Y": 0.8518882989883423}]}, "Id": "af459524-fb2f-4166-9f2f-7628e6a6c107"}, {"BlockType": "WORD", "Confidence": 99.84215545654297, "Text": "ALL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027230430394411087, "Height": 0.02141924761235714, "Left": 0.18947510421276093, "Top": 0.844785749912262}, "Polygon": [{"X": 0.18947510421276093, "Y": 0.844785749912262}, {"X": 0.21550171077251434, "Y": 0.8449190258979797}, {"X": 0.2167055308818817, "Y": 0.8662049770355225}, {"X": 0.19070853292942047, "Y": 0.8660582900047302}]}, "Id": "918da1a5-0a18-45fd-b66a-e3852f450887"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "TAXES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04509909823536873, "Height": 0.02604593150317669, "Left": 0.22098200023174286, "Top": 0.852887749671936}, "Polygon": [{"X": 0.22098200023174286, "Y": 0.852887749671936}, {"X": 0.26469025015830994, "Y": 0.8531200885772705}, {"X": 0.2660810947418213, "Y": 0.8789336681365967}, {"X": 0.22243322432041168, "Y": 0.8786741495132446}]}, "Id": "2c550151-092d-4f81-ab9f-081aee7b5b1d"}, {"BlockType": "WORD", "Confidence": 99.*********, "Text": "INCLUDED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07747171074151993, "Height": 0.028621619567275047, "Left": 0.27390050888061523, "Top": 0.8651893138885498}, "Polygon": [{"X": 0.27390050888061523, "Y": 0.8651893138885498}, {"X": 0.34998413920402527, "Y": 0.8656160235404968}, {"X": 0.35137224197387695, "Y": 0.8938109278678894}, {"X": 0.2754036486148834, "Y": 0.8933327794075012}]}, "Id": "404d3d0a-606a-485b-b58e-26f51292dbfc"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "IN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020212052389979362, "Height": 0.017251525074243546, "Left": 0.36215147376060486, "Top": 0.8787068128585815}, "Polygon": [{"X": 0.36215147376060486, "Y": 0.8787068128585815}, {"X": 0.3815489709377289, "Y": 0.8788217306137085}, {"X": 0.38236352801322937, "Y": 0.8959583044052124}, {"X": 0.3629838824272156, "Y": 0.8958353996276855}]}, "Id": "d0bea79b-92ad-4665-ab07-5de5dc2d3a79"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "FUEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044841330498456955, "Height": 0.019292065873742104, "Left": 0.3924159109592438, "Top": 0.8801783323287964}, "Polygon": [{"X": 0.3924159109592438, "Y": 0.8801783323287964}, {"X": 0.43641000986099243, "Y": 0.8804404139518738}, {"X": 0.43725723028182983, "Y": 0.8994703888893127}, {"X": 0.39330819249153137, "Y": 0.8991883397102356}]}, "Id": "ba1678c2-f9ab-41e5-9c47-3086ed73b8db"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "PRICE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05683993175625801, "Height": 0.020525477826595306, "Left": 0.44770893454551697, "Top": 0.8826239109039307}, "Polygon": [{"X": 0.44770893454551697, "Y": 0.8826239109039307}, {"X": 0.5037246346473694, "Y": 0.8829604387283325}, {"X": 0.5045488476753235, "Y": 0.9031493663787842}, {"X": 0.4485941529273987, "Y": 0.9027858972549438}]}, "Id": "fb776747-97f9-4948-82f0-fd3bddc2aa9d"}, {"BlockType": "WORD", "Confidence": 26.526227951049805, "Text": "-----------------", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03481912985444069, "Height": 0.005659806076437235, "Left": 0.5274569392204285, "Top": 0.8909307718276978}, "Polygon": [{"X": 0.5274569392204285, "Y": 0.8909307718276978}, {"X": 0.5620711445808411, "Y": 0.8911452889442444}, {"X": 0.5622761249542236, "Y": 0.8965905904769897}, {"X": 0.5276721119880676, "Y": 0.896371603012085}]}, "Id": "2a571c0a-5a49-4152-98bb-32df7c211340"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "THANK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056433722376823425, "Height": 0.01773296669125557, "Left": 0.5854092240333557, "Top": 0.8816298842430115}, "Polygon": [{"X": 0.5854092240333557, "Y": 0.8816298842430115}, {"X": 0.6412642002105713, "Y": 0.8819629549980164}, {"X": 0.6418429613113403, "Y": 0.8993628025054932}, {"X": 0.5860405564308167, "Y": 0.8990066051483154}]}, "Id": "00b55a19-2d25-494a-a155-755ed5993cd9"}, {"BlockType": "WORD", "Confidence": 99.72417449951172, "Text": "YOU!", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042814578860998154, "Height": 0.01926741935312748, "Left": 0.6534104347229004, "Top": 0.8800808787345886}, "Polygon": [{"X": 0.6534104347229004, "Y": 0.8800808787345886}, {"X": 0.6956493258476257, "Y": 0.8803308010101318}, {"X": 0.6962249875068665, "Y": 0.8993483185768127}, {"X": 0.6540295481681824, "Y": 0.8990793228149414}]}, "Id": "082da029-d091-41e0-95fc-4f59ec72fba6"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "af098a95-6e09-480e-a3c3-5273739ab74a", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "af098a95-6e09-480e-a3c3-5273739ab74a", "content-type": "application/x-amz-json-1.1", "content-length": "76183", "date": "Thu, 18 Sep 2025 08:51:27 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "2881b03e-c07f-4a5e-bc5f-391f9e20b317", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 08:51:31 GMT", "content-type": "application/json", "content-length": "956", "connection": "keep-alive", "x-amzn-requestid": "2881b03e-c07f-4a5e-bc5f-391f9e20b317"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a page. The content appears to be a fuel receipt. It's a fuel receipt for a truck. Keywords: Fuel, Diesel, Invoice #, etc. According to the enum definitions, \"fuel_receipt\" is for receipt for fuel purchase. So classification should be fuel_receipt.\n\nWe need to output using tool classify_logistics_doc_type. For page 1, we output documents array with one entry: page_no 1, doc_type \"fuel_receipt\".\n\nWe must not output anything else. So call the function with {\"documents\":[{page_no:1, doc_type:\"fuel_receipt\"}]}.\n\nLet's do that."}}}, {"toolUse": {"toolUseId": "tooluse_1xxKvtMCQuC7DKWcigMb-Q", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "fuel_receipt"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2375, "outputTokens": 169, "totalTokens": 2544}, "metrics": {"latencyMs": 783}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/390d5a69_9_fuel_receipt_8.jpg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check whether current page is a contination of previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc. or any other way which indicates that it is a continuation of previous page and document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: Scale ticket may contain keywords like Bill of ladding, Invoice, etc. but if it contains weight related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no. then it is a scale ticket.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n<PERSON><PERSON>K TRIP 139 /7002 SAUK TRAIL RD. /SHEBOYGAN, WI 53081/(920) 457-1028\nNumber: 0900-1223 08/13/2025 Station: 9\nResponse: APPROVED AUTH REF#: P19403\nLOYALTY Response: APPROVED, You earned 2.06 Fleet points! Drink Credit Earned! E\nxpires in 24 Hours. Shower C\nPump\nProducts\nReefer\nQuantity\nUnit Cost\nTotal\n# 1\nDiesel 2\nN\n103.875\n3.499\n363.46\nsubtotal =\n363.46\nsales tax =\n0.00\nTOTAL =\n363.46 Signature:\n<PERSON><PERSON> I agree to pay total amount\naccording to card issuer agreement.\nSalesperson ID: AUTO/SMARTFUEL\nInvoice #: 0091223\nTruck Number: 3482\nMerchant Number: *******1398\nCard Number: xxxxxxxxxxxxxx1773 -ICR-\nLoyalty Card#: xxxxxxxxxxxx8997 -ICR-\nBilling Company: NBS / KWIK TRIP FLEET\nALL TAXES INCLUDED IN FUEL PRICE ----------------- THANK YOU!\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}